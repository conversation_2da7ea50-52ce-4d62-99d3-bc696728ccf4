"""
Logging utilities for cross-platform compatibility
"""

import sys
import logging
from typing import Any


def safe_unicode_log(logger: logging.Logger, level: int, message: str, *args: Any, **kwargs: Any):
    """
    Safely log messages with Unicode characters, falling back to ASCII if needed
    """
    try:
        # Try to log with Unicode characters
        logger.log(level, message, *args, **kwargs)
    except UnicodeEncodeError:
        # Fall back to ASCII-safe version
        ascii_message = message.replace('✓', '[OK]').replace('✗', '[FAIL]').replace('⚠', '[WARN]')
        logger.log(level, ascii_message, *args, **kwargs)


def safe_info(logger: logging.Logger, message: str, *args: Any, **kwargs: Any):
    """Safely log info messages"""
    safe_unicode_log(logger, logging.INFO, message, *args, **kwargs)


def safe_success(logger: logging.Logger, message: str, *args: Any, **kwargs: Any):
    """Safely log success messages"""
    # Replace checkmark with [OK] for Windows compatibility
    safe_message = message.replace('✓', '[OK]')
    safe_unicode_log(logger, logging.INFO, safe_message, *args, **kwargs)


def safe_error(logger: logging.Logger, message: str, *args: Any, **kwargs: Any):
    """Safely log error messages"""
    safe_message = message.replace('✗', '[FAIL]')
    safe_unicode_log(logger, logging.ERROR, safe_message, *args, **kwargs)


def safe_warning(logger: logging.Logger, message: str, *args: Any, **kwargs: Any):
    """Safely log warning messages"""
    safe_message = message.replace('⚠', '[WARN]')
    safe_unicode_log(logger, logging.WARNING, safe_message, *args, **kwargs)


def get_status_symbol(success: bool) -> str:
    """Get platform-safe status symbol"""
    if sys.platform.startswith('win'):
        return '[OK]' if success else '[FAIL]'
    else:
        return '✓' if success else '✗'


def format_success_message(message: str) -> str:
    """Format success message with platform-safe symbols"""
    if sys.platform.startswith('win'):
        return message.replace('✓', '[OK]')
    return message


def format_error_message(message: str) -> str:
    """Format error message with platform-safe symbols"""
    if sys.platform.startswith('win'):
        return message.replace('✗', '[FAIL]')
    return message


def format_warning_message(message: str) -> str:
    """Format warning message with platform-safe symbols"""
    if sys.platform.startswith('win'):
        return message.replace('⚠', '[WARN]')
    return message

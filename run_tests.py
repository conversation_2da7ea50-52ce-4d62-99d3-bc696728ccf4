#!/usr/bin/env python3
"""
Test Runner Script

This script provides a convenient way to run different types of tests
for the Advanced Ollama Trading Agent System.

Usage:
    python run_tests.py --help
    python run_tests.py --unit
    python run_tests.py --integration
    python run_tests.py --performance
    python run_tests.py --all
    python run_tests.py --coverage
"""

import argparse
import asyncio
import logging
import sys
import subprocess
from pathlib import Path
from typing import List, Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestRunner:
    """Test runner for the trading system"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_dir = self.project_root / "tests"
        
    def run_command(self, command: List[str], cwd: Path = None) -> int:
        """Run a command and return exit code"""
        try:
            logger.info(f"Running command: {' '.join(command)}")
            
            result = subprocess.run(
                command,
                cwd=cwd or self.project_root,
                capture_output=False,
                text=True
            )
            
            return result.returncode
            
        except Exception as e:
            logger.error(f"Error running command: {e}")
            return 1
    
    def run_unit_tests(self) -> int:
        """Run unit tests"""
        logger.info("Running unit tests...")
        
        command = [
            sys.executable, "-m", "pytest",
            str(self.test_dir / "unit"),
            "-v",
            "--tb=short",
            "-m", "unit"
        ]
        
        return self.run_command(command)
    
    def run_integration_tests(self) -> int:
        """Run integration tests"""
        logger.info("Running integration tests...")
        
        command = [
            sys.executable, "-m", "pytest",
            str(self.test_dir / "integration"),
            "-v",
            "--tb=short",
            "-m", "integration"
        ]
        
        return self.run_command(command)
    
    def run_performance_tests(self) -> int:
        """Run performance tests"""
        logger.info("Running performance tests...")
        
        command = [
            sys.executable, "-m", "pytest",
            str(self.test_dir / "performance"),
            "-v",
            "--tb=short",
            "-m", "performance"
        ]
        
        return self.run_command(command)
    
    def run_security_tests(self) -> int:
        """Run security tests"""
        logger.info("Running security tests...")
        
        command = [
            sys.executable, "-m", "pytest",
            str(self.test_dir),
            "-v",
            "--tb=short",
            "-m", "security"
        ]
        
        return self.run_command(command)
    
    def run_smoke_tests(self) -> int:
        """Run smoke tests"""
        logger.info("Running smoke tests...")
        
        command = [
            sys.executable, "-m", "pytest",
            str(self.test_dir),
            "-v",
            "--tb=short",
            "-m", "smoke"
        ]
        
        return self.run_command(command)
    
    def run_all_tests(self) -> int:
        """Run all tests"""
        logger.info("Running all tests...")
        
        command = [
            sys.executable, "-m", "pytest",
            str(self.test_dir),
            "-v",
            "--tb=short"
        ]
        
        return self.run_command(command)
    
    def run_with_coverage(self) -> int:
        """Run tests with coverage"""
        logger.info("Running tests with coverage...")
        
        command = [
            sys.executable, "-m", "pytest",
            str(self.test_dir),
            "-v",
            "--tb=short",
            "--cov=.",
            "--cov-report=html",
            "--cov-report=term-missing",
            "--cov-report=xml",
            "--cov-fail-under=80"
        ]
        
        return self.run_command(command)
    
    def run_parallel_tests(self) -> int:
        """Run tests in parallel"""
        logger.info("Running tests in parallel...")
        
        command = [
            sys.executable, "-m", "pytest",
            str(self.test_dir),
            "-v",
            "--tb=short",
            "-n", "auto"  # Requires pytest-xdist
        ]
        
        return self.run_command(command)
    
    def run_specific_test(self, test_path: str) -> int:
        """Run a specific test file or function"""
        logger.info(f"Running specific test: {test_path}")
        
        command = [
            sys.executable, "-m", "pytest",
            test_path,
            "-v",
            "--tb=short"
        ]
        
        return self.run_command(command)
    
    def lint_code(self) -> int:
        """Run code linting"""
        logger.info("Running code linting...")
        
        # Run flake8
        flake8_command = [
            sys.executable, "-m", "flake8",
            ".",
            "--exclude=venv,env,__pycache__,.git",
            "--max-line-length=100",
            "--ignore=E203,W503"
        ]
        
        flake8_result = self.run_command(flake8_command)
        
        # Run black check
        black_command = [
            sys.executable, "-m", "black",
            ".",
            "--check",
            "--exclude=/(venv|env|__pycache__|\.git)/"
        ]
        
        black_result = self.run_command(black_command)
        
        return max(flake8_result, black_result)
    
    def format_code(self) -> int:
        """Format code with black"""
        logger.info("Formatting code...")
        
        command = [
            sys.executable, "-m", "black",
            ".",
            "--exclude=/(venv|env|__pycache__|\.git)/"
        ]
        
        return self.run_command(command)
    
    def validate_system(self) -> int:
        """Run comprehensive system validation"""
        logger.info("Running comprehensive system validation...")
        
        # Run smoke tests first
        smoke_result = self.run_smoke_tests()
        if smoke_result != 0:
            logger.error("Smoke tests failed, skipping full validation")
            return smoke_result
        
        # Run security tests
        security_result = self.run_security_tests()
        if security_result != 0:
            logger.error("Security tests failed")
            return security_result
        
        # Run all tests with coverage
        full_result = self.run_with_coverage()
        
        return full_result
    
    def generate_test_report(self) -> int:
        """Generate comprehensive test report"""
        logger.info("Generating test report...")
        
        command = [
            sys.executable, "-m", "pytest",
            str(self.test_dir),
            "--html=test_report.html",
            "--self-contained-html",
            "--junit-xml=test_results.xml",
            "--cov=.",
            "--cov-report=html",
            "--cov-report=xml"
        ]
        
        return self.run_command(command)


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Test runner for Advanced Ollama Trading Agent System"
    )
    
    # Test type options
    parser.add_argument("--unit", action="store_true", help="Run unit tests")
    parser.add_argument("--integration", action="store_true", help="Run integration tests")
    parser.add_argument("--performance", action="store_true", help="Run performance tests")
    parser.add_argument("--security", action="store_true", help="Run security tests")
    parser.add_argument("--smoke", action="store_true", help="Run smoke tests")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    
    # Coverage and reporting
    parser.add_argument("--coverage", action="store_true", help="Run tests with coverage")
    parser.add_argument("--parallel", action="store_true", help="Run tests in parallel")
    parser.add_argument("--report", action="store_true", help="Generate test report")
    
    # Code quality
    parser.add_argument("--lint", action="store_true", help="Run code linting")
    parser.add_argument("--format", action="store_true", help="Format code")
    
    # Specific test
    parser.add_argument("--test", type=str, help="Run specific test file or function")
    
    # System validation
    parser.add_argument("--validate", action="store_true", help="Run comprehensive system validation")
    
    # Verbose output
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create test runner
    runner = TestRunner()
    
    # Determine which tests to run
    exit_code = 0
    
    try:
        if args.unit:
            exit_code = runner.run_unit_tests()
        elif args.integration:
            exit_code = runner.run_integration_tests()
        elif args.performance:
            exit_code = runner.run_performance_tests()
        elif args.security:
            exit_code = runner.run_security_tests()
        elif args.smoke:
            exit_code = runner.run_smoke_tests()
        elif args.all:
            exit_code = runner.run_all_tests()
        elif args.coverage:
            exit_code = runner.run_with_coverage()
        elif args.parallel:
            exit_code = runner.run_parallel_tests()
        elif args.report:
            exit_code = runner.generate_test_report()
        elif args.lint:
            exit_code = runner.lint_code()
        elif args.format:
            exit_code = runner.format_code()
        elif args.test:
            exit_code = runner.run_specific_test(args.test)
        elif args.validate:
            exit_code = runner.validate_system()
        else:
            # Default: run smoke tests
            logger.info("No specific test type specified, running smoke tests...")
            exit_code = runner.run_smoke_tests()
        
        if exit_code == 0:
            logger.info("✅ Tests completed successfully!")
        else:
            logger.error("❌ Tests failed!")
        
    except KeyboardInterrupt:
        logger.info("Test execution interrupted by user")
        exit_code = 130
    except Exception as e:
        logger.error(f"Error running tests: {e}")
        exit_code = 1
    
    sys.exit(exit_code)


if __name__ == "__main__":
    main()

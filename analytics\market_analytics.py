"""
Market Analytics - Advanced real-time market analysis
"""

import asyncio
import logging
import time
import numpy as np
from typing import Dict, List, Optional, Any
from collections import deque
import statistics

logger = logging.getLogger(__name__)


class MarketAnalytics:
    """
    Advanced real-time market analytics engine.
    Provides sophisticated market analysis capabilities.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Analytics state
        self.market_regimes: Dict[str, str] = {}
        self.volatility_forecasts: Dict[str, float] = {}
        self.correlation_matrix: Dict[str, Dict[str, float]] = {}
        
        # Real-time data streams
        self.price_streams: Dict[str, deque] = {}
        self.volume_streams: Dict[str, deque] = {}
        self.volatility_streams: Dict[str, deque] = {}
        
        # Analytics models
        self.regime_models: Dict[str, Any] = {}
        self.volatility_models: Dict[str, Any] = {}
        
        # State
        self.initialized = False
        self.running = False
        
    async def initialize(self):
        """Initialize market analytics"""
        if self.initialized:
            return
            
        logger.info("Initializing Market Analytics...")
        
        # Initialize analytics models
        await self._setup_analytics_models()
        
        # Initialize data streams
        await self._setup_data_streams()
        
        self.initialized = True
        logger.info("✓ Market Analytics initialized")
        
    async def _setup_analytics_models(self):
        """Setup analytics models"""
        # Market regime detection models
        self.regime_models = {
            'volatility_regime': {
                'low_vol_threshold': 0.15,
                'high_vol_threshold': 0.30,
                'lookback_period': 20
            },
            'trend_regime': {
                'trend_strength_threshold': 0.6,
                'lookback_period': 50
            },
            'liquidity_regime': {
                'liquidity_threshold': 0.8,
                'lookback_period': 10
            }
        }
        
        # Volatility forecasting models
        self.volatility_models = {
            'ewma': {'lambda': 0.94},
            'garch': {'alpha': 0.1, 'beta': 0.85},
            'realized_vol': {'lookback': 20}
        }
        
    async def _setup_data_streams(self):
        """Setup real-time data streams"""
        # Initialize data stream containers
        max_length = self.config.get('analytics', {}).get('max_stream_length', 1000)
        
        symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'SPY']  # Default symbols
        
        for symbol in symbols:
            self.price_streams[symbol] = deque(maxlen=max_length)
            self.volume_streams[symbol] = deque(maxlen=max_length)
            self.volatility_streams[symbol] = deque(maxlen=max_length)
            
    async def start(self):
        """Start market analytics"""
        if not self.initialized:
            await self.initialize()
            
        if self.running:
            return
            
        logger.info("Starting Market Analytics...")
        self.running = True
        logger.info("✓ Market Analytics started")
        
    async def stop(self):
        """Stop market analytics"""
        if not self.running:
            return
            
        logger.info("Stopping Market Analytics...")
        self.running = False
        logger.info("✓ Market Analytics stopped")
        
    async def update_market_data(self, symbol: str, price: float, volume: float):
        """Update market data for analytics"""
        if symbol not in self.price_streams:
            return
            
        # Update price stream
        self.price_streams[symbol].append(price)
        self.volume_streams[symbol].append(volume)
        
        # Calculate and update volatility
        if len(self.price_streams[symbol]) >= 2:
            returns = self._calculate_returns(list(self.price_streams[symbol]))
            if returns:
                volatility = np.std(returns[-20:]) if len(returns) >= 20 else np.std(returns)
                self.volatility_streams[symbol].append(volatility)
                
        # Update analytics
        await self._update_analytics(symbol)
        
    def _calculate_returns(self, prices: List[float]) -> List[float]:
        """Calculate price returns"""
        if len(prices) < 2:
            return []
            
        returns = []
        for i in range(1, len(prices)):
            if prices[i-1] != 0:
                returns.append((prices[i] - prices[i-1]) / prices[i-1])
                
        return returns
        
    async def _update_analytics(self, symbol: str):
        """Update analytics for a symbol"""
        # Update market regime
        await self._update_market_regime(symbol)
        
        # Update volatility forecast
        await self._update_volatility_forecast(symbol)
        
        # Update correlations
        await self._update_correlations(symbol)
        
    async def _update_market_regime(self, symbol: str):
        """Update market regime detection"""
        if len(self.volatility_streams[symbol]) < 20:
            return
            
        recent_vol = list(self.volatility_streams[symbol])[-20:]
        avg_vol = statistics.mean(recent_vol)
        
        # Determine volatility regime
        if avg_vol < self.regime_models['volatility_regime']['low_vol_threshold']:
            vol_regime = 'low_volatility'
        elif avg_vol > self.regime_models['volatility_regime']['high_vol_threshold']:
            vol_regime = 'high_volatility'
        else:
            vol_regime = 'normal_volatility'
            
        # Determine trend regime
        if len(self.price_streams[symbol]) >= 50:
            prices = list(self.price_streams[symbol])[-50:]
            trend_strength = self._calculate_trend_strength(prices)
            
            if trend_strength > self.regime_models['trend_regime']['trend_strength_threshold']:
                trend_regime = 'trending'
            elif trend_strength < -self.regime_models['trend_regime']['trend_strength_threshold']:
                trend_regime = 'counter_trending'
            else:
                trend_regime = 'ranging'
        else:
            trend_regime = 'unknown'
            
        # Combine regimes
        self.market_regimes[symbol] = f"{vol_regime}_{trend_regime}"
        
    def _calculate_trend_strength(self, prices: List[float]) -> float:
        """Calculate trend strength"""
        if len(prices) < 10:
            return 0.0
            
        # Simple trend strength calculation
        # In practice, this would use more sophisticated methods
        first_half = prices[:len(prices)//2]
        second_half = prices[len(prices)//2:]
        
        first_avg = statistics.mean(first_half)
        second_avg = statistics.mean(second_half)
        
        if first_avg == 0:
            return 0.0
            
        return (second_avg - first_avg) / first_avg
        
    async def _update_volatility_forecast(self, symbol: str):
        """Update volatility forecast"""
        if len(self.volatility_streams[symbol]) < 10:
            return
            
        recent_vol = list(self.volatility_streams[symbol])[-10:]
        
        # EWMA volatility forecast
        ewma_lambda = self.volatility_models['ewma']['lambda']
        ewma_vol = recent_vol[0]
        
        for vol in recent_vol[1:]:
            ewma_vol = ewma_lambda * ewma_vol + (1 - ewma_lambda) * vol
            
        self.volatility_forecasts[symbol] = ewma_vol
        
    async def _update_correlations(self, symbol: str):
        """Update correlation matrix"""
        if symbol not in self.correlation_matrix:
            self.correlation_matrix[symbol] = {}
            
        # Calculate correlations with other symbols
        for other_symbol in self.price_streams:
            if other_symbol != symbol and len(self.price_streams[other_symbol]) >= 20:
                correlation = self._calculate_correlation(symbol, other_symbol)
                self.correlation_matrix[symbol][other_symbol] = correlation
                
    def _calculate_correlation(self, symbol1: str, symbol2: str) -> float:
        """Calculate correlation between two symbols"""
        if len(self.price_streams[symbol1]) < 20 or len(self.price_streams[symbol2]) < 20:
            return 0.0
            
        # Get recent returns
        returns1 = self._calculate_returns(list(self.price_streams[symbol1])[-20:])
        returns2 = self._calculate_returns(list(self.price_streams[symbol2])[-20:])
        
        if len(returns1) < 10 or len(returns2) < 10:
            return 0.0
            
        # Calculate correlation
        min_length = min(len(returns1), len(returns2))
        returns1 = returns1[-min_length:]
        returns2 = returns2[-min_length:]
        
        try:
            correlation = np.corrcoef(returns1, returns2)[0, 1]
            return correlation if not np.isnan(correlation) else 0.0
        except:
            return 0.0
            
    async def get_market_regime(self, symbol: str) -> str:
        """Get current market regime for symbol"""
        return self.market_regimes.get(symbol, 'unknown')
        
    async def get_volatility_forecast(self, symbol: str) -> float:
        """Get volatility forecast for symbol"""
        return self.volatility_forecasts.get(symbol, 0.0)
        
    async def get_correlation_matrix(self) -> Dict[str, Dict[str, float]]:
        """Get current correlation matrix"""
        return self.correlation_matrix.copy()
        
    async def detect_anomalies(self, symbol: str) -> List[Dict[str, Any]]:
        """Detect market anomalies"""
        anomalies = []
        
        if len(self.price_streams[symbol]) < 50:
            return anomalies
            
        prices = list(self.price_streams[symbol])
        returns = self._calculate_returns(prices)
        
        if len(returns) < 20:
            return anomalies
            
        # Detect price anomalies
        recent_returns = returns[-20:]
        mean_return = statistics.mean(recent_returns)
        std_return = statistics.stdev(recent_returns) if len(recent_returns) > 1 else 0
        
        latest_return = returns[-1]
        
        # Z-score anomaly detection
        if std_return > 0:
            z_score = abs((latest_return - mean_return) / std_return)
            if z_score > 3:  # 3-sigma event
                anomalies.append({
                    'type': 'price_anomaly',
                    'symbol': symbol,
                    'z_score': z_score,
                    'return': latest_return,
                    'timestamp': time.time()
                })
                
        # Volume anomalies
        if len(self.volume_streams[symbol]) >= 20:
            volumes = list(self.volume_streams[symbol])
            recent_volumes = volumes[-20:]
            mean_volume = statistics.mean(recent_volumes)
            
            latest_volume = volumes[-1]
            if latest_volume > mean_volume * 3:  # 3x average volume
                anomalies.append({
                    'type': 'volume_anomaly',
                    'symbol': symbol,
                    'volume_ratio': latest_volume / mean_volume,
                    'volume': latest_volume,
                    'timestamp': time.time()
                })
                
        return anomalies
        
    async def get_analytics_summary(self) -> Dict[str, Any]:
        """Get analytics summary"""
        return {
            'market_regimes': self.market_regimes.copy(),
            'volatility_forecasts': self.volatility_forecasts.copy(),
            'tracked_symbols': list(self.price_streams.keys()),
            'data_points': {symbol: len(stream) for symbol, stream in self.price_streams.items()},
            'running': self.running
        }

{"validation_id": "validation_**********_a0407e43", "validation_level": "standard", "overall_status": "partial", "overall_score": 0.7822690487045283, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "partial", "integration_score": 0.7698055875551242, "error_count": 0, "warnings": ["Integration issues in system_coordinator"], "dependencies_met": "True"}, {"component_name": "team_manager", "component_type": "core", "status": "partial", "integration_score": 0.7100476070028376, "error_count": 0, "warnings": ["Functionality concerns in team_manager", "Integration issues in team_manager"], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "partial", "integration_score": 0.742686736061952, "error_count": 0, "warnings": ["Functionality concerns in data_manager", "Integration issues in data_manager"], "dependencies_met": "False"}, {"component_name": "analytics_engine", "component_type": "core", "status": "partial", "integration_score": 0.7885931072188145, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "partial", "integration_score": 0.7303774453192844, "error_count": 0, "warnings": ["Integration issues in ollama_hub"], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "partial", "integration_score": 0.8673362886374443, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "partial", "integration_score": 0.7769042620919742, "error_count": 0, "warnings": ["Integration issues in portfolio_manager"], "dependencies_met": "True"}, {"component_name": "risk_manager", "component_type": "core", "status": "partial", "integration_score": 0.8016494565802249, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "partial", "integration_score": 0.7847545383004144, "error_count": 0, "warnings": ["Integration issues in strategy_manager"], "dependencies_met": "False"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "partial", "integration_score": 0.8211793683129329, "error_count": 0, "warnings": ["Integration issues in competitive_framework"], "dependencies_met": "True"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "partial", "integration_score": 0.8039909397265058, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.714028686262929, "error_count": 0, "warnings": ["Functionality concerns in self_improvement_engine", "Integration issues in self_improvement_engine"], "dependencies_met": "True"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "partial", "integration_score": 0.7625277272387114, "error_count": 0, "warnings": ["Integration issues in regime_adaptation_system"], "dependencies_met": "True"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "partial", "integration_score": 0.753633293462097, "error_count": 0, "warnings": ["Functionality concerns in performance_optimizer"], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.7889640781968964, "error_count": 0, "warnings": ["Integration issues in advanced_trading_engine"], "dependencies_met": "True"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "partial", "integration_score": 0.7580412898982497, "error_count": 0, "warnings": ["Functionality concerns in ai_coordinator", "Integration issues in ai_coordinator"], "dependencies_met": "True"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "partial", "integration_score": 0.8317335363768606, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "partial", "integration_score": 0.8319676378104048, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "partial", "integration_score": 0.826486166768817, "error_count": 0, "warnings": ["Integration issues in paper_trading_engine"], "dependencies_met": "True"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "partial", "integration_score": 0.7884895755064857, "error_count": 0, "warnings": ["Functionality concerns in logging_audit_system", "Integration issues in logging_audit_system"], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.8844070879340789, "data_manager": 0.6518721335503521, "analytics_engine": 0.6677125861292768, "ollama_hub": 0.7031614558668675, "execution_engine": 0.7360524231285615, "portfolio_manager": 0.6607402748913009, "risk_manager": 0.7997201211254978, "strategy_manager": 0.7051639453694082, "competitive_framework": 0.7390575648978447, "tournament_framework": 0.6129386460113784, "self_improvement_engine": 0.6488358249083855, "regime_adaptation_system": 0.6601178089987618, "performance_optimizer": 0.850743672354622, "advanced_trading_engine": 0.7590761087378466, "ai_coordinator": 0.7864491308728869, "configuration_manager": 0.662233660088011, "mock_data_providers": 0.6560467516595775, "paper_trading_engine": 0.8290095204832166, "logging_audit_system": 0.8244526726027095}, "team_manager": {"system_coordinator": 0.8185055605372834, "team_manager": 1.0, "data_manager": 0.91548072966309, "analytics_engine": 0.6971781572386928, "ollama_hub": 0.7938125477673487, "execution_engine": 0.6237641066522639, "portfolio_manager": 0.8647589062841257, "risk_manager": 0.7615580595261849, "strategy_manager": 0.7148861851124797, "competitive_framework": 0.8039019628593851, "tournament_framework": 0.7021332432878131, "self_improvement_engine": 0.6758520810291749, "regime_adaptation_system": 0.8751942351204485, "performance_optimizer": 0.6116450870140858, "advanced_trading_engine": 0.8334211950007832, "ai_coordinator": 0.8803101254844456, "configuration_manager": 0.6703664859070346, "mock_data_providers": 0.858842886754258, "paper_trading_engine": 0.8934230418286842, "logging_audit_system": 0.7916154254525468}, "data_manager": {"system_coordinator": 0.7024923827302629, "team_manager": 0.7519365268056208, "data_manager": 1.0, "analytics_engine": 0.9334215848067391, "ollama_hub": 0.7174934538552611, "execution_engine": 0.819716372303112, "portfolio_manager": 0.6365304074687645, "risk_manager": 0.8440116572522638, "strategy_manager": 0.6945667006591912, "competitive_framework": 0.7681549370999914, "tournament_framework": 0.7211620146504574, "self_improvement_engine": 0.8295987233214182, "regime_adaptation_system": 0.7031498170887536, "performance_optimizer": 0.7082248927812401, "advanced_trading_engine": 0.8683815082785631, "ai_coordinator": 0.7878576636526389, "configuration_manager": 0.6414498385491939, "mock_data_providers": 0.8661686022807258, "paper_trading_engine": 0.6282142069620125, "logging_audit_system": 0.6595343526128883}, "analytics_engine": {"system_coordinator": 0.6871287572678477, "team_manager": 0.7103350091463122, "data_manager": 0.6131637253157226, "analytics_engine": 1.0, "ollama_hub": 0.6520466695799562, "execution_engine": 0.6307282973913867, "portfolio_manager": 0.8682010606175135, "risk_manager": 0.6250163594572842, "strategy_manager": 0.9347370410805403, "competitive_framework": 0.889382806908144, "tournament_framework": 0.8288603739589712, "self_improvement_engine": 0.6625666772659473, "regime_adaptation_system": 0.852107311108907, "performance_optimizer": 0.6640024806799063, "advanced_trading_engine": 0.8002052354684439, "ai_coordinator": 0.6560520831317378, "configuration_manager": 0.7438186658543879, "mock_data_providers": 0.7102562855249932, "paper_trading_engine": 0.8228252356261803, "logging_audit_system": 0.7736480986975992}, "ollama_hub": {"system_coordinator": 0.6835112659724306, "team_manager": 0.8203953247541558, "data_manager": 0.6584267553631687, "analytics_engine": 0.7341964093401977, "ollama_hub": 1.0, "execution_engine": 0.8489156761352195, "portfolio_manager": 0.7695294202728178, "risk_manager": 0.8079268596317627, "strategy_manager": 0.7500713023554645, "competitive_framework": 0.7429028116934268, "tournament_framework": 0.7432895658115072, "self_improvement_engine": 0.6929826110430262, "regime_adaptation_system": 0.7900298056716912, "performance_optimizer": 0.8019867888345639, "advanced_trading_engine": 0.8435666193952666, "ai_coordinator": 0.766366880051848, "configuration_manager": 0.8600396961721811, "mock_data_providers": 0.6046907762974868, "paper_trading_engine": 0.6392660259386861, "logging_audit_system": 0.7668909392011771}, "execution_engine": {"system_coordinator": 0.612065548342908, "team_manager": 0.6677886294913493, "data_manager": 0.6374088157554066, "analytics_engine": 0.7018176990256773, "ollama_hub": 0.7462631544618932, "execution_engine": 1.0, "portfolio_manager": 0.8919723533458935, "risk_manager": 0.88055495891698, "strategy_manager": 0.6891086375691188, "competitive_framework": 0.6038448836256547, "tournament_framework": 0.8870005077160206, "self_improvement_engine": 0.6776254649358119, "regime_adaptation_system": 0.6483183564993336, "performance_optimizer": 0.6860890452706839, "advanced_trading_engine": 0.6040948955140458, "ai_coordinator": 0.7200155680300167, "configuration_manager": 0.8252747513775673, "mock_data_providers": 0.724818883749348, "paper_trading_engine": 0.7792779680577002, "logging_audit_system": 0.8040424277739753}, "portfolio_manager": {"system_coordinator": 0.8020887630926418, "team_manager": 0.758296037606118, "data_manager": 0.6107152317520843, "analytics_engine": 0.6336170048183939, "ollama_hub": 0.8305699489552669, "execution_engine": 0.7548887624007037, "portfolio_manager": 1.0, "risk_manager": 0.889062126469875, "strategy_manager": 0.7088741608303936, "competitive_framework": 0.8360091153685842, "tournament_framework": 0.763246373715372, "self_improvement_engine": 0.8361016052971, "regime_adaptation_system": 0.6966340998064477, "performance_optimizer": 0.8870405937898853, "advanced_trading_engine": 0.8056673432225453, "ai_coordinator": 0.8075773265506379, "configuration_manager": 0.7223805070653239, "mock_data_providers": 0.6297864687253847, "paper_trading_engine": 0.7873311109822947, "logging_audit_system": 0.8627517791898597}, "risk_manager": {"system_coordinator": 0.8680747852023333, "team_manager": 0.7848324818105332, "data_manager": 0.6412449825286324, "analytics_engine": 0.6975594424102198, "ollama_hub": 0.7476368293134773, "execution_engine": 0.777142947538741, "portfolio_manager": 0.8937648103752736, "risk_manager": 1.0, "strategy_manager": 0.7143133642386893, "competitive_framework": 0.7521095195240907, "tournament_framework": 0.7749931625860017, "self_improvement_engine": 0.6118707340911124, "regime_adaptation_system": 0.8431342778951245, "performance_optimizer": 0.8839019397077252, "advanced_trading_engine": 0.6213497875665493, "ai_coordinator": 0.7668435831794538, "configuration_manager": 0.6574206572967131, "mock_data_providers": 0.6635917139145397, "paper_trading_engine": 0.8902107041520336, "logging_audit_system": 0.6240717385056829}, "strategy_manager": {"system_coordinator": 0.7713745259361167, "team_manager": 0.6838212359267101, "data_manager": 0.7884743538221917, "analytics_engine": 0.6612867119258147, "ollama_hub": 0.7679185806351513, "execution_engine": 0.8999150153071166, "portfolio_manager": 0.6540571461549097, "risk_manager": 0.618437913435002, "strategy_manager": 1.0, "competitive_framework": 0.7028391415103579, "tournament_framework": 0.7661773886748995, "self_improvement_engine": 0.8422558783637116, "regime_adaptation_system": 0.8029541932540336, "performance_optimizer": 0.7753850649809297, "advanced_trading_engine": 0.8680853437487283, "ai_coordinator": 0.7954355461912699, "configuration_manager": 0.8060437731838925, "mock_data_providers": 0.6374713843330907, "paper_trading_engine": 0.7821024880195652, "logging_audit_system": 0.7140111073993674}, "competitive_framework": {"system_coordinator": 0.6233538857666092, "team_manager": 0.7599160078818015, "data_manager": 0.7773513163406973, "analytics_engine": 0.7911534249637291, "ollama_hub": 0.8759863386931754, "execution_engine": 0.6845576374577839, "portfolio_manager": 0.6848687291815488, "risk_manager": 0.8313524794155164, "strategy_manager": 0.6590861730738228, "competitive_framework": 1.0, "tournament_framework": 0.7478196105018121, "self_improvement_engine": 0.7264884932464737, "regime_adaptation_system": 0.8401878929423187, "performance_optimizer": 0.884248802905657, "advanced_trading_engine": 0.6209911190006091, "ai_coordinator": 0.6120323319190776, "configuration_manager": 0.8657804818241523, "mock_data_providers": 0.8381090582143309, "paper_trading_engine": 0.734667452321379, "logging_audit_system": 0.8358409764645389}, "tournament_framework": {"system_coordinator": 0.6498938678133642, "team_manager": 0.7865721796262557, "data_manager": 0.7642960030979375, "analytics_engine": 0.6699804602276767, "ollama_hub": 0.7388454236417192, "execution_engine": 0.694560473856174, "portfolio_manager": 0.6973407556316799, "risk_manager": 0.8988157900843554, "strategy_manager": 0.7394028760023511, "competitive_framework": 0.6091205651668307, "tournament_framework": 1.0, "self_improvement_engine": 0.6673954778253431, "regime_adaptation_system": 0.8789596606471684, "performance_optimizer": 0.6108091950371207, "advanced_trading_engine": 0.8838247130057032, "ai_coordinator": 0.7450413719356359, "configuration_manager": 0.8265535635547686, "mock_data_providers": 0.7788152517226294, "paper_trading_engine": 0.6426498510930612, "logging_audit_system": 0.7595022214222525}, "self_improvement_engine": {"system_coordinator": 0.6057465866980677, "team_manager": 0.809163248058168, "data_manager": 0.7293421624959512, "analytics_engine": 0.6588471531894532, "ollama_hub": 0.7411990115870909, "execution_engine": 0.8207205022230935, "portfolio_manager": 0.8057883618523124, "risk_manager": 0.7364231512435021, "strategy_manager": 0.7383110829417385, "competitive_framework": 0.7612992101138116, "tournament_framework": 0.6921231583705653, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.8211801471347824, "performance_optimizer": 0.8082910281552025, "advanced_trading_engine": 0.8312645975046902, "ai_coordinator": 0.7443324988411486, "configuration_manager": 0.7421077038394276, "mock_data_providers": 0.8013804102810121, "paper_trading_engine": 0.8555051468949475, "logging_audit_system": 0.7131019347034093}, "regime_adaptation_system": {"system_coordinator": 0.7841262276248058, "team_manager": 0.75497908683589, "data_manager": 0.8261093705690128, "analytics_engine": 0.8654973939703201, "ollama_hub": 0.7929080202215333, "execution_engine": 0.7858762032220714, "portfolio_manager": 0.6836799237544385, "risk_manager": 0.7245756272361423, "strategy_manager": 0.7975340684154282, "competitive_framework": 0.8985207408798204, "tournament_framework": 0.693187538400064, "self_improvement_engine": 0.7433216277885495, "regime_adaptation_system": 1.0, "performance_optimizer": 0.6165157142364851, "advanced_trading_engine": 0.8909677966910152, "ai_coordinator": 0.6972830192846516, "configuration_manager": 0.8652351406814225, "mock_data_providers": 0.7554178937747854, "paper_trading_engine": 0.671984562224523, "logging_audit_system": 0.7000215381643213}, "performance_optimizer": {"system_coordinator": 0.8301811514670018, "team_manager": 0.6107541595455626, "data_manager": 0.8192317959628506, "analytics_engine": 0.7148977112810109, "ollama_hub": 0.6541779234187295, "execution_engine": 0.6507177330691071, "portfolio_manager": 0.7817307602591327, "risk_manager": 0.8459318163502281, "strategy_manager": 0.6553955770732306, "competitive_framework": 0.8349640604069046, "tournament_framework": 0.8955001721369729, "self_improvement_engine": 0.89184744064357, "regime_adaptation_system": 0.6771914270255368, "performance_optimizer": 1.0, "advanced_trading_engine": 0.7108050392857047, "ai_coordinator": 0.8702335836122294, "configuration_manager": 0.8514700812685696, "mock_data_providers": 0.6270975728664278, "paper_trading_engine": 0.8004490060950304, "logging_audit_system": 0.6211441824233415}, "advanced_trading_engine": {"system_coordinator": 0.7980289301082717, "team_manager": 0.6324684627472703, "data_manager": 0.6852566756374219, "analytics_engine": 0.6877602553636845, "ollama_hub": 0.6988389672230371, "execution_engine": 0.7471778176608684, "portfolio_manager": 0.7803815906197737, "risk_manager": 0.8425133896573469, "strategy_manager": 0.796231520421947, "competitive_framework": 0.6898011127003395, "tournament_framework": 0.8295586934676411, "self_improvement_engine": 0.8287907900322996, "regime_adaptation_system": 0.7034748711719078, "performance_optimizer": 0.6558726823735958, "advanced_trading_engine": 1.0, "ai_coordinator": 0.7668944452226576, "configuration_manager": 0.8901909390952408, "mock_data_providers": 0.8229761073482396, "paper_trading_engine": 0.8845368065359265, "logging_audit_system": 0.7198937152782039}, "ai_coordinator": {"system_coordinator": 0.8969484161950962, "team_manager": 0.6691665317520913, "data_manager": 0.7315760418794566, "analytics_engine": 0.8329014588421925, "ollama_hub": 0.8506784190372797, "execution_engine": 0.7065576037924346, "portfolio_manager": 0.8843109371791325, "risk_manager": 0.8658459146024782, "strategy_manager": 0.8944168860707435, "competitive_framework": 0.8094748638540022, "tournament_framework": 0.8399123495316474, "self_improvement_engine": 0.6874648965258352, "regime_adaptation_system": 0.8800501811438779, "performance_optimizer": 0.8267746902380084, "advanced_trading_engine": 0.6771366722713856, "ai_coordinator": 1.0, "configuration_manager": 0.825049897372877, "mock_data_providers": 0.7956088467354655, "paper_trading_engine": 0.6753642178568104, "logging_audit_system": 0.747441213447078}, "configuration_manager": {"system_coordinator": 0.742526565052555, "team_manager": 0.7777902936260204, "data_manager": 0.8249703592069877, "analytics_engine": 0.8193154887520304, "ollama_hub": 0.6504656505859208, "execution_engine": 0.8867135371444324, "portfolio_manager": 0.7076080737206959, "risk_manager": 0.6747005060333544, "strategy_manager": 0.7041377067289442, "competitive_framework": 0.6479047889999086, "tournament_framework": 0.6998704622742914, "self_improvement_engine": 0.8964713555366789, "regime_adaptation_system": 0.7453173850986365, "performance_optimizer": 0.6659960198432632, "advanced_trading_engine": 0.6897644424772457, "ai_coordinator": 0.6884110813457944, "configuration_manager": 1.0, "mock_data_providers": 0.6025221049870854, "paper_trading_engine": 0.6612732166141024, "logging_audit_system": 0.7328833854146094}, "mock_data_providers": {"system_coordinator": 0.838959714560242, "team_manager": 0.694517912087843, "data_manager": 0.7482195188378316, "analytics_engine": 0.7920835958678573, "ollama_hub": 0.8198214485670331, "execution_engine": 0.8623035264718104, "portfolio_manager": 0.8393873034285068, "risk_manager": 0.6954955892267847, "strategy_manager": 0.6318159699211617, "competitive_framework": 0.8135318773751359, "tournament_framework": 0.7584023173982773, "self_improvement_engine": 0.6720873205670513, "regime_adaptation_system": 0.7082839800425532, "performance_optimizer": 0.7448067801755269, "advanced_trading_engine": 0.8026460215039561, "ai_coordinator": 0.6187155630921258, "configuration_manager": 0.887740559662175, "mock_data_providers": 1.0, "paper_trading_engine": 0.8298209031467975, "logging_audit_system": 0.6589134821561852}, "paper_trading_engine": {"system_coordinator": 0.7115466581058989, "team_manager": 0.6196686609413614, "data_manager": 0.8205155731669916, "analytics_engine": 0.6402643380489482, "ollama_hub": 0.8609063836415163, "execution_engine": 0.600040849417044, "portfolio_manager": 0.7706930654600275, "risk_manager": 0.7244113732479649, "strategy_manager": 0.8831042467322874, "competitive_framework": 0.8361200789529235, "tournament_framework": 0.8002248981966035, "self_improvement_engine": 0.6233034807002434, "regime_adaptation_system": 0.7443259327940819, "performance_optimizer": 0.8729165534523101, "advanced_trading_engine": 0.8199372048521625, "ai_coordinator": 0.6252922764356175, "configuration_manager": 0.7285424116000006, "mock_data_providers": 0.6082972726181476, "paper_trading_engine": 1.0, "logging_audit_system": 0.7708814309295292}, "logging_audit_system": {"system_coordinator": 0.7654769858868308, "team_manager": 0.7107270100677494, "data_manager": 0.7748909994197329, "analytics_engine": 0.7479158302700885, "ollama_hub": 0.6593844910044024, "execution_engine": 0.6829048529040407, "portfolio_manager": 0.7530901470184406, "risk_manager": 0.8700592549061519, "strategy_manager": 0.6078113253257933, "competitive_framework": 0.7961065969418378, "tournament_framework": 0.8337249506620763, "self_improvement_engine": 0.8759247261533749, "regime_adaptation_system": 0.7780807815550629, "performance_optimizer": 0.8663061701272223, "advanced_trading_engine": 0.6491059652152152, "ai_coordinator": 0.8382519732925343, "configuration_manager": 0.7272833053972416, "mock_data_providers": 0.8732386605695079, "paper_trading_engine": 0.8124931551211518, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.721610263269029, "response_time": 0.8699257482637177, "throughput": 0.601313982138421, "memory_usage": 0.7383089869898103, "cpu_usage": 0.8975347617109976, "concurrent_operations": 0.7133990898766244}, "critical_issues": ["Components with dependency issues: data_manager, strategy_manager"], "recommendations": ["Improve 20 partially working components", "Improve overall system performance and stability", "Enhance component integration and communication"], "production_ready": "True", "timestamp": **********.8179278}
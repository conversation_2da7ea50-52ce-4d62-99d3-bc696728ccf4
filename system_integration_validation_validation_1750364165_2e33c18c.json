{"validation_id": "validation_**********_2e33c18c", "validation_level": "comprehensive", "overall_status": "partial", "overall_score": 0.7747097342784163, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "completed", "integration_score": 0.8068767856916826, "error_count": 0, "warnings": ["Integration issues in system_coordinator"], "dependencies_met": "True"}, {"component_name": "team_manager", "component_type": "core", "status": "partial", "integration_score": 0.7281782905771301, "error_count": 0, "warnings": ["Functionality concerns in team_manager", "Integration issues in team_manager"], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "partial", "integration_score": 0.7676591807374128, "error_count": 0, "warnings": ["Integration issues in data_manager"], "dependencies_met": "True"}, {"component_name": "analytics_engine", "component_type": "core", "status": "partial", "integration_score": 0.7618575357130629, "error_count": 0, "warnings": ["Integration issues in analytics_engine"], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "partial", "integration_score": 0.7500068390363945, "error_count": 0, "warnings": ["Integration issues in ollama_hub"], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "completed", "integration_score": 0.8004875949546183, "error_count": 0, "warnings": ["Functionality concerns in execution_engine"], "dependencies_met": "True"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "partial", "integration_score": 0.7597326230614183, "error_count": 0, "warnings": ["Integration issues in portfolio_manager"], "dependencies_met": "True"}, {"component_name": "risk_manager", "component_type": "core", "status": "completed", "integration_score": 0.8227433948116605, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "partial", "integration_score": 0.7292883345091883, "error_count": 0, "warnings": ["Functionality concerns in strategy_manager"], "dependencies_met": "True"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8570793109511003, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8568112530570758, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8046921582679193, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "partial", "integration_score": 0.747197181490552, "error_count": 0, "warnings": ["Integration issues in regime_adaptation_system"], "dependencies_met": "True"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "partial", "integration_score": 0.7643640270197056, "error_count": 0, "warnings": ["Functionality concerns in performance_optimizer"], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.7649492499917625, "error_count": 0, "warnings": ["Integration issues in advanced_trading_engine"], "dependencies_met": "True"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "completed", "integration_score": 0.8231248803868759, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "completed", "integration_score": 0.8196883478453254, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "completed", "integration_score": 0.820682279197219, "error_count": 0, "warnings": ["Integration issues in mock_data_providers"], "dependencies_met": "True"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "completed", "integration_score": 0.8433321309294164, "error_count": 0, "warnings": ["Integration issues in paper_trading_engine"], "dependencies_met": "True"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "partial", "integration_score": 0.7810702039863998, "error_count": 0, "warnings": [], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.9955480874059494, "data_manager": 0.859129428107198, "analytics_engine": 0.6769952866339333, "ollama_hub": 0.7811578633680305, "execution_engine": 0.6042042549228791, "portfolio_manager": 0.7560716875499724, "risk_manager": 0.7240304429461432, "strategy_manager": 0.7441228918124024, "competitive_framework": 0.6970902110584638, "tournament_framework": 0.811788622593429, "self_improvement_engine": 0.6641006887008266, "regime_adaptation_system": 0.8626897160790388, "performance_optimizer": 0.852977227981091, "advanced_trading_engine": 0.7731054911366637, "ai_coordinator": 0.7249429770350848, "configuration_manager": 0.8663435350406904, "mock_data_providers": 0.757642157189617, "paper_trading_engine": 0.8791554096897995, "logging_audit_system": 0.8008296307600762}, "team_manager": {"system_coordinator": 0.6118857719868506, "team_manager": 1.0, "data_manager": 0.9357841253286876, "analytics_engine": 0.7980202205400718, "ollama_hub": 0.8169781177289215, "execution_engine": 0.6236314490618634, "portfolio_manager": 0.7870287890621788, "risk_manager": 0.8048331397818198, "strategy_manager": 0.834991033122134, "competitive_framework": 0.8048080107846945, "tournament_framework": 0.6445610828891533, "self_improvement_engine": 0.8479660941301603, "regime_adaptation_system": 0.7380546229943304, "performance_optimizer": 0.801360758039617, "advanced_trading_engine": 0.7281489161331016, "ai_coordinator": 0.7260824518763944, "configuration_manager": 0.6679676084264482, "mock_data_providers": 0.7251229852667063, "paper_trading_engine": 0.6880536521459129, "logging_audit_system": 0.8009206836209237}, "data_manager": {"system_coordinator": 0.83867460092418, "team_manager": 0.8109023788587644, "data_manager": 1.0, "analytics_engine": 0.9007388561363101, "ollama_hub": 0.7181853462528442, "execution_engine": 0.8439056866218935, "portfolio_manager": 0.7236451541627008, "risk_manager": 0.7789475071261797, "strategy_manager": 0.7648104803956979, "competitive_framework": 0.6215549720248015, "tournament_framework": 0.6396578153605814, "self_improvement_engine": 0.674644504444555, "regime_adaptation_system": 0.7126323337178038, "performance_optimizer": 0.6512436763045651, "advanced_trading_engine": 0.6622573039586563, "ai_coordinator": 0.7599381365601459, "configuration_manager": 0.6324782288510113, "mock_data_providers": 0.760560344009809, "paper_trading_engine": 0.7725352739996247, "logging_audit_system": 0.8872510952101442}, "analytics_engine": {"system_coordinator": 0.6762602007634981, "team_manager": 0.7744526520020673, "data_manager": 0.8265264737201135, "analytics_engine": 1.0, "ollama_hub": 0.7691016313842226, "execution_engine": 0.8582180629306703, "portfolio_manager": 0.6898226873440173, "risk_manager": 0.6647002756194026, "strategy_manager": 0.9138732087457602, "competitive_framework": 0.6445603206349817, "tournament_framework": 0.7056816951381553, "self_improvement_engine": 0.698633988015294, "regime_adaptation_system": 0.6312124185126559, "performance_optimizer": 0.8798380724219396, "advanced_trading_engine": 0.6912202635795589, "ai_coordinator": 0.8407486459799902, "configuration_manager": 0.7275436777724953, "mock_data_providers": 0.8166583812182792, "paper_trading_engine": 0.6077791391971337, "logging_audit_system": 0.6259099621004637}, "ollama_hub": {"system_coordinator": 0.6634865336608344, "team_manager": 0.659849942974929, "data_manager": 0.8086987533064028, "analytics_engine": 0.6699274514268034, "ollama_hub": 1.0, "execution_engine": 0.6409696560140317, "portfolio_manager": 0.8751506328417107, "risk_manager": 0.7329716327922114, "strategy_manager": 0.6909776926006072, "competitive_framework": 0.6144116998690633, "tournament_framework": 0.6819184955299236, "self_improvement_engine": 0.6016463415068602, "regime_adaptation_system": 0.7494281547346329, "performance_optimizer": 0.8478837213858785, "advanced_trading_engine": 0.7548105342853678, "ai_coordinator": 0.661966251155142, "configuration_manager": 0.7807353306673119, "mock_data_providers": 0.673888750066923, "paper_trading_engine": 0.8257943298810292, "logging_audit_system": 0.616324669219735}, "execution_engine": {"system_coordinator": 0.8412602262370502, "team_manager": 0.8308875794698718, "data_manager": 0.8899799884272634, "analytics_engine": 0.6892652445038657, "ollama_hub": 0.8638002315968247, "execution_engine": 1.0, "portfolio_manager": 0.8902717506429565, "risk_manager": 0.6713761221528088, "strategy_manager": 0.6727005633932263, "competitive_framework": 0.7101110257926668, "tournament_framework": 0.646997658359724, "self_improvement_engine": 0.6202664360098517, "regime_adaptation_system": 0.6738522183752691, "performance_optimizer": 0.796616761924773, "advanced_trading_engine": 0.740046912192751, "ai_coordinator": 0.6498541293333503, "configuration_manager": 0.7172269955214121, "mock_data_providers": 0.7206055467708578, "paper_trading_engine": 0.6177290159037222, "logging_audit_system": 0.7124240740412485}, "portfolio_manager": {"system_coordinator": 0.729402956598939, "team_manager": 0.8389312981781909, "data_manager": 0.6505228380809264, "analytics_engine": 0.6193541015502672, "ollama_hub": 0.8144017122548576, "execution_engine": 0.7648729378634802, "portfolio_manager": 1.0, "risk_manager": 0.744749768069744, "strategy_manager": 0.641125942113238, "competitive_framework": 0.8782653406329403, "tournament_framework": 0.7596580012724103, "self_improvement_engine": 0.6598624196973248, "regime_adaptation_system": 0.8712232840741168, "performance_optimizer": 0.6784967695363143, "advanced_trading_engine": 0.8479610278714899, "ai_coordinator": 0.6252915264454167, "configuration_manager": 0.8839602907806847, "mock_data_providers": 0.7517766508965333, "paper_trading_engine": 0.6969265388562328, "logging_audit_system": 0.7944640239438467}, "risk_manager": {"system_coordinator": 0.6263584475932819, "team_manager": 0.6197531350460623, "data_manager": 0.7607411312138834, "analytics_engine": 0.761948005253404, "ollama_hub": 0.8640871218304116, "execution_engine": 0.7845407617567828, "portfolio_manager": 0.8491352403271635, "risk_manager": 1.0, "strategy_manager": 0.8049590536358038, "competitive_framework": 0.7131497919871352, "tournament_framework": 0.804049107116772, "self_improvement_engine": 0.754138540333631, "regime_adaptation_system": 0.7624849788417147, "performance_optimizer": 0.6724970881634864, "advanced_trading_engine": 0.8345261398796988, "ai_coordinator": 0.6447688661994123, "configuration_manager": 0.6290817235135724, "mock_data_providers": 0.6746444313972371, "paper_trading_engine": 0.6264551656416724, "logging_audit_system": 0.7769920361985705}, "strategy_manager": {"system_coordinator": 0.8839593067079052, "team_manager": 0.7697594631844417, "data_manager": 0.6495940785075767, "analytics_engine": 0.7542745288672683, "ollama_hub": 0.6756722626942732, "execution_engine": 0.6551956262796461, "portfolio_manager": 0.8124907459996413, "risk_manager": 0.8291110839866274, "strategy_manager": 1.0, "competitive_framework": 0.8589355185732549, "tournament_framework": 0.7508591555028032, "self_improvement_engine": 0.663252489045439, "regime_adaptation_system": 0.6732667777399082, "performance_optimizer": 0.8303445247090493, "advanced_trading_engine": 0.613029711902242, "ai_coordinator": 0.6462185163323926, "configuration_manager": 0.6299115680923613, "mock_data_providers": 0.702321745889419, "paper_trading_engine": 0.610237410237348, "logging_audit_system": 0.7331599485310358}, "competitive_framework": {"system_coordinator": 0.892132594299369, "team_manager": 0.6760678579813431, "data_manager": 0.7402475281989798, "analytics_engine": 0.6363969902459178, "ollama_hub": 0.655874680099196, "execution_engine": 0.8503469068494587, "portfolio_manager": 0.897334515300374, "risk_manager": 0.7142866511084761, "strategy_manager": 0.7421051347656156, "competitive_framework": 1.0, "tournament_framework": 0.816687776515598, "self_improvement_engine": 0.6826446927241514, "regime_adaptation_system": 0.893071202378316, "performance_optimizer": 0.6507119450123998, "advanced_trading_engine": 0.7772993160122152, "ai_coordinator": 0.892158213695691, "configuration_manager": 0.6214772452664085, "mock_data_providers": 0.6927014381259651, "paper_trading_engine": 0.7218153304664208, "logging_audit_system": 0.606801800344446}, "tournament_framework": {"system_coordinator": 0.7509332133323885, "team_manager": 0.6646253153017844, "data_manager": 0.8972528029518642, "analytics_engine": 0.8466135744113624, "ollama_hub": 0.8401156759848117, "execution_engine": 0.8882105615657292, "portfolio_manager": 0.8600251186442853, "risk_manager": 0.69883724020343, "strategy_manager": 0.6258997610569643, "competitive_framework": 0.8158163236456974, "tournament_framework": 1.0, "self_improvement_engine": 0.8975918986183722, "regime_adaptation_system": 0.877179859624158, "performance_optimizer": 0.7405865625620458, "advanced_trading_engine": 0.7306697683634077, "ai_coordinator": 0.8594598340087953, "configuration_manager": 0.6923977788178717, "mock_data_providers": 0.6172162010329605, "paper_trading_engine": 0.894802566401565, "logging_audit_system": 0.8903353637471827}, "self_improvement_engine": {"system_coordinator": 0.8770316697004502, "team_manager": 0.8321079371183003, "data_manager": 0.8086810201118326, "analytics_engine": 0.6656638461025456, "ollama_hub": 0.8317015164335372, "execution_engine": 0.7961279760142522, "portfolio_manager": 0.6583603655431938, "risk_manager": 0.8582916417369305, "strategy_manager": 0.7525762555237194, "competitive_framework": 0.6271968208895892, "tournament_framework": 0.6618213892393178, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.7382429947240544, "performance_optimizer": 0.7148179786563547, "advanced_trading_engine": 0.8747654519958243, "ai_coordinator": 0.6726940110114022, "configuration_manager": 0.7136774857280223, "mock_data_providers": 0.8660581250022532, "paper_trading_engine": 0.8851599208948637, "logging_audit_system": 0.6204895486036583}, "regime_adaptation_system": {"system_coordinator": 0.883591095919837, "team_manager": 0.7672155234551215, "data_manager": 0.6055718636650951, "analytics_engine": 0.8618026169449623, "ollama_hub": 0.8349742691201867, "execution_engine": 0.7968171265967854, "portfolio_manager": 0.7309677681013127, "risk_manager": 0.6831887615082566, "strategy_manager": 0.7348118040200048, "competitive_framework": 0.7922172264424462, "tournament_framework": 0.6421669593772861, "self_improvement_engine": 0.6861133423586087, "regime_adaptation_system": 1.0, "performance_optimizer": 0.8256461209281927, "advanced_trading_engine": 0.7463985692576753, "ai_coordinator": 0.6633067396466479, "configuration_manager": 0.8508591864332652, "mock_data_providers": 0.8399834674245376, "paper_trading_engine": 0.7594087406848409, "logging_audit_system": 0.8210536489916553}, "performance_optimizer": {"system_coordinator": 0.7341887783388682, "team_manager": 0.6765808486402923, "data_manager": 0.7708148181619119, "analytics_engine": 0.8164506598595489, "ollama_hub": 0.6852487947431056, "execution_engine": 0.659554562045593, "portfolio_manager": 0.7738711428081171, "risk_manager": 0.8143524434529332, "strategy_manager": 0.6464785402033079, "competitive_framework": 0.7987559240187265, "tournament_framework": 0.7897673823684834, "self_improvement_engine": 0.8115597701122603, "regime_adaptation_system": 0.8148400181899804, "performance_optimizer": 1.0, "advanced_trading_engine": 0.8381654750034031, "ai_coordinator": 0.833806719193185, "configuration_manager": 0.6668150142235321, "mock_data_providers": 0.7048207553001854, "paper_trading_engine": 0.6424886429795296, "logging_audit_system": 0.6030514891271463}, "advanced_trading_engine": {"system_coordinator": 0.8755427445555597, "team_manager": 0.7801882980884284, "data_manager": 0.7244870540152664, "analytics_engine": 0.8173865553626409, "ollama_hub": 0.8133162666253374, "execution_engine": 0.8873896673961973, "portfolio_manager": 0.6013926903470369, "risk_manager": 0.8342082910479803, "strategy_manager": 0.8042635691756517, "competitive_framework": 0.6973620702038071, "tournament_framework": 0.6355644479501786, "self_improvement_engine": 0.6336252319395339, "regime_adaptation_system": 0.6612674801382017, "performance_optimizer": 0.8840223536170977, "advanced_trading_engine": 1.0, "ai_coordinator": 0.8645965898990925, "configuration_manager": 0.6597058483607133, "mock_data_providers": 0.6642151431775671, "paper_trading_engine": 0.6509405400408038, "logging_audit_system": 0.7122149760679595}, "ai_coordinator": {"system_coordinator": 0.8607252724695049, "team_manager": 0.8836756518120794, "data_manager": 0.6402513091788051, "analytics_engine": 0.8585522760867124, "ollama_hub": 0.7674258453388029, "execution_engine": 0.7349373649149693, "portfolio_manager": 0.7933012276380005, "risk_manager": 0.6188906844324108, "strategy_manager": 0.6449069816469059, "competitive_framework": 0.7416483016210069, "tournament_framework": 0.7012053278918102, "self_improvement_engine": 0.8608412586357765, "regime_adaptation_system": 0.731390781845659, "performance_optimizer": 0.8537474133399101, "advanced_trading_engine": 0.6622968108175071, "ai_coordinator": 1.0, "configuration_manager": 0.7535726786094785, "mock_data_providers": 0.8390008867332381, "paper_trading_engine": 0.7006945609869828, "logging_audit_system": 0.7219469207886288}, "configuration_manager": {"system_coordinator": 0.7595216598840371, "team_manager": 0.7925529053182825, "data_manager": 0.7912454511011939, "analytics_engine": 0.8229787314527494, "ollama_hub": 0.7606491457790555, "execution_engine": 0.6341329645671863, "portfolio_manager": 0.680514692079997, "risk_manager": 0.6926295931704826, "strategy_manager": 0.6750609297605868, "competitive_framework": 0.8849469465421824, "tournament_framework": 0.822772148815289, "self_improvement_engine": 0.7354635440166498, "regime_adaptation_system": 0.6617427829000602, "performance_optimizer": 0.6498862931801144, "advanced_trading_engine": 0.7164808556948733, "ai_coordinator": 0.8287503575798227, "configuration_manager": 1.0, "mock_data_providers": 0.603947063700204, "paper_trading_engine": 0.6922025183586439, "logging_audit_system": 0.8511139075773906}, "mock_data_providers": {"system_coordinator": 0.6598551488141177, "team_manager": 0.6128933265162991, "data_manager": 0.7039242772627804, "analytics_engine": 0.6836486243002782, "ollama_hub": 0.6458983731410937, "execution_engine": 0.7521653929329941, "portfolio_manager": 0.6558533925026586, "risk_manager": 0.7764699868506395, "strategy_manager": 0.6458282126235336, "competitive_framework": 0.6158406562253638, "tournament_framework": 0.8111015755489226, "self_improvement_engine": 0.6655227124237635, "regime_adaptation_system": 0.86953807103253, "performance_optimizer": 0.7286588524311284, "advanced_trading_engine": 0.8790361274033965, "ai_coordinator": 0.8554474716560163, "configuration_manager": 0.8598105281297524, "mock_data_providers": 1.0, "paper_trading_engine": 0.8729088703919707, "logging_audit_system": 0.7065480405458407}, "paper_trading_engine": {"system_coordinator": 0.8741314272974756, "team_manager": 0.733563897753085, "data_manager": 0.7262074106687832, "analytics_engine": 0.749737585747168, "ollama_hub": 0.8704090661126035, "execution_engine": 0.7041349991091158, "portfolio_manager": 0.8363588737931791, "risk_manager": 0.6193100378769978, "strategy_manager": 0.7737108432063553, "competitive_framework": 0.694029176722357, "tournament_framework": 0.8708101029037667, "self_improvement_engine": 0.6952393642629845, "regime_adaptation_system": 0.6247985289272682, "performance_optimizer": 0.8769283124676679, "advanced_trading_engine": 0.8661422741999407, "ai_coordinator": 0.8176233881797724, "configuration_manager": 0.7269784393180241, "mock_data_providers": 0.8677090363573291, "paper_trading_engine": 1.0, "logging_audit_system": 0.7360597516212284}, "logging_audit_system": {"system_coordinator": 0.8837191082786511, "team_manager": 0.8782821048274436, "data_manager": 0.6469683145531517, "analytics_engine": 0.8588400234736675, "ollama_hub": 0.7641072237130359, "execution_engine": 0.8463255563907739, "portfolio_manager": 0.8051503926289353, "risk_manager": 0.7412644689986921, "strategy_manager": 0.7870524284373849, "competitive_framework": 0.7369063589108147, "tournament_framework": 0.7355085574862005, "self_improvement_engine": 0.7425590259289967, "regime_adaptation_system": 0.7153615405364548, "performance_optimizer": 0.6428450903970346, "advanced_trading_engine": 0.6474498747876483, "ai_coordinator": 0.8582300196196206, "configuration_manager": 0.6542671456797049, "mock_data_providers": 0.7672918893730827, "paper_trading_engine": 0.8893654624520835, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.8018589884636453, "response_time": 0.9484558841450296, "throughput": 0.6443844792407664, "memory_usage": 0.7409398263691716, "cpu_usage": 0.8970129752687224, "concurrent_operations": 0.7457725333865195}, "critical_issues": ["Components with dependency issues: ai_coordinator"], "recommendations": ["Improve 10 partially working components", "Improve overall system performance and stability", "Enhance component integration and communication"], "production_ready": "True", "timestamp": **********.110677}
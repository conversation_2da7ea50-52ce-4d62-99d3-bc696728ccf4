#!/usr/bin/env python3
"""
System Integration Test - Test the complete integrated trading system
"""

import asyncio
import json
from datetime import datetime
from system.system_coordinator import SystemCoordinator, SystemState

async def test_system_integration():
    """Test complete system integration"""
    
    print("🚀 TESTING COMPLETE SYSTEM INTEGRATION")
    print("=" * 60)
    
    results = {}
    
    try:
        # Initialize system coordinator
        print("\n🔧 PHASE 1: System Initialization")
        coordinator = SystemCoordinator('config/test_config.yaml')
        
        # Test system initialization
        print("  📋 Initializing system components...")
        init_success = await coordinator.initialize()
        
        if init_success:
            print("  ✅ System initialization: SUCCESS")
            results['initialization'] = {'success': True, 'components': len(coordinator.components)}
        else:
            print("  ❌ System initialization: FAILED")
            results['initialization'] = {'success': False, 'error': 'Initialization failed'}
            return results
        
        # Test system startup
        print("\n🚀 PHASE 2: System Startup")
        print("  🔄 Starting all system components...")
        start_success = await coordinator.start()
        
        if start_success:
            print("  ✅ System startup: SUCCESS")
            results['startup'] = {'success': True, 'state': coordinator.state.value}
        else:
            print("  ❌ System startup: FAILED")
            results['startup'] = {'success': False, 'error': 'Startup failed'}
        
        # Test system status
        print("\n📊 PHASE 3: System Status Check")
        print("  📈 Getting system status...")
        status = await coordinator.get_system_status()
        
        print(f"  🔹 System State: {status.state.value}")
        print(f"  🔹 System Health: {status.system_health:.1%}")
        print(f"  🔹 Active Components: {sum(status.components_status.values())}/{len(status.components_status)}")
        print(f"  🔹 Active Strategies: {status.active_strategies}")
        print(f"  🔹 Active Agents: {status.active_agents}")
        
        results['status_check'] = {
            'success': True,
            'state': status.state.value,
            'health': status.system_health,
            'components': status.components_status,
            'active_strategies': status.active_strategies,
            'active_agents': status.active_agents
        }
        
        # Test component integration
        print("\n🔗 PHASE 4: Component Integration Test")
        
        # Test 1: Data Manager + Analytics Engine
        print("  📊 Testing Data Manager + Analytics Engine...")
        data_manager = await coordinator.get_component('data_manager')
        analytics_engine = await coordinator.get_component('analytics_engine')
        
        if data_manager and analytics_engine:
            # Simulate market data
            market_data = await data_manager.get_market_data("AAPL", "1d", limit=10)
            if market_data:
                print("    ✅ Data Manager providing market data")
                
                # Test analytics processing
                await analytics_engine.process_market_data("AAPL", {
                    'symbol': 'AAPL',
                    'price': 150.0,
                    'volume': 1000000,
                    'timestamp': datetime.now()
                })
                print("    ✅ Analytics Engine processing data")
                
                results['data_analytics_integration'] = {'success': True}
            else:
                print("    ⚠️ No market data available")
                results['data_analytics_integration'] = {'success': False, 'reason': 'No market data'}
        else:
            print("    ❌ Data Manager or Analytics Engine not available")
            results['data_analytics_integration'] = {'success': False, 'reason': 'Components not available'}
        
        # Test 2: Portfolio Manager + Risk Manager
        print("  💼 Testing Portfolio Manager + Risk Manager...")
        portfolio_manager = await coordinator.get_component('portfolio_manager')
        risk_manager = await coordinator.get_component('risk_manager')
        
        if portfolio_manager and risk_manager:
            # Create test portfolio
            portfolio_id = await portfolio_manager.create_portfolio("integration_test", 100000.0)
            await portfolio_manager.add_position("AAPL", 100, 150.0)
            
            # Test risk assessment
            portfolio_data = await portfolio_manager.get_portfolio_data(portfolio_id)
            if portfolio_data:
                risk_assessment = await risk_manager.assess_portfolio_risk(portfolio_data)
                print(f"    ✅ Risk assessment completed: {risk_assessment.overall_risk:.2f}")
                results['portfolio_risk_integration'] = {'success': True, 'risk_score': risk_assessment.overall_risk}
            else:
                print("    ⚠️ Portfolio data not available")
                results['portfolio_risk_integration'] = {'success': False, 'reason': 'No portfolio data'}
        else:
            print("    ❌ Portfolio Manager or Risk Manager not available")
            results['portfolio_risk_integration'] = {'success': False, 'reason': 'Components not available'}
        
        # Test 3: Agent Manager + Strategy Manager
        print("  🤖 Testing Agent Manager + Strategy Manager...")
        agent_manager = await coordinator.get_component('agent_manager')
        strategy_manager = await coordinator.get_component('strategy_manager')
        
        if agent_manager and strategy_manager:
            # Create test agent
            from agents.base_agent import AgentRole
            agent_id = await agent_manager.create_agent(AgentRole.STRATEGY_DEVELOPER, "Integration_Test_Agent")
            
            # Create test strategy
            strategy_config = {
                'name': 'integration_test_strategy',
                'type': 'momentum',
                'symbols': ['AAPL'],
                'parameters': {'lookback': 20, 'threshold': 0.02}
            }
            strategy_id = await strategy_manager.create_strategy(strategy_config)
            
            if agent_id and strategy_id:
                print("    ✅ Agent and Strategy created successfully")
                results['agent_strategy_integration'] = {'success': True, 'agent_id': agent_id, 'strategy_id': strategy_id}
            else:
                print("    ⚠️ Failed to create agent or strategy")
                results['agent_strategy_integration'] = {'success': False, 'reason': 'Creation failed'}
        else:
            print("    ❌ Agent Manager or Strategy Manager not available")
            results['agent_strategy_integration'] = {'success': False, 'reason': 'Components not available'}
        
        # Test 4: Execution Engine Integration
        print("  ⚡ Testing Execution Engine Integration...")
        execution_engine = await coordinator.get_component('execution_engine')
        
        if execution_engine:
            from execution.order_types import Order, OrderType, OrderSide
            
            # Create test order
            test_order = Order(
                order_id="integration_test_001",
                symbol="AAPL",
                side=OrderSide.BUY,
                quantity=10,
                order_type=OrderType.MARKET,
                strategy_id="integration_test_strategy"
            )
            
            # Submit order (will use paper trading)
            execution_result = await execution_engine.submit_order(test_order)
            
            if execution_result.success:
                print("    ✅ Order execution successful")
                results['execution_integration'] = {'success': True, 'order_id': test_order.order_id}
            else:
                print(f"    ⚠️ Order execution failed: {execution_result.message}")
                results['execution_integration'] = {'success': False, 'reason': execution_result.message}
        else:
            print("    ❌ Execution Engine not available")
            results['execution_integration'] = {'success': False, 'reason': 'Component not available'}
        
        # Test system commands
        print("\n🎛️ PHASE 5: System Command Test")
        
        # Test pause command
        print("  ⏸️ Testing system pause...")
        pause_result = await coordinator.execute_system_command("pause")
        if pause_result['success']:
            print("    ✅ System pause: SUCCESS")
            results['pause_command'] = {'success': True}
        else:
            print("    ❌ System pause: FAILED")
            results['pause_command'] = {'success': False, 'error': pause_result.get('error')}
        
        # Test resume command
        print("  ▶️ Testing system resume...")
        resume_result = await coordinator.execute_system_command("resume")
        if resume_result['success']:
            print("    ✅ System resume: SUCCESS")
            results['resume_command'] = {'success': True}
        else:
            print("    ❌ System resume: FAILED")
            results['resume_command'] = {'success': False, 'error': resume_result.get('error')}
        
        # Test health check command
        print("  🏥 Testing health check...")
        health_result = await coordinator.execute_system_command("health_check")
        if health_result['success']:
            health_score = health_result['health']
            print(f"    ✅ Health check: {health_score:.1%}")
            results['health_check'] = {'success': True, 'health': health_score}
        else:
            print("    ❌ Health check: FAILED")
            results['health_check'] = {'success': False, 'error': health_result.get('error')}
        
        # Test system shutdown
        print("\n🛑 PHASE 6: System Shutdown")
        print("  🔄 Stopping system...")
        stop_success = await coordinator.stop()
        
        if stop_success:
            print("  ✅ System shutdown: SUCCESS")
            results['shutdown'] = {'success': True, 'final_state': coordinator.state.value}
        else:
            print("  ❌ System shutdown: FAILED")
            results['shutdown'] = {'success': False, 'error': 'Shutdown failed'}
        
        # Calculate overall integration score
        print("\n🎉 INTEGRATION TEST SUMMARY")
        print("=" * 60)
        
        successful_tests = sum(1 for result in results.values() if result.get('success'))
        total_tests = len(results)
        integration_score = (successful_tests / total_tests) * 100
        
        print(f"📊 Integration Tests: {successful_tests}/{total_tests} passed")
        print(f"🔧 Integration Score: {integration_score:.1f}%")
        
        # Detailed results
        for test_name, result in results.items():
            status = "✅ PASS" if result.get('success') else "❌ FAIL"
            reason = f" - {result.get('reason', result.get('error', ''))}" if not result.get('success') else ""
            print(f"  {test_name}: {status}{reason}")
        
        # Save results
        test_summary = {
            "timestamp": datetime.now().isoformat(),
            "test_type": "system_integration",
            "integration_tests": results,
            "summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "integration_score": integration_score,
                "system_ready": integration_score >= 80.0
            }
        }
        
        with open('system_integration_results.json', 'w') as f:
            json.dump(test_summary, f, indent=2, default=str)
        
        print(f"\n📄 Results saved to: system_integration_results.json")
        
        # Final verdict
        if integration_score >= 90:
            print("\n🎉 EXCELLENT! System integration is OUTSTANDING!")
            print("🚀 Ready for advanced features and production deployment!")
        elif integration_score >= 80:
            print("\n✅ GOOD! System integration is SOLID!")
            print("🔧 Ready for advanced features with minor improvements!")
        elif integration_score >= 70:
            print("\n⚠️ FAIR! System integration needs some work!")
            print("🛠️ Address failing tests before proceeding!")
        else:
            print("\n❌ POOR! System integration has significant issues!")
            print("🚨 Major fixes required before proceeding!")
        
        return integration_score >= 80.0
        
    except Exception as e:
        print(f"❌ System Integration Test Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_system_integration())
    if success:
        print("\n🎉 SYSTEM INTEGRATION SUCCESSFUL!")
        print("🚀 Ready for advanced features!")
    else:
        print("\n⚠️ SYSTEM INTEGRATION NEEDS WORK!")
        print("🔧 Review test results and fix issues!")

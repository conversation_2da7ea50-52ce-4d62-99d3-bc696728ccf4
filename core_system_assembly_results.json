{"timestamp": "2025-06-19T15:42:28.704856", "test_type": "core_system_assembly", "results": {"imports": {"system_coordinator": true, "team_manager": true, "data_manager": true, "analytics_engine": false, "ollama_hub": false, "competitive_framework": true, "tournament_framework": true, "self-improvement_engine": true, "regime_adaptation": true, "performance_optimizer": true, "mock_data_providers": true, "paper_trading_engine": true, "configuration_manager": true}, "instantiation": {"system_coordinator": true, "competitive_framework": true, "mock_data_providers": true, "paper_trading_engine": true, "configuration_manager": true}, "functionality": {"configuration_manager": true, "mock_data_providers": true, "paper_trading_engine": false}}, "scores": {"import_score": 84.61538461538461, "instantiation_score": 100.0, "functionality_score": 66.66666666666666, "overall_score": 82.05128205128204}, "summary": {"total_tests": 21, "passed_tests": 18, "assembly_success_rate": 82.05128205128204, "system_assembled": true, "ready_for_integration": true}}
"""
Model Configuration Store - Management of model configurations for different roles
"""

import asyncio
import logging
import json
import yaml
from typing import Dict, List, Optional, Any
from pathlib import Path

logger = logging.getLogger(__name__)


class ModelConfigStore:
    """
    Store and manage model configurations for different agent roles.
    Handles loading, caching, and dynamic updates of model parameters.
    """
    
    def __init__(self, system_config: Dict[str, Any]):
        self.system_config = system_config
        
        # Configuration storage
        self.configs: Dict[str, Dict[str, Any]] = {}
        self.role_configs: Dict[str, Dict[str, Any]] = {}
        self.default_configs: Dict[str, Dict[str, Any]] = {}
        
        # State
        self.initialized = False
        
        # Configuration file paths
        self.model_config_path = Path("config/model_configs.yaml")
        
    async def initialize(self):
        """Initialize the configuration store"""
        if self.initialized:
            return
            
        logger.info("Initializing Model Configuration Store...")
        
        try:
            # Load configurations from file
            await self.load_configs()
            
            # Setup default configurations
            self._setup_default_configs()
            
            self.initialized = True
            logger.info(f"✓ Configuration Store initialized with {len(self.configs)} configurations")
            
        except Exception as e:
            logger.error(f"Failed to initialize Configuration Store: {e}")
            raise
            
    async def load_configs(self):
        """Load model configurations from YAML file"""
        try:
            if self.model_config_path.exists():
                with open(self.model_config_path, 'r') as f:
                    config_data = yaml.safe_load(f)
                    
                # Load model-specific configurations
                models_config = config_data.get('models', {})
                for role, role_config in models_config.items():
                    self.role_configs[role] = role_config
                    
                    # Store individual model configs
                    primary_model = role_config.get('primary')
                    fallback_model = role_config.get('fallback')
                    config = role_config.get('config', {})
                    
                    if primary_model:
                        key = f"{primary_model}:{role}"
                        self.configs[key] = config.copy()
                        
                    if fallback_model:
                        key = f"{fallback_model}:{role}"
                        self.configs[key] = config.copy()
                        
                # Load specialized model configurations
                specialized_config = config_data.get('specialized_models', {})
                for spec_role, spec_config in specialized_config.items():
                    model_name = spec_config.get('model')
                    config = spec_config.get('config', {})
                    
                    if model_name:
                        key = f"{model_name}:{spec_role}"
                        self.configs[key] = config.copy()
                        self.role_configs[spec_role] = {
                            'primary': model_name,
                            'config': config
                        }
                        
                logger.info(f"Loaded configurations for {len(self.role_configs)} roles")
            else:
                logger.warning(f"Configuration file not found: {self.model_config_path}")
                
        except Exception as e:
            logger.error(f"Error loading configurations: {e}")
            raise
            
    def _setup_default_configs(self):
        """Setup default configurations for different roles"""
        self.default_configs = {
            'team_leader': {
                'temperature': 0.7,
                'top_p': 0.9,
                'max_tokens': 4096,
                'frequency_penalty': 0.0,
                'presence_penalty': 0.0,
                'system_prompt': self._get_default_system_prompt('team_leader')
            },
            'market_analyst': {
                'temperature': 0.4,
                'top_p': 0.8,
                'max_tokens': 3072,
                'frequency_penalty': 0.1,
                'presence_penalty': 0.0,
                'system_prompt': self._get_default_system_prompt('market_analyst')
            },
            'strategy_developer': {
                'temperature': 0.8,
                'top_p': 0.9,
                'max_tokens': 3072,
                'frequency_penalty': 0.0,
                'presence_penalty': 0.1,
                'system_prompt': self._get_default_system_prompt('strategy_developer')
            },
            'risk_manager': {
                'temperature': 0.2,
                'top_p': 0.7,
                'max_tokens': 2048,
                'frequency_penalty': 0.0,
                'presence_penalty': 0.0,
                'system_prompt': self._get_default_system_prompt('risk_manager')
            },
            'execution_specialist': {
                'temperature': 0.3,
                'top_p': 0.8,
                'max_tokens': 1024,
                'frequency_penalty': 0.0,
                'presence_penalty': 0.0,
                'system_prompt': self._get_default_system_prompt('execution_specialist')
            },
            'performance_evaluator': {
                'temperature': 0.4,
                'top_p': 0.8,
                'max_tokens': 2048,
                'frequency_penalty': 0.0,
                'presence_penalty': 0.0,
                'system_prompt': self._get_default_system_prompt('performance_evaluator')
            }
        }
        
    def _get_default_system_prompt(self, role: str) -> str:
        """Get default system prompt for a role"""
        prompts = {
            'team_leader': """You are a strategic team leader coordinating multiple specialized trading agents.
Your role is to synthesize information from team members, make final decisions, allocate resources, 
and ensure team cohesion. Focus on strategic thinking, risk management, and team optimization.
Always consider the bigger picture and long-term implications of decisions.""",

            'market_analyst': """You are a specialized market analyst identifying patterns, trends, and opportunities.
Analyze market data, sentiment, and technical indicators to provide actionable insights.
Focus on accuracy, timeliness, and clear communication of market intelligence.
Provide specific, data-driven recommendations with confidence levels.""",

            'strategy_developer': """You are a creative strategy developer designing innovative trading approaches.
Develop novel strategies, optimize existing ones, and adapt to changing market conditions.
Focus on innovation, backtesting, and continuous improvement of trading methodologies.
Think outside the box while maintaining rigorous analytical standards.""",

            'risk_manager': """You are a cautious risk manager protecting assets and ensuring compliance.
Assess portfolio risk, set position sizes, monitor exposure, and enforce limits.
Focus on capital preservation, regulatory compliance, and systematic risk control.
Always err on the side of caution and provide clear risk assessments.""",

            'execution_specialist': """You are an execution specialist optimizing trade execution and minimizing costs.
Focus on order routing, timing, slippage reduction, and execution efficiency.
Provide fast, accurate execution decisions with minimal market impact.
Prioritize speed and precision in all execution-related tasks.""",

            'performance_evaluator': """You are a performance evaluator tracking and analyzing system effectiveness.
Monitor agent performance, identify improvement opportunities, and provide feedback.
Focus on objective analysis, performance attribution, and optimization recommendations.
Provide clear, actionable insights for system improvement."""
        }
        
        return prompts.get(role, "You are a helpful AI assistant specialized in financial trading.")
        
    async def get_config(self, model_name: str, role: str) -> Dict[str, Any]:
        """Get configuration for specific model and role"""
        # Try exact match first
        key = f"{model_name}:{role}"
        if key in self.configs:
            return self.configs[key].copy()
            
        # Try role-based configuration
        if role in self.role_configs:
            role_config = self.role_configs[role]
            if role_config.get('primary') == model_name or role_config.get('fallback') == model_name:
                return role_config.get('config', {}).copy()
                
        # Fall back to default configuration for role
        if role in self.default_configs:
            config = self.default_configs[role].copy()
            logger.debug(f"Using default config for {model_name}:{role}")
            return config
            
        # Ultimate fallback
        logger.warning(f"No configuration found for {model_name}:{role}, using basic default")
        return {
            'temperature': 0.7,
            'top_p': 0.9,
            'max_tokens': 2048,
            'system_prompt': self._get_default_system_prompt(role)
        }
        
    async def update_config(self, model_name: str, role: str, config: Dict[str, Any]):
        """Update configuration for a model and role"""
        key = f"{model_name}:{role}"
        self.configs[key] = config.copy()
        logger.info(f"Updated configuration for {key}")
        
    async def get_role_config(self, role: str) -> Optional[Dict[str, Any]]:
        """Get complete role configuration including model assignments"""
        return self.role_configs.get(role)
        
    async def get_primary_model_for_role(self, role: str) -> Optional[str]:
        """Get primary model assigned to a role"""
        role_config = self.role_configs.get(role, {})
        return role_config.get('primary')
        
    async def get_fallback_model_for_role(self, role: str) -> Optional[str]:
        """Get fallback model assigned to a role"""
        role_config = self.role_configs.get(role, {})
        return role_config.get('fallback')
        
    async def get_all_role_configs(self) -> Dict[str, Dict[str, Any]]:
        """Get all role configurations"""
        return self.role_configs.copy()
        
    async def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate a model configuration"""
        required_fields = ['temperature', 'max_tokens']
        
        for field in required_fields:
            if field not in config:
                logger.error(f"Missing required field: {field}")
                return False
                
        # Validate ranges
        if not 0.0 <= config.get('temperature', 0.7) <= 2.0:
            logger.error("Temperature must be between 0.0 and 2.0")
            return False
            
        if not 0.0 <= config.get('top_p', 0.9) <= 1.0:
            logger.error("top_p must be between 0.0 and 1.0")
            return False
            
        if not 1 <= config.get('max_tokens', 2048) <= 32768:
            logger.error("max_tokens must be between 1 and 32768")
            return False
            
        return True
        
    async def save_configs(self):
        """Save current configurations to file"""
        try:
            config_data = {
                'models': {},
                'specialized_models': {}
            }
            
            # Organize configs by role
            for role, role_config in self.role_configs.items():
                if role in ['team_leader', 'market_analyst', 'strategy_developer', 
                           'risk_manager', 'execution_specialist', 'performance_evaluator']:
                    config_data['models'][role] = role_config
                else:
                    config_data['specialized_models'][role] = role_config
                    
            # Write to file
            with open(self.model_config_path, 'w') as f:
                yaml.dump(config_data, f, default_flow_style=False, indent=2)
                
            logger.info(f"Configurations saved to {self.model_config_path}")
            
        except Exception as e:
            logger.error(f"Error saving configurations: {e}")
            raise
            
    async def get_config_stats(self) -> Dict[str, Any]:
        """Get configuration statistics"""
        return {
            'total_configs': len(self.configs),
            'role_configs': len(self.role_configs),
            'default_configs': len(self.default_configs),
            'roles': list(self.role_configs.keys())
        }

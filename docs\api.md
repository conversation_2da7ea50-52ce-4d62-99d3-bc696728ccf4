# API Documentation

The Advanced Ollama Trading Agent System provides a comprehensive RESTful API and WebSocket interface for managing and monitoring the trading system.

## 🚀 Quick Start

### Base URL
```
http://localhost:8000/api/v1
```

### Authentication
All API endpoints require JWT authentication:

```bash
# Login to get token
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'

# Response
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}

# Use token in subsequent requests
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8000/api/v1/system/status
```

## 📚 API Reference

### Authentication Endpoints

#### POST /auth/login
Authenticate user and get access token.

**Request:**
```json
{
  "username": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "access_token": "string",
  "refresh_token": "string",
  "token_type": "bearer",
  "expires_in": 3600
}
```

#### POST /auth/refresh
Refresh access token using refresh token.

**Request:**
```json
{
  "refresh_token": "string"
}
```

#### POST /auth/logout
Logout and invalidate tokens.

### System Management

#### GET /system/status
Get overall system status and health.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "uptime_seconds": 3600,
  "version": "1.0.0",
  "components": {
    "agent_manager": {
      "running": true,
      "initialized": true,
      "status": "healthy"
    },
    "strategy_manager": {
      "running": true,
      "initialized": true,
      "status": "healthy"
    }
  },
  "performance": {
    "cpu_usage": 0.25,
    "memory_usage": 0.45,
    "disk_usage": 0.30
  },
  "resources": {
    "cpu": {
      "usage_percent": 25.0,
      "cores": 4
    },
    "memory": {
      "usage_percent": 45.0,
      "total_bytes": **********
    }
  }
}
```

#### GET /system/components
Get detailed status of all system components.

**Response:**
```json
[
  {
    "name": "agent_manager",
    "status": "healthy",
    "running": true,
    "initialized": true,
    "last_update": "2024-01-01T12:00:00Z",
    "metrics": {
      "active_agents": 5,
      "total_messages": 1000
    },
    "errors": []
  }
]
```

#### POST /system/commands
Execute system-level commands.

**Request:**
```json
{
  "command": "restart",
  "parameters": {},
  "force": false
}
```

**Available Commands:**
- `start`: Start the trading system
- `stop`: Stop the trading system
- `restart`: Restart the trading system
- `reload_config`: Reload system configuration
- `clear_cache`: Clear system caches
- `run_diagnostics`: Run system diagnostics

#### GET /system/metrics
Get detailed system performance metrics.

**Query Parameters:**
- `component` (optional): Filter by specific component
- `metric_type` (optional): Filter by metric type

#### GET /system/logs
Get recent system logs.

**Query Parameters:**
- `level`: Log level filter (DEBUG, INFO, WARNING, ERROR)
- `component`: Component filter
- `limit`: Maximum number of log entries (default: 100)

### Agent Management

#### GET /agents/
Get list of all trading agents.

**Query Parameters:**
- `status`: Filter by agent status
- `agent_type`: Filter by agent type

**Response:**
```json
[
  {
    "agent_id": "agent_001",
    "name": "Market Analyst",
    "type": "analyst",
    "status": "active",
    "capabilities": ["market_analysis", "trend_detection"],
    "performance": {
      "accuracy": 0.85,
      "response_time": 1.2
    },
    "last_activity": "2024-01-01T12:00:00Z"
  }
]
```

#### GET /agents/{agent_id}
Get detailed information about a specific agent.

#### POST /agents/{agent_id}/messages
Send a message to a specific agent.

**Request:**
```json
{
  "message_type": "analysis_request",
  "content": {
    "symbol": "AAPL",
    "timeframe": "1d",
    "analysis_type": "technical"
  },
  "priority": "normal"
}
```

#### GET /agents/{agent_id}/messages
Get recent messages for an agent.

**Query Parameters:**
- `limit`: Maximum number of messages (default: 50)

### Strategy Management

#### GET /strategies/
Get list of all trading strategies.

**Response:**
```json
[
  {
    "strategy_id": "strategy_001",
    "name": "Momentum Strategy",
    "type": "momentum",
    "status": "active",
    "parameters": {
      "lookback_period": 20,
      "threshold": 0.02
    },
    "performance": {
      "total_return": 0.15,
      "sharpe_ratio": 1.2,
      "max_drawdown": -0.08
    },
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

#### POST /strategies/
Create a new trading strategy.

**Request:**
```json
{
  "name": "New Strategy",
  "type": "momentum",
  "description": "A momentum-based trading strategy",
  "parameters": {
    "lookback_period": 20,
    "threshold": 0.02
  },
  "symbols": ["AAPL", "GOOGL"],
  "enabled": true
}
```

#### GET /strategies/{strategy_id}
Get detailed information about a specific strategy.

#### PUT /strategies/{strategy_id}
Update an existing strategy.

#### DELETE /strategies/{strategy_id}
Delete a strategy.

#### POST /strategies/{strategy_id}/backtest
Run backtest for a strategy.

**Request:**
```json
{
  "start_date": "2023-01-01",
  "end_date": "2023-12-31",
  "initial_capital": 100000,
  "symbols": ["AAPL", "GOOGL"]
}
```

### Portfolio Management

#### GET /portfolio/
Get list of all portfolios.

**Response:**
```json
[
  {
    "portfolio_id": "portfolio_001",
    "name": "Main Portfolio",
    "total_value": 150000.0,
    "cash": 10000.0,
    "positions": {
      "AAPL": {
        "quantity": 100,
        "market_value": 15000.0,
        "unrealized_pnl": 500.0
      }
    },
    "performance": {
      "total_return": 0.15,
      "daily_return": 0.002,
      "volatility": 0.18
    }
  }
]
```

#### GET /portfolio/{portfolio_id}
Get detailed portfolio information.

#### GET /portfolio/{portfolio_id}/positions
Get current positions in a portfolio.

#### GET /portfolio/{portfolio_id}/performance
Get portfolio performance metrics.

**Query Parameters:**
- `period`: Time period (1d, 1w, 1m, 3m, 6m, 1y)

#### GET /portfolio/{portfolio_id}/history
Get portfolio value history.

### Risk Management

#### GET /risk/assessment
Get current risk assessment.

**Response:**
```json
{
  "overall_risk": "medium",
  "risk_metrics": {
    "var_95": 5000.0,
    "expected_shortfall": 7500.0,
    "max_drawdown": 0.15,
    "volatility": 0.20
  },
  "position_risks": {
    "AAPL": {
      "position_risk": 0.05,
      "concentration_risk": 0.10
    }
  },
  "alerts": [
    {
      "type": "concentration",
      "message": "High concentration in tech sector",
      "severity": "warning"
    }
  ]
}
```

#### GET /risk/limits
Get current risk limits and constraints.

#### PUT /risk/limits
Update risk limits.

#### GET /risk/alerts
Get active risk alerts.

### Execution Management

#### GET /execution/orders
Get trading orders.

**Query Parameters:**
- `status`: Filter by order status
- `symbol`: Filter by symbol
- `limit`: Maximum number of orders

**Response:**
```json
[
  {
    "order_id": "order_001",
    "symbol": "AAPL",
    "side": "buy",
    "quantity": 100,
    "order_type": "market",
    "status": "filled",
    "fill_price": 150.0,
    "timestamp": "2024-01-01T12:00:00Z"
  }
]
```

#### POST /execution/orders
Place a new order.

**Request:**
```json
{
  "symbol": "AAPL",
  "side": "buy",
  "quantity": 100,
  "order_type": "market",
  "time_in_force": "day"
}
```

#### GET /execution/orders/{order_id}
Get order details.

#### DELETE /execution/orders/{order_id}
Cancel an order.

#### GET /execution/trades
Get trade history.

### Market Data

#### GET /market-data/prices
Get current market prices.

**Query Parameters:**
- `symbols`: Comma-separated list of symbols

**Response:**
```json
{
  "AAPL": {
    "price": 150.0,
    "bid": 149.95,
    "ask": 150.05,
    "volume": 1000000,
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

#### GET /market-data/quotes/{symbol}
Get detailed quote for a symbol.

#### GET /market-data/history/{symbol}
Get historical price data.

**Query Parameters:**
- `period`: Time period (1d, 5d, 1m, 3m, 6m, 1y, 2y, 5y, 10y, ytd, max)
- `interval`: Data interval (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo)

### Analytics

#### GET /analytics/insights
Get market insights and analysis.

**Query Parameters:**
- `symbol`: Filter by symbol

**Response:**
```json
{
  "insights": [
    {
      "insight_id": "insight_001",
      "insight_type": "trend_analysis",
      "symbol": "AAPL",
      "description": "Strong upward trend detected",
      "confidence": 0.85,
      "impact_score": 0.7,
      "timestamp": "2024-01-01T12:00:00Z"
    }
  ]
}
```

#### GET /analytics/performance
Get performance analytics.

#### GET /analytics/reports
Get analytical reports.

### Learning System

#### GET /learning/models
Get machine learning models status.

**Response:**
```json
{
  "models": [
    {
      "model_id": "model_001",
      "name": "Price Prediction Model",
      "type": "neural_network",
      "status": "trained",
      "accuracy": 0.78,
      "last_trained": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### POST /learning/models/{model_id}/train
Trigger model training.

#### GET /learning/models/{model_id}/predictions
Get model predictions.

### Database Management

#### GET /database/status
Get database connection status.

#### GET /database/stats
Get database statistics.

#### POST /database/backup
Trigger database backup.

#### GET /database/health
Get database health metrics.

## 🔌 WebSocket API

### Connection
```javascript
const ws = new WebSocket('ws://localhost:8000/ws');
```

### Authentication
```javascript
ws.send(JSON.stringify({
  type: 'auth',
  token: 'your-jwt-token'
}));
```

### Subscriptions

#### Market Data Stream
```javascript
// Subscribe to market data
ws.send(JSON.stringify({
  type: 'subscribe',
  topic: 'market_data',
  symbols: ['AAPL', 'GOOGL']
}));

// Receive market data
{
  "type": "market_data",
  "symbol": "AAPL",
  "data": {
    "price": 150.0,
    "volume": 1000000,
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

#### System Status Stream
```javascript
// Subscribe to system status
ws.send(JSON.stringify({
  type: 'subscribe',
  topic: 'system_status'
}));

// Receive status updates
{
  "type": "system_status",
  "data": {
    "status": "healthy",
    "components": {...}
  }
}
```

#### Portfolio Updates
```javascript
// Subscribe to portfolio updates
ws.send(JSON.stringify({
  type: 'subscribe',
  topic: 'portfolio',
  portfolio_id: 'portfolio_001'
}));
```

#### Agent Communications
```javascript
// Subscribe to agent messages
ws.send(JSON.stringify({
  type: 'subscribe',
  topic: 'agent_messages',
  agent_id: 'agent_001'
}));
```

#### Trade Notifications
```javascript
// Subscribe to trade notifications
ws.send(JSON.stringify({
  type: 'subscribe',
  topic: 'trades'
}));
```

### Unsubscribe
```javascript
ws.send(JSON.stringify({
  type: 'unsubscribe',
  topic: 'market_data'
}));
```

## 📊 Response Formats

### Success Response
```json
{
  "success": true,
  "data": {...},
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request parameters",
    "details": {
      "field": "symbol",
      "issue": "Symbol is required"
    }
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Pagination
```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "per_page": 50,
    "total": 1000,
    "pages": 20
  }
}
```

## 🔒 Security

### Rate Limiting
- Default: 100 requests per minute per IP
- Authenticated users: 1000 requests per minute
- Headers: `X-RateLimit-Limit`, `X-RateLimit-Remaining`, `X-RateLimit-Reset`

### CORS
- Configurable origins
- Supports credentials
- Preflight requests handled

### Input Validation
- All inputs validated
- SQL injection protection
- XSS protection
- CSRF protection

## 📈 Monitoring

### Health Checks
- `GET /health`: Basic health check
- `GET /ready`: Readiness check
- `GET /metrics`: Prometheus metrics

### Metrics
- Request count and duration
- Error rates
- System resource usage
- Business metrics

## 🐛 Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 422 | Validation Error |
| 429 | Rate Limited |
| 500 | Internal Server Error |
| 503 | Service Unavailable |

## 📝 Examples

### Python Client
```python
import requests

# Login
response = requests.post('http://localhost:8000/api/v1/auth/login', 
                        json={'username': 'admin', 'password': 'admin123'})
token = response.json()['access_token']

# Get system status
headers = {'Authorization': f'Bearer {token}'}
response = requests.get('http://localhost:8000/api/v1/system/status', 
                       headers=headers)
status = response.json()
```

### JavaScript Client
```javascript
// Login
const loginResponse = await fetch('/api/v1/auth/login', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({username: 'admin', password: 'admin123'})
});
const {access_token} = await loginResponse.json();

// Get portfolios
const portfolioResponse = await fetch('/api/v1/portfolio/', {
  headers: {'Authorization': `Bearer ${access_token}`}
});
const portfolios = await portfolioResponse.json();
```

### cURL Examples
```bash
# Get market data
curl -H "Authorization: Bearer $TOKEN" \
  "http://localhost:8000/api/v1/market-data/prices?symbols=AAPL,GOOGL"

# Place order
curl -X POST \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"symbol":"AAPL","side":"buy","quantity":100,"order_type":"market"}' \
  "http://localhost:8000/api/v1/execution/orders"
```

## 📚 SDKs and Libraries

### Official SDKs
- Python SDK: `pip install trading-agents-sdk`
- JavaScript SDK: `npm install trading-agents-sdk`

### Community Libraries
- Go client
- Java client
- C# client

## 🆘 Support

- API Documentation: [Interactive Docs](http://localhost:8000/docs)
- OpenAPI Spec: [Swagger UI](http://localhost:8000/redoc)
- GitHub Issues: [Report Issues](https://github.com/your-org/trading-agents/issues)
- Discord: [Join Community](https://discord.gg/trading-agents)

"""
Test Framework - Core testing infrastructure and orchestration
"""

import asyncio
import logging
import time
import traceback
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import pytest
import pytest_asyncio
from unittest.mock import Mock, AsyncMock, patch

logger = logging.getLogger(__name__)


class TestType(Enum):
    """Test type enumeration"""
    UNIT = "unit"
    INTEGRATION = "integration"
    END_TO_END = "end_to_end"
    PERFORMANCE = "performance"
    LOAD = "load"
    SECURITY = "security"
    REGRESSION = "regression"
    SMOKE = "smoke"


class TestStatus(Enum):
    """Test status enumeration"""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


@dataclass
class TestResult:
    """Test result data structure"""
    test_id: str
    test_name: str
    test_type: TestType
    status: TestStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    error_message: Optional[str] = None
    error_traceback: Optional[str] = None
    assertions: List[Dict[str, Any]] = field(default_factory=list)
    metrics: Dict[str, Any] = field(default_factory=dict)
    artifacts: List[str] = field(default_factory=list)


@dataclass
class TestSuite:
    """Test suite configuration"""
    suite_id: str
    name: str
    description: str
    test_type: TestType
    tests: List[Callable] = field(default_factory=list)
    setup_hooks: List[Callable] = field(default_factory=list)
    teardown_hooks: List[Callable] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    timeout: int = 300  # seconds
    retry_count: int = 0
    parallel: bool = False


class TestFramework:
    """
    Comprehensive testing framework for the trading system.
    
    Features:
    - Multiple test types (unit, integration, e2e, performance, etc.)
    - Async test execution
    - Test discovery and organization
    - Mock data generation
    - Test result tracking and reporting
    - Parallel test execution
    - Test dependencies and ordering
    - Retry mechanisms
    - Performance metrics collection
    - Test artifacts management
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.test_config = config.get('testing', {})
        
        # Test configuration
        self.test_timeout = self.test_config.get('timeout', 300)
        self.parallel_execution = self.test_config.get('parallel', True)
        self.max_workers = self.test_config.get('max_workers', 4)
        self.retry_failed_tests = self.test_config.get('retry_failed', True)
        self.max_retries = self.test_config.get('max_retries', 3)
        
        # Test suites
        self.test_suites: Dict[str, TestSuite] = {}
        self.test_results: Dict[str, TestResult] = {}
        
        # Test execution state
        self.running_tests: Dict[str, asyncio.Task] = {}
        self.test_queue: asyncio.Queue = asyncio.Queue()
        
        # Mock objects and fixtures
        self.mocks: Dict[str, Mock] = {}
        self.fixtures: Dict[str, Any] = {}
        
        # Performance tracking
        self.performance_metrics: Dict[str, List[float]] = {}
        
        # State
        self.initialized = False
        self.running = False
    
    async def initialize(self) -> bool:
        """Initialize test framework"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Test Framework...")
            
            # Setup test suites
            await self._setup_test_suites()
            
            # Initialize mock objects
            await self._setup_mocks()
            
            # Setup test fixtures
            await self._setup_fixtures()
            
            self.initialized = True
            logger.info("✓ Test Framework initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Test Framework: {e}")
            return False
    
    async def register_test_suite(self, suite: TestSuite):
        """Register a test suite"""
        try:
            self.test_suites[suite.suite_id] = suite
            logger.debug(f"Registered test suite: {suite.name}")
            
        except Exception as e:
            logger.error(f"Error registering test suite {suite.name}: {e}")
    
    async def run_tests(self, test_types: List[TestType] = None, 
                       suite_ids: List[str] = None) -> Dict[str, TestResult]:
        """Run tests with optional filtering"""
        try:
            logger.info("Starting test execution...")
            self.running = True
            
            # Filter test suites
            suites_to_run = await self._filter_test_suites(test_types, suite_ids)
            
            if not suites_to_run:
                logger.warning("No test suites to run")
                return {}
            
            # Execute test suites
            if self.parallel_execution:
                results = await self._run_tests_parallel(suites_to_run)
            else:
                results = await self._run_tests_sequential(suites_to_run)
            
            # Generate test report
            await self._generate_test_report(results)
            
            self.running = False
            logger.info("Test execution completed")
            
            return results
            
        except Exception as e:
            logger.error(f"Error running tests: {e}")
            self.running = False
            return {}
    
    async def run_single_test(self, test_func: Callable, test_type: TestType = TestType.UNIT) -> TestResult:
        """Run a single test function"""
        try:
            test_id = f"test_{int(time.time() * 1000)}"
            test_name = getattr(test_func, '__name__', 'unknown_test')
            
            result = TestResult(
                test_id=test_id,
                test_name=test_name,
                test_type=test_type,
                status=TestStatus.RUNNING,
                start_time=datetime.now()
            )
            
            # Execute test
            try:
                if asyncio.iscoroutinefunction(test_func):
                    await asyncio.wait_for(test_func(), timeout=self.test_timeout)
                else:
                    test_func()
                
                result.status = TestStatus.PASSED
                
            except AssertionError as e:
                result.status = TestStatus.FAILED
                result.error_message = str(e)
                result.error_traceback = traceback.format_exc()
                
            except Exception as e:
                result.status = TestStatus.ERROR
                result.error_message = str(e)
                result.error_traceback = traceback.format_exc()
            
            # Finalize result
            result.end_time = datetime.now()
            result.duration = (result.end_time - result.start_time).total_seconds()
            
            self.test_results[test_id] = result
            return result
            
        except Exception as e:
            logger.error(f"Error running single test: {e}")
            raise
    
    async def create_mock(self, name: str, spec: Any = None, **kwargs) -> Mock:
        """Create and register a mock object"""
        try:
            if asyncio.iscoroutinefunction(spec):
                mock = AsyncMock(spec=spec, **kwargs)
            else:
                mock = Mock(spec=spec, **kwargs)
            
            self.mocks[name] = mock
            return mock
            
        except Exception as e:
            logger.error(f"Error creating mock {name}: {e}")
            raise
    
    async def get_mock(self, name: str) -> Optional[Mock]:
        """Get a registered mock object"""
        return self.mocks.get(name)
    
    async def setup_fixture(self, name: str, value: Any):
        """Setup a test fixture"""
        self.fixtures[name] = value
    
    async def get_fixture(self, name: str) -> Any:
        """Get a test fixture"""
        return self.fixtures.get(name)
    
    async def assert_performance(self, metric_name: str, expected_value: float, 
                                tolerance: float = 0.1) -> bool:
        """Assert performance metric is within expected range"""
        try:
            if metric_name not in self.performance_metrics:
                return False
            
            metrics = self.performance_metrics[metric_name]
            if not metrics:
                return False
            
            avg_value = sum(metrics) / len(metrics)
            deviation = abs(avg_value - expected_value) / expected_value
            
            return deviation <= tolerance
            
        except Exception as e:
            logger.error(f"Error asserting performance for {metric_name}: {e}")
            return False
    
    async def record_performance_metric(self, metric_name: str, value: float):
        """Record a performance metric"""
        if metric_name not in self.performance_metrics:
            self.performance_metrics[metric_name] = []
        
        self.performance_metrics[metric_name].append(value)
    
    async def get_test_results(self, test_type: TestType = None) -> List[TestResult]:
        """Get test results with optional filtering"""
        results = list(self.test_results.values())
        
        if test_type:
            results = [r for r in results if r.test_type == test_type]
        
        return results
    
    async def get_test_statistics(self) -> Dict[str, Any]:
        """Get test execution statistics"""
        try:
            results = list(self.test_results.values())
            
            if not results:
                return {}
            
            total_tests = len(results)
            passed_tests = len([r for r in results if r.status == TestStatus.PASSED])
            failed_tests = len([r for r in results if r.status == TestStatus.FAILED])
            error_tests = len([r for r in results if r.status == TestStatus.ERROR])
            skipped_tests = len([r for r in results if r.status == TestStatus.SKIPPED])
            
            # Calculate durations
            completed_results = [r for r in results if r.duration is not None]
            avg_duration = sum(r.duration for r in completed_results) / len(completed_results) if completed_results else 0
            total_duration = sum(r.duration for r in completed_results)
            
            # Calculate success rate
            success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
            
            return {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'error_tests': error_tests,
                'skipped_tests': skipped_tests,
                'success_rate': success_rate,
                'average_duration': avg_duration,
                'total_duration': total_duration,
                'test_types': {
                    test_type.value: len([r for r in results if r.test_type == test_type])
                    for test_type in TestType
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting test statistics: {e}")
            return {}
    
    # Private methods
    
    async def _setup_test_suites(self):
        """Setup default test suites"""
        try:
            # Unit test suite
            unit_suite = TestSuite(
                suite_id="unit_tests",
                name="Unit Tests",
                description="Unit tests for individual components",
                test_type=TestType.UNIT,
                timeout=60,
                parallel=True
            )
            await self.register_test_suite(unit_suite)
            
            # Integration test suite
            integration_suite = TestSuite(
                suite_id="integration_tests",
                name="Integration Tests",
                description="Integration tests for component interactions",
                test_type=TestType.INTEGRATION,
                timeout=300,
                parallel=False
            )
            await self.register_test_suite(integration_suite)
            
            # Performance test suite
            performance_suite = TestSuite(
                suite_id="performance_tests",
                name="Performance Tests",
                description="Performance and load testing",
                test_type=TestType.PERFORMANCE,
                timeout=600,
                parallel=False
            )
            await self.register_test_suite(performance_suite)
            
            logger.debug("Default test suites setup completed")
            
        except Exception as e:
            logger.error(f"Error setting up test suites: {e}")
            raise
    
    async def _setup_mocks(self):
        """Setup common mock objects"""
        try:
            # Mock trading system
            await self.create_mock('trading_system', spec=None)
            
            # Mock database
            await self.create_mock('database', spec=None)
            
            # Mock API client
            await self.create_mock('api_client', spec=None)
            
            # Mock market data
            await self.create_mock('market_data', spec=None)
            
            logger.debug("Mock objects setup completed")
            
        except Exception as e:
            logger.error(f"Error setting up mocks: {e}")
            raise
    
    async def _setup_fixtures(self):
        """Setup test fixtures"""
        try:
            # Sample configuration
            await self.setup_fixture('test_config', {
                'testing': True,
                'database': {'host': 'localhost', 'port': 5432},
                'api': {'host': '0.0.0.0', 'port': 8000}
            })
            
            # Sample market data
            await self.setup_fixture('sample_market_data', {
                'AAPL': {'price': 150.0, 'volume': 1000000},
                'GOOGL': {'price': 2500.0, 'volume': 500000}
            })
            
            logger.debug("Test fixtures setup completed")
            
        except Exception as e:
            logger.error(f"Error setting up fixtures: {e}")
            raise
    
    async def _filter_test_suites(self, test_types: List[TestType] = None, 
                                 suite_ids: List[str] = None) -> List[TestSuite]:
        """Filter test suites based on criteria"""
        suites = list(self.test_suites.values())
        
        if test_types:
            suites = [s for s in suites if s.test_type in test_types]
        
        if suite_ids:
            suites = [s for s in suites if s.suite_id in suite_ids]
        
        return suites
    
    async def _run_tests_parallel(self, suites: List[TestSuite]) -> Dict[str, TestResult]:
        """Run test suites in parallel"""
        try:
            tasks = []
            
            for suite in suites:
                if suite.parallel:
                    task = asyncio.create_task(self._execute_test_suite(suite))
                    tasks.append(task)
                else:
                    # Run non-parallel suites sequentially
                    await self._execute_test_suite(suite)
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
            
            return self.test_results
            
        except Exception as e:
            logger.error(f"Error running tests in parallel: {e}")
            return {}
    
    async def _run_tests_sequential(self, suites: List[TestSuite]) -> Dict[str, TestResult]:
        """Run test suites sequentially"""
        try:
            for suite in suites:
                await self._execute_test_suite(suite)
            
            return self.test_results
            
        except Exception as e:
            logger.error(f"Error running tests sequentially: {e}")
            return {}
    
    async def _execute_test_suite(self, suite: TestSuite) -> List[TestResult]:
        """Execute a test suite"""
        try:
            logger.info(f"Executing test suite: {suite.name}")
            
            # Run setup hooks
            for setup_hook in suite.setup_hooks:
                await setup_hook()
            
            # Execute tests
            suite_results = []
            for test_func in suite.tests:
                result = await self.run_single_test(test_func, suite.test_type)
                suite_results.append(result)
            
            # Run teardown hooks
            for teardown_hook in suite.teardown_hooks:
                await teardown_hook()
            
            logger.info(f"Completed test suite: {suite.name}")
            return suite_results
            
        except Exception as e:
            logger.error(f"Error executing test suite {suite.name}: {e}")
            return []
    
    async def _generate_test_report(self, results: Dict[str, TestResult]):
        """Generate test execution report"""
        try:
            stats = await self.get_test_statistics()
            
            logger.info("=" * 60)
            logger.info("TEST EXECUTION REPORT")
            logger.info("=" * 60)
            logger.info(f"Total Tests: {stats.get('total_tests', 0)}")
            logger.info(f"Passed: {stats.get('passed_tests', 0)}")
            logger.info(f"Failed: {stats.get('failed_tests', 0)}")
            logger.info(f"Errors: {stats.get('error_tests', 0)}")
            logger.info(f"Skipped: {stats.get('skipped_tests', 0)}")
            logger.info(f"Success Rate: {stats.get('success_rate', 0):.1f}%")
            logger.info(f"Total Duration: {stats.get('total_duration', 0):.2f}s")
            logger.info("=" * 60)
            
        except Exception as e:
            logger.error(f"Error generating test report: {e}")


# Enhanced Test Framework with additional enterprise features
class AdvancedTestFramework(TestFramework):
    """
    Advanced test framework with enterprise features.

    Additional Features:
    - Load testing capabilities
    - Security testing
    - Test data generation
    - CI/CD integration
    - Test coverage analysis
    - Performance benchmarking
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)

        # Load testing configuration
        self.load_test_config = self.test_config.get('load_testing', {})
        self.max_concurrent_users = self.load_test_config.get('max_users', 100)
        self.load_test_duration = self.load_test_config.get('duration', 300)

        # Security testing configuration
        self.security_config = self.test_config.get('security', {})
        self.security_tests_enabled = self.security_config.get('enabled', True)

        # Coverage tracking
        self.coverage_enabled = self.test_config.get('coverage', {}).get('enabled', True)
        self.coverage_threshold = self.test_config.get('coverage', {}).get('threshold', 80)

        # Benchmark data
        self.benchmarks: Dict[str, Dict[str, float]] = {}

        # Test data generators
        self.data_generators: Dict[str, Callable] = {}

    async def run_load_test(self, test_func: Callable, concurrent_users: int = 10,
                           duration: int = 60) -> Dict[str, Any]:
        """Run load test with specified parameters"""
        try:
            logger.info(f"Starting load test with {concurrent_users} users for {duration}s")

            start_time = time.time()
            end_time = start_time + duration

            # Metrics collection
            response_times = []
            error_count = 0
            success_count = 0

            async def user_simulation():
                nonlocal error_count, success_count

                while time.time() < end_time:
                    request_start = time.time()

                    try:
                        if asyncio.iscoroutinefunction(test_func):
                            await test_func()
                        else:
                            test_func()
                        success_count += 1
                    except Exception as e:
                        error_count += 1
                        logger.debug(f"Load test error: {e}")

                    response_time = time.time() - request_start
                    response_times.append(response_time)

                    # Small delay to prevent overwhelming
                    await asyncio.sleep(0.01)

            # Run concurrent users
            tasks = [user_simulation() for _ in range(concurrent_users)]
            await asyncio.gather(*tasks)

            # Calculate metrics
            total_requests = len(response_times)
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
            throughput = total_requests / duration
            error_rate = (error_count / total_requests) * 100 if total_requests > 0 else 0

            # Percentiles
            import numpy as np
            p95_response_time = np.percentile(response_times, 95) if response_times else 0
            p99_response_time = np.percentile(response_times, 99) if response_times else 0

            results = {
                'concurrent_users': concurrent_users,
                'duration': duration,
                'total_requests': total_requests,
                'successful_requests': success_count,
                'failed_requests': error_count,
                'error_rate': error_rate,
                'throughput': throughput,
                'avg_response_time': avg_response_time,
                'p95_response_time': p95_response_time,
                'p99_response_time': p99_response_time,
                'min_response_time': min(response_times) if response_times else 0,
                'max_response_time': max(response_times) if response_times else 0
            }

            logger.info(f"Load test completed: {throughput:.1f} req/s, {error_rate:.1f}% errors")
            return results

        except Exception as e:
            logger.error(f"Error running load test: {e}")
            return {}

    async def run_security_tests(self) -> List[TestResult]:
        """Run security-focused tests"""
        try:
            if not self.security_tests_enabled:
                logger.info("Security tests disabled")
                return []

            logger.info("Running security tests...")
            security_results = []

            # SQL Injection tests
            sql_injection_result = await self._test_sql_injection()
            security_results.append(sql_injection_result)

            # XSS tests
            xss_result = await self._test_xss_vulnerabilities()
            security_results.append(xss_result)

            # Authentication tests
            auth_result = await self._test_authentication_security()
            security_results.append(auth_result)

            # Authorization tests
            authz_result = await self._test_authorization_security()
            security_results.append(authz_result)

            logger.info(f"Security tests completed: {len(security_results)} tests")
            return security_results

        except Exception as e:
            logger.error(f"Error running security tests: {e}")
            return []

    async def _test_sql_injection(self) -> TestResult:
        """Test for SQL injection vulnerabilities"""
        test_id = "security_sql_injection"
        result = TestResult(
            test_id=test_id,
            test_name="SQL Injection Security Test",
            test_type=TestType.SECURITY,
            status=TestStatus.RUNNING,
            start_time=datetime.now()
        )

        try:
            # Test common SQL injection patterns
            injection_patterns = [
                "'; DROP TABLE users; --",
                "' OR '1'='1",
                "' UNION SELECT * FROM users --",
                "'; INSERT INTO users VALUES ('hacker', 'password'); --"
            ]

            vulnerabilities_found = 0

            for pattern in injection_patterns:
                # Test against API endpoints (mock implementation)
                try:
                    # This would test actual API endpoints
                    # For now, we'll simulate the test
                    await asyncio.sleep(0.1)  # Simulate API call

                    # Check if injection was successful (mock check)
                    if "error" not in pattern.lower():  # Mock vulnerability detection
                        vulnerabilities_found += 1

                except Exception:
                    pass  # Expected for secure systems

            if vulnerabilities_found == 0:
                result.status = TestStatus.PASSED
            else:
                result.status = TestStatus.FAILED
                result.error_message = f"Found {vulnerabilities_found} potential SQL injection vulnerabilities"

        except Exception as e:
            result.status = TestStatus.ERROR
            result.error_message = str(e)

        result.end_time = datetime.now()
        result.duration = (result.end_time - result.start_time).total_seconds()

        return result

    async def _test_xss_vulnerabilities(self) -> TestResult:
        """Test for XSS vulnerabilities"""
        test_id = "security_xss"
        result = TestResult(
            test_id=test_id,
            test_name="XSS Security Test",
            test_type=TestType.SECURITY,
            status=TestStatus.RUNNING,
            start_time=datetime.now()
        )

        try:
            # Test XSS patterns
            xss_patterns = [
                "<script>alert('XSS')</script>",
                "javascript:alert('XSS')",
                "<img src=x onerror=alert('XSS')>",
                "<svg onload=alert('XSS')>"
            ]

            vulnerabilities_found = 0

            for pattern in xss_patterns:
                # Test against web endpoints (mock implementation)
                try:
                    await asyncio.sleep(0.1)  # Simulate web request

                    # Mock vulnerability check
                    if len(pattern) > 20:  # Mock detection logic
                        vulnerabilities_found += 1

                except Exception:
                    pass

            if vulnerabilities_found == 0:
                result.status = TestStatus.PASSED
            else:
                result.status = TestStatus.FAILED
                result.error_message = f"Found {vulnerabilities_found} potential XSS vulnerabilities"

        except Exception as e:
            result.status = TestStatus.ERROR
            result.error_message = str(e)

        result.end_time = datetime.now()
        result.duration = (result.end_time - result.start_time).total_seconds()

        return result

    async def _test_authentication_security(self) -> TestResult:
        """Test authentication security"""
        test_id = "security_authentication"
        result = TestResult(
            test_id=test_id,
            test_name="Authentication Security Test",
            test_type=TestType.SECURITY,
            status=TestStatus.RUNNING,
            start_time=datetime.now()
        )

        try:
            # Test authentication mechanisms
            auth_tests = [
                "weak_password_policy",
                "brute_force_protection",
                "session_management",
                "password_storage"
            ]

            failed_tests = []

            for test_case in auth_tests:
                # Mock authentication tests
                await asyncio.sleep(0.1)

                # Simulate test results
                if test_case == "weak_password_policy":
                    # Mock: check if weak passwords are rejected
                    weak_passwords = ["123", "password", "admin"]
                    for pwd in weak_passwords:
                        # Mock password validation
                        if len(pwd) < 8:  # Simple validation
                            failed_tests.append(f"Weak password accepted: {pwd}")

            if not failed_tests:
                result.status = TestStatus.PASSED
            else:
                result.status = TestStatus.FAILED
                result.error_message = f"Authentication issues: {', '.join(failed_tests)}"

        except Exception as e:
            result.status = TestStatus.ERROR
            result.error_message = str(e)

        result.end_time = datetime.now()
        result.duration = (result.end_time - result.start_time).total_seconds()

        return result

    async def _test_authorization_security(self) -> TestResult:
        """Test authorization security"""
        test_id = "security_authorization"
        result = TestResult(
            test_id=test_id,
            test_name="Authorization Security Test",
            test_type=TestType.SECURITY,
            status=TestStatus.RUNNING,
            start_time=datetime.now()
        )

        try:
            # Test authorization mechanisms
            authz_issues = []

            # Test privilege escalation
            await asyncio.sleep(0.1)

            # Test access control
            await asyncio.sleep(0.1)

            # Test role-based access
            await asyncio.sleep(0.1)

            if not authz_issues:
                result.status = TestStatus.PASSED
            else:
                result.status = TestStatus.FAILED
                result.error_message = f"Authorization issues: {', '.join(authz_issues)}"

        except Exception as e:
            result.status = TestStatus.ERROR
            result.error_message = str(e)

        result.end_time = datetime.now()
        result.duration = (result.end_time - result.start_time).total_seconds()

        return result

    async def benchmark_performance(self, test_func: Callable, test_name: str,
                                  iterations: int = 100) -> Dict[str, float]:
        """Benchmark performance of a function"""
        try:
            logger.info(f"Benchmarking {test_name} with {iterations} iterations")

            execution_times = []

            for i in range(iterations):
                start_time = time.time()

                if asyncio.iscoroutinefunction(test_func):
                    await test_func()
                else:
                    test_func()

                execution_time = time.time() - start_time
                execution_times.append(execution_time)

            # Calculate statistics
            import numpy as np

            benchmark_results = {
                'iterations': iterations,
                'min_time': min(execution_times),
                'max_time': max(execution_times),
                'avg_time': np.mean(execution_times),
                'median_time': np.median(execution_times),
                'std_dev': np.std(execution_times),
                'p95_time': np.percentile(execution_times, 95),
                'p99_time': np.percentile(execution_times, 99)
            }

            # Store benchmark
            self.benchmarks[test_name] = benchmark_results

            logger.info(f"Benchmark completed: {test_name} avg={benchmark_results['avg_time']:.4f}s")
            return benchmark_results

        except Exception as e:
            logger.error(f"Error benchmarking {test_name}: {e}")
            return {}

    async def generate_test_data(self, data_type: str, count: int = 100) -> List[Dict[str, Any]]:
        """Generate test data"""
        try:
            if data_type in self.data_generators:
                generator = self.data_generators[data_type]
                return [generator() for _ in range(count)]

            # Default generators
            if data_type == "market_data":
                return self._generate_market_data(count)
            elif data_type == "user_data":
                return self._generate_user_data(count)
            elif data_type == "trade_data":
                return self._generate_trade_data(count)
            else:
                logger.warning(f"Unknown data type: {data_type}")
                return []

        except Exception as e:
            logger.error(f"Error generating test data: {e}")
            return []

    def _generate_market_data(self, count: int) -> List[Dict[str, Any]]:
        """Generate mock market data"""
        import random

        symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN", "META", "NVDA"]
        data = []

        for i in range(count):
            symbol = random.choice(symbols)
            base_price = random.uniform(50, 500)

            data.append({
                'symbol': symbol,
                'price': round(base_price + random.uniform(-5, 5), 2),
                'volume': random.randint(100000, 10000000),
                'timestamp': datetime.now().isoformat(),
                'bid': round(base_price - 0.01, 2),
                'ask': round(base_price + 0.01, 2)
            })

        return data

    def _generate_user_data(self, count: int) -> List[Dict[str, Any]]:
        """Generate mock user data"""
        import random
        import string

        data = []

        for i in range(count):
            username = ''.join(random.choices(string.ascii_lowercase, k=8))

            data.append({
                'user_id': f"user_{i:06d}",
                'username': username,
                'email': f"{username}@example.com",
                'created_at': datetime.now().isoformat(),
                'is_active': random.choice([True, False]),
                'balance': round(random.uniform(1000, 100000), 2)
            })

        return data

    def _generate_trade_data(self, count: int) -> List[Dict[str, Any]]:
        """Generate mock trade data"""
        import random

        symbols = ["AAPL", "GOOGL", "MSFT", "TSLA"]
        sides = ["buy", "sell"]

        data = []

        for i in range(count):
            data.append({
                'trade_id': f"trade_{i:08d}",
                'symbol': random.choice(symbols),
                'side': random.choice(sides),
                'quantity': random.randint(1, 1000),
                'price': round(random.uniform(50, 500), 2),
                'timestamp': datetime.now().isoformat(),
                'user_id': f"user_{random.randint(1, 1000):06d}"
            })

        return data

    async def export_test_results(self, format: str = "json") -> str:
        """Export test results in specified format"""
        try:
            stats = await self.get_test_statistics()
            results = await self.get_test_results()

            export_data = {
                'statistics': stats,
                'results': [
                    {
                        'test_id': r.test_id,
                        'test_name': r.test_name,
                        'test_type': r.test_type.value,
                        'status': r.status.value,
                        'duration': r.duration,
                        'error_message': r.error_message
                    }
                    for r in results
                ],
                'benchmarks': self.benchmarks,
                'exported_at': datetime.now().isoformat()
            }

            if format.lower() == "json":
                import json
                return json.dumps(export_data, indent=2)
            elif format.lower() == "xml":
                return self._export_to_xml(export_data)
            elif format.lower() == "junit":
                return self._export_to_junit(export_data)
            else:
                raise ValueError(f"Unsupported export format: {format}")

        except Exception as e:
            logger.error(f"Error exporting test results: {e}")
            return ""

    def _export_to_junit(self, data: Dict[str, Any]) -> str:
        """Export to JUnit XML format"""
        xml_lines = ['<?xml version="1.0" encoding="UTF-8"?>']

        stats = data['statistics']
        xml_lines.append(
            f'<testsuite name="TradingSystemTests" '
            f'tests="{stats.get("total_tests", 0)}" '
            f'failures="{stats.get("failed_tests", 0)}" '
            f'errors="{stats.get("error_tests", 0)}" '
            f'time="{stats.get("total_duration", 0):.3f}">'
        )

        for result in data['results']:
            xml_lines.append(
                f'  <testcase name="{result["test_name"]}" '
                f'classname="{result["test_id"]}" '
                f'time="{result.get("duration", 0):.3f}">'
            )

            if result['status'] == 'failed':
                xml_lines.append(f'    <failure message="{result.get("error_message", "")}"></failure>')
            elif result['status'] == 'error':
                xml_lines.append(f'    <error message="{result.get("error_message", "")}"></error>')

            xml_lines.append('  </testcase>')

        xml_lines.append('</testsuite>')
        return '\n'.join(xml_lines)


# Global test framework instance
advanced_test_framework = None


def get_advanced_test_framework(config: Dict[str, Any] = None) -> AdvancedTestFramework:
    """Get global advanced test framework instance"""
    global advanced_test_framework

    if advanced_test_framework is None:
        config = config or {}
        advanced_test_framework = AdvancedTestFramework(config)

    return advanced_test_framework


# Test decorators and utilities

def test_case(test_type: TestType = TestType.UNIT, timeout: int = 60):
    """Decorator for marking test cases"""
    def decorator(func):
        func._test_type = test_type
        func._test_timeout = timeout
        return func
    return decorator


def async_test(func):
    """Decorator for async test functions"""
    return pytest_asyncio.mark.asyncio(func)


def performance_test(metric_name: str, expected_value: float, tolerance: float = 0.1):
    """Decorator for performance tests"""
    def decorator(func):
        func._performance_metric = metric_name
        func._expected_value = expected_value
        func._tolerance = tolerance
        return func
    return decorator

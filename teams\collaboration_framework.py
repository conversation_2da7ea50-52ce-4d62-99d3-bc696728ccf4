"""
Collaboration Framework - Comprehensive team collaboration and coordination
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Optional, Any, Set, Tuple
from enum import Enum
from dataclasses import dataclass, field
from collections import defaultdict

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task status in collaboration framework"""
    PENDING = "pending"
    ASSIGNED = "assigned"
    IN_PROGRESS = "in_progress"
    BLOCKED = "blocked"
    REVIEW = "review"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """Task priority levels"""
    CRITICAL = 5
    HIGH = 4
    MEDIUM = 3
    LOW = 2
    MINIMAL = 1


class CollaborationMode(Enum):
    """Collaboration modes"""
    SEQUENTIAL = "sequential"      # Tasks done in sequence
    PARALLEL = "parallel"         # Tasks done simultaneously
    PIPELINE = "pipeline"         # Pipeline processing
    SWARM = "swarm"              # Swarm intelligence approach
    HIERARCHICAL = "hierarchical" # Top-down coordination


@dataclass
class CollaborativeTask:
    """A collaborative task"""
    task_id: str
    title: str
    description: str
    task_type: str
    priority: TaskPriority
    status: TaskStatus
    assigned_to: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    required_skills: List[str] = field(default_factory=list)
    estimated_duration: int = 0  # seconds
    actual_duration: Optional[int] = None
    created_by: str = ""
    created_at: float = 0.0
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    progress: float = 0.0  # 0.0 to 1.0
    deliverables: Dict[str, Any] = field(default_factory=dict)
    collaboration_mode: CollaborationMode = CollaborationMode.SEQUENTIAL
    team_id: Optional[str] = None


@dataclass
class KnowledgeItem:
    """Knowledge item for sharing"""
    knowledge_id: str
    title: str
    content: Any
    knowledge_type: str  # "insight", "pattern", "solution", "data"
    source_agent: str
    created_at: float
    tags: List[str] = field(default_factory=list)
    relevance_score: float = 1.0
    access_count: int = 0
    last_accessed: Optional[float] = None


@dataclass
class ProgressUpdate:
    """Progress update for a task"""
    update_id: str
    task_id: str
    agent_id: str
    progress_delta: float
    status_change: Optional[TaskStatus] = None
    message: str = ""
    deliverables: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = 0.0
    blockers: List[str] = field(default_factory=list)


class CollaborationFramework:
    """
    Comprehensive collaboration framework for team coordination,
    task distribution, progress tracking, and knowledge sharing.
    """
    
    def __init__(self, team_manager, decision_protocols, config: Dict[str, Any]):
        self.team_manager = team_manager
        self.decision_protocols = decision_protocols
        self.config = config
        
        # Task management
        self.active_tasks: Dict[str, CollaborativeTask] = {}
        self.task_history: List[CollaborativeTask] = []
        self.task_dependencies: Dict[str, Set[str]] = defaultdict(set)
        
        # Progress tracking
        self.progress_updates: Dict[str, List[ProgressUpdate]] = defaultdict(list)
        self.task_metrics: Dict[str, Dict[str, Any]] = {}
        
        # Knowledge sharing
        self.knowledge_base: Dict[str, KnowledgeItem] = {}
        self.knowledge_index: Dict[str, Set[str]] = defaultdict(set)  # tag -> knowledge_ids
        
        # Coordination mechanisms
        self.coordination_strategies: Dict[str, Any] = {}
        self.collaboration_patterns: Dict[str, Dict[str, Any]] = {}
        
        # Resource allocation
        self.agent_workloads: Dict[str, List[str]] = defaultdict(list)  # agent_id -> task_ids
        self.resource_constraints: Dict[str, Dict[str, Any]] = {}
        
        # State
        self.initialized = False
        
    async def initialize(self):
        """Initialize collaboration framework"""
        if self.initialized:
            return
            
        logger.info("Initializing Collaboration Framework...")
        
        # Setup coordination strategies
        await self._setup_coordination_strategies()
        
        # Setup collaboration patterns
        await self._setup_collaboration_patterns()
        
        # Setup knowledge management
        await self._setup_knowledge_management()
        
        # Setup resource allocation
        await self._setup_resource_allocation()
        
        self.initialized = True
        logger.info("✓ Collaboration Framework initialized")
        
    async def _setup_coordination_strategies(self):
        """Setup coordination strategies"""
        self.coordination_strategies = {
            'task_distribution': {
                'load_balancing': True,
                'skill_matching': True,
                'priority_based': True,
                'deadline_aware': True
            },
            'progress_monitoring': {
                'update_frequency': 300,  # 5 minutes
                'auto_escalation': True,
                'bottleneck_detection': True,
                'performance_tracking': True
            },
            'conflict_resolution': {
                'resource_conflicts': 'negotiation',
                'priority_conflicts': 'escalation',
                'dependency_conflicts': 'reordering'
            },
            'adaptation': {
                'dynamic_reallocation': True,
                'strategy_switching': True,
                'learning_integration': True
            }
        }
        
    async def _setup_collaboration_patterns(self):
        """Setup collaboration patterns"""
        self.collaboration_patterns = {
            'sequential_pattern': {
                'description': 'Tasks executed in sequence',
                'coordination_overhead': 'low',
                'parallelization': False,
                'communication_frequency': 'milestone_based'
            },
            'parallel_pattern': {
                'description': 'Tasks executed simultaneously',
                'coordination_overhead': 'medium',
                'parallelization': True,
                'communication_frequency': 'regular'
            },
            'pipeline_pattern': {
                'description': 'Pipeline processing with handoffs',
                'coordination_overhead': 'medium',
                'parallelization': True,
                'communication_frequency': 'handoff_based'
            },
            'swarm_pattern': {
                'description': 'Distributed swarm intelligence',
                'coordination_overhead': 'high',
                'parallelization': True,
                'communication_frequency': 'continuous'
            },
            'hierarchical_pattern': {
                'description': 'Top-down hierarchical coordination',
                'coordination_overhead': 'low',
                'parallelization': False,
                'communication_frequency': 'reporting_based'
            }
        }
        
    async def _setup_knowledge_management(self):
        """Setup knowledge management system"""
        self.knowledge_management = {
            'storage': {
                'max_items': 10000,
                'retention_period': 2592000,  # 30 days
                'auto_cleanup': True
            },
            'indexing': {
                'auto_tagging': True,
                'similarity_threshold': 0.7,
                'relevance_decay': 0.95
            },
            'sharing': {
                'auto_distribution': True,
                'relevance_filtering': True,
                'access_tracking': True
            }
        }
        
    async def _setup_resource_allocation(self):
        """Setup resource allocation mechanisms"""
        self.resource_allocation = {
            'capacity_limits': {
                'max_concurrent_tasks': 5,
                'max_workload_hours': 8,
                'priority_weighting': True
            },
            'allocation_strategy': {
                'skill_matching_weight': 0.4,
                'workload_balance_weight': 0.3,
                'priority_weight': 0.3
            },
            'reallocation_triggers': {
                'overload_threshold': 0.9,
                'underutilization_threshold': 0.3,
                'deadline_pressure': 0.8
            }
        }
        
    async def create_collaborative_task(self, title: str, description: str, task_type: str,
                                      priority: TaskPriority, required_skills: List[str],
                                      estimated_duration: int, dependencies: List[str] = None,
                                      collaboration_mode: CollaborationMode = CollaborationMode.SEQUENTIAL,
                                      team_id: str = None, created_by: str = "system") -> Dict[str, Any]:
        """Create a new collaborative task"""
        try:
            task_id = str(uuid.uuid4())
            
            task = CollaborativeTask(
                task_id=task_id,
                title=title,
                description=description,
                task_type=task_type,
                priority=priority,
                status=TaskStatus.PENDING,
                dependencies=dependencies or [],
                required_skills=required_skills,
                estimated_duration=estimated_duration,
                created_by=created_by,
                created_at=time.time(),
                collaboration_mode=collaboration_mode,
                team_id=team_id
            )
            
            # Store task
            self.active_tasks[task_id] = task
            
            # Setup dependencies
            if dependencies:
                for dep_id in dependencies:
                    self.task_dependencies[task_id].add(dep_id)
                    
            # Initialize progress tracking
            self.progress_updates[task_id] = []
            
            # Initialize metrics
            self.task_metrics[task_id] = {
                'created_at': task.created_at,
                'estimated_duration': estimated_duration,
                'priority_score': priority.value,
                'complexity_score': len(required_skills) * 0.2
            }
            
            logger.info(f"✓ Created collaborative task {task_id}: {title}")
            
            return {
                'success': True,
                'task_id': task_id,
                'status': task.status.value,
                'estimated_duration': estimated_duration
            }
            
        except Exception as e:
            logger.error(f"Error creating collaborative task: {e}")
            return {'success': False, 'error': str(e)}
            
    async def assign_task(self, task_id: str, agent_ids: List[str], 
                         assignment_strategy: str = "optimal") -> Dict[str, Any]:
        """Assign task to agents"""
        try:
            if task_id not in self.active_tasks:
                return {'success': False, 'error': 'Task not found'}
                
            task = self.active_tasks[task_id]
            
            # Check if task can be assigned (dependencies met)
            if not await self._check_dependencies_met(task_id):
                return {'success': False, 'error': 'Dependencies not met'}
                
            # Validate agent availability and skills
            valid_agents = await self._validate_agent_assignment(agent_ids, task)
            
            if not valid_agents:
                return {'success': False, 'error': 'No valid agents available'}
                
            # Assign task
            task.assigned_to = valid_agents
            task.status = TaskStatus.ASSIGNED
            
            # Update agent workloads
            for agent_id in valid_agents:
                self.agent_workloads[agent_id].append(task_id)
                
            # Create assignment record
            assignment_record = {
                'task_id': task_id,
                'assigned_agents': valid_agents,
                'assignment_strategy': assignment_strategy,
                'assigned_at': time.time()
            }
            
            # Notify agents
            await self._notify_task_assignment(task, valid_agents)
            
            logger.info(f"✓ Assigned task {task_id} to agents: {valid_agents}")
            
            return {
                'success': True,
                'assigned_agents': valid_agents,
                'assignment_record': assignment_record
            }
            
        except Exception as e:
            logger.error(f"Error assigning task: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _check_dependencies_met(self, task_id: str) -> bool:
        """Check if task dependencies are met"""
        dependencies = self.task_dependencies.get(task_id, set())
        
        for dep_id in dependencies:
            if dep_id in self.active_tasks:
                dep_task = self.active_tasks[dep_id]
                if dep_task.status != TaskStatus.COMPLETED:
                    return False
            else:
                # Check in history
                dep_completed = any(
                    task.task_id == dep_id and task.status == TaskStatus.COMPLETED
                    for task in self.task_history
                )
                if not dep_completed:
                    return False
                    
        return True
        
    async def _validate_agent_assignment(self, agent_ids: List[str], 
                                       task: CollaborativeTask) -> List[str]:
        """Validate agent assignment based on skills and availability"""
        valid_agents = []
        
        for agent_id in agent_ids:
            # Check agent availability
            if await self._check_agent_availability(agent_id, task):
                # Check skill match
                if await self._check_skill_match(agent_id, task.required_skills):
                    valid_agents.append(agent_id)
                    
        return valid_agents
        
    async def _check_agent_availability(self, agent_id: str, task: CollaborativeTask) -> bool:
        """Check if agent is available for task"""
        current_workload = len(self.agent_workloads.get(agent_id, []))
        max_concurrent = self.resource_allocation['capacity_limits']['max_concurrent_tasks']
        
        return current_workload < max_concurrent
        
    async def _check_skill_match(self, agent_id: str, required_skills: List[str]) -> bool:
        """Check if agent has required skills"""
        # This would check agent skills from agent manager
        # For now, assume all agents have all skills
        return True
        
    async def _notify_task_assignment(self, task: CollaborativeTask, agent_ids: List[str]):
        """Notify agents about task assignment"""
        # This would send notifications to assigned agents
        logger.info(f"Notifying agents {agent_ids} about task assignment: {task.task_id}")
        
    async def start_task(self, task_id: str, agent_id: str) -> Dict[str, Any]:
        """Start task execution"""
        try:
            if task_id not in self.active_tasks:
                return {'success': False, 'error': 'Task not found'}
                
            task = self.active_tasks[task_id]
            
            # Check if agent is assigned to task
            if agent_id not in task.assigned_to:
                return {'success': False, 'error': 'Agent not assigned to task'}
                
            # Update task status
            if task.status == TaskStatus.ASSIGNED:
                task.status = TaskStatus.IN_PROGRESS
                task.started_at = time.time()
                
                # Create progress update
                await self._create_progress_update(
                    task_id, agent_id, 0.0, TaskStatus.IN_PROGRESS,
                    "Task started"
                )
                
                logger.info(f"✓ Started task {task_id} by agent {agent_id}")
                
                return {
                    'success': True,
                    'status': task.status.value,
                    'started_at': task.started_at
                }
            else:
                return {'success': False, 'error': f'Task in invalid status: {task.status.value}'}
                
        except Exception as e:
            logger.error(f"Error starting task: {e}")
            return {'success': False, 'error': str(e)}
            
    async def update_task_progress(self, task_id: str, agent_id: str, progress_delta: float,
                                 message: str = "", deliverables: Dict[str, Any] = None,
                                 blockers: List[str] = None) -> Dict[str, Any]:
        """Update task progress"""
        try:
            if task_id not in self.active_tasks:
                return {'success': False, 'error': 'Task not found'}
                
            task = self.active_tasks[task_id]
            
            # Check if agent is assigned to task
            if agent_id not in task.assigned_to:
                return {'success': False, 'error': 'Agent not assigned to task'}
                
            # Update progress
            old_progress = task.progress
            task.progress = min(1.0, max(0.0, task.progress + progress_delta))
            
            # Update deliverables
            if deliverables:
                task.deliverables.update(deliverables)
                
            # Create progress update
            await self._create_progress_update(
                task_id, agent_id, progress_delta, None, message,
                deliverables or {}, blockers or []
            )
            
            # Check if task is completed
            if task.progress >= 1.0 and task.status != TaskStatus.COMPLETED:
                await self._complete_task(task_id)
                
            # Check for blockers
            if blockers:
                await self._handle_task_blockers(task_id, blockers)
                
            logger.info(f"✓ Updated progress for task {task_id}: {old_progress:.2f} -> {task.progress:.2f}")
            
            return {
                'success': True,
                'new_progress': task.progress,
                'status': task.status.value
            }
            
        except Exception as e:
            logger.error(f"Error updating task progress: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _create_progress_update(self, task_id: str, agent_id: str, progress_delta: float,
                                    status_change: Optional[TaskStatus], message: str,
                                    deliverables: Dict[str, Any] = None,
                                    blockers: List[str] = None):
        """Create progress update record"""
        update = ProgressUpdate(
            update_id=str(uuid.uuid4()),
            task_id=task_id,
            agent_id=agent_id,
            progress_delta=progress_delta,
            status_change=status_change,
            message=message,
            deliverables=deliverables or {},
            timestamp=time.time(),
            blockers=blockers or []
        )
        
        self.progress_updates[task_id].append(update)
        
    async def _complete_task(self, task_id: str):
        """Complete a task"""
        task = self.active_tasks[task_id]
        task.status = TaskStatus.COMPLETED
        task.completed_at = time.time()
        task.actual_duration = task.completed_at - (task.started_at or task.created_at)
        
        # Remove from agent workloads
        for agent_id in task.assigned_to:
            if task_id in self.agent_workloads[agent_id]:
                self.agent_workloads[agent_id].remove(task_id)
                
        # Move to history
        self.task_history.append(task)
        del self.active_tasks[task_id]
        
        # Check for dependent tasks
        await self._check_dependent_tasks(task_id)
        
        logger.info(f"✓ Completed task {task_id}")
        
    async def _check_dependent_tasks(self, completed_task_id: str):
        """Check and potentially start dependent tasks"""
        for task_id, dependencies in self.task_dependencies.items():
            if completed_task_id in dependencies and task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                if task.status == TaskStatus.PENDING:
                    if await self._check_dependencies_met(task_id):
                        # Auto-assign if possible
                        await self._auto_assign_task(task_id)
                        
    async def _auto_assign_task(self, task_id: str):
        """Automatically assign task based on optimal allocation"""
        task = self.active_tasks[task_id]
        
        # Find best agents for task
        best_agents = await self._find_optimal_agents(task)
        
        if best_agents:
            await self.assign_task(task_id, best_agents, "auto_optimal")
            
    async def _find_optimal_agents(self, task: CollaborativeTask) -> List[str]:
        """Find optimal agents for task assignment"""
        # This would implement sophisticated agent selection
        # For now, return placeholder
        return ["agent_001"]
        
    async def _handle_task_blockers(self, task_id: str, blockers: List[str]):
        """Handle task blockers"""
        task = self.active_tasks[task_id]
        
        if task.status != TaskStatus.BLOCKED:
            task.status = TaskStatus.BLOCKED
            
            # Escalate blockers
            await self._escalate_blockers(task_id, blockers)
            
    async def _escalate_blockers(self, task_id: str, blockers: List[str]):
        """Escalate task blockers"""
        # This would escalate blockers to appropriate authorities
        logger.warning(f"Escalating blockers for task {task_id}: {blockers}")
        
    async def share_knowledge(self, title: str, content: Any, knowledge_type: str,
                            source_agent: str, tags: List[str] = None) -> Dict[str, Any]:
        """Share knowledge item"""
        try:
            knowledge_id = str(uuid.uuid4())
            
            knowledge_item = KnowledgeItem(
                knowledge_id=knowledge_id,
                title=title,
                content=content,
                knowledge_type=knowledge_type,
                source_agent=source_agent,
                created_at=time.time(),
                tags=tags or []
            )
            
            # Store knowledge
            self.knowledge_base[knowledge_id] = knowledge_item
            
            # Index by tags
            for tag in knowledge_item.tags:
                self.knowledge_index[tag].add(knowledge_id)
                
            # Auto-distribute to relevant agents
            await self._distribute_knowledge(knowledge_item)
            
            logger.info(f"✓ Shared knowledge item {knowledge_id}: {title}")
            
            return {
                'success': True,
                'knowledge_id': knowledge_id,
                'tags': knowledge_item.tags
            }
            
        except Exception as e:
            logger.error(f"Error sharing knowledge: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _distribute_knowledge(self, knowledge_item: KnowledgeItem):
        """Distribute knowledge to relevant agents"""
        # This would distribute knowledge based on relevance
        logger.info(f"Distributing knowledge {knowledge_item.knowledge_id} to relevant agents")
        
    async def search_knowledge(self, query: str, knowledge_types: List[str] = None,
                             tags: List[str] = None) -> Dict[str, Any]:
        """Search knowledge base"""
        try:
            matching_items = []
            
            for knowledge_item in self.knowledge_base.values():
                # Filter by type
                if knowledge_types and knowledge_item.knowledge_type not in knowledge_types:
                    continue
                    
                # Filter by tags
                if tags and not any(tag in knowledge_item.tags for tag in tags):
                    continue
                    
                # Simple text matching (would be more sophisticated in practice)
                if query.lower() in knowledge_item.title.lower():
                    matching_items.append({
                        'knowledge_id': knowledge_item.knowledge_id,
                        'title': knowledge_item.title,
                        'knowledge_type': knowledge_item.knowledge_type,
                        'source_agent': knowledge_item.source_agent,
                        'tags': knowledge_item.tags,
                        'relevance_score': knowledge_item.relevance_score
                    })
                    
            # Sort by relevance
            matching_items.sort(key=lambda x: x['relevance_score'], reverse=True)
            
            return {
                'success': True,
                'results': matching_items[:10],  # Top 10 results
                'total_matches': len(matching_items)
            }
            
        except Exception as e:
            logger.error(f"Error searching knowledge: {e}")
            return {'success': False, 'error': str(e)}
            
    async def get_collaboration_analytics(self) -> Dict[str, Any]:
        """Get collaboration analytics"""
        total_tasks = len(self.active_tasks) + len(self.task_history)
        completed_tasks = len(self.task_history)
        
        if total_tasks == 0:
            return {'total_tasks': 0}
            
        # Task completion rate
        completion_rate = completed_tasks / total_tasks
        
        # Average task duration
        completed_durations = [
            task.actual_duration for task in self.task_history
            if task.actual_duration is not None
        ]
        avg_duration = sum(completed_durations) / len(completed_durations) if completed_durations else 0
        
        # Agent workload distribution
        workload_distribution = {
            agent_id: len(task_ids)
            for agent_id, task_ids in self.agent_workloads.items()
        }
        
        # Knowledge sharing metrics
        knowledge_metrics = {
            'total_items': len(self.knowledge_base),
            'knowledge_types': len(set(item.knowledge_type for item in self.knowledge_base.values())),
            'total_tags': len(self.knowledge_index)
        }
        
        return {
            'total_tasks': total_tasks,
            'active_tasks': len(self.active_tasks),
            'completed_tasks': completed_tasks,
            'completion_rate': completion_rate,
            'average_duration_seconds': avg_duration,
            'workload_distribution': workload_distribution,
            'knowledge_metrics': knowledge_metrics
        }

    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get detailed status of a task"""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            progress_updates = self.progress_updates.get(task_id, [])

            return {
                'task_id': task_id,
                'title': task.title,
                'status': task.status.value,
                'progress': task.progress,
                'assigned_to': task.assigned_to,
                'created_at': task.created_at,
                'started_at': task.started_at,
                'estimated_duration': task.estimated_duration,
                'progress_updates': len(progress_updates),
                'deliverables': task.deliverables,
                'collaboration_mode': task.collaboration_mode.value
            }
        else:
            # Check history
            for task in self.task_history:
                if task.task_id == task_id:
                    return {
                        'task_id': task_id,
                        'title': task.title,
                        'status': task.status.value,
                        'progress': task.progress,
                        'completed_at': task.completed_at,
                        'actual_duration': task.actual_duration,
                        'deliverables': task.deliverables
                    }

            return {'task_id': task_id, 'status': 'not_found'}

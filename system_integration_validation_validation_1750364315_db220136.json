{"validation_id": "validation_**********_db220136", "validation_level": "production", "overall_status": "failed", "overall_score": 0.7938599081939869, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "completed", "integration_score": 0.8749433074661118, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "team_manager", "component_type": "core", "status": "completed", "integration_score": 0.8630837794012739, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "completed", "integration_score": 0.8249665741471108, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "analytics_engine", "component_type": "core", "status": "completed", "integration_score": 0.8384235642676529, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "completed", "integration_score": 0.8481659107683366, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "completed", "integration_score": 0.8305486320249288, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "completed", "integration_score": 0.7963234644038549, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "risk_manager", "component_type": "core", "status": "completed", "integration_score": 0.8788667576930121, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "completed", "integration_score": 0.811012656543574, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8141296711856294, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.82901254469075, "error_count": 0, "warnings": ["Integration issues in tournament_framework"], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8114773372613393, "error_count": 0, "warnings": ["Integration issues in self_improvement_engine"], "dependencies_met": "True"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "completed", "integration_score": 0.8116253942602106, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "completed", "integration_score": 0.8292185524769468, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8625969051713364, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "completed", "integration_score": 0.8745391642170827, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "completed", "integration_score": 0.8536139818617261, "error_count": 0, "warnings": ["Integration issues in configuration_manager"], "dependencies_met": "True"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "completed", "integration_score": 0.8748787187460134, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "completed", "integration_score": 0.9069924364928929, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "completed", "integration_score": 0.8198798914330091, "error_count": 0, "warnings": ["Integration issues in logging_audit_system"], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.8571806923984172, "data_manager": 0.8593203727936464, "analytics_engine": 0.8133460943392015, "ollama_hub": 0.657916048752073, "execution_engine": 0.6134795292114426, "portfolio_manager": 0.7977575002945512, "risk_manager": 0.7632327689315974, "strategy_manager": 0.714268063168824, "competitive_framework": 0.742004850412256, "tournament_framework": 0.7138296709760364, "self_improvement_engine": 0.8184613934954573, "regime_adaptation_system": 0.7219854671431523, "performance_optimizer": 0.8087239353019522, "advanced_trading_engine": 0.8759611119498456, "ai_coordinator": 0.8024371040907522, "configuration_manager": 0.8029957458769912, "mock_data_providers": 0.8560980039577952, "paper_trading_engine": 0.6618464739096906, "logging_audit_system": 0.8070571424137496}, "team_manager": {"system_coordinator": 0.7689423923779701, "team_manager": 1.0, "data_manager": 0.8479815572650066, "analytics_engine": 0.6743210756699087, "ollama_hub": 0.7862495984789907, "execution_engine": 0.8746642220346512, "portfolio_manager": 0.7958806349378154, "risk_manager": 0.6496467019919973, "strategy_manager": 0.8619175537627903, "competitive_framework": 0.6850393004435883, "tournament_framework": 0.8869584577698034, "self_improvement_engine": 0.8222991003618851, "regime_adaptation_system": 0.8671154509480193, "performance_optimizer": 0.7100432363915931, "advanced_trading_engine": 0.6324115308264872, "ai_coordinator": 0.6031581520554662, "configuration_manager": 0.771944655668366, "mock_data_providers": 0.8953248888846919, "paper_trading_engine": 0.7580227035045126, "logging_audit_system": 0.8216479475237564}, "data_manager": {"system_coordinator": 0.7344511584932536, "team_manager": 0.7397864982950335, "data_manager": 1.0, "analytics_engine": 0.8723433345830034, "ollama_hub": 0.6929047261134551, "execution_engine": 0.8591020418927606, "portfolio_manager": 0.7497462212537523, "risk_manager": 0.7161436480301279, "strategy_manager": 0.7295024694417636, "competitive_framework": 0.7070501835026447, "tournament_framework": 0.6254679745549111, "self_improvement_engine": 0.7966623195251993, "regime_adaptation_system": 0.7626649536348501, "performance_optimizer": 0.8813325505215142, "advanced_trading_engine": 0.7913907775151245, "ai_coordinator": 0.7231507780339894, "configuration_manager": 0.686451922415914, "mock_data_providers": 0.8855945133136561, "paper_trading_engine": 0.6950951631366141, "logging_audit_system": 0.6617955592109753}, "analytics_engine": {"system_coordinator": 0.8343266182218798, "team_manager": 0.7022161382182617, "data_manager": 0.6736988607119478, "analytics_engine": 1.0, "ollama_hub": 0.6430841777834233, "execution_engine": 0.8452897158542215, "portfolio_manager": 0.6498609847071257, "risk_manager": 0.6595038573818767, "strategy_manager": 0.9564516862999306, "competitive_framework": 0.8778663236188682, "tournament_framework": 0.8325290707890096, "self_improvement_engine": 0.707394191579095, "regime_adaptation_system": 0.613956864026003, "performance_optimizer": 0.7588647957089347, "advanced_trading_engine": 0.7697129729679815, "ai_coordinator": 0.6681264204798371, "configuration_manager": 0.8317993086503396, "mock_data_providers": 0.8775016434587077, "paper_trading_engine": 0.7373956159603751, "logging_audit_system": 0.6682451510626363}, "ollama_hub": {"system_coordinator": 0.7173199294556254, "team_manager": 0.8865272799982162, "data_manager": 0.8261032006905636, "analytics_engine": 0.6456074451855445, "ollama_hub": 1.0, "execution_engine": 0.7276370611429693, "portfolio_manager": 0.8799668895842906, "risk_manager": 0.7162203646162892, "strategy_manager": 0.83190050305509, "competitive_framework": 0.8315980097783606, "tournament_framework": 0.6709290489217664, "self_improvement_engine": 0.8775115837744911, "regime_adaptation_system": 0.6316583290441825, "performance_optimizer": 0.6165448807800098, "advanced_trading_engine": 0.7727968705520285, "ai_coordinator": 0.79531443068415, "configuration_manager": 0.7222770257194155, "mock_data_providers": 0.8964799226845463, "paper_trading_engine": 0.6470080946699192, "logging_audit_system": 0.8862512250428614}, "execution_engine": {"system_coordinator": 0.7474215459253251, "team_manager": 0.7340114227596929, "data_manager": 0.6213340484380142, "analytics_engine": 0.7724489684700498, "ollama_hub": 0.8190322909990047, "execution_engine": 1.0, "portfolio_manager": 0.9731571116493363, "risk_manager": 0.7972475468137206, "strategy_manager": 0.7079794687299955, "competitive_framework": 0.6640593031867572, "tournament_framework": 0.8684720771565226, "self_improvement_engine": 0.8543521521178045, "regime_adaptation_system": 0.8802066011059703, "performance_optimizer": 0.6620256607375773, "advanced_trading_engine": 0.8710938979396391, "ai_coordinator": 0.6234984387466899, "configuration_manager": 0.6446136980951934, "mock_data_providers": 0.7395950874891428, "paper_trading_engine": 0.6237574311678643, "logging_audit_system": 0.7341139571665353}, "portfolio_manager": {"system_coordinator": 0.7932109647818455, "team_manager": 0.6702371228792484, "data_manager": 0.6861049144853172, "analytics_engine": 0.7949289052147077, "ollama_hub": 0.7047086376585604, "execution_engine": 0.6852115512210685, "portfolio_manager": 1.0, "risk_manager": 0.7304307311460811, "strategy_manager": 0.6986375930485302, "competitive_framework": 0.7773507988250583, "tournament_framework": 0.7826734416935436, "self_improvement_engine": 0.7315202948174753, "regime_adaptation_system": 0.7231377150789572, "performance_optimizer": 0.8191123258025768, "advanced_trading_engine": 0.8815695747835048, "ai_coordinator": 0.8856159198442015, "configuration_manager": 0.6642071950072334, "mock_data_providers": 0.7625700317153394, "paper_trading_engine": 0.6968316116558838, "logging_audit_system": 0.7660458458904372}, "risk_manager": {"system_coordinator": 0.68844253575229, "team_manager": 0.6862617163097997, "data_manager": 0.8323677186737047, "analytics_engine": 0.6557350316040502, "ollama_hub": 0.7514592828413125, "execution_engine": 0.8247049013340711, "portfolio_manager": 0.8450953117360578, "risk_manager": 1.0, "strategy_manager": 0.8758909801502446, "competitive_framework": 0.8967630297431277, "tournament_framework": 0.7281378457367602, "self_improvement_engine": 0.7393845324001198, "regime_adaptation_system": 0.8815401984663983, "performance_optimizer": 0.8749008047303969, "advanced_trading_engine": 0.7390752686675789, "ai_coordinator": 0.6629886296643743, "configuration_manager": 0.7374090219323529, "mock_data_providers": 0.6544786780114445, "paper_trading_engine": 0.848661337073683, "logging_audit_system": 0.6631883606425374}, "strategy_manager": {"system_coordinator": 0.7108096834164706, "team_manager": 0.6869922987197892, "data_manager": 0.7546937030749954, "analytics_engine": 0.8768108827581476, "ollama_hub": 0.8317710555502601, "execution_engine": 0.6488832962420196, "portfolio_manager": 0.7671491996933145, "risk_manager": 0.857043573298689, "strategy_manager": 1.0, "competitive_framework": 0.8378547511674502, "tournament_framework": 0.6945041092740615, "self_improvement_engine": 0.6190616582690517, "regime_adaptation_system": 0.6073160790027217, "performance_optimizer": 0.8400094654361281, "advanced_trading_engine": 0.7658690623937984, "ai_coordinator": 0.7416728819430838, "configuration_manager": 0.696808818624307, "mock_data_providers": 0.7641238788204793, "paper_trading_engine": 0.7148726351090571, "logging_audit_system": 0.65461847225095}, "competitive_framework": {"system_coordinator": 0.6304302500797946, "team_manager": 0.7308119961551958, "data_manager": 0.7966205884650108, "analytics_engine": 0.7401860366327409, "ollama_hub": 0.6003582557101426, "execution_engine": 0.6874356056456502, "portfolio_manager": 0.6350792328888352, "risk_manager": 0.7805632603806447, "strategy_manager": 0.6190069498960109, "competitive_framework": 1.0, "tournament_framework": 0.8079240878856555, "self_improvement_engine": 0.6042179774636017, "regime_adaptation_system": 0.6034663653159512, "performance_optimizer": 0.6427375062848878, "advanced_trading_engine": 0.6662606293812869, "ai_coordinator": 0.6983983243208081, "configuration_manager": 0.8819974101258068, "mock_data_providers": 0.7697302488313605, "paper_trading_engine": 0.6281550017708573, "logging_audit_system": 0.6282511462481346}, "tournament_framework": {"system_coordinator": 0.6969913674677839, "team_manager": 0.7940873631148928, "data_manager": 0.8431043931839467, "analytics_engine": 0.7467482030755984, "ollama_hub": 0.754017492407794, "execution_engine": 0.7103611431787774, "portfolio_manager": 0.6797001110626884, "risk_manager": 0.8630566767338378, "strategy_manager": 0.7982021412124797, "competitive_framework": 0.6502894245914472, "tournament_framework": 1.0, "self_improvement_engine": 0.8575608425578968, "regime_adaptation_system": 0.8582433501574432, "performance_optimizer": 0.7591422286733076, "advanced_trading_engine": 0.8670943996960172, "ai_coordinator": 0.7139640496322884, "configuration_manager": 0.8641330272856701, "mock_data_providers": 0.6117325649066252, "paper_trading_engine": 0.8663250770020735, "logging_audit_system": 0.6145099089020145}, "self_improvement_engine": {"system_coordinator": 0.8438533850315585, "team_manager": 0.800897597401317, "data_manager": 0.6286216751218487, "analytics_engine": 0.8213048095094566, "ollama_hub": 0.6669744848891649, "execution_engine": 0.630832780381105, "portfolio_manager": 0.7491012716162649, "risk_manager": 0.8014920549122563, "strategy_manager": 0.886558584634634, "competitive_framework": 0.7094849391773936, "tournament_framework": 0.815946627056627, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.8485763649430309, "performance_optimizer": 0.7654106467763807, "advanced_trading_engine": 0.8617445315520065, "ai_coordinator": 0.6004611776701264, "configuration_manager": 0.7546068970135033, "mock_data_providers": 0.8125867716954469, "paper_trading_engine": 0.69647649290148, "logging_audit_system": 0.6935890327509613}, "regime_adaptation_system": {"system_coordinator": 0.6244159510160464, "team_manager": 0.8672615462066836, "data_manager": 0.802323311866358, "analytics_engine": 0.894162170030663, "ollama_hub": 0.7385748628186748, "execution_engine": 0.7552281440291775, "portfolio_manager": 0.747319990496141, "risk_manager": 0.8902119687072658, "strategy_manager": 0.6782412253247656, "competitive_framework": 0.807870188086631, "tournament_framework": 0.8584051428425157, "self_improvement_engine": 0.7068229874340024, "regime_adaptation_system": 1.0, "performance_optimizer": 0.6343186793010152, "advanced_trading_engine": 0.6132013031007834, "ai_coordinator": 0.8914487111345192, "configuration_manager": 0.8752669019423034, "mock_data_providers": 0.7475185694494063, "paper_trading_engine": 0.7914651911734332, "logging_audit_system": 0.6066759211180645}, "performance_optimizer": {"system_coordinator": 0.7119085562676171, "team_manager": 0.7903395602394558, "data_manager": 0.7422742922123757, "analytics_engine": 0.7287581132148685, "ollama_hub": 0.6991867931024949, "execution_engine": 0.6587816799016749, "portfolio_manager": 0.8116753596487972, "risk_manager": 0.8622503948746445, "strategy_manager": 0.8744816310541358, "competitive_framework": 0.6385347549354892, "tournament_framework": 0.7889123833385444, "self_improvement_engine": 0.7254949156124688, "regime_adaptation_system": 0.6111848498957898, "performance_optimizer": 1.0, "advanced_trading_engine": 0.6831336344429713, "ai_coordinator": 0.8129785061691025, "configuration_manager": 0.7870850087378228, "mock_data_providers": 0.7028996247150484, "paper_trading_engine": 0.7012872477507407, "logging_audit_system": 0.6217157501218382}, "advanced_trading_engine": {"system_coordinator": 0.7872170765689883, "team_manager": 0.8683088472332106, "data_manager": 0.7292239119672461, "analytics_engine": 0.7279885275427342, "ollama_hub": 0.8666407217355221, "execution_engine": 0.7478089043338652, "portfolio_manager": 0.7116188545031945, "risk_manager": 0.7969033853930906, "strategy_manager": 0.6192894434758753, "competitive_framework": 0.7217114667158788, "tournament_framework": 0.6301740917457902, "self_improvement_engine": 0.7497296125263704, "regime_adaptation_system": 0.6732106722947135, "performance_optimizer": 0.6592859012159696, "advanced_trading_engine": 1.0, "ai_coordinator": 0.8936420635904183, "configuration_manager": 0.8589145727919635, "mock_data_providers": 0.6064880221541339, "paper_trading_engine": 0.7894314336422454, "logging_audit_system": 0.8472966057588249}, "ai_coordinator": {"system_coordinator": 0.871727093976818, "team_manager": 0.749288842741709, "data_manager": 0.8531420286611893, "analytics_engine": 0.8914603123931372, "ollama_hub": 0.828537984647566, "execution_engine": 0.8801474220616579, "portfolio_manager": 0.6257918698600651, "risk_manager": 0.8506221252423953, "strategy_manager": 0.6915266761146311, "competitive_framework": 0.6935055394845719, "tournament_framework": 0.658578125823857, "self_improvement_engine": 0.7485159378580629, "regime_adaptation_system": 0.6738183300891213, "performance_optimizer": 0.6053344127647349, "advanced_trading_engine": 0.6750791371835172, "ai_coordinator": 1.0, "configuration_manager": 0.6289257105061518, "mock_data_providers": 0.7381949334141725, "paper_trading_engine": 0.830097894165505, "logging_audit_system": 0.790722883181615}, "configuration_manager": {"system_coordinator": 0.661314517508221, "team_manager": 0.6569228166705481, "data_manager": 0.8785602186594763, "analytics_engine": 0.738058730230191, "ollama_hub": 0.860655842993646, "execution_engine": 0.8955500615833575, "portfolio_manager": 0.7900195679977249, "risk_manager": 0.62053121730011, "strategy_manager": 0.7096892396465079, "competitive_framework": 0.6078083378531941, "tournament_framework": 0.6890059282359021, "self_improvement_engine": 0.8724858017318262, "regime_adaptation_system": 0.7859291311099758, "performance_optimizer": 0.8221606033245807, "advanced_trading_engine": 0.6676824007588166, "ai_coordinator": 0.6763235913495655, "configuration_manager": 1.0, "mock_data_providers": 0.8997198564864439, "paper_trading_engine": 0.6998151966391647, "logging_audit_system": 0.8009152838783105}, "mock_data_providers": {"system_coordinator": 0.8465195177519055, "team_manager": 0.8300025278316772, "data_manager": 0.6628179853922799, "analytics_engine": 0.8825464125696133, "ollama_hub": 0.6194940604508509, "execution_engine": 0.7846507119890249, "portfolio_manager": 0.7549934702452927, "risk_manager": 0.6236375657246217, "strategy_manager": 0.6276631467583631, "competitive_framework": 0.8396019989564496, "tournament_framework": 0.6957401014507133, "self_improvement_engine": 0.657868000238303, "regime_adaptation_system": 0.7628669093133806, "performance_optimizer": 0.8528899020010834, "advanced_trading_engine": 0.6723063427918241, "ai_coordinator": 0.6713669821874231, "configuration_manager": 0.623805939562515, "mock_data_providers": 1.0, "paper_trading_engine": 0.7292483243772516, "logging_audit_system": 0.6075837556587883}, "paper_trading_engine": {"system_coordinator": 0.7808116618189371, "team_manager": 0.7110189598337978, "data_manager": 0.8415167472145713, "analytics_engine": 0.8028122250585173, "ollama_hub": 0.8342261390423176, "execution_engine": 0.8018449146070576, "portfolio_manager": 0.8966844545144748, "risk_manager": 0.8974221869673602, "strategy_manager": 0.8510059274142854, "competitive_framework": 0.8824386060033045, "tournament_framework": 0.7839718962938429, "self_improvement_engine": 0.6471675003011815, "regime_adaptation_system": 0.6659141447823458, "performance_optimizer": 0.6763163890655621, "advanced_trading_engine": 0.724448714730052, "ai_coordinator": 0.615098063976133, "configuration_manager": 0.7262106879634759, "mock_data_providers": 0.7426174626858102, "paper_trading_engine": 1.0, "logging_audit_system": 0.8970880629756451}, "logging_audit_system": {"system_coordinator": 0.6776764160243285, "team_manager": 0.8233803474125418, "data_manager": 0.8383736700897784, "analytics_engine": 0.7330619392672737, "ollama_hub": 0.7282921732737935, "execution_engine": 0.6155079835159737, "portfolio_manager": 0.645223169334425, "risk_manager": 0.6717118727412791, "strategy_manager": 0.7766350914272151, "competitive_framework": 0.7949523843715837, "tournament_framework": 0.7853183988325393, "self_improvement_engine": 0.7963688213505438, "regime_adaptation_system": 0.6752197642898368, "performance_optimizer": 0.837799402278289, "advanced_trading_engine": 0.8124766956270388, "ai_coordinator": 0.844659717147395, "configuration_manager": 0.8634623921259703, "mock_data_providers": 0.8934164350991891, "paper_trading_engine": 0.6183194132514493, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.7176373445076061, "response_time": 0.8633977991532059, "throughput": 0.83933537515537, "memory_usage": 0.754907012723976, "cpu_usage": 0.8458637909955502, "concurrent_operations": 0.682428670421626}, "critical_issues": ["Components with dependency issues: strategy_manager"], "recommendations": ["Improve overall system performance and stability", "Enhance component integration and communication", "Address all issues before production deployment"], "production_ready": "True", "timestamp": **********.75009}
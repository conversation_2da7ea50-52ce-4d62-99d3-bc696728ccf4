
// Advanced Ollama Trading Agent System - Main JavaScript

class TradingSystemAPI {
    constructor(baseUrl = '/api/v1') {
        this.baseUrl = baseUrl;
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    // System endpoints
    async getSystemStatus() {
        return this.request('/system/status');
    }

    async getComponents() {
        return this.request('/system/components');
    }

    async executeCommand(command, parameters = {}) {
        return this.request('/system/commands', {
            method: 'POST',
            body: JSON.stringify({ command, parameters })
        });
    }

    // Agent endpoints
    async getAgents() {
        return this.request('/agents/');
    }

    async getAgent(agentId) {
        return this.request(`/agents/${agentId}`);
    }

    // Portfolio endpoints
    async getPortfolios() {
        return this.request('/portfolio/');
    }

    // Analytics endpoints
    async getMarketInsights(symbol = null) {
        const params = symbol ? `?symbol=${symbol}` : '';
        return this.request(`/analytics/insights${params}`);
    }
}

class WebSocketManager {
    constructor(baseUrl = '') {
        this.baseUrl = baseUrl.replace('http', 'ws');
        this.connections = new Map();
        this.reconnectAttempts = new Map();
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
    }

    connect(endpoint, onMessage, onError = null) {
        const url = `${this.baseUrl}/ws/${endpoint}`;
        
        try {
            const ws = new WebSocket(url);
            
            ws.onopen = () => {
                console.log(`WebSocket connected: ${endpoint}`);
                this.reconnectAttempts.set(endpoint, 0);
            };
            
            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    onMessage(data);
                } catch (error) {
                    console.error('Failed to parse WebSocket message:', error);
                }
            };
            
            ws.onclose = () => {
                console.log(`WebSocket disconnected: ${endpoint}`);
                this.connections.delete(endpoint);
                this.attemptReconnect(endpoint, onMessage, onError);
            };
            
            ws.onerror = (error) => {
                console.error(`WebSocket error on ${endpoint}:`, error);
                if (onError) onError(error);
            };
            
            this.connections.set(endpoint, ws);
            return ws;
            
        } catch (error) {
            console.error(`Failed to connect WebSocket ${endpoint}:`, error);
            if (onError) onError(error);
        }
    }

    attemptReconnect(endpoint, onMessage, onError) {
        const attempts = this.reconnectAttempts.get(endpoint) || 0;
        
        if (attempts < this.maxReconnectAttempts) {
            this.reconnectAttempts.set(endpoint, attempts + 1);
            
            setTimeout(() => {
                console.log(`Attempting to reconnect WebSocket ${endpoint} (${attempts + 1}/${this.maxReconnectAttempts})`);
                this.connect(endpoint, onMessage, onError);
            }, this.reconnectDelay * Math.pow(2, attempts));
        } else {
            console.error(`Max reconnection attempts reached for ${endpoint}`);
        }
    }

    send(endpoint, message) {
        const ws = this.connections.get(endpoint);
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(message));
        } else {
            console.warn(`WebSocket ${endpoint} not connected`);
        }
    }

    disconnect(endpoint) {
        const ws = this.connections.get(endpoint);
        if (ws) {
            ws.close();
            this.connections.delete(endpoint);
        }
    }

    disconnectAll() {
        for (const [endpoint, ws] of this.connections) {
            ws.close();
        }
        this.connections.clear();
    }
}

// Utility functions
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

function formatPercentage(value, decimals = 2) {
    return `${(value * 100).toFixed(decimals)}%`;
}

function formatDateTime(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(date));
}

function showLoading(element) {
    element.innerHTML = '<div class="loading"><div class="spinner"></div>Loading...</div>';
}

function showError(element, message) {
    element.innerHTML = `<div class="error">Error: ${message}</div>`;
}

// Global instances
const api = new TradingSystemAPI();
const wsManager = new WebSocketManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('Trading System Web Interface loaded');
    
    // Set active navigation
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav a');
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    wsManager.disconnectAll();
});

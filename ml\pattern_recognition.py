"""
ML Pattern Recognition - Advanced pattern detection using machine learning
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from sklearn.cluster import KMeans, DBSCAN
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.ensemble import IsolationForest
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class MLPatternRecognition:
    """
    Machine Learning-based pattern recognition for financial markets.
    Detects complex patterns using unsupervised and supervised ML techniques.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Pattern detection models
        self.clustering_models: Dict[str, Any] = {}
        self.anomaly_detectors: Dict[str, Any] = {}
        self.pattern_classifiers: Dict[str, Any] = {}
        
        # Pattern storage
        self.detected_patterns: Dict[str, List[Dict[str, Any]]] = {}
        self.pattern_templates: Dict[str, np.ndarray] = {}
        
        # Feature extractors
        self.scalers: Dict[str, StandardScaler] = {}
        
        # State
        self.initialized = False
        
    async def initialize(self):
        """Initialize ML pattern recognition"""
        if self.initialized:
            return
            
        logger.info("Initializing ML Pattern Recognition...")
        
        # Initialize pattern detection models
        await self._setup_pattern_models()
        
        # Initialize pattern templates
        await self._setup_pattern_templates()
        
        self.initialized = True
        logger.info("✓ ML Pattern Recognition initialized")
        
    async def _setup_pattern_models(self):
        """Setup pattern detection models"""
        # Clustering models for pattern discovery
        self.clustering_models = {
            'kmeans': KMeans(n_clusters=8, random_state=42),
            'dbscan': DBSCAN(eps=0.5, min_samples=5)
        }
        
        # Anomaly detection models
        self.anomaly_detectors = {
            'isolation_forest': IsolationForest(contamination=0.1, random_state=42)
        }
        
    async def _setup_pattern_templates(self):
        """Setup known pattern templates"""
        # Define common chart patterns as templates
        self.pattern_templates = {
            'head_and_shoulders': self._create_head_shoulders_template(),
            'double_top': self._create_double_top_template(),
            'double_bottom': self._create_double_bottom_template(),
            'triangle': self._create_triangle_template(),
            'flag': self._create_flag_template(),
            'wedge': self._create_wedge_template()
        }
        
    def _create_head_shoulders_template(self) -> np.ndarray:
        """Create head and shoulders pattern template"""
        # Simplified template - in practice would be more sophisticated
        return np.array([0.0, 0.3, 0.1, 0.5, 0.2, 0.4, 0.0])
        
    def _create_double_top_template(self) -> np.ndarray:
        """Create double top pattern template"""
        return np.array([0.0, 0.4, 0.1, 0.4, 0.0])
        
    def _create_double_bottom_template(self) -> np.ndarray:
        """Create double bottom pattern template"""
        return np.array([1.0, 0.6, 0.9, 0.6, 1.0])
        
    def _create_triangle_template(self) -> np.ndarray:
        """Create triangle pattern template"""
        return np.array([0.0, 0.2, 0.1, 0.15, 0.05, 0.1, 0.0])
        
    def _create_flag_template(self) -> np.ndarray:
        """Create flag pattern template"""
        return np.array([0.0, 0.5, 0.45, 0.4, 0.35, 0.3, 0.6])
        
    def _create_wedge_template(self) -> np.ndarray:
        """Create wedge pattern template"""
        return np.array([0.0, 0.1, 0.15, 0.2, 0.22, 0.23, 0.24])
        
    async def detect_patterns(self, symbol: str, data: pd.DataFrame, 
                            pattern_types: List[str] = None) -> Dict[str, Any]:
        """Detect patterns in price data"""
        if pattern_types is None:
            pattern_types = list(self.pattern_templates.keys())
            
        try:
            # Prepare features for pattern detection
            features = await self._extract_pattern_features(data)
            
            if len(features) < 10:
                return {'success': False, 'error': 'Insufficient data for pattern detection'}
                
            detected_patterns = []
            
            # Template matching for known patterns
            for pattern_type in pattern_types:
                if pattern_type in self.pattern_templates:
                    matches = await self._template_matching(features, pattern_type)
                    detected_patterns.extend(matches)
                    
            # Clustering-based pattern discovery
            cluster_patterns = await self._clustering_pattern_detection(features)
            detected_patterns.extend(cluster_patterns)
            
            # Anomaly-based pattern detection
            anomaly_patterns = await self._anomaly_pattern_detection(features)
            detected_patterns.extend(anomaly_patterns)
            
            # Store detected patterns
            if symbol not in self.detected_patterns:
                self.detected_patterns[symbol] = []
                
            self.detected_patterns[symbol].extend(detected_patterns)
            
            return {
                'success': True,
                'symbol': symbol,
                'patterns_detected': len(detected_patterns),
                'patterns': detected_patterns
            }
            
        except Exception as e:
            logger.error(f"Error detecting patterns for {symbol}: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _extract_pattern_features(self, data: pd.DataFrame) -> np.ndarray:
        """Extract features for pattern recognition"""
        # Normalize prices to 0-1 range for pattern matching
        prices = data['close'].values
        normalized_prices = (prices - prices.min()) / (prices.max() - prices.min())
        
        # Create sliding windows for pattern detection
        window_size = 20
        features = []
        
        for i in range(len(normalized_prices) - window_size + 1):
            window = normalized_prices[i:i + window_size]
            
            # Basic features
            feature_vector = [
                window.mean(),
                window.std(),
                window.max() - window.min(),
                np.argmax(window) / len(window),  # Position of max
                np.argmin(window) / len(window),  # Position of min
            ]
            
            # Add the actual price pattern
            feature_vector.extend(window.tolist())
            features.append(feature_vector)
            
        return np.array(features)
        
    async def _template_matching(self, features: np.ndarray, pattern_type: str) -> List[Dict[str, Any]]:
        """Match features against pattern templates"""
        template = self.pattern_templates[pattern_type]
        matches = []
        
        # Extract price patterns from features (last part of feature vector)
        price_patterns = features[:, -20:]  # Assuming 20-point patterns
        
        for i, pattern in enumerate(price_patterns):
            # Resize pattern to match template length
            if len(pattern) != len(template):
                # Simple interpolation
                pattern_resized = np.interp(
                    np.linspace(0, 1, len(template)),
                    np.linspace(0, 1, len(pattern)),
                    pattern
                )
            else:
                pattern_resized = pattern
                
            # Calculate similarity (correlation)
            correlation = np.corrcoef(pattern_resized, template)[0, 1]
            
            # Threshold for pattern match
            if correlation > 0.7:  # 70% correlation threshold
                matches.append({
                    'pattern_type': pattern_type,
                    'detection_method': 'template_matching',
                    'position': i,
                    'confidence': correlation,
                    'pattern_data': pattern.tolist()
                })
                
        return matches
        
    async def _clustering_pattern_detection(self, features: np.ndarray) -> List[Dict[str, Any]]:
        """Detect patterns using clustering"""
        patterns = []
        
        try:
            # Scale features
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features)
            
            # Apply K-means clustering
            kmeans = self.clustering_models['kmeans']
            cluster_labels = kmeans.fit_predict(features_scaled)
            
            # Identify interesting clusters (patterns)
            unique_labels, counts = np.unique(cluster_labels, return_counts=True)
            
            for label, count in zip(unique_labels, counts):
                if count >= 3:  # At least 3 occurrences
                    cluster_indices = np.where(cluster_labels == label)[0]
                    
                    # Calculate cluster characteristics
                    cluster_features = features[cluster_indices]
                    centroid = cluster_features.mean(axis=0)
                    
                    patterns.append({
                        'pattern_type': f'cluster_{label}',
                        'detection_method': 'clustering',
                        'occurrences': count,
                        'positions': cluster_indices.tolist(),
                        'confidence': min(count / len(features), 1.0),
                        'centroid': centroid.tolist()
                    })
                    
        except Exception as e:
            logger.warning(f"Error in clustering pattern detection: {e}")
            
        return patterns
        
    async def _anomaly_pattern_detection(self, features: np.ndarray) -> List[Dict[str, Any]]:
        """Detect anomalous patterns"""
        patterns = []
        
        try:
            # Scale features
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features)
            
            # Apply isolation forest
            iso_forest = self.anomaly_detectors['isolation_forest']
            anomaly_labels = iso_forest.fit_predict(features_scaled)
            
            # Find anomalies
            anomaly_indices = np.where(anomaly_labels == -1)[0]
            
            for idx in anomaly_indices:
                # Calculate anomaly score
                anomaly_score = iso_forest.decision_function(features_scaled[idx:idx+1])[0]
                
                patterns.append({
                    'pattern_type': 'anomaly',
                    'detection_method': 'isolation_forest',
                    'position': idx,
                    'confidence': abs(anomaly_score),
                    'anomaly_score': anomaly_score,
                    'pattern_data': features[idx].tolist()
                })
                
        except Exception as e:
            logger.warning(f"Error in anomaly pattern detection: {e}")
            
        return patterns
        
    async def classify_pattern(self, pattern_data: np.ndarray) -> Dict[str, Any]:
        """Classify a given pattern"""
        try:
            # Compare against all templates
            similarities = {}
            
            for pattern_type, template in self.pattern_templates.items():
                # Resize pattern to match template
                if len(pattern_data) != len(template):
                    pattern_resized = np.interp(
                        np.linspace(0, 1, len(template)),
                        np.linspace(0, 1, len(pattern_data)),
                        pattern_data
                    )
                else:
                    pattern_resized = pattern_data
                    
                # Calculate similarity
                correlation = np.corrcoef(pattern_resized, template)[0, 1]
                similarities[pattern_type] = correlation
                
            # Find best match
            best_match = max(similarities, key=similarities.get)
            best_score = similarities[best_match]
            
            return {
                'success': True,
                'best_match': best_match,
                'confidence': best_score,
                'all_similarities': similarities
            }
            
        except Exception as e:
            logger.error(f"Error classifying pattern: {e}")
            return {'success': False, 'error': str(e)}
            
    async def get_pattern_summary(self, symbol: str = None) -> Dict[str, Any]:
        """Get summary of detected patterns"""
        if symbol:
            patterns = self.detected_patterns.get(symbol, [])
            
            # Summarize patterns by type
            pattern_counts = {}
            for pattern in patterns:
                pattern_type = pattern['pattern_type']
                pattern_counts[pattern_type] = pattern_counts.get(pattern_type, 0) + 1
                
            return {
                'symbol': symbol,
                'total_patterns': len(patterns),
                'pattern_counts': pattern_counts,
                'recent_patterns': patterns[-5:]  # Last 5 patterns
            }
        else:
            # Overall summary
            total_patterns = sum(len(patterns) for patterns in self.detected_patterns.values())
            symbols_analyzed = len(self.detected_patterns)
            
            return {
                'total_patterns': total_patterns,
                'symbols_analyzed': symbols_analyzed,
                'available_templates': list(self.pattern_templates.keys()),
                'detection_methods': ['template_matching', 'clustering', 'isolation_forest']
            }
            
    async def train_custom_pattern(self, pattern_name: str, training_data: List[np.ndarray]) -> Dict[str, Any]:
        """Train a custom pattern template"""
        try:
            if len(training_data) < 3:
                return {'success': False, 'error': 'Need at least 3 examples for training'}
                
            # Normalize all patterns to same length
            target_length = 20
            normalized_patterns = []
            
            for pattern in training_data:
                normalized = np.interp(
                    np.linspace(0, 1, target_length),
                    np.linspace(0, 1, len(pattern)),
                    pattern
                )
                normalized_patterns.append(normalized)
                
            # Create template as average of normalized patterns
            template = np.mean(normalized_patterns, axis=0)
            
            # Store template
            self.pattern_templates[pattern_name] = template
            
            # Calculate template quality (consistency)
            variations = [np.corrcoef(pattern, template)[0, 1] for pattern in normalized_patterns]
            quality = np.mean(variations)
            
            return {
                'success': True,
                'pattern_name': pattern_name,
                'template_quality': quality,
                'training_examples': len(training_data)
            }
            
        except Exception as e:
            logger.error(f"Error training custom pattern: {e}")
            return {'success': False, 'error': str(e)}

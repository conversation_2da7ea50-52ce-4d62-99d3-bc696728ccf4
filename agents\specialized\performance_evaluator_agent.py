"""
Performance Evaluator Agent - Performance analysis and optimization feedback
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
import json
import statistics

from ..base_agent import BaseAgent, AgentRole, AgentState
from communication.message_types import MessageType

logger = logging.getLogger(__name__)


class PerformanceEvaluatorAgent(BaseAgent):
    """
    Performance Evaluator Agent specializing in performance analysis and optimization feedback.
    
    Responsibilities:
    - Performance attribution and analysis
    - Benchmarking and comparison
    - Optimization recommendations
    - Learning feedback and insights
    - Risk-adjusted performance metrics
    - Strategy effectiveness evaluation
    - Agent performance assessment
    - System-wide performance monitoring
    
    Uses: granite3.3:8b (primary), marco-o1:7b (fallback)
    """
    
    def __init__(self, agent_id: str = None, name: str = None, config: Dict[str, Any] = None):
        super().__init__(
            agent_id=agent_id,
            name=name or "PerformanceEvaluator",
            role=AgentRole.PERFORMANCE_EVALUATOR,
            config=config
        )
        
        # Performance tracking state
        self.performance_data: Dict[str, List[Dict[str, Any]]] = {}
        self.benchmarks: Dict[str, Dict[str, Any]] = {}
        self.evaluation_reports: Dict[str, Dict[str, Any]] = {}
        
        # Analysis frameworks
        self.attribution_models: Dict[str, Dict[str, Any]] = {}
        self.performance_metrics: Dict[str, List[str]] = {}
        
        # Learning and feedback
        self.learning_insights: List[Dict[str, Any]] = []
        self.optimization_recommendations: Dict[str, List[Dict[str, Any]]] = {}
        
    async def _initialize_agent(self):
        """Performance Evaluator specific initialization"""
        logger.info(f"Initializing Performance Evaluator Agent: {self.name}")
        
        # Initialize performance frameworks
        await self._setup_performance_frameworks()
        
        # Initialize benchmarking system
        await self._setup_benchmarking()
        
        # Initialize learning systems
        await self._setup_learning_systems()
        
        logger.info(f"✓ Performance Evaluator Agent {self.name} initialized")
        
    async def _setup_performance_frameworks(self):
        """Setup performance evaluation frameworks"""
        self.performance_frameworks = {
            'return_metrics': {
                'absolute_returns': ['total_return', 'annualized_return', 'compound_return'],
                'risk_adjusted': ['sharpe_ratio', 'sortino_ratio', 'calmar_ratio', 'information_ratio'],
                'downside_metrics': ['max_drawdown', 'downside_deviation', 'var', 'cvar']
            },
            'attribution_models': {
                'factor_attribution': ['market_factor', 'size_factor', 'value_factor', 'momentum_factor'],
                'sector_attribution': ['sector_allocation', 'sector_selection', 'interaction_effect'],
                'strategy_attribution': ['alpha_generation', 'beta_exposure', 'residual_return']
            },
            'efficiency_metrics': {
                'execution_quality': ['implementation_shortfall', 'slippage', 'fill_rate'],
                'timing_metrics': ['market_timing', 'volatility_timing', 'sector_timing'],
                'cost_metrics': ['transaction_costs', 'management_fees', 'opportunity_costs']
            }
        }
        
        # Store frameworks in memory
        await self.memory.store_experience({
            'type': 'initialization',
            'component': 'performance_frameworks',
            'frameworks': self.performance_frameworks,
            'timestamp': time.time()
        })
        
    async def _setup_benchmarking(self):
        """Setup benchmarking system"""
        self.benchmarking_system = {
            'benchmark_types': {
                'market_benchmarks': ['sp500', 'nasdaq', 'russell2000', 'msci_world'],
                'strategy_benchmarks': ['long_short_equity', 'market_neutral', 'momentum'],
                'risk_benchmarks': ['risk_parity', 'min_variance', 'equal_weight']
            },
            'comparison_methods': {
                'absolute_comparison': 'Direct return comparison',
                'risk_adjusted_comparison': 'Sharpe ratio and other risk metrics',
                'rolling_comparison': 'Time-varying performance analysis',
                'regime_comparison': 'Performance across market regimes'
            },
            'statistical_tests': {
                'significance_tests': ['t_test', 'bootstrap', 'monte_carlo'],
                'performance_persistence': ['autocorrelation', 'regime_analysis'],
                'benchmark_validity': ['tracking_error', 'correlation_analysis']
            }
        }
        
        # Store benchmarking system
        await self.memory.store_experience({
            'type': 'initialization',
            'component': 'benchmarking_system',
            'system': self.benchmarking_system,
            'timestamp': time.time()
        })
        
    async def _setup_learning_systems(self):
        """Setup learning and feedback systems"""
        self.learning_systems = {
            'feedback_loops': {
                'performance_feedback': 'Strategy performance to strategy development',
                'execution_feedback': 'Execution quality to execution optimization',
                'risk_feedback': 'Risk outcomes to risk management',
                'market_feedback': 'Market analysis accuracy to analysis improvement'
            },
            'learning_mechanisms': {
                'pattern_recognition': 'Identify successful patterns and strategies',
                'failure_analysis': 'Analyze failures and extract lessons',
                'adaptation_triggers': 'Identify when to adapt strategies',
                'optimization_opportunities': 'Find improvement opportunities'
            },
            'knowledge_integration': {
                'cross_strategy_learning': 'Share insights across strategies',
                'temporal_learning': 'Learn from historical patterns',
                'regime_learning': 'Adapt to different market regimes',
                'collaborative_learning': 'Learn from other agents'
            }
        }
        
    def _register_role_specific_handlers(self):
        """Register Performance Evaluator specific message handlers"""
        self.task_handlers.update({
            'evaluate_performance': self._handle_evaluate_performance,
            'attribution_analysis': self._handle_attribution_analysis,
            'benchmark_comparison': self._handle_benchmark_comparison,
            'optimization_recommendations': self._handle_optimization_recommendations,
            'learning_feedback': self._handle_learning_feedback,
            'agent_performance_review': self._handle_agent_performance_review,
            'strategy_effectiveness': self._handle_strategy_effectiveness,
            'system_performance_audit': self._handle_system_performance_audit
        })
        
    async def _idle_activities(self):
        """Activities when idle - continuous performance monitoring"""
        # Monitor ongoing performance
        await self._monitor_performance()
        
        # Generate learning insights
        await self._generate_learning_insights()
        
        # Update benchmarks
        await self._update_benchmarks()
        
    async def _handle_evaluate_performance(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle performance evaluation requests"""
        try:
            entity_type = task.get('entity_type', 'strategy')  # strategy, agent, portfolio
            entity_id = task.get('entity_id')
            performance_data = task.get('performance_data', {})
            evaluation_period = task.get('period', '1M')
            
            if not entity_id:
                return {
                    'success': False,
                    'error': 'Entity ID is required for performance evaluation'
                }
                
            # Performance evaluation prompt
            evaluation_prompt = f"""
            Perform comprehensive performance evaluation:
            
            Entity Type: {entity_type}
            Entity ID: {entity_id}
            Evaluation Period: {evaluation_period}
            Performance Data: {json.dumps(performance_data, indent=2)}
            
            Conduct detailed performance analysis:
            
            1. Return Analysis:
               - Total return and annualized return
               - Risk-adjusted returns (Sharpe, Sortino, Calmar ratios)
               - Return distribution analysis
               - Consistency metrics
               - Compound annual growth rate (CAGR)
            
            2. Risk Analysis:
               - Volatility and downside deviation
               - Maximum drawdown and recovery analysis
               - Value at Risk (VaR) and Expected Shortfall
               - Beta and correlation analysis
               - Tail risk assessment
            
            3. Performance Attribution:
               - Alpha and beta decomposition
               - Factor attribution analysis
               - Sector/style attribution
               - Timing vs. selection contribution
               - Residual return analysis
            
            4. Efficiency Metrics:
               - Information ratio and tracking error
               - Hit rate and win/loss ratio
               - Profit factor and expectancy
               - Trade efficiency metrics
               - Cost-adjusted performance
            
            5. Comparative Analysis:
               - Benchmark comparison
               - Peer group analysis
               - Historical performance context
               - Market regime performance
               - Relative strength analysis
            
            6. Performance Quality Assessment:
               - Consistency of returns
               - Performance persistence
               - Skill vs. luck analysis
               - Statistical significance
               - Robustness testing
            
            7. Improvement Opportunities:
               - Performance gaps identification
               - Optimization recommendations
               - Risk management improvements
               - Strategy refinements
               - Execution enhancements
            
            Provide comprehensive evaluation with actionable insights.
            Format as JSON with detailed metrics and recommendations.
            """
            
            result = await self.model_instance.generate(evaluation_prompt, temperature=0.4)
            
            if result['success']:
                try:
                    evaluation = json.loads(result['response'])
                    
                    # Store evaluation results
                    evaluation_id = f"eval_{entity_type}_{entity_id}_{int(time.time())}"
                    self.evaluation_reports[evaluation_id] = {
                        'entity_type': entity_type,
                        'entity_id': entity_id,
                        'evaluation': evaluation,
                        'period': evaluation_period,
                        'timestamp': time.time()
                    }
                    
                    # Extract learning insights
                    insights = await self._extract_learning_insights(evaluation)
                    
                    # Store in memory
                    await self.memory.store_analysis({
                        'type': 'performance_evaluation',
                        'entity_type': entity_type,
                        'entity_id': entity_id,
                        'evaluation_id': evaluation_id,
                        'evaluation': evaluation,
                        'insights': insights,
                        'timestamp': time.time()
                    })
                    
                    return {
                        'success': True,
                        'entity_type': entity_type,
                        'entity_id': entity_id,
                        'evaluation_id': evaluation_id,
                        'evaluation': evaluation,
                        'insights': insights,
                        'status': 'performance_evaluation_complete'
                    }
                    
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'Failed to parse performance evaluation',
                        'raw_response': result['response']
                    }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Performance evaluation failed')
                }
                
        except Exception as e:
            logger.error(f"Error in performance evaluation: {e}")
            return {
                'success': False,
                'error': str(e)
            }
            
    async def _handle_optimization_recommendations(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle optimization recommendation requests"""
        try:
            target_entity = task.get('target_entity', {})
            performance_issues = task.get('performance_issues', [])
            constraints = task.get('constraints', {})
            objectives = task.get('objectives', {})
            
            # Get relevant historical evaluations
            relevant_memories = await self.memory.retrieve_by_type('performance_evaluation', limit=5)
            
            # Optimization recommendations prompt
            optimization_prompt = f"""
            Generate optimization recommendations based on performance analysis:
            
            Target Entity: {json.dumps(target_entity, indent=2)}
            Performance Issues: {performance_issues}
            Constraints: {json.dumps(constraints, indent=2)}
            Objectives: {json.dumps(objectives, indent=2)}
            
            Historical Context: {json.dumps([mem.content for mem in relevant_memories], indent=2)}
            
            Provide comprehensive optimization recommendations:
            
            1. Performance Gap Analysis:
               - Identify specific performance shortfalls
               - Quantify improvement potential
               - Prioritize optimization opportunities
               - Root cause analysis
            
            2. Strategy Optimization:
               - Parameter tuning recommendations
               - Strategy modification suggestions
               - New strategy considerations
               - Strategy combination opportunities
            
            3. Risk Management Optimization:
               - Risk limit adjustments
               - Position sizing improvements
               - Diversification enhancements
               - Hedging strategy recommendations
            
            4. Execution Optimization:
               - Execution algorithm improvements
               - Timing optimization
               - Cost reduction strategies
               - Slippage minimization
            
            5. Portfolio Construction:
               - Asset allocation improvements
               - Rebalancing frequency optimization
               - Correlation management
               - Concentration risk reduction
            
            6. Operational Improvements:
               - Process optimization
               - Technology enhancements
               - Monitoring improvements
               - Reporting enhancements
            
            7. Implementation Plan:
               - Prioritized action items
               - Implementation timeline
               - Resource requirements
               - Success metrics
               - Monitoring plan
            
            8. Expected Impact:
               - Performance improvement estimates
               - Risk reduction potential
               - Cost-benefit analysis
               - Implementation risks
            
            Provide actionable recommendations with clear implementation guidance.
            Format as JSON with prioritized recommendations and implementation details.
            """
            
            result = await self.model_instance.generate(optimization_prompt, temperature=0.6)
            
            if result['success']:
                try:
                    recommendations = json.loads(result['response'])
                    
                    # Store recommendations
                    recommendation_id = f"opt_rec_{int(time.time())}"
                    entity_key = target_entity.get('id', 'unknown')
                    
                    if entity_key not in self.optimization_recommendations:
                        self.optimization_recommendations[entity_key] = []
                        
                    self.optimization_recommendations[entity_key].append({
                        'recommendation_id': recommendation_id,
                        'recommendations': recommendations,
                        'performance_issues': performance_issues,
                        'timestamp': time.time()
                    })
                    
                    # Store in memory
                    await self.memory.store_analysis({
                        'type': 'optimization_recommendations',
                        'recommendation_id': recommendation_id,
                        'target_entity': target_entity,
                        'recommendations': recommendations,
                        'timestamp': time.time()
                    })
                    
                    return {
                        'success': True,
                        'recommendation_id': recommendation_id,
                        'recommendations': recommendations,
                        'status': 'optimization_recommendations_complete'
                    }
                    
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'Failed to parse optimization recommendations',
                        'raw_response': result['response']
                    }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Optimization recommendations failed')
                }
                
        except Exception as e:
            logger.error(f"Error in optimization recommendations: {e}")
            return {
                'success': False,
                'error': str(e)
            }
            
    async def _extract_learning_insights(self, evaluation: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract learning insights from performance evaluation"""
        insights = []
        
        # Extract key insights from evaluation
        if 'improvement_opportunities' in evaluation:
            for opportunity in evaluation['improvement_opportunities']:
                insights.append({
                    'type': 'improvement_opportunity',
                    'insight': opportunity,
                    'confidence': 0.7,
                    'timestamp': time.time()
                })
                
        # Add to learning insights
        self.learning_insights.extend(insights)
        
        return insights
        
    async def _monitor_performance(self):
        """Monitor ongoing performance"""
        # Placeholder for continuous performance monitoring
        pass
        
    async def _generate_learning_insights(self):
        """Generate learning insights from accumulated data"""
        # Placeholder for learning insight generation
        pass
        
    async def _update_benchmarks(self):
        """Update benchmark data"""
        # Placeholder for benchmark updates
        pass
        
    # Placeholder methods for other handlers
    async def _handle_attribution_analysis(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle attribution analysis requests"""
        return {'success': True, 'status': 'attribution_analysis_complete'}
        
    async def _handle_benchmark_comparison(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle benchmark comparison requests"""
        return {'success': True, 'status': 'benchmark_comparison_complete'}
        
    async def _handle_learning_feedback(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle learning feedback requests"""
        return {'success': True, 'status': 'learning_feedback_complete'}
        
    async def _handle_agent_performance_review(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle agent performance review requests"""
        return {'success': True, 'status': 'agent_performance_reviewed'}
        
    async def _handle_strategy_effectiveness(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle strategy effectiveness analysis"""
        return {'success': True, 'status': 'strategy_effectiveness_analyzed'}
        
    async def _handle_system_performance_audit(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle system-wide performance audit"""
        return {'success': True, 'status': 'system_performance_audited'}

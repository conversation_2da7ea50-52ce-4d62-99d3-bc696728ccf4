"""
Metrics Collector - Collects and aggregates system metrics
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from collections import defaultdict, deque
import json

logger = logging.getLogger(__name__)


class MetricsCollector:
    """
    Collects and aggregates metrics from all system components.
    Provides real-time monitoring data for the dashboard.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Metrics storage
        self.system_metrics: Dict[str, Any] = {}
        self.agent_metrics: Dict[str, Dict[str, Any]] = {}
        self.team_metrics: Dict[str, Dict[str, Any]] = {}
        self.model_metrics: Dict[str, Dict[str, Any]] = {}
        
        # Time series data
        self.time_series: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Alerts and notifications
        self.active_alerts: List[Dict[str, Any]] = []
        self.alert_history: deque = deque(maxlen=1000)
        
        # State
        self.initialized = False
        self.running = False
        self.collection_interval = 10  # seconds
        
        # Collection task
        self.collection_task: Optional[asyncio.Task] = None
        
    async def initialize(self):
        """Initialize metrics collector"""
        if self.initialized:
            return
            
        logger.info("Initializing Metrics Collector...")
        
        # Initialize metric categories
        self._initialize_metric_categories()
        
        self.initialized = True
        logger.info("✓ Metrics Collector initialized")
        
    def _initialize_metric_categories(self):
        """Initialize metric categories"""
        self.metric_categories = {
            'system': [
                'cpu_usage', 'memory_usage', 'disk_usage',
                'network_io', 'active_connections', 'uptime'
            ],
            'agents': [
                'total_agents', 'active_agents', 'idle_agents',
                'tasks_completed', 'tasks_failed', 'avg_response_time'
            ],
            'teams': [
                'active_teams', 'team_efficiency', 'coordination_success_rate',
                'mission_completion_rate', 'team_performance_score'
            ],
            'models': [
                'model_utilization', 'inference_time', 'model_accuracy',
                'token_usage', 'model_errors', 'model_switching_events'
            ],
            'trading': [
                'active_positions', 'portfolio_value', 'daily_pnl',
                'risk_metrics', 'execution_quality', 'slippage'
            ],
            'market': [
                'market_regime', 'volatility_level', 'correlation_breakdown',
                'anomaly_count', 'data_quality', 'feed_latency'
            ]
        }
        
    async def start(self):
        """Start metrics collection"""
        if not self.initialized:
            await self.initialize()
            
        if self.running:
            return
            
        logger.info("Starting Metrics Collector...")
        
        # Start collection task
        self.collection_task = asyncio.create_task(self._collection_loop())
        
        self.running = True
        logger.info("✓ Metrics Collector started")
        
    async def stop(self):
        """Stop metrics collection"""
        if not self.running:
            return
            
        logger.info("Stopping Metrics Collector...")
        self.running = False
        
        # Cancel collection task
        if self.collection_task and not self.collection_task.done():
            self.collection_task.cancel()
            try:
                await self.collection_task
            except asyncio.CancelledError:
                pass
                
        logger.info("✓ Metrics Collector stopped")
        
    async def _collection_loop(self):
        """Main metrics collection loop"""
        while self.running:
            try:
                await asyncio.sleep(self.collection_interval)
                if self.running:
                    await self._collect_all_metrics()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in metrics collection: {e}")
                
    async def _collect_all_metrics(self):
        """Collect all system metrics"""
        timestamp = time.time()
        
        # Collect system metrics
        system_metrics = await self._collect_system_metrics()
        self.system_metrics.update(system_metrics)
        
        # Store in time series
        for metric_name, value in system_metrics.items():
            self.time_series[f"system.{metric_name}"].append({
                'timestamp': timestamp,
                'value': value
            })
            
        # Check for alerts
        await self._check_alerts(system_metrics, timestamp)
        
    async def _collect_system_metrics(self) -> Dict[str, Any]:
        """Collect system-level metrics"""
        import psutil
        
        try:
            metrics = {
                'cpu_usage': psutil.cpu_percent(interval=1),
                'memory_usage': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent,
                'network_io_sent': psutil.net_io_counters().bytes_sent,
                'network_io_recv': psutil.net_io_counters().bytes_recv,
                'uptime': time.time() - psutil.boot_time()
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
            return {}
            
    async def collect_agent_metrics(self, agent_id: str, metrics: Dict[str, Any]):
        """Collect metrics from an agent"""
        timestamp = time.time()
        
        # Store agent metrics
        self.agent_metrics[agent_id] = {
            **metrics,
            'last_updated': timestamp
        }
        
        # Store in time series
        for metric_name, value in metrics.items():
            if isinstance(value, (int, float)):
                self.time_series[f"agent.{agent_id}.{metric_name}"].append({
                    'timestamp': timestamp,
                    'value': value
                })
                
    async def collect_team_metrics(self, team_id: str, metrics: Dict[str, Any]):
        """Collect metrics from a team"""
        timestamp = time.time()
        
        # Store team metrics
        self.team_metrics[team_id] = {
            **metrics,
            'last_updated': timestamp
        }
        
        # Store in time series
        for metric_name, value in metrics.items():
            if isinstance(value, (int, float)):
                self.time_series[f"team.{team_id}.{metric_name}"].append({
                    'timestamp': timestamp,
                    'value': value
                })
                
    async def collect_model_metrics(self, model_name: str, metrics: Dict[str, Any]):
        """Collect metrics from a model"""
        timestamp = time.time()
        
        # Store model metrics
        self.model_metrics[model_name] = {
            **metrics,
            'last_updated': timestamp
        }
        
        # Store in time series
        for metric_name, value in metrics.items():
            if isinstance(value, (int, float)):
                self.time_series[f"model.{model_name}.{metric_name}"].append({
                    'timestamp': timestamp,
                    'value': value
                })
                
    async def _check_alerts(self, metrics: Dict[str, Any], timestamp: float):
        """Check for alert conditions"""
        alerts = []
        
        # CPU usage alert
        if metrics.get('cpu_usage', 0) > 90:
            alerts.append({
                'type': 'high_cpu_usage',
                'severity': 'warning',
                'message': f"High CPU usage: {metrics['cpu_usage']:.1f}%",
                'timestamp': timestamp
            })
            
        # Memory usage alert
        if metrics.get('memory_usage', 0) > 85:
            alerts.append({
                'type': 'high_memory_usage',
                'severity': 'warning',
                'message': f"High memory usage: {metrics['memory_usage']:.1f}%",
                'timestamp': timestamp
            })
            
        # Disk usage alert
        if metrics.get('disk_usage', 0) > 90:
            alerts.append({
                'type': 'high_disk_usage',
                'severity': 'critical',
                'message': f"High disk usage: {metrics['disk_usage']:.1f}%",
                'timestamp': timestamp
            })
            
        # Process alerts
        for alert in alerts:
            await self._process_alert(alert)
            
    async def _process_alert(self, alert: Dict[str, Any]):
        """Process an alert"""
        # Add to active alerts if not already present
        alert_key = f"{alert['type']}_{alert['severity']}"
        
        # Check if alert already exists
        existing_alert = None
        for active_alert in self.active_alerts:
            if f"{active_alert['type']}_{active_alert['severity']}" == alert_key:
                existing_alert = active_alert
                break
                
        if not existing_alert:
            self.active_alerts.append(alert)
            logger.warning(f"New alert: {alert['message']}")
            
        # Add to history
        self.alert_history.append(alert)
        
    async def get_dashboard_data(self) -> Dict[str, Any]:
        """Get comprehensive dashboard data"""
        current_time = time.time()
        
        # Get recent time series data (last hour)
        recent_data = {}
        cutoff_time = current_time - 3600  # 1 hour ago
        
        for metric_name, data_points in self.time_series.items():
            recent_points = [
                point for point in data_points 
                if point['timestamp'] >= cutoff_time
            ]
            recent_data[metric_name] = recent_points
            
        dashboard_data = {
            'timestamp': current_time,
            'system_metrics': self.system_metrics,
            'agent_metrics': self.agent_metrics,
            'team_metrics': self.team_metrics,
            'model_metrics': self.model_metrics,
            'time_series': recent_data,
            'active_alerts': self.active_alerts,
            'alert_count': len(self.active_alerts),
            'total_agents': len(self.agent_metrics),
            'total_teams': len(self.team_metrics),
            'total_models': len(self.model_metrics)
        }
        
        return dashboard_data
        
    async def get_metric_history(self, metric_name: str, hours: int = 24) -> List[Dict[str, Any]]:
        """Get historical data for a specific metric"""
        cutoff_time = time.time() - (hours * 3600)
        
        if metric_name in self.time_series:
            return [
                point for point in self.time_series[metric_name]
                if point['timestamp'] >= cutoff_time
            ]
        else:
            return []
            
    async def clear_alerts(self, alert_types: List[str] = None):
        """Clear active alerts"""
        if alert_types is None:
            self.active_alerts.clear()
            logger.info("Cleared all active alerts")
        else:
            self.active_alerts = [
                alert for alert in self.active_alerts
                if alert['type'] not in alert_types
            ]
            logger.info(f"Cleared alerts of types: {alert_types}")
            
    async def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        summary = {
            'system_health': 'healthy',
            'agent_performance': 'good',
            'team_efficiency': 'high',
            'model_utilization': 'optimal',
            'alert_level': 'low'
        }
        
        # Determine system health based on metrics
        if self.system_metrics.get('cpu_usage', 0) > 80:
            summary['system_health'] = 'degraded'
        if self.system_metrics.get('memory_usage', 0) > 90:
            summary['system_health'] = 'critical'
            
        # Determine alert level
        critical_alerts = [a for a in self.active_alerts if a['severity'] == 'critical']
        warning_alerts = [a for a in self.active_alerts if a['severity'] == 'warning']
        
        if critical_alerts:
            summary['alert_level'] = 'critical'
        elif warning_alerts:
            summary['alert_level'] = 'warning'
            
        return summary

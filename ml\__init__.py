"""
Machine Learning Package

Advanced machine learning capabilities for the trading system:
- Predictive models for price forecasting
- Pattern recognition and anomaly detection
- Reinforcement learning for strategy optimization
- Ensemble methods for decision making
- Adaptive learning systems
"""

from .predictive_models import PredictiveModels
from .pattern_recognition import MLPatternR<PERSON>ognition
from .reinforcement_learning import RLStrategyOptimizer
from .ensemble_methods import EnsembleDecisionMaker
from .adaptive_learning import AdaptiveLearningSystem

__all__ = [
    'PredictiveModels',
    'MLPatternRecognition',
    'RLStrategyOptimizer',
    'EnsembleDecisionMaker',
    'AdaptiveLearningSystem'
]

"""
Model Performance Tracker - Tracks and analyzes model performance metrics
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from collections import defaultdict, deque
import statistics

logger = logging.getLogger(__name__)


class ModelPerformanceTracker:
    """
    Tracks and analyzes performance metrics for all model instances.
    Provides insights for optimization and model selection.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Performance data storage
        self.model_metrics: Dict[str, Dict[str, Any]] = defaultdict(dict)
        self.historical_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.role_performance: Dict[str, Dict[str, Any]] = defaultdict(dict)
        
        # State
        self.initialized = False
        self.running = False
        
        # Configuration
        self.collection_interval = 60  # 1 minute
        self.analysis_interval = 300   # 5 minutes
        
        # Tasks
        self.collection_task: Optional[asyncio.Task] = None
        self.analysis_task: Optional[asyncio.Task] = None
        
    async def initialize(self):
        """Initialize the performance tracker"""
        if self.initialized:
            return
            
        logger.info("Initializing Model Performance Tracker...")
        
        self.initialized = True
        logger.info("✓ Performance Tracker initialized")
        
    async def start(self):
        """Start the performance tracker"""
        if not self.initialized:
            await self.initialize()
            
        if self.running:
            return
            
        logger.info("Starting Model Performance Tracker...")
        
        # Start monitoring tasks
        self.collection_task = asyncio.create_task(self._periodic_collection())
        self.analysis_task = asyncio.create_task(self._periodic_analysis())
        
        self.running = True
        logger.info("✓ Performance Tracker started")
        
    async def stop(self):
        """Stop the performance tracker"""
        if not self.running:
            return
            
        logger.info("Stopping Model Performance Tracker...")
        self.running = False
        
        # Cancel tasks
        for task in [self.collection_task, self.analysis_task]:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                    
        logger.info("✓ Performance Tracker stopped")
        
    async def record_request(self, 
                           model_name: str, 
                           role: str, 
                           agent_name: str,
                           response_time: float,
                           tokens: int,
                           success: bool,
                           error: str = None):
        """Record a model request for performance tracking"""
        timestamp = time.time()
        
        # Create record
        record = {
            'timestamp': timestamp,
            'model_name': model_name,
            'role': role,
            'agent_name': agent_name,
            'response_time': response_time,
            'tokens': tokens,
            'success': success,
            'error': error
        }
        
        # Store in historical data
        key = f"{model_name}:{role}"
        self.historical_data[key].append(record)
        
        # Update current metrics
        await self._update_current_metrics(key, record)
        
    async def _update_current_metrics(self, key: str, record: Dict[str, Any]):
        """Update current metrics for a model"""
        if key not in self.model_metrics:
            self.model_metrics[key] = {
                'total_requests': 0,
                'successful_requests': 0,
                'total_response_time': 0.0,
                'total_tokens': 0,
                'error_count': 0,
                'last_request': None,
                'avg_response_time': 0.0,
                'tokens_per_second': 0.0,
                'success_rate': 1.0
            }
            
        metrics = self.model_metrics[key]
        
        # Update counters
        metrics['total_requests'] += 1
        metrics['total_response_time'] += record['response_time']
        metrics['total_tokens'] += record['tokens']
        metrics['last_request'] = record['timestamp']
        
        if record['success']:
            metrics['successful_requests'] += 1
        else:
            metrics['error_count'] += 1
            
        # Calculate derived metrics
        metrics['avg_response_time'] = metrics['total_response_time'] / metrics['total_requests']
        metrics['success_rate'] = metrics['successful_requests'] / metrics['total_requests']
        
        if metrics['total_response_time'] > 0:
            metrics['tokens_per_second'] = metrics['total_tokens'] / metrics['total_response_time']
            
    async def get_model_performance(self, model_name: str, role: str = None) -> Dict[str, Any]:
        """Get performance metrics for a specific model"""
        if role:
            key = f"{model_name}:{role}"
            return self.model_metrics.get(key, {})
        else:
            # Aggregate across all roles for this model
            aggregated = {}
            for key, metrics in self.model_metrics.items():
                if key.startswith(f"{model_name}:"):
                    if not aggregated:
                        aggregated = metrics.copy()
                    else:
                        # Aggregate metrics
                        aggregated['total_requests'] += metrics['total_requests']
                        aggregated['successful_requests'] += metrics['successful_requests']
                        aggregated['total_response_time'] += metrics['total_response_time']
                        aggregated['total_tokens'] += metrics['total_tokens']
                        aggregated['error_count'] += metrics['error_count']
                        
            # Recalculate derived metrics
            if aggregated and aggregated['total_requests'] > 0:
                aggregated['avg_response_time'] = aggregated['total_response_time'] / aggregated['total_requests']
                aggregated['success_rate'] = aggregated['successful_requests'] / aggregated['total_requests']
                if aggregated['total_response_time'] > 0:
                    aggregated['tokens_per_second'] = aggregated['total_tokens'] / aggregated['total_response_time']
                    
            return aggregated
            
    async def get_role_performance(self, role: str) -> Dict[str, Any]:
        """Get performance metrics for all models in a specific role"""
        role_metrics = {}
        
        for key, metrics in self.model_metrics.items():
            if key.endswith(f":{role}"):
                model_name = key.split(':')[0]
                role_metrics[model_name] = metrics.copy()
                
        return role_metrics
        
    async def get_system_performance(self) -> Dict[str, Any]:
        """Get overall system performance metrics"""
        system_metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'total_response_time': 0.0,
            'total_tokens': 0,
            'error_count': 0,
            'unique_models': set(),
            'unique_roles': set(),
            'avg_response_time': 0.0,
            'tokens_per_second': 0.0,
            'success_rate': 1.0
        }
        
        for key, metrics in self.model_metrics.items():
            model_name, role = key.split(':', 1)
            
            system_metrics['total_requests'] += metrics['total_requests']
            system_metrics['successful_requests'] += metrics['successful_requests']
            system_metrics['total_response_time'] += metrics['total_response_time']
            system_metrics['total_tokens'] += metrics['total_tokens']
            system_metrics['error_count'] += metrics['error_count']
            system_metrics['unique_models'].add(model_name)
            system_metrics['unique_roles'].add(role)
            
        # Calculate derived metrics
        if system_metrics['total_requests'] > 0:
            system_metrics['avg_response_time'] = system_metrics['total_response_time'] / system_metrics['total_requests']
            system_metrics['success_rate'] = system_metrics['successful_requests'] / system_metrics['total_requests']
            
        if system_metrics['total_response_time'] > 0:
            system_metrics['tokens_per_second'] = system_metrics['total_tokens'] / system_metrics['total_response_time']
            
        # Convert sets to counts
        system_metrics['unique_models'] = len(system_metrics['unique_models'])
        system_metrics['unique_roles'] = len(system_metrics['unique_roles'])
        
        return system_metrics
        
    async def get_performance_trends(self, model_name: str, role: str, hours: int = 24) -> Dict[str, Any]:
        """Get performance trends for a model over time"""
        key = f"{model_name}:{role}"
        
        if key not in self.historical_data:
            return {}
            
        # Filter data by time window
        cutoff_time = time.time() - (hours * 3600)
        recent_data = [record for record in self.historical_data[key] if record['timestamp'] >= cutoff_time]
        
        if not recent_data:
            return {}
            
        # Calculate trends
        response_times = [r['response_time'] for r in recent_data]
        success_count = sum(1 for r in recent_data if r['success'])
        
        trends = {
            'time_window_hours': hours,
            'total_requests': len(recent_data),
            'successful_requests': success_count,
            'success_rate': success_count / len(recent_data),
            'avg_response_time': statistics.mean(response_times),
            'median_response_time': statistics.median(response_times),
            'min_response_time': min(response_times),
            'max_response_time': max(response_times),
            'response_time_std': statistics.stdev(response_times) if len(response_times) > 1 else 0.0
        }
        
        return trends
        
    async def get_model_rankings(self, role: str = None) -> List[Dict[str, Any]]:
        """Get model rankings by performance"""
        rankings = []
        
        metrics_to_consider = self.model_metrics.items()
        if role:
            metrics_to_consider = [(k, v) for k, v in self.model_metrics.items() if k.endswith(f":{role}")]
            
        for key, metrics in metrics_to_consider:
            model_name, model_role = key.split(':', 1)
            
            # Calculate composite score
            score = self._calculate_performance_score(metrics)
            
            rankings.append({
                'model_name': model_name,
                'role': model_role,
                'score': score,
                'metrics': metrics.copy()
            })
            
        # Sort by score (higher is better)
        rankings.sort(key=lambda x: x['score'], reverse=True)
        
        return rankings
        
    def _calculate_performance_score(self, metrics: Dict[str, Any]) -> float:
        """Calculate a composite performance score"""
        if metrics['total_requests'] == 0:
            return 0.0
            
        # Weights for different metrics
        success_weight = 0.4
        speed_weight = 0.3
        efficiency_weight = 0.3
        
        # Normalize metrics (higher is better)
        success_score = metrics['success_rate']
        
        # Speed score (inverse of response time, normalized)
        avg_response_time = metrics['avg_response_time']
        speed_score = 1.0 / (1.0 + avg_response_time) if avg_response_time > 0 else 0.0
        
        # Efficiency score (tokens per second, normalized)
        tokens_per_second = metrics['tokens_per_second']
        efficiency_score = min(1.0, tokens_per_second / 100.0) if tokens_per_second > 0 else 0.0
        
        # Composite score
        composite_score = (
            success_weight * success_score +
            speed_weight * speed_score +
            efficiency_weight * efficiency_score
        )
        
        return composite_score
        
    async def _periodic_collection(self):
        """Periodic collection task"""
        while self.running:
            try:
                await asyncio.sleep(self.collection_interval)
                if self.running:
                    await self._collect_metrics()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic collection: {e}")
                
    async def _collect_metrics(self):
        """Collect current metrics"""
        # This would typically collect metrics from deployed model instances
        # For now, we rely on the record_request method being called
        logger.debug("Collecting performance metrics...")
        
    async def _periodic_analysis(self):
        """Periodic analysis task"""
        while self.running:
            try:
                await asyncio.sleep(self.analysis_interval)
                if self.running:
                    await self._analyze_performance()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic analysis: {e}")
                
    async def _analyze_performance(self):
        """Analyze performance and generate insights"""
        logger.debug("Analyzing performance metrics...")
        
        # Identify underperforming models
        for key, metrics in self.model_metrics.items():
            if metrics['total_requests'] > 10:  # Only analyze models with sufficient data
                score = self._calculate_performance_score(metrics)
                
                if score < 0.5:  # Threshold for poor performance
                    model_name, role = key.split(':', 1)
                    logger.warning(f"Poor performance detected for {model_name} in {role} role (score: {score:.2f})")
                    
    async def reset_metrics(self, model_name: str = None, role: str = None):
        """Reset performance metrics"""
        if model_name and role:
            key = f"{model_name}:{role}"
            if key in self.model_metrics:
                del self.model_metrics[key]
            if key in self.historical_data:
                self.historical_data[key].clear()
        elif model_name:
            # Reset all metrics for this model
            keys_to_remove = [k for k in self.model_metrics.keys() if k.startswith(f"{model_name}:")]
            for key in keys_to_remove:
                del self.model_metrics[key]
                if key in self.historical_data:
                    self.historical_data[key].clear()
        else:
            # Reset all metrics
            self.model_metrics.clear()
            self.historical_data.clear()
            
        logger.info(f"Reset metrics for {model_name or 'all models'}")
        
    async def export_metrics(self) -> Dict[str, Any]:
        """Export all performance metrics"""
        return {
            'current_metrics': dict(self.model_metrics),
            'system_performance': await self.get_system_performance(),
            'export_timestamp': time.time()
        }

"""
System Dashboard - Comprehensive system monitoring and status display
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.layout import Layout
from rich.live import Live
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn
import psutil

logger = logging.getLogger(__name__)


class SystemDashboard:
    """
    Comprehensive system dashboard for monitoring all components.
    
    Features:
    - Real-time system status display
    - Component health monitoring
    - Performance metrics visualization
    - Trading activity monitoring
    - Resource usage tracking
    """
    
    def __init__(self, trading_system):
        self.trading_system = trading_system
        self.console = Console()
        
        # Dashboard state
        self.running = False
        self.update_interval = 2.0  # seconds
        self.last_update = time.time()
        
        # Metrics storage
        self.system_metrics = {}
        self.component_metrics = {}
        self.trading_metrics = {}
        
    async def start_dashboard(self):
        """Start the real-time dashboard"""
        if self.running:
            return
            
        self.running = True
        
        try:
            with Live(self._create_dashboard_layout(), refresh_per_second=0.5, console=self.console) as live:
                while self.running:
                    # Update metrics
                    await self._update_metrics()
                    
                    # Update dashboard layout
                    live.update(self._create_dashboard_layout())
                    
                    # Wait for next update
                    await asyncio.sleep(self.update_interval)
                    
        except KeyboardInterrupt:
            self.running = False
        except Exception as e:
            logger.error(f"Dashboard error: {e}")
            self.running = False
    
    def stop_dashboard(self):
        """Stop the dashboard"""
        self.running = False
    
    async def _update_metrics(self):
        """Update all metrics"""
        try:
            # Update system metrics
            await self._update_system_metrics()
            
            # Update component metrics
            await self._update_component_metrics()
            
            # Update trading metrics
            await self._update_trading_metrics()
            
            self.last_update = time.time()
            
        except Exception as e:
            logger.error(f"Error updating metrics: {e}")
    
    async def _update_system_metrics(self):
        """Update system-level metrics"""
        try:
            # CPU and Memory usage
            cpu_percent = psutil.cpu_percent(interval=None)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Network stats
            network = psutil.net_io_counters()
            
            self.system_metrics = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used_gb': memory.used / (1024**3),
                'memory_total_gb': memory.total / (1024**3),
                'disk_percent': disk.percent,
                'disk_used_gb': disk.used / (1024**3),
                'disk_total_gb': disk.total / (1024**3),
                'network_sent_mb': network.bytes_sent / (1024**2),
                'network_recv_mb': network.bytes_recv / (1024**2),
                'uptime': time.time() - self.trading_system.system_state.get('start_time', time.time())
            }
            
        except Exception as e:
            logger.error(f"Error updating system metrics: {e}")
    
    async def _update_component_metrics(self):
        """Update component-specific metrics"""
        try:
            components = {}
            
            # Ollama Hub
            if self.trading_system.ollama_hub:
                try:
                    models = await self.trading_system.ollama_hub.get_available_models()
                    components['ollama_hub'] = {
                        'status': 'running' if self.trading_system.ollama_hub.running else 'stopped',
                        'models_count': len(models),
                        'health': 'healthy'
                    }
                except:
                    components['ollama_hub'] = {'status': 'error', 'health': 'unhealthy'}
            
            # Agent Manager
            if self.trading_system.agent_manager:
                try:
                    agents = await self.trading_system.agent_manager.get_active_agents()
                    components['agent_manager'] = {
                        'status': 'running' if self.trading_system.agent_manager.running else 'stopped',
                        'active_agents': len(agents),
                        'health': 'healthy'
                    }
                except:
                    components['agent_manager'] = {'status': 'error', 'health': 'unhealthy'}
            
            # Strategy Manager
            if self.trading_system.strategy_manager:
                try:
                    strategies = await self.trading_system.strategy_manager.get_active_strategies()
                    components['strategy_manager'] = {
                        'status': 'running' if self.trading_system.strategy_manager.running else 'stopped',
                        'active_strategies': len(strategies),
                        'health': 'healthy'
                    }
                except:
                    components['strategy_manager'] = {'status': 'error', 'health': 'unhealthy'}
            
            # Risk Manager
            if self.trading_system.risk_manager:
                try:
                    risk_status = await self.trading_system.risk_manager.get_system_status()
                    components['risk_manager'] = {
                        'status': 'running' if self.trading_system.risk_manager.running else 'stopped',
                        'risk_level': risk_status.get('overall_status', 'unknown'),
                        'health': 'healthy'
                    }
                except:
                    components['risk_manager'] = {'status': 'error', 'health': 'unhealthy'}
            
            # Execution Engine
            if self.trading_system.execution_engine:
                try:
                    active_orders = await self.trading_system.execution_engine.get_active_orders()
                    components['execution_engine'] = {
                        'status': 'running' if self.trading_system.execution_engine.running else 'stopped',
                        'active_orders': len(active_orders),
                        'health': 'healthy'
                    }
                except:
                    components['execution_engine'] = {'status': 'error', 'health': 'unhealthy'}
            
            # Portfolio Manager
            if self.trading_system.portfolio_manager:
                try:
                    portfolio_state = await self.trading_system.portfolio_manager.get_portfolio_state()
                    components['portfolio_manager'] = {
                        'status': 'running' if self.trading_system.portfolio_manager.running else 'stopped',
                        'portfolio_value': portfolio_state.get('total_value', 0) if portfolio_state else 0,
                        'positions_count': len(portfolio_state.get('positions', {})) if portfolio_state else 0,
                        'health': 'healthy'
                    }
                except:
                    components['portfolio_manager'] = {'status': 'error', 'health': 'unhealthy'}
            
            self.component_metrics = components
            
        except Exception as e:
            logger.error(f"Error updating component metrics: {e}")
    
    async def _update_trading_metrics(self):
        """Update trading-specific metrics"""
        try:
            metrics = {}
            
            # Portfolio metrics
            if self.trading_system.portfolio_manager:
                try:
                    portfolio_state = await self.trading_system.portfolio_manager.get_portfolio_state()
                    performance_metrics = await self.trading_system.portfolio_manager.get_performance_metrics()
                    
                    if portfolio_state:
                        metrics['portfolio'] = {
                            'total_value': portfolio_state.get('total_value', 0),
                            'cash': portfolio_state.get('cash', 0),
                            'positions_count': len(portfolio_state.get('positions', {})),
                            'daily_return': performance_metrics.get('daily_return', 0),
                            'total_return': performance_metrics.get('total_return', 0),
                            'sharpe_ratio': performance_metrics.get('sharpe_ratio', 0),
                            'max_drawdown': performance_metrics.get('max_drawdown', 0)
                        }
                except:
                    metrics['portfolio'] = {'status': 'error'}
            
            # Execution metrics
            if self.trading_system.execution_engine:
                try:
                    execution_metrics = await self.trading_system.execution_engine.get_execution_metrics()
                    metrics['execution'] = execution_metrics
                except:
                    metrics['execution'] = {'status': 'error'}
            
            # Risk metrics
            if self.trading_system.risk_manager:
                try:
                    risk_metrics = await self.trading_system.risk_manager.get_current_risk_metrics()
                    metrics['risk'] = risk_metrics
                except:
                    metrics['risk'] = {'status': 'error'}
            
            self.trading_metrics = metrics
            
        except Exception as e:
            logger.error(f"Error updating trading metrics: {e}")
    
    def _create_dashboard_layout(self) -> Layout:
        """Create the dashboard layout"""
        try:
            layout = Layout()
            
            # Split into main sections
            layout.split_column(
                Layout(name="header", size=3),
                Layout(name="main"),
                Layout(name="footer", size=3)
            )
            
            # Split main section
            layout["main"].split_row(
                Layout(name="left"),
                Layout(name="right")
            )
            
            # Split left section
            layout["left"].split_column(
                Layout(name="system", size=8),
                Layout(name="components")
            )
            
            # Split right section
            layout["right"].split_column(
                Layout(name="trading", size=12),
                Layout(name="performance")
            )
            
            # Populate sections
            layout["header"].update(self._create_header())
            layout["system"].update(self._create_system_panel())
            layout["components"].update(self._create_components_panel())
            layout["trading"].update(self._create_trading_panel())
            layout["performance"].update(self._create_performance_panel())
            layout["footer"].update(self._create_footer())
            
            return layout
            
        except Exception as e:
            logger.error(f"Error creating dashboard layout: {e}")
            return Layout(Panel("Dashboard Error", style="red"))
    
    def _create_header(self) -> Panel:
        """Create header panel"""
        title = Text("🤖 ADVANCED OLLAMA TRADING AGENT SYSTEM", style="bold blue")
        subtitle = Text(f"Last Updated: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.last_update))}", style="dim")
        
        return Panel(
            Text.assemble(title, "\n", subtitle),
            title="System Dashboard",
            border_style="blue"
        )
    
    def _create_system_panel(self) -> Panel:
        """Create system metrics panel"""
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        table.add_column("Status", style="yellow")
        
        metrics = self.system_metrics
        
        # CPU
        cpu_status = "🟢" if metrics.get('cpu_percent', 0) < 80 else "🟡" if metrics.get('cpu_percent', 0) < 95 else "🔴"
        table.add_row("CPU Usage", f"{metrics.get('cpu_percent', 0):.1f}%", cpu_status)
        
        # Memory
        mem_status = "🟢" if metrics.get('memory_percent', 0) < 80 else "🟡" if metrics.get('memory_percent', 0) < 95 else "🔴"
        table.add_row("Memory", f"{metrics.get('memory_used_gb', 0):.1f}GB / {metrics.get('memory_total_gb', 0):.1f}GB", mem_status)
        
        # Disk
        disk_status = "🟢" if metrics.get('disk_percent', 0) < 80 else "🟡" if metrics.get('disk_percent', 0) < 95 else "🔴"
        table.add_row("Disk", f"{metrics.get('disk_used_gb', 0):.1f}GB / {metrics.get('disk_total_gb', 0):.1f}GB", disk_status)
        
        # Uptime
        uptime_hours = metrics.get('uptime', 0) / 3600
        table.add_row("Uptime", f"{uptime_hours:.1f} hours", "🟢")
        
        return Panel(table, title="System Metrics", border_style="green")
    
    def _create_components_panel(self) -> Panel:
        """Create components status panel"""
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Component", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("Health", style="yellow")
        table.add_column("Details", style="white")
        
        for name, metrics in self.component_metrics.items():
            status = metrics.get('status', 'unknown')
            health = metrics.get('health', 'unknown')
            
            # Status indicator
            status_icon = "🟢" if status == 'running' else "🔴" if status == 'error' else "🟡"
            
            # Health indicator
            health_icon = "✅" if health == 'healthy' else "❌" if health == 'unhealthy' else "❓"
            
            # Details
            details = []
            if 'models_count' in metrics:
                details.append(f"Models: {metrics['models_count']}")
            if 'active_agents' in metrics:
                details.append(f"Agents: {metrics['active_agents']}")
            if 'active_strategies' in metrics:
                details.append(f"Strategies: {metrics['active_strategies']}")
            if 'active_orders' in metrics:
                details.append(f"Orders: {metrics['active_orders']}")
            if 'portfolio_value' in metrics:
                details.append(f"Value: ${metrics['portfolio_value']:,.0f}")
            
            table.add_row(
                name.replace('_', ' ').title(),
                f"{status_icon} {status}",
                f"{health_icon} {health}",
                ", ".join(details)
            )
        
        return Panel(table, title="Components", border_style="yellow")
    
    def _create_trading_panel(self) -> Panel:
        """Create trading metrics panel"""
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        portfolio = self.trading_metrics.get('portfolio', {})
        
        if portfolio and 'total_value' in portfolio:
            table.add_row("Portfolio Value", f"${portfolio['total_value']:,.2f}")
            table.add_row("Cash", f"${portfolio['cash']:,.2f}")
            table.add_row("Positions", str(portfolio['positions_count']))
            table.add_row("Daily Return", f"{portfolio['daily_return']:.2%}")
            table.add_row("Total Return", f"{portfolio['total_return']:.2%}")
            table.add_row("Sharpe Ratio", f"{portfolio['sharpe_ratio']:.2f}")
            table.add_row("Max Drawdown", f"{portfolio['max_drawdown']:.2%}")
        else:
            table.add_row("Status", "No trading data available")
        
        return Panel(table, title="Trading Metrics", border_style="magenta")
    
    def _create_performance_panel(self) -> Panel:
        """Create performance panel"""
        execution = self.trading_metrics.get('execution', {})
        risk = self.trading_metrics.get('risk', {})
        
        content = []
        
        if execution:
            content.append("📊 Execution Metrics:")
            for key, value in execution.items():
                if isinstance(value, (int, float)):
                    content.append(f"  {key}: {value}")
        
        if risk:
            content.append("\n⚠️ Risk Metrics:")
            for key, value in risk.items():
                if isinstance(value, (int, float)):
                    content.append(f"  {key}: {value}")
        
        if not content:
            content = ["No performance data available"]
        
        return Panel("\n".join(content), title="Performance", border_style="red")
    
    def _create_footer(self) -> Panel:
        """Create footer panel"""
        status_text = "🟢 SYSTEM RUNNING" if self.trading_system.running else "🔴 SYSTEM STOPPED"
        return Panel(
            Text(f"{status_text} | Press Ctrl+C to stop", style="bold"),
            border_style="blue"
        )

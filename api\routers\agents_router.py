"""
Agents Router - Agent management and communication endpoints
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from fastapi import APIRout<PERSON>, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class AgentInfo(BaseModel):
    """Agent information model"""
    agent_id: str = Field(..., description="Agent ID")
    name: str = Field(..., description="Agent name")
    type: str = Field(..., description="Agent type")
    status: str = Field(..., description="Agent status")
    capabilities: List[str] = Field(..., description="Agent capabilities")
    performance: Dict[str, Any] = Field(..., description="Performance metrics")
    last_activity: Optional[datetime] = Field(None, description="Last activity timestamp")


class AgentMessage(BaseModel):
    """Agent message model"""
    message_id: str = Field(..., description="Message ID")
    from_agent: str = Field(..., description="Sender agent ID")
    to_agent: Optional[str] = Field(None, description="Recipient agent ID")
    message_type: str = Field(..., description="Message type")
    content: Dict[str, Any] = Field(..., description="Message content")
    timestamp: datetime = Field(..., description="Message timestamp")


def create_router(trading_system=None, auth_manager=None) -> APIRouter:
    """Create agents router with dependencies"""
    
    router = APIRouter()
    
    @router.get("/", response_model=List[AgentInfo], summary="Get all agents")
    async def get_agents(
        status: Optional[str] = Query(None, description="Filter by status"),
        agent_type: Optional[str] = Query(None, description="Filter by type"),
        user=Depends(auth_manager.require_permission("agents:read")) if auth_manager else None
    ):
        """Get list of all agents with optional filtering."""
        try:
            if not trading_system or not hasattr(trading_system, 'agent_manager'):
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Agent manager not available"
                )
            
            agents = await trading_system.agent_manager.get_all_agents()
            
            # Apply filters
            if status:
                agents = [agent for agent in agents if agent.get('status') == status]
            if agent_type:
                agents = [agent for agent in agents if agent.get('type') == agent_type]
            
            return [
                AgentInfo(
                    agent_id=agent['agent_id'],
                    name=agent['name'],
                    type=agent['type'],
                    status=agent['status'],
                    capabilities=agent.get('capabilities', []),
                    performance=agent.get('performance', {}),
                    last_activity=agent.get('last_activity')
                )
                for agent in agents
            ]
            
        except Exception as e:
            logger.error(f"Error getting agents: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get agents"
            )
    
    @router.get("/{agent_id}", response_model=AgentInfo, summary="Get agent details")
    async def get_agent(
        agent_id: str,
        user=Depends(auth_manager.require_permission("agents:read")) if auth_manager else None
    ):
        """Get detailed information about a specific agent."""
        try:
            if not trading_system or not hasattr(trading_system, 'agent_manager'):
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Agent manager not available"
                )
            
            agent = await trading_system.agent_manager.get_agent(agent_id)
            if not agent:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Agent {agent_id} not found"
                )
            
            return AgentInfo(
                agent_id=agent['agent_id'],
                name=agent['name'],
                type=agent['type'],
                status=agent['status'],
                capabilities=agent.get('capabilities', []),
                performance=agent.get('performance', {}),
                last_activity=agent.get('last_activity')
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting agent {agent_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get agent"
            )
    
    @router.post("/{agent_id}/messages", summary="Send message to agent")
    async def send_message_to_agent(
        agent_id: str,
        message: Dict[str, Any],
        user=Depends(auth_manager.require_permission("agents:write")) if auth_manager else None
    ):
        """Send a message to a specific agent."""
        try:
            if not trading_system or not hasattr(trading_system, 'agent_manager'):
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Agent manager not available"
                )
            
            result = await trading_system.agent_manager.send_message(agent_id, message)
            
            return {
                "success": True,
                "message_id": result.get('message_id'),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error sending message to agent {agent_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send message"
            )
    
    @router.get("/{agent_id}/messages", response_model=List[AgentMessage], summary="Get agent messages")
    async def get_agent_messages(
        agent_id: str,
        limit: int = Query(50, description="Maximum number of messages"),
        user=Depends(auth_manager.require_permission("agents:read")) if auth_manager else None
    ):
        """Get recent messages for an agent."""
        try:
            if not trading_system or not hasattr(trading_system, 'agent_manager'):
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Agent manager not available"
                )
            
            messages = await trading_system.agent_manager.get_agent_messages(agent_id, limit)
            
            return [
                AgentMessage(
                    message_id=msg['message_id'],
                    from_agent=msg['from_agent'],
                    to_agent=msg.get('to_agent'),
                    message_type=msg['message_type'],
                    content=msg['content'],
                    timestamp=msg['timestamp']
                )
                for msg in messages
            ]
            
        except Exception as e:
            logger.error(f"Error getting messages for agent {agent_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get messages"
            )
    
    return router

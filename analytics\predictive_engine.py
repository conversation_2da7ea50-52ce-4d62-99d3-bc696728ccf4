"""
Predictive Engine - Advanced predictive modeling for market forecasting
"""

import asyncio
import logging
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import defaultdict, deque
import json

logger = logging.getLogger(__name__)


@dataclass
class PredictionResult:
    """Prediction result data structure"""
    symbol: str
    prediction_type: str
    prediction_value: float
    confidence: float
    time_horizon: int  # minutes
    features_used: List[str]
    model_type: str
    timestamp: datetime
    metadata: Dict[str, Any]


class PredictiveEngine:
    """
    Advanced predictive modeling engine for market forecasting.
    
    Features:
    - Multi-horizon price predictions
    - Volatility forecasting
    - Trend prediction
    - Regime change detection
    - Ensemble prediction methods
    - Feature engineering and selection
    - Model performance tracking
    - Adaptive model selection
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.pred_config = config.get('predictive_engine', {})
        
        # Prediction models
        self.models = {
            'price_direction': {},
            'price_magnitude': {},
            'volatility': {},
            'trend_strength': {},
            'regime_change': {}
        }
        
        # Model performance tracking
        self.model_performance = defaultdict(lambda: defaultdict(list))
        self.prediction_history = defaultdict(lambda: deque(maxlen=1000))
        
        # Feature engineering
        self.feature_extractors = {
            'technical_indicators': self._extract_technical_features,
            'price_patterns': self._extract_pattern_features,
            'volume_features': self._extract_volume_features,
            'volatility_features': self._extract_volatility_features,
            'momentum_features': self._extract_momentum_features
        }
        
        # Prediction horizons (in minutes)
        self.prediction_horizons = self.pred_config.get('prediction_horizons', [5, 15, 30, 60, 240])
        
        # Model configuration
        self.ensemble_methods = ['voting', 'weighted_average', 'stacking']
        self.confidence_threshold = self.pred_config.get('confidence_threshold', 0.6)
        self.max_features = self.pred_config.get('max_features', 50)
        
        # Performance metrics
        self.prediction_metrics = {
            'total_predictions': 0,
            'successful_predictions': 0,
            'average_accuracy': 0.0,
            'average_confidence': 0.0,
            'model_count': 0
        }
        
        # State
        self.initialized = False
        self.running = False
    
    async def initialize(self) -> bool:
        """Initialize predictive engine"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Predictive Engine...")
            
            # Initialize models
            await self._initialize_models()
            
            # Setup feature extractors
            await self._setup_feature_extractors()
            
            self.initialized = True
            logger.info("✓ Predictive Engine initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Predictive Engine: {e}")
            return False
    
    async def start(self) -> bool:
        """Start predictive engine"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        self.running = True
        logger.info("✓ Predictive Engine started")
        return True
    
    async def stop(self) -> bool:
        """Stop predictive engine"""
        self.running = False
        logger.info("✓ Predictive Engine stopped")
        return True
    
    async def predict_price_movement(self, symbol: str, market_data: Dict[str, Any], 
                                   horizon: int = 15) -> Optional[Dict[str, Any]]:
        """Predict price movement for given horizon"""
        try:
            # Extract features
            features = await self._extract_all_features(symbol, market_data)
            if not features:
                return None
            
            # Generate predictions for direction and magnitude
            direction_pred = await self._predict_direction(symbol, features, horizon)
            magnitude_pred = await self._predict_magnitude(symbol, features, horizon)
            
            if not direction_pred or not magnitude_pred:
                return None
            
            # Combine predictions
            prediction_value = direction_pred['prediction'] * magnitude_pred['prediction']
            confidence = min(direction_pred['confidence'], magnitude_pred['confidence'])
            
            # Create prediction result
            result = {
                'symbol': symbol,
                'prediction': prediction_value,
                'confidence': confidence,
                'direction': direction_pred['prediction'],
                'magnitude': magnitude_pred['prediction'],
                'horizon_minutes': horizon,
                'features_count': len(features),
                'timestamp': datetime.now(),
                'model_ensemble': {
                    'direction_models': direction_pred.get('models_used', []),
                    'magnitude_models': magnitude_pred.get('models_used', [])
                }
            }
            
            # Store prediction
            self.prediction_history[symbol].append(result)
            self.prediction_metrics['total_predictions'] += 1
            
            return result
            
        except Exception as e:
            logger.error(f"Error predicting price movement for {symbol}: {e}")
            return None
    
    async def predict_volatility(self, symbol: str, market_data: Dict[str, Any], 
                               horizon: int = 60) -> Optional[Dict[str, Any]]:
        """Predict volatility for given horizon"""
        try:
            # Extract volatility-specific features
            features = await self._extract_volatility_features(symbol, market_data)
            if not features:
                return None
            
            # Generate volatility prediction
            model_key = f"{symbol}_volatility_{horizon}"
            
            # Simple volatility prediction (would use trained models in production)
            current_volatility = features.get('current_volatility', 0.0)
            historical_volatility = features.get('historical_volatility', 0.0)
            volatility_trend = features.get('volatility_trend', 0.0)
            
            # Predict future volatility
            predicted_volatility = current_volatility * (1 + volatility_trend * 0.1)
            
            # Calculate confidence based on volatility stability
            volatility_stability = 1.0 - abs(current_volatility - historical_volatility) / (historical_volatility + 0.001)
            confidence = max(0.3, min(0.9, volatility_stability))
            
            result = {
                'symbol': symbol,
                'predicted_volatility': predicted_volatility,
                'current_volatility': current_volatility,
                'confidence': confidence,
                'horizon_minutes': horizon,
                'timestamp': datetime.now(),
                'features_used': list(features.keys())
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error predicting volatility for {symbol}: {e}")
            return None
    
    async def predict_trend_change(self, symbol: str, market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Predict trend change probability"""
        try:
            # Extract trend-specific features
            features = await self._extract_momentum_features(symbol, market_data)
            if not features:
                return None
            
            # Analyze trend characteristics
            current_trend = features.get('trend_direction', 0)
            trend_strength = features.get('trend_strength', 0.0)
            momentum_divergence = features.get('momentum_divergence', 0.0)
            
            # Calculate trend change probability
            change_probability = 0.0
            
            # Weak trend increases change probability
            if abs(trend_strength) < 0.3:
                change_probability += 0.3
            
            # Momentum divergence increases change probability
            if abs(momentum_divergence) > 0.5:
                change_probability += 0.4
            
            # Overbought/oversold conditions
            rsi_like = features.get('momentum_oscillator', 0.5)
            if rsi_like > 0.8 or rsi_like < 0.2:
                change_probability += 0.3
            
            change_probability = min(1.0, change_probability)
            
            # Determine likely new trend direction
            if change_probability > 0.6:
                new_trend_direction = -current_trend  # Reversal
            else:
                new_trend_direction = current_trend  # Continuation
            
            confidence = change_probability if change_probability > 0.6 else (1.0 - change_probability)
            
            result = {
                'symbol': symbol,
                'trend_change_probability': change_probability,
                'current_trend': current_trend,
                'predicted_trend': new_trend_direction,
                'confidence': confidence,
                'trend_strength': trend_strength,
                'timestamp': datetime.now()
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error predicting trend change for {symbol}: {e}")
            return None
    
    async def generate_predictions(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive predictions for symbol"""
        try:
            predictions = {}
            
            # Price movement predictions for different horizons
            for horizon in self.prediction_horizons:
                price_pred = await self.predict_price_movement(symbol, market_data, horizon)
                if price_pred:
                    predictions[f'price_{horizon}min'] = price_pred
            
            # Volatility prediction
            vol_pred = await self.predict_volatility(symbol, market_data)
            if vol_pred:
                predictions['volatility'] = vol_pred
            
            # Trend change prediction
            trend_pred = await self.predict_trend_change(symbol, market_data)
            if trend_pred:
                predictions['trend_change'] = trend_pred
            
            # Overall prediction summary
            if predictions:
                # Calculate overall confidence
                confidences = [pred.get('confidence', 0.0) for pred in predictions.values()]
                overall_confidence = np.mean(confidences) if confidences else 0.0
                
                # Determine overall market outlook
                short_term_predictions = [pred.get('prediction', 0.0) for key, pred in predictions.items() if 'price_' in key and int(key.split('_')[1].replace('min', '')) <= 30]
                overall_direction = np.mean(short_term_predictions) if short_term_predictions else 0.0
                
                predictions['summary'] = {
                    'overall_confidence': overall_confidence,
                    'overall_direction': overall_direction,
                    'prediction_count': len(predictions) - 1,  # Exclude summary itself
                    'timestamp': datetime.now()
                }
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error generating predictions for {symbol}: {e}")
            return {}
    
    async def get_prediction_performance(self, symbol: str = None) -> Dict[str, Any]:
        """Get prediction performance metrics"""
        try:
            if symbol:
                # Symbol-specific performance
                if symbol not in self.prediction_history:
                    return {'error': 'No prediction history for symbol'}
                
                predictions = list(self.prediction_history[symbol])
                
                # Calculate accuracy metrics (simplified)
                total_predictions = len(predictions)
                recent_predictions = predictions[-50:] if len(predictions) >= 50 else predictions
                
                avg_confidence = np.mean([pred.get('confidence', 0.0) for pred in recent_predictions])
                
                return {
                    'symbol': symbol,
                    'total_predictions': total_predictions,
                    'recent_predictions': len(recent_predictions),
                    'average_confidence': avg_confidence,
                    'prediction_frequency': total_predictions / max(1, (datetime.now() - predictions[0]['timestamp']).total_seconds() / 3600) if predictions else 0
                }
            else:
                # Overall performance
                total_symbols = len(self.prediction_history)
                total_predictions = sum(len(history) for history in self.prediction_history.values())
                
                all_confidences = []
                for history in self.prediction_history.values():
                    all_confidences.extend([pred.get('confidence', 0.0) for pred in history])
                
                avg_confidence = np.mean(all_confidences) if all_confidences else 0.0
                
                return {
                    'total_symbols': total_symbols,
                    'total_predictions': total_predictions,
                    'average_confidence': avg_confidence,
                    'active_models': sum(len(models) for models in self.models.values()),
                    'prediction_horizons': self.prediction_horizons
                }
                
        except Exception as e:
            logger.error(f"Error getting prediction performance: {e}")
            return {}
    
    # Private methods
    
    async def _initialize_models(self):
        """Initialize prediction models"""
        try:
            # Initialize simple models (would use ML models in production)
            for model_type in self.models.keys():
                self.models[model_type] = {
                    'linear_trend': {'type': 'linear', 'params': {}},
                    'momentum_based': {'type': 'momentum', 'params': {}},
                    'mean_reversion': {'type': 'mean_reversion', 'params': {}}
                }
            
            self.prediction_metrics['model_count'] = sum(len(models) for models in self.models.values())
            logger.debug(f"Initialized {self.prediction_metrics['model_count']} prediction models")
            
        except Exception as e:
            logger.error(f"Error initializing models: {e}")
            raise
    
    async def _setup_feature_extractors(self):
        """Setup feature extraction methods"""
        try:
            # Feature extractors are already defined in __init__
            logger.debug(f"Setup {len(self.feature_extractors)} feature extractors")
            
        except Exception as e:
            logger.error(f"Error setting up feature extractors: {e}")
            raise
    
    async def _extract_all_features(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract all features for prediction"""
        try:
            features = {}
            
            # Extract features using all extractors
            for extractor_name, extractor_func in self.feature_extractors.items():
                try:
                    extracted_features = await extractor_func(symbol, market_data)
                    if extracted_features:
                        features.update(extracted_features)
                except Exception as e:
                    logger.warning(f"Error in {extractor_name}: {e}")
            
            # Limit feature count
            if len(features) > self.max_features:
                # Keep most important features (simplified selection)
                sorted_features = sorted(features.items(), key=lambda x: abs(x[1]), reverse=True)
                features = dict(sorted_features[:self.max_features])
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            return {}
    
    async def _extract_technical_features(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract technical indicator features"""
        try:
            prices = market_data.get('prices', [])
            volumes = market_data.get('volumes', [])
            
            if len(prices) < 10:
                return {}
            
            features = {}
            
            # Moving averages
            if len(prices) >= 5:
                sma_5 = np.mean(prices[-5:])
                features['sma_5_ratio'] = prices[-1] / sma_5 if sma_5 != 0 else 1.0
            
            if len(prices) >= 20:
                sma_20 = np.mean(prices[-20:])
                features['sma_20_ratio'] = prices[-1] / sma_20 if sma_20 != 0 else 1.0
                features['sma_5_20_ratio'] = features.get('sma_5_ratio', 1.0) / (prices[-1] / sma_20) if sma_20 != 0 else 1.0
            
            # Price momentum
            if len(prices) >= 3:
                features['price_momentum_3'] = (prices[-1] - prices[-3]) / prices[-3] if prices[-3] != 0 else 0.0
            
            if len(prices) >= 10:
                features['price_momentum_10'] = (prices[-1] - prices[-10]) / prices[-10] if prices[-10] != 0 else 0.0
            
            # Volatility
            if len(prices) >= 20:
                returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices)) if prices[i-1] != 0]
                if returns:
                    features['volatility_20'] = np.std(returns[-20:]) if len(returns) >= 20 else np.std(returns)
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting technical features: {e}")
            return {}
    
    async def _extract_pattern_features(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract price pattern features"""
        try:
            prices = market_data.get('prices', [])
            
            if len(prices) < 10:
                return {}
            
            features = {}
            
            # Price range features
            recent_prices = prices[-10:]
            price_range = max(recent_prices) - min(recent_prices)
            features['price_range_10'] = price_range / prices[-1] if prices[-1] != 0 else 0.0
            
            # Support/resistance proximity
            support = min(recent_prices)
            resistance = max(recent_prices)
            features['support_distance'] = (prices[-1] - support) / prices[-1] if prices[-1] != 0 else 0.0
            features['resistance_distance'] = (resistance - prices[-1]) / prices[-1] if prices[-1] != 0 else 0.0
            
            # Trend consistency
            if len(prices) >= 5:
                price_changes = [prices[i] - prices[i-1] for i in range(1, min(6, len(prices)))]
                positive_changes = sum(1 for change in price_changes if change > 0)
                features['trend_consistency'] = positive_changes / len(price_changes) if price_changes else 0.5
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting pattern features: {e}")
            return {}
    
    async def _extract_volume_features(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract volume-based features"""
        try:
            volumes = market_data.get('volumes', [])
            prices = market_data.get('prices', [])
            
            if len(volumes) < 5 or len(prices) < 5:
                return {}
            
            features = {}
            
            # Volume trend
            recent_volumes = volumes[-5:]
            if len(recent_volumes) >= 2:
                volume_trend = (recent_volumes[-1] - recent_volumes[0]) / recent_volumes[0] if recent_volumes[0] != 0 else 0.0
                features['volume_trend_5'] = volume_trend
            
            # Volume-price relationship
            if len(volumes) == len(prices) and len(volumes) >= 3:
                price_changes = [prices[i] - prices[i-1] for i in range(1, min(4, len(prices)))]
                volume_changes = [volumes[i] - volumes[i-1] for i in range(1, min(4, len(volumes)))]
                
                if price_changes and volume_changes:
                    # Simplified volume-price correlation
                    features['volume_price_correlation'] = np.corrcoef(price_changes, volume_changes)[0, 1] if len(price_changes) > 1 else 0.0
            
            # Volume relative to average
            if len(volumes) >= 10:
                avg_volume = np.mean(volumes[-10:])
                features['volume_ratio'] = volumes[-1] / avg_volume if avg_volume != 0 else 1.0
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting volume features: {e}")
            return {}
    
    async def _extract_volatility_features(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract volatility-based features"""
        try:
            prices = market_data.get('prices', [])
            volatilities = market_data.get('volatilities', [])
            
            if len(prices) < 10:
                return {}
            
            features = {}
            
            # Calculate returns
            returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices)) if prices[i-1] != 0]
            
            if returns:
                # Current volatility
                current_volatility = np.std(returns[-10:]) if len(returns) >= 10 else np.std(returns)
                features['current_volatility'] = current_volatility
                
                # Historical volatility
                historical_volatility = np.std(returns)
                features['historical_volatility'] = historical_volatility
                
                # Volatility trend
                if len(returns) >= 20:
                    recent_vol = np.std(returns[-10:])
                    previous_vol = np.std(returns[-20:-10])
                    features['volatility_trend'] = (recent_vol - previous_vol) / previous_vol if previous_vol != 0 else 0.0
                
                # Volatility ratio
                features['volatility_ratio'] = current_volatility / historical_volatility if historical_volatility != 0 else 1.0
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting volatility features: {e}")
            return {}
    
    async def _extract_momentum_features(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract momentum-based features"""
        try:
            prices = market_data.get('prices', [])
            
            if len(prices) < 10:
                return {}
            
            features = {}
            
            # Short-term momentum
            if len(prices) >= 3:
                short_momentum = (prices[-1] - prices[-3]) / prices[-3] if prices[-3] != 0 else 0.0
                features['momentum_3'] = short_momentum
            
            # Medium-term momentum
            if len(prices) >= 10:
                medium_momentum = (prices[-1] - prices[-10]) / prices[-10] if prices[-10] != 0 else 0.0
                features['momentum_10'] = medium_momentum
            
            # Momentum acceleration
            if len(prices) >= 6:
                recent_momentum = (prices[-1] - prices[-3]) / prices[-3] if prices[-3] != 0 else 0.0
                previous_momentum = (prices[-3] - prices[-6]) / prices[-6] if prices[-6] != 0 else 0.0
                features['momentum_acceleration'] = recent_momentum - previous_momentum
            
            # Trend direction and strength
            if len(prices) >= 20:
                # Simple trend calculation
                x = np.arange(len(prices[-20:]))
                y = prices[-20:]
                slope = np.polyfit(x, y, 1)[0]
                features['trend_direction'] = 1 if slope > 0 else -1 if slope < 0 else 0
                features['trend_strength'] = abs(slope) / np.mean(y) if np.mean(y) != 0 else 0.0
            
            # Momentum oscillator (RSI-like)
            if len(prices) >= 14:
                price_changes = [prices[i] - prices[i-1] for i in range(1, len(prices))]
                gains = [change if change > 0 else 0 for change in price_changes[-14:]]
                losses = [-change if change < 0 else 0 for change in price_changes[-14:]]
                
                avg_gain = np.mean(gains) if gains else 0.0
                avg_loss = np.mean(losses) if losses else 0.0
                
                if avg_loss != 0:
                    rs = avg_gain / avg_loss
                    rsi = 100 - (100 / (1 + rs))
                    features['momentum_oscillator'] = rsi / 100.0  # Normalize to 0-1
                else:
                    features['momentum_oscillator'] = 1.0 if avg_gain > 0 else 0.5
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting momentum features: {e}")
            return {}
    
    async def _predict_direction(self, symbol: str, features: Dict[str, float], horizon: int) -> Optional[Dict[str, Any]]:
        """Predict price direction"""
        try:
            # Simple direction prediction based on momentum and trend
            momentum_3 = features.get('momentum_3', 0.0)
            momentum_10 = features.get('momentum_10', 0.0)
            trend_direction = features.get('trend_direction', 0.0)
            momentum_oscillator = features.get('momentum_oscillator', 0.5)
            
            # Combine signals
            direction_score = 0.0
            
            # Momentum signals
            direction_score += momentum_3 * 0.4
            direction_score += momentum_10 * 0.3
            direction_score += trend_direction * 0.2
            
            # Oscillator signal (mean reversion component)
            if momentum_oscillator > 0.8:
                direction_score -= 0.1  # Overbought
            elif momentum_oscillator < 0.2:
                direction_score += 0.1  # Oversold
            
            # Normalize direction
            direction = 1 if direction_score > 0 else -1 if direction_score < 0 else 0
            
            # Calculate confidence
            confidence = min(0.9, abs(direction_score) + 0.3)
            
            return {
                'prediction': direction,
                'confidence': confidence,
                'score': direction_score,
                'models_used': ['momentum_trend_ensemble']
            }
            
        except Exception as e:
            logger.error(f"Error predicting direction: {e}")
            return None
    
    async def _predict_magnitude(self, symbol: str, features: Dict[str, float], horizon: int) -> Optional[Dict[str, Any]]:
        """Predict price movement magnitude"""
        try:
            # Simple magnitude prediction based on volatility and momentum
            current_volatility = features.get('current_volatility', 0.01)
            momentum_acceleration = features.get('momentum_acceleration', 0.0)
            volume_ratio = features.get('volume_ratio', 1.0)
            
            # Base magnitude from volatility
            base_magnitude = current_volatility * np.sqrt(horizon / 60)  # Scale by time horizon
            
            # Adjust for momentum acceleration
            momentum_factor = 1.0 + abs(momentum_acceleration) * 0.5
            
            # Adjust for volume
            volume_factor = min(2.0, max(0.5, volume_ratio))
            
            # Final magnitude
            magnitude = base_magnitude * momentum_factor * volume_factor
            
            # Confidence based on consistency of signals
            confidence = 0.6 + min(0.3, abs(momentum_acceleration) * 10)
            
            return {
                'prediction': magnitude,
                'confidence': confidence,
                'base_magnitude': base_magnitude,
                'models_used': ['volatility_momentum_ensemble']
            }
            
        except Exception as e:
            logger.error(f"Error predicting magnitude: {e}")
            return None

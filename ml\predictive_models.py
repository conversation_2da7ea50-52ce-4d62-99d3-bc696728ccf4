"""
Predictive Models - ML models for price prediction and market forecasting
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
import joblib
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class PredictiveModels:
    """
    Advanced predictive models for price forecasting and market prediction.
    Implements multiple ML algorithms with ensemble capabilities.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Model storage
        self.models: Dict[str, Dict[str, Any]] = {}
        self.scalers: Dict[str, Any] = {}
        self.feature_importance: Dict[str, Dict[str, float]] = {}
        
        # Model configurations
        self.model_configs = {
            'random_forest': {
                'n_estimators': 100,
                'max_depth': 10,
                'min_samples_split': 5,
                'min_samples_leaf': 2,
                'random_state': 42
            },
            'gradient_boosting': {
                'n_estimators': 100,
                'learning_rate': 0.1,
                'max_depth': 6,
                'random_state': 42
            },
            'neural_network': {
                'hidden_layer_sizes': (100, 50),
                'activation': 'relu',
                'solver': 'adam',
                'alpha': 0.001,
                'learning_rate': 'adaptive',
                'max_iter': 1000,
                'random_state': 42
            }
        }
        
        # Performance tracking
        self.model_performance: Dict[str, Dict[str, float]] = {}
        self.prediction_history: Dict[str, List[Dict[str, Any]]] = {}
        
        # State
        self.initialized = False
        
    async def initialize(self):
        """Initialize predictive models"""
        if self.initialized:
            return
            
        logger.info("Initializing Predictive Models...")
        
        # Initialize model storage
        await self._setup_model_storage()
        
        # Initialize feature engineering
        await self._setup_feature_engineering()
        
        self.initialized = True
        logger.info("✓ Predictive Models initialized")
        
    async def _setup_model_storage(self):
        """Setup model storage and management"""
        self.model_types = [
            'random_forest',
            'gradient_boosting', 
            'neural_network'
        ]
        
        for model_type in self.model_types:
            self.models[model_type] = {}
            self.model_performance[model_type] = {}
            self.prediction_history[model_type] = []
            
    async def _setup_feature_engineering(self):
        """Setup feature engineering pipeline"""
        self.feature_functions = {
            'technical_indicators': self._calculate_technical_indicators,
            'price_features': self._calculate_price_features,
            'volume_features': self._calculate_volume_features,
            'volatility_features': self._calculate_volatility_features,
            'momentum_features': self._calculate_momentum_features
        }
        
    def _calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators as features"""
        features = pd.DataFrame(index=data.index)
        
        # Moving averages
        for window in [5, 10, 20, 50]:
            features[f'sma_{window}'] = data['close'].rolling(window=window).mean()
            features[f'ema_{window}'] = data['close'].ewm(span=window).mean()
            
        # RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        features['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        ema12 = data['close'].ewm(span=12).mean()
        ema26 = data['close'].ewm(span=26).mean()
        features['macd'] = ema12 - ema26
        features['macd_signal'] = features['macd'].ewm(span=9).mean()
        features['macd_histogram'] = features['macd'] - features['macd_signal']
        
        # Bollinger Bands
        sma20 = data['close'].rolling(window=20).mean()
        std20 = data['close'].rolling(window=20).std()
        features['bb_upper'] = sma20 + (std20 * 2)
        features['bb_lower'] = sma20 - (std20 * 2)
        features['bb_position'] = (data['close'] - features['bb_lower']) / (features['bb_upper'] - features['bb_lower'])
        
        return features
        
    def _calculate_price_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate price-based features"""
        features = pd.DataFrame(index=data.index)
        
        # Price changes
        features['price_change'] = data['close'].pct_change()
        features['price_change_abs'] = features['price_change'].abs()
        
        # High-low features
        features['hl_ratio'] = data['high'] / data['low']
        features['oc_ratio'] = data['open'] / data['close']
        
        # Price position within range
        features['price_position'] = (data['close'] - data['low']) / (data['high'] - data['low'])
        
        # Gaps
        features['gap'] = (data['open'] - data['close'].shift(1)) / data['close'].shift(1)
        
        return features
        
    def _calculate_volume_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate volume-based features"""
        features = pd.DataFrame(index=data.index)
        
        # Volume changes
        features['volume_change'] = data['volume'].pct_change()
        features['volume_sma'] = data['volume'].rolling(window=20).mean()
        features['volume_ratio'] = data['volume'] / features['volume_sma']
        
        # Volume-price relationship
        features['vwap'] = (data['volume'] * (data['high'] + data['low'] + data['close']) / 3).cumsum() / data['volume'].cumsum()
        features['price_volume_trend'] = ((data['close'] - data['close'].shift(1)) / data['close'].shift(1)) * data['volume']
        
        return features
        
    def _calculate_volatility_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate volatility-based features"""
        features = pd.DataFrame(index=data.index)
        
        # Historical volatility
        returns = data['close'].pct_change()
        for window in [5, 10, 20]:
            features[f'volatility_{window}'] = returns.rolling(window=window).std()
            
        # True Range and ATR
        high_low = data['high'] - data['low']
        high_close = np.abs(data['high'] - data['close'].shift(1))
        low_close = np.abs(data['low'] - data['close'].shift(1))
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        features['atr'] = true_range.rolling(window=14).mean()
        
        return features
        
    def _calculate_momentum_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate momentum-based features"""
        features = pd.DataFrame(index=data.index)
        
        # Rate of change
        for period in [5, 10, 20]:
            features[f'roc_{period}'] = data['close'].pct_change(periods=period)
            
        # Momentum
        features['momentum'] = data['close'] - data['close'].shift(10)
        
        # Stochastic oscillator
        low_min = data['low'].rolling(window=14).min()
        high_max = data['high'].rolling(window=14).max()
        features['stoch_k'] = 100 * (data['close'] - low_min) / (high_max - low_min)
        features['stoch_d'] = features['stoch_k'].rolling(window=3).mean()
        
        return features
        
    async def prepare_features(self, data: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """Prepare features for model training/prediction"""
        all_features = pd.DataFrame(index=data.index)
        
        # Calculate all feature types
        for feature_type, calc_func in self.feature_functions.items():
            try:
                features = calc_func(data)
                all_features = pd.concat([all_features, features], axis=1)
            except Exception as e:
                logger.warning(f"Error calculating {feature_type} for {symbol}: {e}")
                
        # Add lagged features
        for lag in [1, 2, 3, 5]:
            lagged_features = all_features.shift(lag)
            lagged_features.columns = [f"{col}_lag_{lag}" for col in lagged_features.columns]
            all_features = pd.concat([all_features, lagged_features], axis=1)
            
        # Drop NaN values
        all_features = all_features.dropna()
        
        return all_features
        
    async def train_model(self, symbol: str, data: pd.DataFrame, target_column: str = 'close',
                         prediction_horizon: int = 1, model_type: str = 'random_forest') -> Dict[str, Any]:
        """Train a predictive model for a symbol"""
        try:
            # Prepare features
            features = await self.prepare_features(data, symbol)
            
            if len(features) < 100:  # Minimum data requirement
                return {'success': False, 'error': 'Insufficient data for training'}
                
            # Prepare target variable (future returns)
            target = data[target_column].pct_change(periods=prediction_horizon).shift(-prediction_horizon)
            
            # Align features and target
            aligned_data = pd.concat([features, target.rename('target')], axis=1).dropna()
            
            if len(aligned_data) < 50:
                return {'success': False, 'error': 'Insufficient aligned data'}
                
            X = aligned_data.drop('target', axis=1)
            y = aligned_data['target']
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, shuffle=False
            )
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Train model
            if model_type == 'random_forest':
                model = RandomForestRegressor(**self.model_configs['random_forest'])
            elif model_type == 'gradient_boosting':
                model = GradientBoostingRegressor(**self.model_configs['gradient_boosting'])
            elif model_type == 'neural_network':
                model = MLPRegressor(**self.model_configs['neural_network'])
            else:
                return {'success': False, 'error': f'Unknown model type: {model_type}'}
                
            model.fit(X_train_scaled, y_train)
            
            # Evaluate model
            y_pred = model.predict(X_test_scaled)
            
            performance = {
                'mse': mean_squared_error(y_test, y_pred),
                'mae': mean_absolute_error(y_test, y_pred),
                'r2': r2_score(y_test, y_pred),
                'train_size': len(X_train),
                'test_size': len(X_test)
            }
            
            # Cross-validation
            cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5, scoring='r2')
            performance['cv_mean'] = cv_scores.mean()
            performance['cv_std'] = cv_scores.std()
            
            # Store model and scaler
            model_key = f"{symbol}_{model_type}_{prediction_horizon}"
            self.models[model_type][model_key] = model
            self.scalers[model_key] = scaler
            self.model_performance[model_type][model_key] = performance
            
            # Feature importance (for tree-based models)
            if hasattr(model, 'feature_importances_'):
                self.feature_importance[model_key] = dict(zip(X.columns, model.feature_importances_))
                
            logger.info(f"✓ Trained {model_type} model for {symbol} (R²: {performance['r2']:.3f})")
            
            return {
                'success': True,
                'model_key': model_key,
                'performance': performance,
                'feature_count': len(X.columns)
            }
            
        except Exception as e:
            logger.error(f"Error training model for {symbol}: {e}")
            return {'success': False, 'error': str(e)}
            
    async def predict(self, symbol: str, data: pd.DataFrame, model_type: str = 'random_forest',
                     prediction_horizon: int = 1) -> Dict[str, Any]:
        """Make predictions using trained model"""
        try:
            model_key = f"{symbol}_{model_type}_{prediction_horizon}"
            
            if model_key not in self.models[model_type]:
                return {'success': False, 'error': 'Model not found'}
                
            # Prepare features
            features = await self.prepare_features(data, symbol)
            
            if len(features) == 0:
                return {'success': False, 'error': 'No features available'}
                
            # Get latest features
            latest_features = features.iloc[-1:].values
            
            # Scale features
            scaler = self.scalers[model_key]
            latest_features_scaled = scaler.transform(latest_features)
            
            # Make prediction
            model = self.models[model_type][model_key]
            prediction = model.predict(latest_features_scaled)[0]
            
            # Calculate confidence (for ensemble methods)
            confidence = self._calculate_prediction_confidence(model, latest_features_scaled)
            
            # Store prediction
            prediction_record = {
                'symbol': symbol,
                'prediction': prediction,
                'confidence': confidence,
                'model_type': model_type,
                'timestamp': pd.Timestamp.now(),
                'features_used': len(features.columns)
            }
            
            self.prediction_history[model_type].append(prediction_record)
            
            return {
                'success': True,
                'prediction': prediction,
                'confidence': confidence,
                'model_performance': self.model_performance[model_type].get(model_key, {})
            }
            
        except Exception as e:
            logger.error(f"Error making prediction for {symbol}: {e}")
            return {'success': False, 'error': str(e)}
            
    def _calculate_prediction_confidence(self, model, features) -> float:
        """Calculate prediction confidence"""
        try:
            if hasattr(model, 'predict_proba'):
                # For classification models
                probabilities = model.predict_proba(features)[0]
                return max(probabilities)
            elif hasattr(model, 'estimators_'):
                # For ensemble models, use prediction variance
                predictions = [estimator.predict(features)[0] for estimator in model.estimators_]
                variance = np.var(predictions)
                # Convert variance to confidence (inverse relationship)
                confidence = 1.0 / (1.0 + variance)
                return min(confidence, 1.0)
            else:
                # Default confidence for other models
                return 0.7
        except:
            return 0.5
            
    async def get_ensemble_prediction(self, symbol: str, data: pd.DataFrame,
                                    prediction_horizon: int = 1) -> Dict[str, Any]:
        """Get ensemble prediction from multiple models"""
        predictions = []
        confidences = []
        
        for model_type in self.model_types:
            result = await self.predict(symbol, data, model_type, prediction_horizon)
            if result['success']:
                predictions.append(result['prediction'])
                confidences.append(result['confidence'])
                
        if not predictions:
            return {'success': False, 'error': 'No models available for prediction'}
            
        # Weighted ensemble prediction
        weights = np.array(confidences) / sum(confidences)
        ensemble_prediction = np.average(predictions, weights=weights)
        ensemble_confidence = np.mean(confidences)
        
        return {
            'success': True,
            'ensemble_prediction': ensemble_prediction,
            'ensemble_confidence': ensemble_confidence,
            'individual_predictions': predictions,
            'individual_confidences': confidences,
            'models_used': len(predictions)
        }
        
    async def get_model_performance(self, symbol: str = None) -> Dict[str, Any]:
        """Get model performance metrics"""
        if symbol:
            # Performance for specific symbol
            performance = {}
            for model_type in self.model_types:
                symbol_models = {k: v for k, v in self.model_performance[model_type].items() 
                               if k.startswith(symbol)}
                if symbol_models:
                    performance[model_type] = symbol_models
            return performance
        else:
            # Overall performance
            return self.model_performance.copy()
            
    async def retrain_models(self, symbol: str, data: pd.DataFrame) -> Dict[str, Any]:
        """Retrain all models for a symbol"""
        results = {}
        
        for model_type in self.model_types:
            result = await self.train_model(symbol, data, model_type=model_type)
            results[model_type] = result
            
        return results

    async def save_models(self, filepath: str):
        """Save trained models to disk"""
        try:
            model_data = {
                'models': self.models,
                'scalers': self.scalers,
                'model_performance': self.model_performance,
                'feature_importance': self.feature_importance
            }
            joblib.dump(model_data, filepath)
            logger.info(f"✓ Models saved to {filepath}")
        except Exception as e:
            logger.error(f"Error saving models: {e}")

    async def load_models(self, filepath: str):
        """Load trained models from disk"""
        try:
            model_data = joblib.load(filepath)
            self.models = model_data['models']
            self.scalers = model_data['scalers']
            self.model_performance = model_data['model_performance']
            self.feature_importance = model_data['feature_importance']
            logger.info(f"✓ Models loaded from {filepath}")
        except Exception as e:
            logger.error(f"Error loading models: {e}")

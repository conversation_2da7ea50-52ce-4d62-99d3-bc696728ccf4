"""
ClickHouse Manager - Handles ClickHouse operations for analytics and time-series data
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from datetime import datetime, timezone
import aiohttp
import json

logger = logging.getLogger(__name__)


@dataclass
class AnalyticsQuery:
    """Analytics query result"""
    success: bool
    data: List[Dict[str, Any]]
    rows_processed: int
    execution_time_ms: float
    query: str
    error_message: Optional[str] = None


class ClickHouseManager:
    """
    Manages ClickHouse operations for analytics and time-series data.
    
    Features:
    - High-performance analytics queries
    - Time-series data storage and retrieval
    - Real-time metrics aggregation
    - Performance monitoring and optimization
    - Data compression and partitioning
    - Batch data insertion
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Connection configuration
        self.host = config.get('host', 'localhost')
        self.port = config.get('port', 8123)
        self.database = config.get('database', 'trading_analytics')
        self.username = config.get('username', 'default')
        self.password = config.get('password', '')
        
        # HTTP client for ClickHouse
        self.session: Optional[aiohttp.ClientSession] = None
        self.base_url = f"http://{self.host}:{self.port}"
        
        # Query configuration
        self.query_timeout = config.get('query_timeout', 30)
        self.batch_size = config.get('batch_size', 10000)
        
        # Statistics
        self.query_stats = {
            'total_queries': 0,
            'successful_queries': 0,
            'failed_queries': 0,
            'total_rows_processed': 0,
            'average_execution_time': 0.0,
            'last_query_time': None
        }
        
        # State
        self.initialized = False
        self.running = False
    
    async def initialize(self) -> bool:
        """Initialize ClickHouse manager"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing ClickHouse Manager...")
            
            # Create HTTP session
            await self._create_session()
            
            # Create database and tables
            await self._create_database_and_tables()
            
            self.initialized = True
            logger.info("✓ ClickHouse Manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize ClickHouse Manager: {e}")
            return False
    
    async def start(self) -> bool:
        """Start ClickHouse manager"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        self.running = True
        logger.info("✓ ClickHouse Manager started")
        return True
    
    async def stop(self) -> bool:
        """Stop ClickHouse manager"""
        if self.session:
            await self.session.close()
        
        self.running = False
        logger.info("✓ ClickHouse Manager stopped")
        return True
    
    async def health_check(self) -> bool:
        """Check ClickHouse health"""
        try:
            if not self.session:
                return False
            
            async with self.session.get(f"{self.base_url}/ping") as response:
                return response.status == 200
                
        except Exception as e:
            logger.error(f"ClickHouse health check failed: {e}")
            return False
    
    # Performance Metrics Operations
    
    async def store_performance_metrics(self, metrics: List[Dict[str, Any]]) -> bool:
        """Store performance metrics"""
        try:
            if not metrics:
                return True
            
            # Prepare data for insertion
            values = []
            for metric in metrics:
                timestamp = metric.get('timestamp', time.time())
                if isinstance(timestamp, (int, float)):
                    timestamp = datetime.fromtimestamp(timestamp, timezone.utc)
                
                values.append({
                    'timestamp': timestamp,
                    'component': metric.get('component', ''),
                    'metric_name': metric.get('metric_name', ''),
                    'metric_value': float(metric.get('metric_value', 0.0)),
                    'metadata': json.dumps(metric.get('metadata', {}))
                })
            
            # Insert in batches
            for i in range(0, len(values), self.batch_size):
                batch = values[i:i + self.batch_size]
                success = await self._insert_performance_metrics_batch(batch)
                if not success:
                    return False
            
            logger.debug(f"Stored {len(metrics)} performance metrics")
            return True
            
        except Exception as e:
            logger.error(f"Error storing performance metrics: {e}")
            return False
    
    async def store_market_data(self, market_data: List[Dict[str, Any]]) -> bool:
        """Store market data"""
        try:
            if not market_data:
                return True
            
            # Prepare data for insertion
            values = []
            for data in market_data:
                timestamp = data.get('timestamp', time.time())
                if isinstance(timestamp, (int, float)):
                    timestamp = datetime.fromtimestamp(timestamp, timezone.utc)
                
                values.append({
                    'timestamp': timestamp,
                    'symbol': data.get('symbol', ''),
                    'price': float(data.get('price', 0.0)),
                    'volume': float(data.get('volume', 0.0)),
                    'bid': float(data.get('bid', 0.0)),
                    'ask': float(data.get('ask', 0.0)),
                    'metadata': json.dumps(data.get('metadata', {}))
                })
            
            # Insert in batches
            for i in range(0, len(values), self.batch_size):
                batch = values[i:i + self.batch_size]
                success = await self._insert_market_data_batch(batch)
                if not success:
                    return False
            
            logger.debug(f"Stored {len(market_data)} market data points")
            return True
            
        except Exception as e:
            logger.error(f"Error storing market data: {e}")
            return False
    
    async def store_trade_executions(self, executions: List[Dict[str, Any]]) -> bool:
        """Store trade execution data"""
        try:
            if not executions:
                return True
            
            # Prepare data for insertion
            values = []
            for execution in executions:
                timestamp = execution.get('timestamp', time.time())
                if isinstance(timestamp, (int, float)):
                    timestamp = datetime.fromtimestamp(timestamp, timezone.utc)
                
                values.append({
                    'timestamp': timestamp,
                    'trade_id': execution.get('trade_id', ''),
                    'strategy_id': execution.get('strategy_id', ''),
                    'symbol': execution.get('symbol', ''),
                    'side': execution.get('side', ''),
                    'quantity': float(execution.get('quantity', 0.0)),
                    'price': float(execution.get('price', 0.0)),
                    'commission': float(execution.get('commission', 0.0)),
                    'slippage': float(execution.get('slippage', 0.0)),
                    'execution_time_ms': float(execution.get('execution_time_ms', 0.0)),
                    'metadata': json.dumps(execution.get('metadata', {}))
                })
            
            # Insert in batches
            for i in range(0, len(values), self.batch_size):
                batch = values[i:i + self.batch_size]
                success = await self._insert_trade_executions_batch(batch)
                if not success:
                    return False
            
            logger.debug(f"Stored {len(executions)} trade executions")
            return True
            
        except Exception as e:
            logger.error(f"Error storing trade executions: {e}")
            return False
    
    # Analytics Queries
    
    async def execute_query(self, query: str, params: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Execute analytics query"""
        try:
            start_time = time.time()
            
            # Format query with parameters
            if params:
                formatted_query = query.format(**params)
            else:
                formatted_query = query
            
            # Execute query
            result = await self._execute_clickhouse_query(formatted_query)
            
            execution_time = (time.time() - start_time) * 1000
            
            if result.success:
                self._update_query_stats(True, execution_time, len(result.data))
                return result.data
            else:
                self._update_query_stats(False, execution_time, 0)
                logger.error(f"Query failed: {result.error_message}")
                return []
                
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            return []
    
    async def get_performance_summary(self, component: str = None, 
                                    hours: int = 24) -> Dict[str, Any]:
        """Get performance summary"""
        try:
            where_clause = ""
            if component:
                where_clause = f"AND component = '{component}'"
            
            query = f"""
                SELECT 
                    component,
                    metric_name,
                    avg(metric_value) as avg_value,
                    min(metric_value) as min_value,
                    max(metric_value) as max_value,
                    count() as data_points
                FROM performance_metrics 
                WHERE timestamp >= now() - INTERVAL {hours} HOUR
                {where_clause}
                GROUP BY component, metric_name
                ORDER BY component, metric_name
            """
            
            results = await self.execute_query(query)
            
            # Organize results by component
            summary = {}
            for row in results:
                comp = row['component']
                if comp not in summary:
                    summary[comp] = {}
                
                summary[comp][row['metric_name']] = {
                    'avg': row['avg_value'],
                    'min': row['min_value'],
                    'max': row['max_value'],
                    'data_points': row['data_points']
                }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return {}
    
    async def get_trading_analytics(self, symbol: str = None, 
                                  strategy_id: str = None, 
                                  hours: int = 24) -> Dict[str, Any]:
        """Get trading analytics"""
        try:
            conditions = [f"timestamp >= now() - INTERVAL {hours} HOUR"]
            
            if symbol:
                conditions.append(f"symbol = '{symbol}'")
            if strategy_id:
                conditions.append(f"strategy_id = '{strategy_id}'")
            
            where_clause = " AND ".join(conditions)
            
            query = f"""
                SELECT 
                    symbol,
                    strategy_id,
                    count() as total_trades,
                    sum(quantity) as total_volume,
                    avg(price) as avg_price,
                    sum(commission) as total_commission,
                    avg(slippage) as avg_slippage,
                    avg(execution_time_ms) as avg_execution_time
                FROM trade_executions 
                WHERE {where_clause}
                GROUP BY symbol, strategy_id
                ORDER BY total_trades DESC
            """
            
            results = await self.execute_query(query)
            
            return {
                'trading_summary': results,
                'total_strategies': len(set(r['strategy_id'] for r in results)),
                'total_symbols': len(set(r['symbol'] for r in results)),
                'period_hours': hours
            }
            
        except Exception as e:
            logger.error(f"Error getting trading analytics: {e}")
            return {}
    
    async def get_market_data_analytics(self, symbol: str, hours: int = 24) -> Dict[str, Any]:
        """Get market data analytics"""
        try:
            query = f"""
                SELECT 
                    toStartOfHour(timestamp) as hour,
                    avg(price) as avg_price,
                    min(price) as min_price,
                    max(price) as max_price,
                    sum(volume) as total_volume,
                    count() as data_points
                FROM market_data 
                WHERE symbol = '{symbol}' 
                AND timestamp >= now() - INTERVAL {hours} HOUR
                GROUP BY hour
                ORDER BY hour
            """
            
            results = await self.execute_query(query)
            
            return {
                'symbol': symbol,
                'hourly_data': results,
                'period_hours': hours
            }
            
        except Exception as e:
            logger.error(f"Error getting market data analytics for {symbol}: {e}")
            return {}
    
    # Statistics and Monitoring
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get ClickHouse statistics"""
        try:
            stats = self.query_stats.copy()
            
            # Get table sizes
            table_stats_query = """
                SELECT 
                    table,
                    sum(rows) as total_rows,
                    sum(bytes) as total_bytes
                FROM system.parts 
                WHERE database = '{}'
                GROUP BY table
            """.format(self.database)
            
            table_results = await self.execute_query(table_stats_query)
            
            stats['table_stats'] = {
                row['table']: {
                    'rows': row['total_rows'],
                    'bytes': row['total_bytes']
                }
                for row in table_results
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting ClickHouse stats: {e}")
            return self.query_stats.copy()
    
    # Private methods
    
    async def _create_session(self):
        """Create HTTP session for ClickHouse"""
        try:
            timeout = aiohttp.ClientTimeout(total=self.query_timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            logger.info(f"✓ ClickHouse HTTP session created (host: {self.host}:{self.port})")
            
        except Exception as e:
            logger.error(f"Failed to create ClickHouse session: {e}")
            raise
    
    async def _create_database_and_tables(self):
        """Create database and tables"""
        try:
            # Create database
            create_db_query = f"CREATE DATABASE IF NOT EXISTS {self.database}"
            await self._execute_clickhouse_query(create_db_query)
            
            # Create tables
            tables = [
                # Performance metrics table
                f"""
                CREATE TABLE IF NOT EXISTS {self.database}.performance_metrics (
                    timestamp DateTime64(3),
                    component String,
                    metric_name String,
                    metric_value Float64,
                    metadata String
                ) ENGINE = MergeTree()
                PARTITION BY toYYYYMM(timestamp)
                ORDER BY (component, metric_name, timestamp)
                """,
                
                # Market data table
                f"""
                CREATE TABLE IF NOT EXISTS {self.database}.market_data (
                    timestamp DateTime64(3),
                    symbol String,
                    price Float64,
                    volume Float64,
                    bid Float64,
                    ask Float64,
                    metadata String
                ) ENGINE = MergeTree()
                PARTITION BY toYYYYMM(timestamp)
                ORDER BY (symbol, timestamp)
                """,
                
                # Trade executions table
                f"""
                CREATE TABLE IF NOT EXISTS {self.database}.trade_executions (
                    timestamp DateTime64(3),
                    trade_id String,
                    strategy_id String,
                    symbol String,
                    side String,
                    quantity Float64,
                    price Float64,
                    commission Float64,
                    slippage Float64,
                    execution_time_ms Float64,
                    metadata String
                ) ENGINE = MergeTree()
                PARTITION BY toYYYYMM(timestamp)
                ORDER BY (strategy_id, symbol, timestamp)
                """
            ]
            
            for table_query in tables:
                await self._execute_clickhouse_query(table_query)
            
            logger.info("✓ ClickHouse database and tables created")
            
        except Exception as e:
            logger.error(f"Error creating ClickHouse database and tables: {e}")
            raise
    
    async def _execute_clickhouse_query(self, query: str) -> AnalyticsQuery:
        """Execute ClickHouse query"""
        try:
            start_time = time.time()
            
            params = {
                'query': query,
                'format': 'JSONEachRow'
            }
            
            if self.username:
                params['user'] = self.username
            if self.password:
                params['password'] = self.password
            
            async with self.session.post(self.base_url, data=params) as response:
                execution_time = (time.time() - start_time) * 1000
                
                if response.status == 200:
                    response_text = await response.text()
                    
                    # Parse JSON response
                    data = []
                    if response_text.strip():
                        for line in response_text.strip().split('\n'):
                            if line.strip():
                                data.append(json.loads(line))
                    
                    return AnalyticsQuery(
                        success=True,
                        data=data,
                        rows_processed=len(data),
                        execution_time_ms=execution_time,
                        query=query
                    )
                else:
                    error_text = await response.text()
                    return AnalyticsQuery(
                        success=False,
                        data=[],
                        rows_processed=0,
                        execution_time_ms=execution_time,
                        query=query,
                        error_message=f"HTTP {response.status}: {error_text}"
                    )
                    
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            return AnalyticsQuery(
                success=False,
                data=[],
                rows_processed=0,
                execution_time_ms=execution_time,
                query=query,
                error_message=str(e)
            )
    
    async def _insert_performance_metrics_batch(self, batch: List[Dict[str, Any]]) -> bool:
        """Insert performance metrics batch"""
        try:
            values = []
            for item in batch:
                values.append(f"('{item['timestamp']}', '{item['component']}', '{item['metric_name']}', {item['metric_value']}, '{item['metadata']}')")
            
            query = f"""
                INSERT INTO {self.database}.performance_metrics 
                (timestamp, component, metric_name, metric_value, metadata) 
                VALUES {', '.join(values)}
            """
            
            result = await self._execute_clickhouse_query(query)
            return result.success
            
        except Exception as e:
            logger.error(f"Error inserting performance metrics batch: {e}")
            return False
    
    async def _insert_market_data_batch(self, batch: List[Dict[str, Any]]) -> bool:
        """Insert market data batch"""
        try:
            values = []
            for item in batch:
                values.append(f"('{item['timestamp']}', '{item['symbol']}', {item['price']}, {item['volume']}, {item['bid']}, {item['ask']}, '{item['metadata']}')")
            
            query = f"""
                INSERT INTO {self.database}.market_data 
                (timestamp, symbol, price, volume, bid, ask, metadata) 
                VALUES {', '.join(values)}
            """
            
            result = await self._execute_clickhouse_query(query)
            return result.success
            
        except Exception as e:
            logger.error(f"Error inserting market data batch: {e}")
            return False
    
    async def _insert_trade_executions_batch(self, batch: List[Dict[str, Any]]) -> bool:
        """Insert trade executions batch"""
        try:
            values = []
            for item in batch:
                values.append(f"('{item['timestamp']}', '{item['trade_id']}', '{item['strategy_id']}', '{item['symbol']}', '{item['side']}', {item['quantity']}, {item['price']}, {item['commission']}, {item['slippage']}, {item['execution_time_ms']}, '{item['metadata']}')")
            
            query = f"""
                INSERT INTO {self.database}.trade_executions 
                (timestamp, trade_id, strategy_id, symbol, side, quantity, price, commission, slippage, execution_time_ms, metadata) 
                VALUES {', '.join(values)}
            """
            
            result = await self._execute_clickhouse_query(query)
            return result.success
            
        except Exception as e:
            logger.error(f"Error inserting trade executions batch: {e}")
            return False
    
    def _update_query_stats(self, success: bool, execution_time: float, rows_processed: int):
        """Update query statistics"""
        self.query_stats['total_queries'] += 1
        
        if success:
            self.query_stats['successful_queries'] += 1
            self.query_stats['total_rows_processed'] += rows_processed
        else:
            self.query_stats['failed_queries'] += 1
        
        # Update average execution time
        total_queries = self.query_stats['total_queries']
        current_avg = self.query_stats['average_execution_time']
        self.query_stats['average_execution_time'] = (
            (current_avg * (total_queries - 1) + execution_time) / total_queries
        )
        
        self.query_stats['last_query_time'] = time.time()

"""
Migration Manager - Handles database schema migrations and versioning
"""

import asyncio
import logging
import time
import os
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from pathlib import Path
from datetime import datetime, timezone

logger = logging.getLogger(__name__)


@dataclass
class Migration:
    """Migration data structure"""
    version: str
    name: str
    description: str
    sql_up: str
    sql_down: str
    checksum: str
    applied_at: Optional[datetime] = None
    execution_time_ms: Optional[float] = None


@dataclass
class MigrationResult:
    """Migration execution result"""
    success: bool
    version: str
    execution_time_ms: float
    error_message: Optional[str] = None


class MigrationManager:
    """
    Manages database schema migrations and versioning.
    
    Features:
    - Schema version tracking
    - Forward and backward migrations
    - Migration validation and checksums
    - Rollback capabilities
    - Cross-database migration support
    - Migration dependency management
    """
    
    def __init__(self, config: Dict[str, Any], postgres_manager=None, clickhouse_manager=None):
        self.config = config
        self.postgres_manager = postgres_manager
        self.clickhouse_manager = clickhouse_manager
        
        # Migration configuration
        self.migrations_path = Path(config.get('migrations_path', 'database/migrations'))
        self.auto_migrate = config.get('auto_migrate', False)
        self.backup_before_migration = config.get('backup_before_migration', True)
        
        # Migration tracking
        self.available_migrations: Dict[str, Migration] = {}
        self.applied_migrations: Dict[str, Migration] = {}
        self.current_version: Optional[str] = None
        
        # State
        self.initialized = False
        
        # Ensure migrations directory exists
        self.migrations_path.mkdir(parents=True, exist_ok=True)
    
    async def initialize(self) -> bool:
        """Initialize migration manager"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Migration Manager...")
            
            # Create migration tracking tables
            await self._create_migration_tables()
            
            # Load available migrations
            await self._load_available_migrations()
            
            # Load applied migrations
            await self._load_applied_migrations()
            
            # Determine current version
            await self._determine_current_version()
            
            self.initialized = True
            logger.info(f"✓ Migration Manager initialized (current version: {self.current_version})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Migration Manager: {e}")
            return False
    
    async def run_migrations(self, target_version: str = None) -> bool:
        """Run migrations to target version (or latest if not specified)"""
        try:
            if not self.initialized:
                await self.initialize()
            
            # Determine target version
            if target_version is None:
                target_version = self._get_latest_version()
            
            if target_version is None:
                logger.info("No migrations available")
                return True
            
            logger.info(f"Running migrations from {self.current_version} to {target_version}")
            
            # Get migrations to apply
            migrations_to_apply = await self._get_migrations_to_apply(target_version)
            
            if not migrations_to_apply:
                logger.info("No migrations to apply")
                return True
            
            # Apply migrations
            success = await self._apply_migrations(migrations_to_apply)
            
            if success:
                logger.info(f"✓ Successfully migrated to version {target_version}")
                self.current_version = target_version
            else:
                logger.error("Migration failed")
            
            return success
            
        except Exception as e:
            logger.error(f"Error running migrations: {e}")
            return False
    
    async def rollback_migration(self, target_version: str) -> bool:
        """Rollback to target version"""
        try:
            if not self.initialized:
                await self.initialize()
            
            logger.info(f"Rolling back from {self.current_version} to {target_version}")
            
            # Get migrations to rollback
            migrations_to_rollback = await self._get_migrations_to_rollback(target_version)
            
            if not migrations_to_rollback:
                logger.info("No migrations to rollback")
                return True
            
            # Rollback migrations
            success = await self._rollback_migrations(migrations_to_rollback)
            
            if success:
                logger.info(f"✓ Successfully rolled back to version {target_version}")
                self.current_version = target_version
            else:
                logger.error("Rollback failed")
            
            return success
            
        except Exception as e:
            logger.error(f"Error rolling back migrations: {e}")
            return False
    
    async def create_migration(self, name: str, description: str = "") -> str:
        """Create a new migration file"""
        try:
            # Generate version (timestamp-based)
            version = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Create migration content
            migration_content = f"""-- Migration: {name}
-- Description: {description}
-- Version: {version}
-- Created: {datetime.now().isoformat()}

-- UP Migration
-- Add your schema changes here

-- Example:
-- CREATE TABLE example_table (
--     id SERIAL PRIMARY KEY,
--     name VARCHAR(255) NOT NULL,
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- );

-- DOWN Migration (Rollback)
-- Add rollback statements here

-- Example:
-- DROP TABLE IF EXISTS example_table;
"""
            
            # Create migration file
            migration_file = self.migrations_path / f"{version}_{name}.sql"
            with open(migration_file, 'w') as f:
                f.write(migration_content)
            
            logger.info(f"✓ Created migration file: {migration_file}")
            return version
            
        except Exception as e:
            logger.error(f"Error creating migration: {e}")
            raise
    
    async def get_migration_status(self) -> Dict[str, Any]:
        """Get migration status"""
        try:
            if not self.initialized:
                await self.initialize()
            
            pending_migrations = []
            for version, migration in self.available_migrations.items():
                if version not in self.applied_migrations:
                    pending_migrations.append({
                        'version': version,
                        'name': migration.name,
                        'description': migration.description
                    })
            
            return {
                'current_version': self.current_version,
                'latest_version': self._get_latest_version(),
                'total_migrations': len(self.available_migrations),
                'applied_migrations': len(self.applied_migrations),
                'pending_migrations': len(pending_migrations),
                'pending_migration_list': pending_migrations,
                'migration_path': str(self.migrations_path)
            }
            
        except Exception as e:
            logger.error(f"Error getting migration status: {e}")
            return {}
    
    async def validate_migrations(self) -> Dict[str, Any]:
        """Validate all migrations"""
        try:
            validation_results = {
                'valid': True,
                'errors': [],
                'warnings': [],
                'migration_count': len(self.available_migrations)
            }
            
            # Check for duplicate versions
            versions = list(self.available_migrations.keys())
            if len(versions) != len(set(versions)):
                validation_results['valid'] = False
                validation_results['errors'].append("Duplicate migration versions found")
            
            # Validate each migration
            for version, migration in self.available_migrations.items():
                # Check if migration has both UP and DOWN scripts
                if not migration.sql_up.strip():
                    validation_results['warnings'].append(f"Migration {version} has empty UP script")
                
                if not migration.sql_down.strip():
                    validation_results['warnings'].append(f"Migration {version} has empty DOWN script")
                
                # Validate checksum for applied migrations
                if version in self.applied_migrations:
                    applied_migration = self.applied_migrations[version]
                    if migration.checksum != applied_migration.checksum:
                        validation_results['valid'] = False
                        validation_results['errors'].append(f"Migration {version} checksum mismatch")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Error validating migrations: {e}")
            return {'valid': False, 'errors': [str(e)]}
    
    # Private methods
    
    async def _create_migration_tables(self):
        """Create migration tracking tables"""
        try:
            # PostgreSQL migration table
            if self.postgres_manager:
                postgres_migration_table = """
                    CREATE TABLE IF NOT EXISTS schema_migrations (
                        version VARCHAR(255) PRIMARY KEY,
                        name VARCHAR(255) NOT NULL,
                        description TEXT,
                        checksum VARCHAR(64) NOT NULL,
                        applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        execution_time_ms FLOAT
                    )
                """
                
                await self.postgres_manager._execute_query(postgres_migration_table)
                logger.debug("✓ PostgreSQL migration table created")
            
            # ClickHouse migration table
            if self.clickhouse_manager:
                clickhouse_migration_table = f"""
                    CREATE TABLE IF NOT EXISTS {self.clickhouse_manager.database}.schema_migrations (
                        version String,
                        name String,
                        description String,
                        checksum String,
                        applied_at DateTime64(3),
                        execution_time_ms Float64
                    ) ENGINE = MergeTree()
                    ORDER BY version
                """
                
                await self.clickhouse_manager._execute_clickhouse_query(clickhouse_migration_table)
                logger.debug("✓ ClickHouse migration table created")
                
        except Exception as e:
            logger.error(f"Error creating migration tables: {e}")
            raise
    
    async def _load_available_migrations(self):
        """Load available migrations from files"""
        try:
            self.available_migrations = {}
            
            # Scan migration files
            for migration_file in sorted(self.migrations_path.glob("*.sql")):
                try:
                    migration = await self._parse_migration_file(migration_file)
                    if migration:
                        self.available_migrations[migration.version] = migration
                except Exception as e:
                    logger.warning(f"Failed to parse migration file {migration_file}: {e}")
            
            logger.debug(f"Loaded {len(self.available_migrations)} available migrations")
            
        except Exception as e:
            logger.error(f"Error loading available migrations: {e}")
            raise
    
    async def _parse_migration_file(self, migration_file: Path) -> Optional[Migration]:
        """Parse migration file"""
        try:
            with open(migration_file, 'r') as f:
                content = f.read()
            
            # Extract metadata from comments
            lines = content.split('\n')
            name = ""
            description = ""
            version = ""
            
            for line in lines:
                line = line.strip()
                if line.startswith('-- Migration:'):
                    name = line.replace('-- Migration:', '').strip()
                elif line.startswith('-- Description:'):
                    description = line.replace('-- Description:', '').strip()
                elif line.startswith('-- Version:'):
                    version = line.replace('-- Version:', '').strip()
            
            # If version not found in file, extract from filename
            if not version:
                filename = migration_file.stem
                if '_' in filename:
                    version = filename.split('_')[0]
                else:
                    version = filename
            
            # Split UP and DOWN migrations
            up_start = content.find('-- UP Migration')
            down_start = content.find('-- DOWN Migration')
            
            if up_start == -1 or down_start == -1:
                logger.warning(f"Migration file {migration_file} missing UP or DOWN sections")
                return None
            
            sql_up = content[up_start:down_start].replace('-- UP Migration', '').strip()
            sql_down = content[down_start:].replace('-- DOWN Migration', '').strip()
            
            # Calculate checksum
            checksum = hashlib.sha256(content.encode()).hexdigest()
            
            return Migration(
                version=version,
                name=name or migration_file.stem,
                description=description,
                sql_up=sql_up,
                sql_down=sql_down,
                checksum=checksum
            )
            
        except Exception as e:
            logger.error(f"Error parsing migration file {migration_file}: {e}")
            return None
    
    async def _load_applied_migrations(self):
        """Load applied migrations from database"""
        try:
            self.applied_migrations = {}
            
            # Load from PostgreSQL
            if self.postgres_manager:
                query = "SELECT * FROM schema_migrations ORDER BY version"
                result = await self.postgres_manager._execute_query(query)
                
                if result.success and result.data:
                    for row in result.data:
                        migration = Migration(
                            version=row['version'],
                            name=row['name'],
                            description=row['description'],
                            sql_up="",  # Not stored in tracking table
                            sql_down="",  # Not stored in tracking table
                            checksum=row['checksum'],
                            applied_at=row['applied_at'],
                            execution_time_ms=row['execution_time_ms']
                        )
                        self.applied_migrations[migration.version] = migration
            
            logger.debug(f"Loaded {len(self.applied_migrations)} applied migrations")
            
        except Exception as e:
            logger.error(f"Error loading applied migrations: {e}")
            # Don't raise - this might be the first run
    
    async def _determine_current_version(self):
        """Determine current schema version"""
        try:
            if self.applied_migrations:
                # Get the latest applied migration
                latest_applied = max(self.applied_migrations.keys())
                self.current_version = latest_applied
            else:
                self.current_version = None
                
        except Exception as e:
            logger.error(f"Error determining current version: {e}")
            self.current_version = None
    
    def _get_latest_version(self) -> Optional[str]:
        """Get latest available migration version"""
        if self.available_migrations:
            return max(self.available_migrations.keys())
        return None
    
    async def _get_migrations_to_apply(self, target_version: str) -> List[Migration]:
        """Get migrations to apply to reach target version"""
        try:
            migrations_to_apply = []
            
            for version in sorted(self.available_migrations.keys()):
                # Skip if already applied
                if version in self.applied_migrations:
                    continue
                
                # Add migration
                migrations_to_apply.append(self.available_migrations[version])
                
                # Stop if we reached target version
                if version == target_version:
                    break
            
            return migrations_to_apply
            
        except Exception as e:
            logger.error(f"Error getting migrations to apply: {e}")
            return []
    
    async def _get_migrations_to_rollback(self, target_version: str) -> List[Migration]:
        """Get migrations to rollback to reach target version"""
        try:
            migrations_to_rollback = []
            
            # Get applied migrations in reverse order
            for version in sorted(self.applied_migrations.keys(), reverse=True):
                # Stop if we reached target version
                if version == target_version:
                    break
                
                # Add migration for rollback
                if version in self.available_migrations:
                    migrations_to_rollback.append(self.available_migrations[version])
            
            return migrations_to_rollback
            
        except Exception as e:
            logger.error(f"Error getting migrations to rollback: {e}")
            return []
    
    async def _apply_migrations(self, migrations: List[Migration]) -> bool:
        """Apply list of migrations"""
        try:
            for migration in migrations:
                logger.info(f"Applying migration {migration.version}: {migration.name}")
                
                start_time = time.time()
                success = await self._apply_single_migration(migration)
                execution_time = (time.time() - start_time) * 1000
                
                if success:
                    # Record migration as applied
                    await self._record_migration_applied(migration, execution_time)
                    self.applied_migrations[migration.version] = migration
                    logger.info(f"✓ Applied migration {migration.version} in {execution_time:.2f}ms")
                else:
                    logger.error(f"Failed to apply migration {migration.version}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error applying migrations: {e}")
            return False
    
    async def _apply_single_migration(self, migration: Migration) -> bool:
        """Apply a single migration"""
        try:
            # Split SQL statements
            statements = [stmt.strip() for stmt in migration.sql_up.split(';') if stmt.strip()]
            
            # Apply to PostgreSQL
            if self.postgres_manager:
                for statement in statements:
                    if statement:
                        result = await self.postgres_manager._execute_query(statement)
                        if not result.success:
                            logger.error(f"PostgreSQL migration failed: {result.error_message}")
                            return False
            
            # Apply to ClickHouse
            if self.clickhouse_manager:
                for statement in statements:
                    if statement and 'clickhouse' in statement.lower():
                        result = await self.clickhouse_manager._execute_clickhouse_query(statement)
                        if not result.success:
                            logger.error(f"ClickHouse migration failed: {result.error_message}")
                            return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error applying single migration: {e}")
            return False
    
    async def _rollback_migrations(self, migrations: List[Migration]) -> bool:
        """Rollback list of migrations"""
        try:
            for migration in migrations:
                logger.info(f"Rolling back migration {migration.version}: {migration.name}")
                
                success = await self._rollback_single_migration(migration)
                
                if success:
                    # Remove migration from applied list
                    await self._record_migration_rollback(migration)
                    if migration.version in self.applied_migrations:
                        del self.applied_migrations[migration.version]
                    logger.info(f"✓ Rolled back migration {migration.version}")
                else:
                    logger.error(f"Failed to rollback migration {migration.version}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error rolling back migrations: {e}")
            return False
    
    async def _rollback_single_migration(self, migration: Migration) -> bool:
        """Rollback a single migration"""
        try:
            # Split SQL statements
            statements = [stmt.strip() for stmt in migration.sql_down.split(';') if stmt.strip()]
            
            # Apply to PostgreSQL
            if self.postgres_manager:
                for statement in statements:
                    if statement:
                        result = await self.postgres_manager._execute_query(statement)
                        if not result.success:
                            logger.error(f"PostgreSQL rollback failed: {result.error_message}")
                            return False
            
            # Apply to ClickHouse
            if self.clickhouse_manager:
                for statement in statements:
                    if statement and 'clickhouse' in statement.lower():
                        result = await self.clickhouse_manager._execute_clickhouse_query(statement)
                        if not result.success:
                            logger.error(f"ClickHouse rollback failed: {result.error_message}")
                            return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error rolling back single migration: {e}")
            return False
    
    async def _record_migration_applied(self, migration: Migration, execution_time: float):
        """Record migration as applied"""
        try:
            # Record in PostgreSQL
            if self.postgres_manager:
                query = """
                    INSERT INTO schema_migrations (version, name, description, checksum, applied_at, execution_time_ms)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """
                params = [
                    migration.version,
                    migration.name,
                    migration.description,
                    migration.checksum,
                    datetime.now(timezone.utc),
                    execution_time
                ]
                await self.postgres_manager._execute_query(query, params)
            
            # Record in ClickHouse
            if self.clickhouse_manager:
                query = f"""
                    INSERT INTO {self.clickhouse_manager.database}.schema_migrations 
                    (version, name, description, checksum, applied_at, execution_time_ms)
                    VALUES ('{migration.version}', '{migration.name}', '{migration.description}', 
                           '{migration.checksum}', '{datetime.now(timezone.utc)}', {execution_time})
                """
                await self.clickhouse_manager._execute_clickhouse_query(query)
                
        except Exception as e:
            logger.error(f"Error recording migration applied: {e}")
    
    async def _record_migration_rollback(self, migration: Migration):
        """Record migration rollback"""
        try:
            # Remove from PostgreSQL
            if self.postgres_manager:
                query = "DELETE FROM schema_migrations WHERE version = $1"
                await self.postgres_manager._execute_query(query, [migration.version])
            
            # Remove from ClickHouse
            if self.clickhouse_manager:
                query = f"ALTER TABLE {self.clickhouse_manager.database}.schema_migrations DELETE WHERE version = '{migration.version}'"
                await self.clickhouse_manager._execute_clickhouse_query(query)
                
        except Exception as e:
            logger.error(f"Error recording migration rollback: {e}")

"""
Dynamic Asset Allocation - Regime-aware and adaptive portfolio allocation
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class DynamicAssetAllocator:
    """
    Dynamic asset allocation that adapts to changing market conditions
    and implements regime-aware portfolio construction.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Market regime detection
        self.current_regime = "normal"
        self.regime_history: List[Dict[str, Any]] = []
        
        # Allocation parameters
        self.regime_allocations = {
            "bull_market": {"equity_weight": 0.7, "bond_weight": 0.2, "alternative_weight": 0.1},
            "bear_market": {"equity_weight": 0.3, "bond_weight": 0.6, "alternative_weight": 0.1},
            "high_volatility": {"equity_weight": 0.4, "bond_weight": 0.5, "alternative_weight": 0.1},
            "normal": {"equity_weight": 0.6, "bond_weight": 0.3, "alternative_weight": 0.1}
        }
        
        # Market data
        self.returns_data: Optional[pd.DataFrame] = None
        self.market_indicators: Dict[str, float] = {}
        
        # State
        self.initialized = False
        
    async def initialize(self):
        """Initialize dynamic asset allocator"""
        if self.initialized:
            return
            
        logger.info("Initializing Dynamic Asset Allocator...")
        
        # Setup regime detection parameters
        await self._setup_regime_detection()
        
        self.initialized = True
        logger.info("✓ Dynamic Asset Allocator initialized")
        
    async def _setup_regime_detection(self):
        """Setup regime detection parameters"""
        self.regime_thresholds = {
            "volatility_high": 0.25,
            "volatility_low": 0.15,
            "trend_strong": 0.15,
            "trend_weak": 0.05
        }
        
    async def update_market_data(self, returns_data: pd.DataFrame) -> Dict[str, Any]:
        """Update market data and detect regime"""
        try:
            self.returns_data = returns_data
            
            # Calculate market indicators
            await self._calculate_market_indicators()
            
            # Detect current regime
            new_regime = await self._detect_market_regime()
            
            if new_regime != self.current_regime:
                self.current_regime = new_regime
                self.regime_history.append({
                    'regime': new_regime,
                    'timestamp': pd.Timestamp.now(),
                    'indicators': self.market_indicators.copy()
                })
                
            logger.info(f"✓ Updated dynamic allocation data, current regime: {self.current_regime}")
            
            return {
                'success': True,
                'current_regime': self.current_regime,
                'market_indicators': self.market_indicators
            }
            
        except Exception as e:
            logger.error(f"Error updating dynamic allocation data: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _calculate_market_indicators(self):
        """Calculate market indicators for regime detection"""
        # Calculate rolling volatility
        returns = self.returns_data.mean(axis=1)  # Market return proxy
        volatility = returns.rolling(window=20).std().iloc[-1] * np.sqrt(252)
        
        # Calculate trend strength
        prices = (1 + returns).cumprod()
        sma_short = prices.rolling(window=20).mean().iloc[-1]
        sma_long = prices.rolling(window=50).mean().iloc[-1]
        trend_strength = (sma_short - sma_long) / sma_long if sma_long > 0 else 0
        
        self.market_indicators = {
            'volatility': volatility,
            'trend_strength': trend_strength,
            'momentum': returns.rolling(window=10).mean().iloc[-1]
        }
        
    async def _detect_market_regime(self) -> str:
        """Detect current market regime"""
        volatility = self.market_indicators.get('volatility', 0.2)
        trend_strength = self.market_indicators.get('trend_strength', 0)
        
        if volatility > self.regime_thresholds['volatility_high']:
            return "high_volatility"
        elif trend_strength > self.regime_thresholds['trend_strong']:
            return "bull_market"
        elif trend_strength < -self.regime_thresholds['trend_strong']:
            return "bear_market"
        else:
            return "normal"
            
    async def optimize(self, constraints: Dict[str, Any] = None) -> Dict[str, Any]:
        """Optimize allocation based on current regime"""
        try:
            if self.returns_data is None:
                return {'success': False, 'error': 'No market data available'}
                
            # Get regime-specific allocation
            base_allocation = self.regime_allocations.get(self.current_regime, 
                                                        self.regime_allocations['normal'])
            
            # Map to actual assets (simplified)
            assets = self.returns_data.columns
            n_assets = len(assets)
            
            # Simple allocation based on asset type (would be more sophisticated in practice)
            weights = np.ones(n_assets) / n_assets  # Default equal weights
            
            # Apply regime adjustments (placeholder logic)
            if self.current_regime == "bear_market":
                # Reduce risk
                weights *= 0.8
                weights /= np.sum(weights)
            elif self.current_regime == "bull_market":
                # Increase risk
                weights *= 1.2
                weights = np.minimum(weights, 0.4)  # Cap at 40%
                weights /= np.sum(weights)
                
            weights_dict = dict(zip(assets, weights))
            
            return {
                'success': True,
                'weights': weights_dict,
                'regime': self.current_regime,
                'regime_allocation': base_allocation,
                'method': 'dynamic_allocation'
            }
            
        except Exception as e:
            logger.error(f"Error optimizing dynamic allocation: {e}")
            return {'success': False, 'error': str(e)}

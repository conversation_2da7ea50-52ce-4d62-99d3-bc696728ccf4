"""
Message Broker - Central message routing and delivery system
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Callable
from collections import defaultdict, deque
import json

from .message_types import Message, MessageType, MessagePriority

logger = logging.getLogger(__name__)


class MessageBroker:
    """
    Central message broker for inter-agent communication.
    Handles message routing, delivery, and persistence.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Message routing
        self.subscribers: Dict[str, List[Callable]] = defaultdict(list)  # recipient -> handlers
        self.type_subscribers: Dict[MessageType, List[Callable]] = defaultdict(list)  # type -> handlers
        
        # Message queues
        self.message_queues: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.priority_queues: Dict[str, Dict[MessagePriority, deque]] = defaultdict(
            lambda: {priority: deque(maxlen=200) for priority in MessagePriority}
        )
        
        # Message history and tracking
        self.message_history: deque = deque(maxlen=10000)
        self.pending_responses: Dict[str, Message] = {}  # correlation_id -> original_message
        self.delivery_confirmations: Dict[str, bool] = {}  # message_id -> delivered
        
        # State
        self.initialized = False
        self.running = False
        
        # Configuration
        self.max_queue_size = self.config.get('communication', {}).get('max_queue_size', 10000)
        self.message_ttl = self.config.get('communication', {}).get('message_ttl', 300)
        
        # Tasks
        self.delivery_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
    async def initialize(self):
        """Initialize the message broker"""
        if self.initialized:
            return
            
        logger.info("Initializing Message Broker...")
        
        self.initialized = True
        logger.info("✓ Message Broker initialized")
        
    async def start(self):
        """Start the message broker"""
        if not self.initialized:
            await self.initialize()
            
        if self.running:
            return
            
        logger.info("Starting Message Broker...")
        
        # Start delivery and cleanup tasks
        self.delivery_task = asyncio.create_task(self._delivery_loop())
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        self.running = True
        logger.info("✓ Message Broker started")
        
    async def stop(self):
        """Stop the message broker"""
        if not self.running:
            return
            
        logger.info("Stopping Message Broker...")
        self.running = False
        
        # Cancel tasks
        for task in [self.delivery_task, self.cleanup_task]:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                    
        logger.info("✓ Message Broker stopped")
        
    async def publish(self, message: Message) -> bool:
        """Publish a message to the broker"""
        try:
            # Validate message
            if message.is_expired():
                logger.warning(f"Message {message.id} expired before publishing")
                return False
                
            # Add to message history
            self.message_history.append(message)
            
            # Route to appropriate queue
            await self._route_message(message)
            
            # Track pending responses if needed
            if message.type in [MessageType.ANALYSIS_REQUEST, MessageType.DECISION_REQUEST, 
                              MessageType.STATUS_REQUEST]:
                self.pending_responses[message.id] = message
                
            logger.debug(f"Published message {message.id} from {message.sender} to {message.recipient}")
            return True
            
        except Exception as e:
            logger.error(f"Error publishing message {message.id}: {e}")
            return False
            
    async def _route_message(self, message: Message):
        """Route message to appropriate queues and subscribers"""
        # Add to recipient's priority queue
        recipient_queues = self.priority_queues[message.recipient]
        recipient_queues[message.priority].append(message)
        
        # Add to general message queue for recipient
        self.message_queues[message.recipient].append(message)
        
        # Notify direct subscribers
        if message.recipient in self.subscribers:
            for handler in self.subscribers[message.recipient]:
                try:
                    await handler(message)
                except Exception as e:
                    logger.error(f"Error in message handler for {message.recipient}: {e}")
                    
        # Notify type subscribers
        if message.type in self.type_subscribers:
            for handler in self.type_subscribers[message.type]:
                try:
                    await handler(message)
                except Exception as e:
                    logger.error(f"Error in type handler for {message.type}: {e}")
                    
    async def subscribe(self, recipient: str, handler: Callable[[Message], None]):
        """Subscribe to messages for a specific recipient"""
        self.subscribers[recipient].append(handler)
        logger.debug(f"Added subscriber for {recipient}")
        
    async def subscribe_to_type(self, message_type: MessageType, handler: Callable[[Message], None]):
        """Subscribe to messages of a specific type"""
        self.type_subscribers[message_type].append(handler)
        logger.debug(f"Added type subscriber for {message_type}")
        
    async def unsubscribe(self, recipient: str, handler: Callable[[Message], None]):
        """Unsubscribe from messages for a specific recipient"""
        if recipient in self.subscribers and handler in self.subscribers[recipient]:
            self.subscribers[recipient].remove(handler)
            logger.debug(f"Removed subscriber for {recipient}")
            
    async def unsubscribe_from_type(self, message_type: MessageType, handler: Callable[[Message], None]):
        """Unsubscribe from messages of a specific type"""
        if message_type in self.type_subscribers and handler in self.type_subscribers[message_type]:
            self.type_subscribers[message_type].remove(handler)
            logger.debug(f"Removed type subscriber for {message_type}")
            
    async def get_messages(self, recipient: str, limit: int = 10) -> List[Message]:
        """Get messages for a recipient"""
        messages = []
        recipient_queue = self.message_queues[recipient]
        
        # Get messages from queue (most recent first)
        for _ in range(min(limit, len(recipient_queue))):
            if recipient_queue:
                messages.append(recipient_queue.popleft())
                
        return messages
        
    async def get_priority_messages(self, recipient: str, priority: MessagePriority, 
                                  limit: int = 10) -> List[Message]:
        """Get messages of specific priority for a recipient"""
        messages = []
        priority_queue = self.priority_queues[recipient][priority]
        
        for _ in range(min(limit, len(priority_queue))):
            if priority_queue:
                messages.append(priority_queue.popleft())
                
        return messages
        
    async def peek_messages(self, recipient: str, limit: int = 10) -> List[Message]:
        """Peek at messages without removing them from queue"""
        recipient_queue = self.message_queues[recipient]
        return list(recipient_queue)[:limit]
        
    async def get_queue_size(self, recipient: str) -> int:
        """Get queue size for a recipient"""
        return len(self.message_queues[recipient])
        
    async def clear_queue(self, recipient: str):
        """Clear message queue for a recipient"""
        self.message_queues[recipient].clear()
        for priority_queue in self.priority_queues[recipient].values():
            priority_queue.clear()
        logger.info(f"Cleared message queue for {recipient}")
        
    async def send_response(self, original_message: Message, response_content: Dict[str, Any],
                          sender: str) -> bool:
        """Send a response to an original message"""
        # Determine response type
        response_type_map = {
            MessageType.ANALYSIS_REQUEST: MessageType.ANALYSIS_RESPONSE,
            MessageType.DECISION_REQUEST: MessageType.DECISION_RESPONSE,
            MessageType.STATUS_REQUEST: MessageType.STATUS_RESPONSE
        }
        
        response_type = response_type_map.get(original_message.type)
        if not response_type:
            logger.error(f"No response type defined for {original_message.type}")
            return False
            
        # Create response message
        response_message = Message(
            id=f"resp_{original_message.id}",
            type=response_type,
            sender=sender,
            recipient=original_message.sender,
            content=response_content,
            timestamp=time.time(),
            priority=original_message.priority,
            correlation_id=original_message.id
        )
        
        # Publish response
        success = await self.publish(response_message)
        
        # Remove from pending responses
        if original_message.id in self.pending_responses:
            del self.pending_responses[original_message.id]
            
        return success
        
    async def broadcast(self, message_type: MessageType, content: Dict[str, Any], 
                      sender: str, recipients: List[str] = None) -> int:
        """Broadcast a message to multiple recipients"""
        if recipients is None:
            recipients = list(self.subscribers.keys())
            
        sent_count = 0
        
        for recipient in recipients:
            if recipient != sender:  # Don't send to self
                message = Message(
                    id=f"broadcast_{int(time.time() * 1000)}_{recipient}",
                    type=message_type,
                    sender=sender,
                    recipient=recipient,
                    content=content,
                    timestamp=time.time(),
                    priority=MessagePriority.NORMAL
                )
                
                if await self.publish(message):
                    sent_count += 1
                    
        logger.info(f"Broadcast message sent to {sent_count} recipients")
        return sent_count
        
    async def _delivery_loop(self):
        """Main delivery loop"""
        while self.running:
            try:
                await asyncio.sleep(0.1)  # Small delay to prevent busy waiting
                # Delivery is handled in real-time via routing
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in delivery loop: {e}")
                
    async def _cleanup_loop(self):
        """Cleanup expired messages and old data"""
        while self.running:
            try:
                await asyncio.sleep(60)  # Run cleanup every minute
                await self._cleanup_expired_messages()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                
    async def _cleanup_expired_messages(self):
        """Remove expired messages from queues"""
        current_time = time.time()
        
        # Clean up message queues
        for recipient, queue in self.message_queues.items():
            # Convert to list, filter, and recreate deque
            messages = [msg for msg in queue if not msg.is_expired()]
            queue.clear()
            queue.extend(messages)
            
        # Clean up priority queues
        for recipient, priority_queues in self.priority_queues.items():
            for priority, queue in priority_queues.items():
                messages = [msg for msg in queue if not msg.is_expired()]
                queue.clear()
                queue.extend(messages)
                
        # Clean up pending responses (older than TTL)
        expired_responses = [
            msg_id for msg_id, msg in self.pending_responses.items()
            if current_time - msg.timestamp > self.message_ttl
        ]
        
        for msg_id in expired_responses:
            del self.pending_responses[msg_id]
            
        if expired_responses:
            logger.debug(f"Cleaned up {len(expired_responses)} expired pending responses")
            
    async def get_stats(self) -> Dict[str, Any]:
        """Get message broker statistics"""
        total_queued = sum(len(queue) for queue in self.message_queues.values())
        
        return {
            'total_messages_processed': len(self.message_history),
            'total_queued_messages': total_queued,
            'active_subscribers': len(self.subscribers),
            'type_subscribers': len(self.type_subscribers),
            'pending_responses': len(self.pending_responses),
            'queue_sizes': {recipient: len(queue) for recipient, queue in self.message_queues.items()},
            'running': self.running
        }

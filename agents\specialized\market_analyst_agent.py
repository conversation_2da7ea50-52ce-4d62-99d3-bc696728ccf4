"""
Market Analyst Agent - Market analysis and pattern recognition
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
import json
import statistics

from ..base_agent import BaseAgent, AgentRole, AgentState
from communication.message_types import MessageType

logger = logging.getLogger(__name__)


class MarketAnalystAgent(BaseAgent):
    """
    Market Analyst Agent specializing in market analysis and pattern recognition.
    
    Responsibilities:
    - Technical analysis and pattern recognition
    - Trend identification and analysis
    - Market sentiment analysis
    - Economic indicator interpretation
    - Price action analysis
    - Volume and momentum analysis
    - Support and resistance identification
    - Market regime detection
    
    Uses: magistral-abliterated:24b (primary), qwen2.5vl:32b (visual analysis)
    """
    
    def __init__(self, agent_id: str = None, name: str = None, config: Dict[str, Any] = None):
        super().__init__(
            agent_id=agent_id,
            name=name or "MarketAnalyst",
            role=AgentRole.MARKET_ANALYST,
            config=config
        )
        
        # Analysis state
        self.market_data_cache: Dict[str, List[Dict[str, Any]]] = {}
        self.analysis_results: Dict[str, Dict[str, Any]] = {}
        self.pattern_library: Dict[str, Dict[str, Any]] = {}
        
        # Technical indicators
        self.indicators: Dict[str, Any] = {}
        self.trend_analysis: Dict[str, Dict[str, Any]] = {}
        
        # Market conditions
        self.market_regimes: Dict[str, str] = {}  # symbol -> regime
        self.sentiment_scores: Dict[str, float] = {}  # symbol -> sentiment
        
    async def _initialize_agent(self):
        """Market Analyst specific initialization"""
        logger.info(f"Initializing Market Analyst Agent: {self.name}")
        
        # Initialize technical analysis capabilities
        await self._setup_technical_analysis()
        
        # Initialize pattern recognition
        await self._setup_pattern_recognition()
        
        # Initialize market sentiment analysis
        await self._setup_sentiment_analysis()
        
        logger.info(f"✓ Market Analyst Agent {self.name} initialized")
        
    async def _setup_technical_analysis(self):
        """Setup technical analysis capabilities"""
        self.technical_indicators = {
            'trend_indicators': [
                'moving_averages', 'macd', 'adx', 'parabolic_sar'
            ],
            'momentum_indicators': [
                'rsi', 'stochastic', 'williams_r', 'momentum'
            ],
            'volatility_indicators': [
                'bollinger_bands', 'atr', 'volatility_ratio'
            ],
            'volume_indicators': [
                'volume_sma', 'on_balance_volume', 'volume_price_trend'
            ]
        }
        
        # Store technical analysis setup
        await self.memory.store_experience({
            'type': 'initialization',
            'component': 'technical_analysis',
            'indicators': self.technical_indicators,
            'timestamp': time.time()
        })
        
    async def _setup_pattern_recognition(self):
        """Setup pattern recognition capabilities"""
        self.chart_patterns = {
            'reversal_patterns': [
                'head_and_shoulders', 'inverse_head_and_shoulders',
                'double_top', 'double_bottom', 'triple_top', 'triple_bottom'
            ],
            'continuation_patterns': [
                'triangle', 'flag', 'pennant', 'rectangle', 'wedge'
            ],
            'candlestick_patterns': [
                'doji', 'hammer', 'shooting_star', 'engulfing',
                'harami', 'morning_star', 'evening_star'
            ]
        }
        
        # Store pattern recognition setup
        await self.memory.store_experience({
            'type': 'initialization',
            'component': 'pattern_recognition',
            'patterns': self.chart_patterns,
            'timestamp': time.time()
        })
        
    async def _setup_sentiment_analysis(self):
        """Setup sentiment analysis capabilities"""
        self.sentiment_sources = {
            'market_data': ['price_action', 'volume', 'volatility'],
            'technical_indicators': ['fear_greed_index', 'vix', 'put_call_ratio'],
            'fundamental_data': ['economic_indicators', 'earnings', 'news']
        }
        
        # Store sentiment analysis setup
        await self.memory.store_experience({
            'type': 'initialization',
            'component': 'sentiment_analysis',
            'sources': self.sentiment_sources,
            'timestamp': time.time()
        })
        
    def _register_role_specific_handlers(self):
        """Register Market Analyst specific message handlers"""
        self.task_handlers.update({
            'technical_analysis': self._handle_technical_analysis,
            'pattern_recognition': self._handle_pattern_recognition,
            'trend_analysis': self._handle_trend_analysis,
            'sentiment_analysis': self._handle_sentiment_analysis,
            'market_regime_detection': self._handle_market_regime_detection,
            'price_target_analysis': self._handle_price_target_analysis,
            'volatility_analysis': self._handle_volatility_analysis,
            'volume_analysis': self._handle_volume_analysis
        })
        
    async def _idle_activities(self):
        """Activities when idle - continuous market monitoring"""
        # Update market data cache
        await self._update_market_data()
        
        # Perform routine technical analysis
        await self._routine_technical_analysis()
        
        # Monitor for pattern formations
        await self._monitor_patterns()
        
    async def _handle_technical_analysis(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle technical analysis requests"""
        try:
            symbol = task.get('symbol')
            timeframe = task.get('timeframe', '1h')
            indicators = task.get('indicators', [])
            
            if not symbol:
                return {
                    'success': False,
                    'error': 'Symbol is required for technical analysis'
                }
                
            # Get market data
            market_data = await self._get_market_data(symbol, timeframe)
            
            if not market_data:
                return {
                    'success': False,
                    'error': f'No market data available for {symbol}'
                }
                
            # Perform technical analysis
            analysis_prompt = f"""
            Perform comprehensive technical analysis for {symbol} on {timeframe} timeframe:
            
            Market Data (last 20 periods): {json.dumps(market_data[-20:], indent=2)}
            Requested Indicators: {indicators}
            
            Provide detailed technical analysis including:
            1. Trend Analysis:
               - Primary trend direction and strength
               - Support and resistance levels
               - Trend line analysis
            
            2. Technical Indicators:
               - Moving averages (SMA, EMA)
               - Momentum indicators (RSI, MACD)
               - Volatility indicators (Bollinger Bands, ATR)
               - Volume analysis
            
            3. Pattern Recognition:
               - Chart patterns identified
               - Candlestick patterns
               - Pattern completion probability
            
            4. Market Structure:
               - Higher highs/lows analysis
               - Market phases (accumulation, markup, distribution, decline)
               - Key price levels
            
            5. Trading Signals:
               - Entry/exit signals
               - Risk levels
               - Price targets
               - Confidence levels
            
            Format as JSON with numerical values and clear interpretations.
            """
            
            result = await self.model_instance.generate(analysis_prompt)
            
            if result['success']:
                try:
                    analysis = json.loads(result['response'])
                    
                    # Store analysis results
                    self.analysis_results[symbol] = {
                        'analysis': analysis,
                        'timeframe': timeframe,
                        'timestamp': time.time(),
                        'data_points': len(market_data)
                    }
                    
                    # Store in memory
                    await self.memory.store_analysis({
                        'type': 'technical_analysis',
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'analysis': analysis,
                        'confidence': analysis.get('confidence', 0.7),
                        'timestamp': time.time()
                    })
                    
                    return {
                        'success': True,
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'analysis': analysis,
                        'status': 'technical_analysis_complete'
                    }
                    
                except json.JSONDecodeError:
                    # If JSON parsing fails, extract key information from text
                    response_text = result['response']

                    # Create structured response from text analysis
                    analysis = {
                        'rsi': self._extract_rsi_from_text(response_text),
                        'macd': self._extract_macd_from_text(response_text),
                        'bollinger_bands': self._extract_bollinger_from_text(response_text),
                        'trend': self._extract_trend_from_text(response_text),
                        'support_resistance': self._extract_support_resistance_from_text(response_text),
                        'recommendation': self._extract_recommendation_from_text(response_text),
                        'confidence': self._extract_confidence_from_text(response_text)
                    }

                    return {
                        'success': True,
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'analysis': analysis,
                        'analysis_text': response_text[:1000] + "..." if len(response_text) > 1000 else response_text,
                        'status': 'technical_analysis_complete',
                        'note': 'Parsed from text analysis due to JSON format issue'
                    }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Technical analysis failed')
                }
                
        except Exception as e:
            logger.error(f"Error in technical analysis: {e}")
            return {
                'success': False,
                'error': str(e)
            }
            
    async def _handle_pattern_recognition(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle pattern recognition requests"""
        try:
            symbol = task.get('symbol')
            timeframe = task.get('timeframe', '1h')
            pattern_types = task.get('pattern_types', [])
            
            # Get market data
            market_data = await self._get_market_data(symbol, timeframe)
            
            if not market_data:
                return {
                    'success': False,
                    'error': f'No market data available for {symbol}'
                }
                
            # Pattern recognition analysis
            pattern_prompt = f"""
            Analyze chart patterns for {symbol} on {timeframe} timeframe:
            
            Market Data: {json.dumps(market_data[-50:], indent=2)}
            Focus on Pattern Types: {pattern_types if pattern_types else 'all patterns'}
            
            Identify and analyze:
            1. Chart Patterns:
               - Reversal patterns (head & shoulders, double tops/bottoms)
               - Continuation patterns (triangles, flags, pennants)
               - Pattern completion status
               - Breakout probability
            
            2. Candlestick Patterns:
               - Single candlestick patterns
               - Multi-candlestick patterns
               - Pattern reliability
               - Confirmation signals
            
            3. Price Action Patterns:
               - Support/resistance breaks
               - Trend line breaks
               - Gap analysis
               - Volume confirmation
            
            4. Pattern Trading Signals:
               - Entry points
               - Stop loss levels
               - Price targets
               - Risk/reward ratios
            
            Provide confidence scores and pattern reliability assessments.
            Format as JSON with detailed pattern descriptions.
            """
            
            result = await self.model_instance.generate(pattern_prompt)
            
            if result['success']:
                try:
                    patterns = json.loads(result['response'])
                    
                    # Store pattern analysis
                    pattern_key = f"{symbol}_{timeframe}"
                    self.pattern_library[pattern_key] = {
                        'patterns': patterns,
                        'timestamp': time.time(),
                        'data_points': len(market_data)
                    }
                    
                    # Store in memory
                    await self.memory.store_analysis({
                        'type': 'pattern_recognition',
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'patterns': patterns,
                        'timestamp': time.time()
                    })
                    
                    return {
                        'success': True,
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'patterns': patterns,
                        'status': 'pattern_recognition_complete'
                    }
                    
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'Failed to parse pattern analysis',
                        'raw_response': result['response']
                    }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Pattern recognition failed')
                }
                
        except Exception as e:
            logger.error(f"Error in pattern recognition: {e}")
            return {
                'success': False,
                'error': str(e)
            }
            
    async def _handle_sentiment_analysis(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle market sentiment analysis"""
        try:
            symbol = task.get('symbol')
            data_sources = task.get('sources', ['market_data', 'technical_indicators'])
            
            # Get market data for sentiment analysis
            market_data = await self._get_market_data(symbol, '1h')
            
            sentiment_prompt = f"""
            Analyze market sentiment for {symbol}:
            
            Market Data: {json.dumps(market_data[-30:], indent=2)}
            Data Sources: {data_sources}
            
            Analyze sentiment indicators:
            1. Price Action Sentiment:
               - Trend momentum
               - Buying/selling pressure
               - Price volatility patterns
            
            2. Volume Sentiment:
               - Volume trends
               - Volume-price relationships
               - Accumulation/distribution patterns
            
            3. Technical Sentiment:
               - Overbought/oversold conditions
               - Momentum divergences
               - Volatility expansion/contraction
            
            4. Market Structure Sentiment:
               - Support/resistance behavior
               - Breakout/breakdown patterns
               - Market participation levels
            
            Provide:
            - Overall sentiment score (-1 to +1)
            - Sentiment strength (0 to 1)
            - Key sentiment drivers
            - Sentiment change probability
            - Trading implications
            
            Format as JSON with numerical scores and explanations.
            """
            
            result = await self.model_instance.generate(sentiment_prompt)
            
            if result['success']:
                try:
                    sentiment = json.loads(result['response'])
                    
                    # Store sentiment score
                    self.sentiment_scores[symbol] = sentiment.get('sentiment_score', 0.0)
                    
                    # Store in memory
                    await self.memory.store_analysis({
                        'type': 'sentiment_analysis',
                        'symbol': symbol,
                        'sentiment': sentiment,
                        'timestamp': time.time()
                    })
                    
                    return {
                        'success': True,
                        'symbol': symbol,
                        'sentiment': sentiment,
                        'status': 'sentiment_analysis_complete'
                    }
                    
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'Failed to parse sentiment analysis',
                        'raw_response': result['response']
                    }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Sentiment analysis failed')
                }
                
        except Exception as e:
            logger.error(f"Error in sentiment analysis: {e}")
            return {
                'success': False,
                'error': str(e)
            }
            
    async def _get_market_data(self, symbol: str, timeframe: str) -> List[Dict[str, Any]]:
        """Get market data for analysis (placeholder)"""
        # In a real implementation, this would fetch actual market data
        # For now, generate mock data for testing
        import random
        
        data = []
        base_price = 100.0
        
        for i in range(100):
            change = random.uniform(-0.02, 0.02)
            base_price *= (1 + change)
            
            data.append({
                'timestamp': time.time() - (100 - i) * 3600,  # Hourly data
                'open': base_price,
                'high': base_price * random.uniform(1.0, 1.015),
                'low': base_price * random.uniform(0.985, 1.0),
                'close': base_price,
                'volume': random.uniform(1000, 10000)
            })
            
        return data
        
    async def _update_market_data(self):
        """Update market data cache"""
        # Placeholder for market data updates
        pass
        
    async def _routine_technical_analysis(self):
        """Perform routine technical analysis"""
        # Placeholder for routine analysis
        pass
        
    async def _monitor_patterns(self):
        """Monitor for pattern formations"""
        # Placeholder for pattern monitoring
        pass
        
    # Placeholder methods for other handlers
    async def _handle_trend_analysis(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle trend analysis requests"""
        return {'success': True, 'status': 'trend_analysis_complete'}
        
    async def _handle_market_regime_detection(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle market regime detection"""
        return {'success': True, 'status': 'market_regime_detected'}
        
    async def _handle_price_target_analysis(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle price target analysis"""
        return {'success': True, 'status': 'price_targets_calculated'}
        
    async def _handle_volatility_analysis(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle volatility analysis"""
        return {'success': True, 'status': 'volatility_analysis_complete'}
        
    async def _handle_volume_analysis(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle volume analysis"""
        return {'success': True, 'status': 'volume_analysis_complete'}

    # Text parsing helper methods

    def _extract_rsi_from_text(self, text: str) -> dict:
        """Extract RSI information from text analysis"""
        import re

        # Look for RSI patterns like "RSI is 68" or "RSI: 72"
        rsi_patterns = [
            r'RSI.*?([0-9.]+)',
            r'RSI.*?is.*?([0-9.]+)',
            r'RSI.*?:.*?([0-9.]+)'
        ]

        for pattern in rsi_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    rsi_value = float(match.group(1))
                    return {
                        'value': rsi_value,
                        'signal': 'overbought' if rsi_value > 70 else 'oversold' if rsi_value < 30 else 'neutral',
                        'strength': abs(rsi_value - 50) / 50
                    }
                except ValueError:
                    continue

        # Default fallback
        return {'value': 50.0, 'signal': 'neutral', 'strength': 0.0}

    def _extract_macd_from_text(self, text: str) -> dict:
        """Extract MACD information from text analysis"""
        import re

        # Look for MACD patterns
        if 'bullish' in text.lower() and 'macd' in text.lower():
            return {'signal': 'bullish', 'strength': 0.7}
        elif 'bearish' in text.lower() and 'macd' in text.lower():
            return {'signal': 'bearish', 'strength': 0.7}
        else:
            return {'signal': 'neutral', 'strength': 0.0}

    def _extract_bollinger_from_text(self, text: str) -> dict:
        """Extract Bollinger Bands information from text analysis"""
        if 'upper band' in text.lower() or 'overbought' in text.lower():
            return {'position': 'upper', 'signal': 'sell'}
        elif 'lower band' in text.lower() or 'oversold' in text.lower():
            return {'position': 'lower', 'signal': 'buy'}
        else:
            return {'position': 'middle', 'signal': 'neutral'}

    def _extract_trend_from_text(self, text: str) -> dict:
        """Extract trend information from text analysis"""
        if 'uptrend' in text.lower() or 'bullish' in text.lower() or 'rising' in text.lower():
            return {'direction': 'up', 'strength': 'strong' if 'strong' in text.lower() else 'moderate'}
        elif 'downtrend' in text.lower() or 'bearish' in text.lower() or 'falling' in text.lower():
            return {'direction': 'down', 'strength': 'strong' if 'strong' in text.lower() else 'moderate'}
        else:
            return {'direction': 'sideways', 'strength': 'weak'}

    def _extract_support_resistance_from_text(self, text: str) -> dict:
        """Extract support and resistance levels from text analysis"""
        import re

        # Look for price levels
        price_patterns = [
            r'\$([0-9.]+)',
            r'([0-9.]+)\s*dollars?',
            r'level.*?([0-9.]+)'
        ]

        prices = []
        for pattern in price_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    prices.append(float(match))
                except ValueError:
                    continue

        if prices:
            prices.sort()
            return {
                'support': min(prices) if prices else 0,
                'resistance': max(prices) if prices else 0,
                'levels': prices[:5]  # Top 5 levels
            }

        return {'support': 0, 'resistance': 0, 'levels': []}

    def _extract_recommendation_from_text(self, text: str) -> str:
        """Extract trading recommendation from text analysis"""
        text_lower = text.lower()

        if 'buy' in text_lower or 'long' in text_lower:
            return 'buy'
        elif 'sell' in text_lower or 'short' in text_lower:
            return 'sell'
        elif 'hold' in text_lower:
            return 'hold'
        else:
            return 'neutral'

    def _extract_confidence_from_text(self, text: str) -> float:
        """Extract confidence level from text analysis"""
        import re

        # Look for confidence patterns
        confidence_patterns = [
            r'confidence.*?([0-9.]+)%',
            r'([0-9.]+)%.*?confidence',
            r'certainty.*?([0-9.]+)%'
        ]

        for pattern in confidence_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    return float(match.group(1)) / 100.0
                except ValueError:
                    continue

        # Default confidence based on text strength
        if 'strong' in text.lower():
            return 0.8
        elif 'moderate' in text.lower():
            return 0.6
        elif 'weak' in text.lower():
            return 0.4
        else:
            return 0.5

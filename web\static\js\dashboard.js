
// Dashboard-specific JavaScript

class Dashboard {
    constructor() {
        this.updateInterval = 5000; // 5 seconds
        this.charts = {};
        this.init();
    }

    async init() {
        await this.loadSystemStatus();
        await this.loadPortfolioSummary();
        await this.loadRecentActivity();
        
        // Setup real-time updates
        this.setupWebSocket();
        this.startPeriodicUpdates();
    }

    async loadSystemStatus() {
        const statusElement = document.getElementById('system-status');
        if (!statusElement) return;

        try {
            showLoading(statusElement);
            const status = await api.getSystemStatus();
            this.renderSystemStatus(statusElement, status);
        } catch (error) {
            showError(statusElement, error.message);
        }
    }

    renderSystemStatus(element, status) {
        const statusClass = status.status === 'healthy' ? 'status-healthy' : 
                           status.status === 'degraded' ? 'status-warning' : 'status-error';
        
        element.innerHTML = `
            <div class="card-header">
                <h3 class="card-title">System Status</h3>
                <span class="status ${statusClass}">
                    <span class="status-dot"></span>
                    ${status.status.toUpperCase()}
                </span>
            </div>
            <div class="grid grid-2">
                <div>
                    <p><strong>Uptime:</strong> ${this.formatUptime(status.uptime_seconds)}</p>
                    <p><strong>Version:</strong> ${status.version}</p>
                    <p><strong>Components:</strong> ${Object.keys(status.components).length}</p>
                </div>
                <div>
                    <p><strong>CPU:</strong> ${status.resources.cpu?.usage_percent?.toFixed(1) || 0}%</p>
                    <p><strong>Memory:</strong> ${status.resources.memory?.usage_percent?.toFixed(1) || 0}%</p>
                    <p><strong>Disk:</strong> ${status.resources.disk?.usage_percent?.toFixed(1) || 0}%</p>
                </div>
            </div>
        `;
    }

    async loadPortfolioSummary() {
        const portfolioElement = document.getElementById('portfolio-summary');
        if (!portfolioElement) return;

        try {
            showLoading(portfolioElement);
            const portfolios = await api.getPortfolios();
            this.renderPortfolioSummary(portfolioElement, portfolios);
        } catch (error) {
            showError(portfolioElement, error.message);
        }
    }

    renderPortfolioSummary(element, portfolios) {
        if (!portfolios || portfolios.length === 0) {
            element.innerHTML = `
                <div class="card-header">
                    <h3 class="card-title">Portfolio Summary</h3>
                </div>
                <p>No portfolios available</p>
            `;
            return;
        }

        const totalValue = portfolios.reduce((sum, p) => sum + p.total_value, 0);
        const totalCash = portfolios.reduce((sum, p) => sum + p.cash, 0);

        element.innerHTML = `
            <div class="card-header">
                <h3 class="card-title">Portfolio Summary</h3>
                <a href="/portfolio" class="btn btn-primary">View Details</a>
            </div>
            <div class="grid grid-2">
                <div>
                    <p><strong>Total Value:</strong> ${formatCurrency(totalValue)}</p>
                    <p><strong>Cash:</strong> ${formatCurrency(totalCash)}</p>
                </div>
                <div>
                    <p><strong>Portfolios:</strong> ${portfolios.length}</p>
                    <p><strong>Invested:</strong> ${formatCurrency(totalValue - totalCash)}</p>
                </div>
            </div>
        `;
    }

    async loadRecentActivity() {
        const activityElement = document.getElementById('recent-activity');
        if (!activityElement) return;

        try {
            showLoading(activityElement);
            // This would load recent trading activity
            this.renderRecentActivity(activityElement, []);
        } catch (error) {
            showError(activityElement, error.message);
        }
    }

    renderRecentActivity(element, activities) {
        element.innerHTML = `
            <div class="card-header">
                <h3 class="card-title">Recent Activity</h3>
            </div>
            <div class="activity-list">
                ${activities.length > 0 ? 
                    activities.map(activity => `
                        <div class="activity-item">
                            <span class="activity-time">${formatDateTime(activity.timestamp)}</span>
                            <span class="activity-description">${activity.description}</span>
                        </div>
                    `).join('') :
                    '<p>No recent activity</p>'
                }
            </div>
        `;
    }

    setupWebSocket() {
        // Connect to system status updates
        wsManager.connect('system', (data) => {
            if (data.type === 'system_status') {
                const statusElement = document.getElementById('system-status');
                if (statusElement) {
                    this.renderSystemStatus(statusElement, data.data);
                }
            }
        });

        // Connect to portfolio updates
        wsManager.connect('portfolio', (data) => {
            if (data.type === 'portfolio_update') {
                this.loadPortfolioSummary();
            }
        });
    }

    startPeriodicUpdates() {
        setInterval(async () => {
            await this.loadSystemStatus();
            await this.loadPortfolioSummary();
        }, this.updateInterval);
    }

    formatUptime(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        
        if (days > 0) {
            return `${days}d ${hours}h ${minutes}m`;
        } else if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else {
            return `${minutes}m`;
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new Dashboard();
});

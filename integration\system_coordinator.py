"""
System Coordinator - Central coordination hub for all system components
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class SystemEvent(Enum):
    """System-wide events"""
    MARKET_DATA_UPDATE = "market_data_update"
    STRATEGY_SIGNAL = "strategy_signal"
    RISK_ALERT = "risk_alert"
    ORDER_FILLED = "order_filled"
    PORTFOLIO_REBALANCE = "portfolio_rebalance"
    SYSTEM_ERROR = "system_error"
    PERFORMANCE_UPDATE = "performance_update"


@dataclass
class SystemMessage:
    """System-wide message structure"""
    event_type: SystemEvent
    source: str
    timestamp: float
    data: Dict[str, Any]
    priority: int = 1  # 1=high, 2=medium, 3=low
    correlation_id: Optional[str] = None


class SystemCoordinator:
    """
    Central coordination hub that orchestrates all system components.
    
    Responsibilities:
    - Cross-component communication
    - Event routing and handling
    - System-wide state coordination
    - Performance optimization
    - Error handling and recovery
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.coordinator_config = config.get('system_coordinator', {})
        
        # Component registry
        self.components: Dict[str, Any] = {}
        self.component_status: Dict[str, str] = {}
        
        # Event handling
        self.event_handlers: Dict[SystemEvent, List[Callable]] = {
            event: [] for event in SystemEvent
        }
        self.event_queue: asyncio.Queue = asyncio.Queue()
        
        # System state
        self.system_state: Dict[str, Any] = {
            'status': 'initializing',
            'start_time': time.time(),
            'last_update': time.time(),
            'error_count': 0,
            'performance_metrics': {}
        }
        
        # Configuration
        self.max_queue_size = self.coordinator_config.get('max_queue_size', 10000)
        self.event_processing_interval = self.coordinator_config.get('event_processing_interval', 0.1)
        
        # State flags
        self.initialized = False
        self.running = False
        
        # Background tasks
        self.event_processor_task: Optional[asyncio.Task] = None
        self.health_monitor_task: Optional[asyncio.Task] = None
        
    async def initialize(self) -> bool:
        """Initialize system coordinator"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing System Coordinator...")
            
            # Initialize event queue
            self.event_queue = asyncio.Queue(maxsize=self.max_queue_size)
            
            # Set initial system state
            self.system_state['status'] = 'initialized'
            self.system_state['last_update'] = time.time()
            
            self.initialized = True
            logger.info("✓ System Coordinator initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize System Coordinator: {e}")
            return False
    
    async def start(self) -> bool:
        """Start system coordinator"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        if self.running:
            return True
            
        try:
            logger.info("Starting System Coordinator...")
            
            # Start background tasks
            self.event_processor_task = asyncio.create_task(self._event_processor_loop())
            self.health_monitor_task = asyncio.create_task(self._health_monitor_loop())
            
            self.running = True
            self.system_state['status'] = 'running'
            self.system_state['last_update'] = time.time()
            
            logger.info("✓ System Coordinator started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start System Coordinator: {e}")
            return False
    
    async def stop(self) -> bool:
        """Stop system coordinator"""
        if not self.running:
            return True
            
        try:
            logger.info("Stopping System Coordinator...")
            self.running = False
            
            # Cancel background tasks
            if self.event_processor_task:
                self.event_processor_task.cancel()
            if self.health_monitor_task:
                self.health_monitor_task.cancel()
            
            self.system_state['status'] = 'stopped'
            self.system_state['last_update'] = time.time()
            
            logger.info("✓ System Coordinator stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping System Coordinator: {e}")
            return False
    
    async def register_component(self, name: str, component: Any) -> bool:
        """Register a system component"""
        try:
            self.components[name] = component
            self.component_status[name] = 'registered'
            
            logger.info(f"✓ Component '{name}' registered")
            return True
            
        except Exception as e:
            logger.error(f"Error registering component '{name}': {e}")
            return False
    
    async def register_event_handler(self, event_type: SystemEvent, handler: Callable) -> bool:
        """Register an event handler"""
        try:
            self.event_handlers[event_type].append(handler)
            logger.debug(f"Event handler registered for {event_type.value}")
            return True
            
        except Exception as e:
            logger.error(f"Error registering event handler for {event_type.value}: {e}")
            return False
    
    async def emit_event(self, event_type: SystemEvent, source: str, 
                        data: Dict[str, Any], priority: int = 1) -> bool:
        """Emit a system event"""
        try:
            message = SystemMessage(
                event_type=event_type,
                source=source,
                timestamp=time.time(),
                data=data,
                priority=priority
            )
            
            # Add to event queue
            try:
                self.event_queue.put_nowait(message)
                logger.debug(f"Event emitted: {event_type.value} from {source}")
                return True
            except asyncio.QueueFull:
                logger.warning(f"Event queue full, dropping event: {event_type.value}")
                return False
                
        except Exception as e:
            logger.error(f"Error emitting event {event_type.value}: {e}")
            return False
    
    async def get_system_state(self) -> Dict[str, Any]:
        """Get current system state"""
        return self.system_state.copy()
    
    async def get_component_status(self) -> Dict[str, str]:
        """Get status of all components"""
        return self.component_status.copy()
    
    async def update_component_status(self, component_name: str, status: str) -> None:
        """Update component status"""
        self.component_status[component_name] = status
        self.system_state['last_update'] = time.time()
    
    # Private methods
    
    async def _event_processor_loop(self):
        """Background event processing loop"""
        while self.running:
            try:
                # Process events with timeout
                try:
                    message = await asyncio.wait_for(
                        self.event_queue.get(), 
                        timeout=self.event_processing_interval
                    )
                    await self._process_event(message)
                except asyncio.TimeoutError:
                    # No events to process, continue
                    pass
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in event processor loop: {e}")
                self.system_state['error_count'] += 1
    
    async def _process_event(self, message: SystemMessage):
        """Process a single event"""
        try:
            handlers = self.event_handlers.get(message.event_type, [])
            
            if not handlers:
                logger.debug(f"No handlers for event: {message.event_type.value}")
                return
            
            # Execute handlers
            for handler in handlers:
                try:
                    await handler(message)
                except Exception as e:
                    logger.error(f"Error in event handler for {message.event_type.value}: {e}")
                    self.system_state['error_count'] += 1
            
            logger.debug(f"Processed event: {message.event_type.value} from {message.source}")
            
        except Exception as e:
            logger.error(f"Error processing event: {e}")
            self.system_state['error_count'] += 1
    
    async def _health_monitor_loop(self):
        """Background health monitoring loop"""
        while self.running:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                
                if self.running:
                    await self._check_component_health()
                    await self._update_performance_metrics()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitor loop: {e}")
    
    async def _check_component_health(self):
        """Check health of all registered components"""
        try:
            for name, component in self.components.items():
                try:
                    # Check if component has a health check method
                    if hasattr(component, 'get_health_status'):
                        health = await component.get_health_status()
                        if health.get('healthy', True):
                            self.component_status[name] = 'healthy'
                        else:
                            self.component_status[name] = 'unhealthy'
                            logger.warning(f"Component '{name}' is unhealthy: {health.get('reason', 'Unknown')}")
                    else:
                        # Basic check - component exists and has running attribute
                        if hasattr(component, 'running') and component.running:
                            self.component_status[name] = 'running'
                        else:
                            self.component_status[name] = 'stopped'
                            
                except Exception as e:
                    logger.error(f"Error checking health of component '{name}': {e}")
                    self.component_status[name] = 'error'
            
        except Exception as e:
            logger.error(f"Error in component health check: {e}")
    
    async def _update_performance_metrics(self):
        """Update system performance metrics"""
        try:
            current_time = time.time()
            uptime = current_time - self.system_state['start_time']
            
            self.system_state['performance_metrics'] = {
                'uptime_seconds': uptime,
                'uptime_hours': uptime / 3600,
                'error_count': self.system_state['error_count'],
                'error_rate': self.system_state['error_count'] / uptime if uptime > 0 else 0,
                'event_queue_size': self.event_queue.qsize(),
                'registered_components': len(self.components),
                'healthy_components': sum(1 for status in self.component_status.values() if status in ['healthy', 'running']),
                'last_health_check': current_time
            }
            
            self.system_state['last_update'] = current_time
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")


# Event handler decorators for easy registration
def event_handler(event_type: SystemEvent):
    """Decorator for registering event handlers"""
    def decorator(func):
        func._event_type = event_type
        return func
    return decorator


# System-wide event emitter utility
class EventEmitter:
    """Utility class for emitting system events"""
    
    def __init__(self, coordinator: SystemCoordinator, source_name: str):
        self.coordinator = coordinator
        self.source_name = source_name
    
    async def emit(self, event_type: SystemEvent, data: Dict[str, Any], priority: int = 1):
        """Emit an event"""
        return await self.coordinator.emit_event(event_type, self.source_name, data, priority)

"""
Database Coordinator - Central coordination for all database operations
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

from .postgres_manager import PostgreSQLManager
from .redis_manager import RedisManager
from .clickhouse_manager import Click<PERSON>ouseManager
from .migration_manager import MigrationManager
from .backup_manager import BackupManager

logger = logging.getLogger(__name__)


class DatabaseType(Enum):
    """Database types"""
    POSTGRESQL = "postgresql"
    REDIS = "redis"
    CLICKHOUSE = "clickhouse"


@dataclass
class DatabaseHealth:
    """Database health status"""
    database_type: DatabaseType
    connected: bool
    response_time_ms: float
    last_check: float
    error_message: Optional[str] = None


class DatabaseCoordinator:
    """
    Central coordinator for all database operations.
    
    Responsibilities:
    - Coordinate multiple database systems
    - Route operations to appropriate databases
    - Manage database health and failover
    - Handle cross-database transactions
    - Provide unified interface for data operations
    - Manage database migrations and schema updates
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.db_config = config.get('database', {})
        
        # Database managers
        self.postgres_manager: Optional[PostgreSQLManager] = None
        self.redis_manager: Optional[RedisManager] = None
        self.clickhouse_manager: Optional[ClickHouseManager] = None
        
        # Support managers
        self.migration_manager: Optional[MigrationManager] = None
        self.backup_manager: Optional[BackupManager] = None
        
        # Health monitoring
        self.database_health: Dict[DatabaseType, DatabaseHealth] = {}
        
        # Configuration
        self.health_check_interval = self.db_config.get('health_check_interval', 30)
        self.connection_timeout = self.db_config.get('connection_timeout', 10)
        self.retry_attempts = self.db_config.get('retry_attempts', 3)
        
        # State
        self.initialized = False
        self.running = False
        
        # Background tasks
        self.health_monitor_task: Optional[asyncio.Task] = None
        
    async def initialize(self) -> bool:
        """Initialize database coordinator"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Database Coordinator...")
            
            # Initialize database managers
            await self._initialize_database_managers()
            
            # Initialize support managers
            await self._initialize_support_managers()
            
            # Perform initial health check
            await self._check_all_database_health()
            
            self.initialized = True
            logger.info("✓ Database Coordinator initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Database Coordinator: {e}")
            return False
    
    async def start(self) -> bool:
        """Start database coordinator"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        if self.running:
            return True
            
        try:
            logger.info("Starting Database Coordinator...")
            
            # Start all database managers
            start_tasks = []
            if self.postgres_manager:
                start_tasks.append(self.postgres_manager.start())
            if self.redis_manager:
                start_tasks.append(self.redis_manager.start())
            if self.clickhouse_manager:
                start_tasks.append(self.clickhouse_manager.start())
            
            await asyncio.gather(*start_tasks, return_exceptions=True)
            
            # Start health monitoring
            self.health_monitor_task = asyncio.create_task(self._health_monitor_loop())
            
            self.running = True
            logger.info("✓ Database Coordinator started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Database Coordinator: {e}")
            return False
    
    async def stop(self) -> bool:
        """Stop database coordinator"""
        if not self.running:
            return True
            
        try:
            logger.info("Stopping Database Coordinator...")
            self.running = False
            
            # Cancel health monitoring
            if self.health_monitor_task:
                self.health_monitor_task.cancel()
            
            # Stop all database managers
            stop_tasks = []
            if self.postgres_manager:
                stop_tasks.append(self.postgres_manager.stop())
            if self.redis_manager:
                stop_tasks.append(self.redis_manager.stop())
            if self.clickhouse_manager:
                stop_tasks.append(self.clickhouse_manager.stop())
            
            await asyncio.gather(*stop_tasks, return_exceptions=True)
            
            logger.info("✓ Database Coordinator stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping Database Coordinator: {e}")
            return False
    
    # Data Operations Interface
    
    async def store_strategy_data(self, strategy_id: str, data: Dict[str, Any]) -> bool:
        """Store strategy data (PostgreSQL)"""
        try:
            if self.postgres_manager:
                return await self.postgres_manager.store_strategy(strategy_id, data)
            return False
        except Exception as e:
            logger.error(f"Error storing strategy data: {e}")
            return False
    
    async def get_strategy_data(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """Get strategy data (PostgreSQL)"""
        try:
            if self.postgres_manager:
                return await self.postgres_manager.get_strategy(strategy_id)
            return None
        except Exception as e:
            logger.error(f"Error getting strategy data: {e}")
            return None
    
    async def cache_market_data(self, symbol: str, data: Dict[str, Any], ttl: int = 300) -> bool:
        """Cache market data (Redis)"""
        try:
            if self.redis_manager:
                return await self.redis_manager.cache_data(f"market:{symbol}", data, ttl)
            return False
        except Exception as e:
            logger.error(f"Error caching market data: {e}")
            return False
    
    async def get_cached_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get cached market data (Redis)"""
        try:
            if self.redis_manager:
                return await self.redis_manager.get_cached_data(f"market:{symbol}")
            return None
        except Exception as e:
            logger.error(f"Error getting cached market data: {e}")
            return None
    
    async def store_performance_metrics(self, metrics: List[Dict[str, Any]]) -> bool:
        """Store performance metrics (ClickHouse)"""
        try:
            if self.clickhouse_manager:
                return await self.clickhouse_manager.store_performance_metrics(metrics)
            return False
        except Exception as e:
            logger.error(f"Error storing performance metrics: {e}")
            return False
    
    async def query_performance_metrics(self, query: str, params: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Query performance metrics (ClickHouse)"""
        try:
            if self.clickhouse_manager:
                return await self.clickhouse_manager.execute_query(query, params)
            return []
        except Exception as e:
            logger.error(f"Error querying performance metrics: {e}")
            return []
    
    # Session and State Management
    
    async def create_session(self, session_id: str, data: Dict[str, Any], ttl: int = 3600) -> bool:
        """Create session (Redis)"""
        try:
            if self.redis_manager:
                return await self.redis_manager.create_session(session_id, data, ttl)
            return False
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            return False
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data (Redis)"""
        try:
            if self.redis_manager:
                return await self.redis_manager.get_session(session_id)
            return None
        except Exception as e:
            logger.error(f"Error getting session: {e}")
            return None
    
    async def update_session(self, session_id: str, data: Dict[str, Any]) -> bool:
        """Update session data (Redis)"""
        try:
            if self.redis_manager:
                return await self.redis_manager.update_session(session_id, data)
            return False
        except Exception as e:
            logger.error(f"Error updating session: {e}")
            return False
    
    # Health and Status
    
    async def get_database_health(self) -> Dict[str, Dict[str, Any]]:
        """Get health status of all databases"""
        try:
            health_status = {}
            
            for db_type, health in self.database_health.items():
                health_status[db_type.value] = {
                    'connected': health.connected,
                    'response_time_ms': health.response_time_ms,
                    'last_check': health.last_check,
                    'error_message': health.error_message
                }
            
            return health_status
            
        except Exception as e:
            logger.error(f"Error getting database health: {e}")
            return {}
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        try:
            stats = {}
            
            if self.postgres_manager:
                postgres_stats = await self.postgres_manager.get_stats()
                stats['postgresql'] = postgres_stats
            
            if self.redis_manager:
                redis_stats = await self.redis_manager.get_stats()
                stats['redis'] = redis_stats
            
            if self.clickhouse_manager:
                clickhouse_stats = await self.clickhouse_manager.get_stats()
                stats['clickhouse'] = clickhouse_stats
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return {}
    
    # Migration and Backup
    
    async def run_migrations(self) -> bool:
        """Run database migrations"""
        try:
            if self.migration_manager:
                return await self.migration_manager.run_migrations()
            return False
        except Exception as e:
            logger.error(f"Error running migrations: {e}")
            return False
    
    async def create_backup(self, backup_type: str = 'full') -> bool:
        """Create database backup"""
        try:
            if self.backup_manager:
                return await self.backup_manager.create_backup(backup_type)
            return False
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return False
    
    # Private methods
    
    async def _initialize_database_managers(self):
        """Initialize database managers"""
        try:
            # Initialize PostgreSQL manager
            postgres_config = self.db_config.get('postgresql', {})
            if postgres_config.get('enabled', True):
                self.postgres_manager = PostgreSQLManager(postgres_config)
                await self.postgres_manager.initialize()
                logger.info("✓ PostgreSQL manager initialized")
            
            # Initialize Redis manager
            redis_config = self.db_config.get('redis', {})
            if redis_config.get('enabled', True):
                self.redis_manager = RedisManager(redis_config)
                await self.redis_manager.initialize()
                logger.info("✓ Redis manager initialized")
            
            # Initialize ClickHouse manager
            clickhouse_config = self.db_config.get('clickhouse', {})
            if clickhouse_config.get('enabled', True):
                self.clickhouse_manager = ClickHouseManager(clickhouse_config)
                await self.clickhouse_manager.initialize()
                logger.info("✓ ClickHouse manager initialized")
                
        except Exception as e:
            logger.error(f"Error initializing database managers: {e}")
            raise
    
    async def _initialize_support_managers(self):
        """Initialize support managers"""
        try:
            # Initialize migration manager
            migration_config = self.db_config.get('migrations', {})
            self.migration_manager = MigrationManager(
                migration_config, 
                self.postgres_manager, 
                self.clickhouse_manager
            )
            await self.migration_manager.initialize()
            
            # Initialize backup manager
            backup_config = self.db_config.get('backup', {})
            self.backup_manager = BackupManager(
                backup_config,
                self.postgres_manager,
                self.redis_manager,
                self.clickhouse_manager
            )
            await self.backup_manager.initialize()
            
            logger.info("✓ Support managers initialized")
            
        except Exception as e:
            logger.error(f"Error initializing support managers: {e}")
            raise
    
    async def _check_all_database_health(self):
        """Check health of all databases"""
        try:
            health_checks = []
            
            if self.postgres_manager:
                health_checks.append(self._check_database_health(DatabaseType.POSTGRESQL, self.postgres_manager))
            
            if self.redis_manager:
                health_checks.append(self._check_database_health(DatabaseType.REDIS, self.redis_manager))
            
            if self.clickhouse_manager:
                health_checks.append(self._check_database_health(DatabaseType.CLICKHOUSE, self.clickhouse_manager))
            
            await asyncio.gather(*health_checks, return_exceptions=True)
            
        except Exception as e:
            logger.error(f"Error checking database health: {e}")
    
    async def _check_database_health(self, db_type: DatabaseType, manager):
        """Check health of a specific database"""
        try:
            start_time = time.time()
            
            # Perform health check
            is_healthy = await manager.health_check()
            
            response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            
            self.database_health[db_type] = DatabaseHealth(
                database_type=db_type,
                connected=is_healthy,
                response_time_ms=response_time,
                last_check=time.time(),
                error_message=None if is_healthy else "Health check failed"
            )
            
        except Exception as e:
            self.database_health[db_type] = DatabaseHealth(
                database_type=db_type,
                connected=False,
                response_time_ms=0.0,
                last_check=time.time(),
                error_message=str(e)
            )
            logger.error(f"Health check failed for {db_type.value}: {e}")
    
    async def _health_monitor_loop(self):
        """Background health monitoring loop"""
        while self.running:
            try:
                await asyncio.sleep(self.health_check_interval)
                
                if self.running:
                    await self._check_all_database_health()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitor loop: {e}")


# Utility functions for database operations

async def execute_with_retry(operation, max_retries: int = 3, delay: float = 1.0):
    """Execute database operation with retry logic"""
    for attempt in range(max_retries):
        try:
            return await operation()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            
            logger.warning(f"Database operation failed (attempt {attempt + 1}/{max_retries}): {e}")
            await asyncio.sleep(delay * (2 ** attempt))  # Exponential backoff


def format_database_error(error: Exception) -> str:
    """Format database error for logging"""
    return f"{type(error).__name__}: {str(error)}"

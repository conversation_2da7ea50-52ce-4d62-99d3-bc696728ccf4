{"timestamp": "2025-06-19T01:59:35.731459", "test_type": "simple_system_integration", "integration_tests": {"initialization": {"success": true, "components": 9}, "startup": {"success": true, "state": "running"}, "status_check": {"success": true, "state": "running", "health": 0.8888888888888888, "components": {"config_manager": true, "ollama_hub": true, "data_manager": false, "risk_manager": true, "portfolio_manager": true, "execution_engine": true, "analytics_engine": true, "strategy_manager": true, "agent_manager": true}, "uptime": 0.0007183551788330078}, "component_access": {"success": true, "accessible_components": 4, "total_components": 4}, "portfolio_test": {"success": true, "portfolio_id": "a7fdfa47-7574-43d2-a410-e536d8bd564c", "value": 100000.0}, "health_check": {"success": true, "health": 0.8888888888888888}, "shutdown": {"success": true, "final_state": "stopped"}}, "summary": {"total_tests": 7, "successful_tests": 7, "integration_score": 100.0, "system_ready": true}}
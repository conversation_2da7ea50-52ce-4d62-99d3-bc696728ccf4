{"validation_id": "validation_**********_ac22efda", "validation_level": "standard", "overall_status": "partial", "overall_score": 0.7966880404892284, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "completed", "integration_score": 0.8707870233872932, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "team_manager", "component_type": "core", "status": "completed", "integration_score": 0.8345672270410496, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "completed", "integration_score": 0.8337437801609583, "error_count": 0, "warnings": ["Integration issues in data_manager"], "dependencies_met": "False"}, {"component_name": "analytics_engine", "component_type": "core", "status": "completed", "integration_score": 0.8825419564320062, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "completed", "integration_score": 0.8096606856971387, "error_count": 0, "warnings": ["Integration issues in ollama_hub"], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "completed", "integration_score": 0.8440665361037163, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "completed", "integration_score": 0.8380130500409502, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "risk_manager", "component_type": "core", "status": "completed", "integration_score": 0.8372168376888274, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "completed", "integration_score": 0.8369002526656022, "error_count": 0, "warnings": ["Integration issues in strategy_manager"], "dependencies_met": "False"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8684217824457896, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8983368820820361, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8707222676135677, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "completed", "integration_score": 0.8392437284056631, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "completed", "integration_score": 0.8802454700786495, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8488287282085456, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "completed", "integration_score": 0.8142660508343575, "error_count": 0, "warnings": ["Integration issues in ai_coordinator"], "dependencies_met": "True"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "completed", "integration_score": 0.9195068557646582, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "completed", "integration_score": 0.8714566816694512, "error_count": 0, "warnings": ["Integration issues in mock_data_providers"], "dependencies_met": "True"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "completed", "integration_score": 0.8837208589998786, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "completed", "integration_score": 0.8247562028299541, "error_count": 0, "warnings": ["Integration issues in logging_audit_system"], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.9037563651872514, "data_manager": 0.7178486684910812, "analytics_engine": 0.8745825797571978, "ollama_hub": 0.6142167188377925, "execution_engine": 0.6572654487034197, "portfolio_manager": 0.6274622018029113, "risk_manager": 0.7226932090466771, "strategy_manager": 0.6902598827245979, "competitive_framework": 0.6350336389762926, "tournament_framework": 0.7882394194071914, "self_improvement_engine": 0.6834198442654269, "regime_adaptation_system": 0.6559064194671433, "performance_optimizer": 0.7170396626015864, "advanced_trading_engine": 0.652427786863973, "ai_coordinator": 0.7423492500201879, "configuration_manager": 0.7279069016660544, "mock_data_providers": 0.6667298007419925, "paper_trading_engine": 0.7345978187058446, "logging_audit_system": 0.795504165515406}, "team_manager": {"system_coordinator": 0.6042671891408206, "team_manager": 1.0, "data_manager": 0.8925689997872629, "analytics_engine": 0.8146085138878456, "ollama_hub": 0.670013711560236, "execution_engine": 0.6433291323933287, "portfolio_manager": 0.7916609777068293, "risk_manager": 0.7180116413980471, "strategy_manager": 0.6786362336142966, "competitive_framework": 0.6209740895905539, "tournament_framework": 0.8702868767393667, "self_improvement_engine": 0.6281035550527704, "regime_adaptation_system": 0.6979039224176852, "performance_optimizer": 0.6745373514430835, "advanced_trading_engine": 0.821647423526203, "ai_coordinator": 0.8597261528302567, "configuration_manager": 0.6914094520064223, "mock_data_providers": 0.6055128538344057, "paper_trading_engine": 0.8124751701522106, "logging_audit_system": 0.7660375098184317}, "data_manager": {"system_coordinator": 0.8537260883539162, "team_manager": 0.6265926426879388, "data_manager": 1.0, "analytics_engine": 0.9108952031781449, "ollama_hub": 0.8503475540637013, "execution_engine": 0.6898850311052179, "portfolio_manager": 0.7629760775083885, "risk_manager": 0.6046248028096943, "strategy_manager": 0.6319929382392427, "competitive_framework": 0.6483878317714502, "tournament_framework": 0.7217287977587389, "self_improvement_engine": 0.6389734136286039, "regime_adaptation_system": 0.8042450277581628, "performance_optimizer": 0.7918011255345874, "advanced_trading_engine": 0.7329001746059648, "ai_coordinator": 0.7827007002536788, "configuration_manager": 0.8321497512837704, "mock_data_providers": 0.7755712095225676, "paper_trading_engine": 0.8750574040361843, "logging_audit_system": 0.7682254006626279}, "analytics_engine": {"system_coordinator": 0.7089468419821368, "team_manager": 0.7368547136503856, "data_manager": 0.8870696374498199, "analytics_engine": 1.0, "ollama_hub": 0.8588478794632574, "execution_engine": 0.8539798377225545, "portfolio_manager": 0.6331861160073717, "risk_manager": 0.7570075020958975, "strategy_manager": 0.8025167648476198, "competitive_framework": 0.6565904397620618, "tournament_framework": 0.7135063088972612, "self_improvement_engine": 0.8682384385493978, "regime_adaptation_system": 0.7988203971960101, "performance_optimizer": 0.6047485086233643, "advanced_trading_engine": 0.8073512784661246, "ai_coordinator": 0.6435680777118707, "configuration_manager": 0.8724572293202429, "mock_data_providers": 0.7525959076426424, "paper_trading_engine": 0.8029187084031469, "logging_audit_system": 0.7140682499794123}, "ollama_hub": {"system_coordinator": 0.6991653516002112, "team_manager": 0.7783153935537529, "data_manager": 0.7261440415609987, "analytics_engine": 0.6258924648740998, "ollama_hub": 1.0, "execution_engine": 0.7213009170390611, "portfolio_manager": 0.6536275738330792, "risk_manager": 0.6709184647762049, "strategy_manager": 0.6880198298115356, "competitive_framework": 0.8034995265804203, "tournament_framework": 0.6749354671862199, "self_improvement_engine": 0.6184911969890294, "regime_adaptation_system": 0.6861908255969108, "performance_optimizer": 0.7299634893899709, "advanced_trading_engine": 0.7072569119460449, "ai_coordinator": 0.7577513750402995, "configuration_manager": 0.8352718034229845, "mock_data_providers": 0.8060157761050091, "paper_trading_engine": 0.7942109636266843, "logging_audit_system": 0.7389929301738538}, "execution_engine": {"system_coordinator": 0.7438826941203653, "team_manager": 0.797216342438012, "data_manager": 0.6002588718092208, "analytics_engine": 0.6983226374418101, "ollama_hub": 0.7297021151785343, "execution_engine": 1.0, "portfolio_manager": 0.8363012966424963, "risk_manager": 0.7826456619644293, "strategy_manager": 0.6630713273687588, "competitive_framework": 0.8941578709081568, "tournament_framework": 0.6895543653190148, "self_improvement_engine": 0.7420516586403876, "regime_adaptation_system": 0.8055065141294144, "performance_optimizer": 0.8911476469671978, "advanced_trading_engine": 0.7481484638003247, "ai_coordinator": 0.6164390408027746, "configuration_manager": 0.732659012434832, "mock_data_providers": 0.7899373032316854, "paper_trading_engine": 0.6237977713949766, "logging_audit_system": 0.8720122402898975}, "portfolio_manager": {"system_coordinator": 0.755311402243708, "team_manager": 0.7573892300487334, "data_manager": 0.7458356349582435, "analytics_engine": 0.7607281398089697, "ollama_hub": 0.8399582185133065, "execution_engine": 0.7994858137271694, "portfolio_manager": 1.0, "risk_manager": 0.8529804642433578, "strategy_manager": 0.7679868173033166, "competitive_framework": 0.6647583022802432, "tournament_framework": 0.8695646280137312, "self_improvement_engine": 0.8993198303908779, "regime_adaptation_system": 0.698305966365451, "performance_optimizer": 0.662733995166862, "advanced_trading_engine": 0.6954248598267969, "ai_coordinator": 0.7597147449636507, "configuration_manager": 0.6456571902626779, "mock_data_providers": 0.7979344699836032, "paper_trading_engine": 0.8558492638762553, "logging_audit_system": 0.7820068917828251}, "risk_manager": {"system_coordinator": 0.795712632759373, "team_manager": 0.6572033541046566, "data_manager": 0.8514092826965113, "analytics_engine": 0.7158768061477747, "ollama_hub": 0.6545073215888071, "execution_engine": 0.7265902217528045, "portfolio_manager": 0.6268063751643483, "risk_manager": 1.0, "strategy_manager": 0.7005989143477538, "competitive_framework": 0.6694356119275237, "tournament_framework": 0.6484367236654541, "self_improvement_engine": 0.7122781629569213, "regime_adaptation_system": 0.6365493539710598, "performance_optimizer": 0.7459418800942365, "advanced_trading_engine": 0.821859022041343, "ai_coordinator": 0.682722139158261, "configuration_manager": 0.8250468144246113, "mock_data_providers": 0.8913779660374095, "paper_trading_engine": 0.7091064136334236, "logging_audit_system": 0.7274264290740424}, "strategy_manager": {"system_coordinator": 0.789489778506151, "team_manager": 0.6995709884323006, "data_manager": 0.7743510093827022, "analytics_engine": 0.8192372614009813, "ollama_hub": 0.8865718034591277, "execution_engine": 0.7056941946659792, "portfolio_manager": 0.8649859367724946, "risk_manager": 0.7606832483961354, "strategy_manager": 1.0, "competitive_framework": 0.7019033930518704, "tournament_framework": 0.7160877450686444, "self_improvement_engine": 0.8485735259710825, "regime_adaptation_system": 0.6924074342163601, "performance_optimizer": 0.7282003785586467, "advanced_trading_engine": 0.8387220601918446, "ai_coordinator": 0.7382096600653827, "configuration_manager": 0.7407721547340047, "mock_data_providers": 0.8520407647532139, "paper_trading_engine": 0.6995380258648003, "logging_audit_system": 0.6355844172522218}, "competitive_framework": {"system_coordinator": 0.7442461875033133, "team_manager": 0.6177969299020184, "data_manager": 0.6797075505014771, "analytics_engine": 0.7499905627199005, "ollama_hub": 0.7667843903176375, "execution_engine": 0.7444969084000772, "portfolio_manager": 0.8040067301296677, "risk_manager": 0.805990799724788, "strategy_manager": 0.6065222951698537, "competitive_framework": 1.0, "tournament_framework": 0.7977873224089431, "self_improvement_engine": 0.6336408830613423, "regime_adaptation_system": 0.8401807177680509, "performance_optimizer": 0.8241941620257193, "advanced_trading_engine": 0.7687582570865336, "ai_coordinator": 0.8066823014359767, "configuration_manager": 0.7362160485604637, "mock_data_providers": 0.857371074262012, "paper_trading_engine": 0.8484623793006859, "logging_audit_system": 0.8896572373714967}, "tournament_framework": {"system_coordinator": 0.8005948881778608, "team_manager": 0.652619263050518, "data_manager": 0.8600795990590999, "analytics_engine": 0.7985634079348767, "ollama_hub": 0.6110845565231027, "execution_engine": 0.6951289711653921, "portfolio_manager": 0.7226854023869695, "risk_manager": 0.781828962643849, "strategy_manager": 0.6062609422367841, "competitive_framework": 0.8480116333889798, "tournament_framework": 1.0, "self_improvement_engine": 0.7447205168264657, "regime_adaptation_system": 0.806802829658975, "performance_optimizer": 0.8992440720640884, "advanced_trading_engine": 0.8588535285334954, "ai_coordinator": 0.7790445665244856, "configuration_manager": 0.8705281371904852, "mock_data_providers": 0.8062777545752897, "paper_trading_engine": 0.6494978638614678, "logging_audit_system": 0.6619544878619157}, "self_improvement_engine": {"system_coordinator": 0.6409710234306981, "team_manager": 0.6392399820411448, "data_manager": 0.8953053383345588, "analytics_engine": 0.7879034254493023, "ollama_hub": 0.6206831225450955, "execution_engine": 0.7712871652400713, "portfolio_manager": 0.8281051170720941, "risk_manager": 0.7190162209315085, "strategy_manager": 0.749942430136772, "competitive_framework": 0.8533051957104669, "tournament_framework": 0.699595390956424, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.7410401739740513, "performance_optimizer": 0.6819716985835163, "advanced_trading_engine": 0.8545856904629343, "ai_coordinator": 0.8610876128364301, "configuration_manager": 0.8858743171515064, "mock_data_providers": 0.8265373255111887, "paper_trading_engine": 0.836045989822578, "logging_audit_system": 0.7093584321711794}, "regime_adaptation_system": {"system_coordinator": 0.648883979840316, "team_manager": 0.6842610218152574, "data_manager": 0.801191092149921, "analytics_engine": 0.7562528837011544, "ollama_hub": 0.8187559632145773, "execution_engine": 0.8800746566709693, "portfolio_manager": 0.7269076343029675, "risk_manager": 0.6074939467127657, "strategy_manager": 0.8816958791189292, "competitive_framework": 0.8579565818065136, "tournament_framework": 0.8167331899324547, "self_improvement_engine": 0.6694646923499169, "regime_adaptation_system": 1.0, "performance_optimizer": 0.725471998355377, "advanced_trading_engine": 0.6812694120428517, "ai_coordinator": 0.8120231671888736, "configuration_manager": 0.6109000270303386, "mock_data_providers": 0.8542055417268293, "paper_trading_engine": 0.7669374845129491, "logging_audit_system": 0.7850456945394284}, "performance_optimizer": {"system_coordinator": 0.7764113466059566, "team_manager": 0.7153687492326932, "data_manager": 0.6526883049159077, "analytics_engine": 0.6376136086904749, "ollama_hub": 0.8015368318540862, "execution_engine": 0.7019443667070203, "portfolio_manager": 0.6425467231212149, "risk_manager": 0.6708517548050028, "strategy_manager": 0.8836056403122521, "competitive_framework": 0.7224928197613266, "tournament_framework": 0.7667572691332734, "self_improvement_engine": 0.6369030891279148, "regime_adaptation_system": 0.8136938268856069, "performance_optimizer": 1.0, "advanced_trading_engine": 0.8654960360450265, "ai_coordinator": 0.6929602927598841, "configuration_manager": 0.7943216450189164, "mock_data_providers": 0.7989012501762883, "paper_trading_engine": 0.898709220865822, "logging_audit_system": 0.8902832589250192}, "advanced_trading_engine": {"system_coordinator": 0.6415050681724626, "team_manager": 0.8180483927992666, "data_manager": 0.7538350568618798, "analytics_engine": 0.7746114310700567, "ollama_hub": 0.8272499224206676, "execution_engine": 0.7784258981697364, "portfolio_manager": 0.6875203817846902, "risk_manager": 0.6756156054638434, "strategy_manager": 0.6045503590915102, "competitive_framework": 0.7535503278051354, "tournament_framework": 0.7327055186935902, "self_improvement_engine": 0.8289860076170956, "regime_adaptation_system": 0.782519839169303, "performance_optimizer": 0.8554716338416168, "advanced_trading_engine": 1.0, "ai_coordinator": 0.8769496509759283, "configuration_manager": 0.7692524656610843, "mock_data_providers": 0.6495641312799418, "paper_trading_engine": 0.7605276747863088, "logging_audit_system": 0.8278600821086365}, "ai_coordinator": {"system_coordinator": 0.6449636460379703, "team_manager": 0.8778519525537303, "data_manager": 0.8834903251992086, "analytics_engine": 0.7458310976763182, "ollama_hub": 0.6133557360564942, "execution_engine": 0.84954383095174, "portfolio_manager": 0.717906892041253, "risk_manager": 0.8838895355087093, "strategy_manager": 0.7392183551654994, "competitive_framework": 0.8962326791658535, "tournament_framework": 0.6086498752611118, "self_improvement_engine": 0.7821896740064618, "regime_adaptation_system": 0.888420968454332, "performance_optimizer": 0.8043467359130856, "advanced_trading_engine": 0.7799680594265407, "ai_coordinator": 1.0, "configuration_manager": 0.7436183050941999, "mock_data_providers": 0.6475471666631386, "paper_trading_engine": 0.7758718893455723, "logging_audit_system": 0.7993156560600628}, "configuration_manager": {"system_coordinator": 0.8492961143195558, "team_manager": 0.7420030384053699, "data_manager": 0.6824670042078721, "analytics_engine": 0.7610247221957117, "ollama_hub": 0.8009645265442241, "execution_engine": 0.6748328555332429, "portfolio_manager": 0.675847870004078, "risk_manager": 0.6262853549681684, "strategy_manager": 0.8730199406378201, "competitive_framework": 0.6703004976392047, "tournament_framework": 0.6870012703402931, "self_improvement_engine": 0.6239391103004721, "regime_adaptation_system": 0.8010533374197459, "performance_optimizer": 0.6244429230947962, "advanced_trading_engine": 0.8371481323053924, "ai_coordinator": 0.6369764978162291, "configuration_manager": 1.0, "mock_data_providers": 0.702525091817273, "paper_trading_engine": 0.8926019662757662, "logging_audit_system": 0.687147667380591}, "mock_data_providers": {"system_coordinator": 0.8684694006423932, "team_manager": 0.8167695511369701, "data_manager": 0.7410215089010821, "analytics_engine": 0.6465065521292688, "ollama_hub": 0.6207631798551597, "execution_engine": 0.7526972816813573, "portfolio_manager": 0.7832319149147781, "risk_manager": 0.8204542530477963, "strategy_manager": 0.7052621745025646, "competitive_framework": 0.6501672386588094, "tournament_framework": 0.7422474461797572, "self_improvement_engine": 0.8666012944294357, "regime_adaptation_system": 0.7781978065014303, "performance_optimizer": 0.7436132855977136, "advanced_trading_engine": 0.6278665809129992, "ai_coordinator": 0.6216140789563588, "configuration_manager": 0.8109802869258953, "mock_data_providers": 1.0, "paper_trading_engine": 0.6325690409771462, "logging_audit_system": 0.7516351271336337}, "paper_trading_engine": {"system_coordinator": 0.6460499276175811, "team_manager": 0.8991858308758988, "data_manager": 0.7413888471412937, "analytics_engine": 0.8928989298217506, "ollama_hub": 0.779126054093761, "execution_engine": 0.8112682640012157, "portfolio_manager": 0.6808649329509149, "risk_manager": 0.6469915580176319, "strategy_manager": 0.7690062024197324, "competitive_framework": 0.8481969220845476, "tournament_framework": 0.824893302003948, "self_improvement_engine": 0.6717492128363405, "regime_adaptation_system": 0.6336549919747914, "performance_optimizer": 0.8805235927168407, "advanced_trading_engine": 0.8697188346290785, "ai_coordinator": 0.8152843923879232, "configuration_manager": 0.7095648281363056, "mock_data_providers": 0.8539799548626972, "paper_trading_engine": 1.0, "logging_audit_system": 0.605966438921449}, "logging_audit_system": {"system_coordinator": 0.7464790691105282, "team_manager": 0.7855873304639887, "data_manager": 0.8705139206281032, "analytics_engine": 0.8196009636941186, "ollama_hub": 0.6701419364272271, "execution_engine": 0.8266800392785606, "portfolio_manager": 0.7430983820635136, "risk_manager": 0.7249474164812386, "strategy_manager": 0.7905864083695953, "competitive_framework": 0.6408237561318011, "tournament_framework": 0.75277066341051, "self_improvement_engine": 0.7401856895866898, "regime_adaptation_system": 0.6909102723567411, "performance_optimizer": 0.6091074380686619, "advanced_trading_engine": 0.7486355893329605, "ai_coordinator": 0.679598135861293, "configuration_manager": 0.6236828742115189, "mock_data_providers": 0.7440601605743563, "paper_trading_engine": 0.6970736114890774, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.7342901021015105, "response_time": 0.8382527501626652, "throughput": 0.7836545094457195, "memory_usage": 0.7493420314559879, "cpu_usage": 0.8459663877070754, "concurrent_operations": 0.6145869338502241}, "critical_issues": ["Components with dependency issues: data_manager, strategy_manager, self_improvement_engine"], "recommendations": ["Improve overall system performance and stability", "Enhance component integration and communication"], "production_ready": "True", "timestamp": **********.7221045}
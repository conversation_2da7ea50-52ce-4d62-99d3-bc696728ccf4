{"timestamp": "2025-06-19T00:50:40.974410", "test_type": "complete_system_integration", "phases": {"core_initialization": {"success": true, "components": 4}, "agent_deployment": {"success": true, "agents_created": 3, "total_attempted": 3}, "strategy_management": {"success": true, "strategies_created": 2}, "risk_management": {"success": false, "error": "'RiskManager' object has no attribute 'check_risk_limits'"}, "ai_task_execution": {"success": true, "tasks_completed": 1, "results": {"risk_monitoring": {"success": true, "status": "risk_limits_monitored"}}}, "system_integration": {"success": true, "integration_score": 3, "total_integrations": 5, "integration_percentage": 60.0}}, "summary": {"total_phases": 6, "successful_phases": 5, "success_rate": 83.33333333333334, "ai_agents_deployed": 3, "integration_score": 3, "message_broker_messages": 0}}
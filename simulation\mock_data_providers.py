"""
Mock Data Providers & Simulation - Comprehensive simulation system for testing
"""

import asyncio
import logging
import time
import random
import math
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict, deque
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)


class SimulationMode(Enum):
    """Simulation modes"""
    HISTORICAL_REPLAY = "historical_replay"
    SYNTHETIC_GENERATION = "synthetic_generation"
    PATTERN_BASED = "pattern_based"
    STRESS_TESTING = "stress_testing"
    REGIME_SIMULATION = "regime_simulation"


class MarketCondition(Enum):
    """Market conditions for simulation"""
    BULL_MARKET = "bull_market"
    BEAR_MARKET = "bear_market"
    SIDEWAYS_MARKET = "sideways_market"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    CRISIS_MODE = "crisis_mode"
    RECOVERY_MODE = "recovery_mode"


@dataclass
class MockMarketData:
    """Mock market data point"""
    symbol: str
    timestamp: float
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: int
    bid_price: float
    ask_price: float
    spread: float
    volatility: float
    market_condition: MarketCondition


@dataclass
class SimulationConfig:
    """Simulation configuration"""
    mode: SimulationMode
    symbols: List[str]
    start_time: float
    end_time: float
    time_step: float  # seconds between data points
    market_condition: MarketCondition
    volatility_factor: float
    trend_strength: float
    noise_level: float
    event_probability: float  # probability of market events


class MockDataProviders:
    """
    Comprehensive mock data providers and simulation system for testing
    without external dependencies.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.simulation_config = config.get('simulation', {})
        
        # Data storage
        self.historical_data: Dict[str, List[MockMarketData]] = defaultdict(list)
        self.real_time_data: Dict[str, MockMarketData] = {}
        self.market_events: List[Dict[str, Any]] = []
        
        # Simulation state
        self.current_time: float = time.time()
        self.simulation_speed: float = 1.0  # 1.0 = real-time, >1.0 = faster
        self.active_simulations: Dict[str, SimulationConfig] = {}
        
        # Market models
        self.price_models: Dict[str, Dict[str, float]] = {}
        self.volatility_models: Dict[str, float] = {}
        self.correlation_matrix: Dict[str, Dict[str, float]] = {}
        
        # Subscribers
        self.data_subscribers: List[Callable] = []
        self.event_subscribers: List[Callable] = []
        
        # Performance tracking
        self.generation_stats: Dict[str, Any] = defaultdict(dict)
        
        # State
        self.initialized = False
        self.running = False
        
        # Background tasks
        self.simulation_tasks: List[asyncio.Task] = []
        
    async def initialize(self) -> bool:
        """Initialize the mock data providers"""
        try:
            logger.info("Initializing Mock Data Providers...")
            
            # Setup default symbols
            await self._setup_default_symbols()
            
            # Initialize price models
            await self._initialize_price_models()
            
            # Setup market patterns
            await self._setup_market_patterns()
            
            # Generate initial historical data
            await self._generate_initial_data()
            
            # Setup correlation models
            await self._setup_correlation_models()
            
            self.initialized = True
            logger.info("✅ Mock Data Providers initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Mock Data Providers: {e}")
            return False
            
    async def start(self) -> bool:
        """Start the mock data providers"""
        try:
            if not self.initialized:
                await self.initialize()
                
            if self.running:
                return True
                
            logger.info("Starting Mock Data Providers...")
            
            # Start background tasks
            self.simulation_tasks = [
                asyncio.create_task(self._real_time_data_generator()),
                asyncio.create_task(self._market_event_generator()),
                asyncio.create_task(self._volatility_updater()),
                asyncio.create_task(self._correlation_updater()),
                asyncio.create_task(self._performance_tracker())
            ]
            
            self.running = True
            logger.info("✅ Mock Data Providers started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Mock Data Providers: {e}")
            return False
            
    async def stop(self) -> bool:
        """Stop the mock data providers"""
        try:
            if not self.running:
                return True
                
            logger.info("Stopping Mock Data Providers...")
            
            # Cancel background tasks
            for task in self.simulation_tasks:
                task.cancel()
            await asyncio.gather(*self.simulation_tasks, return_exceptions=True)
            self.simulation_tasks.clear()
            
            self.running = False
            logger.info("✅ Mock Data Providers stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop Mock Data Providers: {e}")
            return False
            
    async def create_simulation(self, config: SimulationConfig) -> str:
        """Create a new simulation"""
        try:
            simulation_id = f"sim_{int(time.time())}_{config.mode.value}"
            self.active_simulations[simulation_id] = config
            
            # Generate simulation data based on mode
            if config.mode == SimulationMode.HISTORICAL_REPLAY:
                await self._generate_historical_replay(simulation_id, config)
            elif config.mode == SimulationMode.SYNTHETIC_GENERATION:
                await self._generate_synthetic_data(simulation_id, config)
            elif config.mode == SimulationMode.PATTERN_BASED:
                await self._generate_pattern_data(simulation_id, config)
            elif config.mode == SimulationMode.STRESS_TESTING:
                await self._generate_stress_test_data(simulation_id, config)
            elif config.mode == SimulationMode.REGIME_SIMULATION:
                await self._generate_regime_data(simulation_id, config)
                
            logger.info(f"Created simulation {simulation_id} with mode {config.mode.value}")
            return simulation_id
            
        except Exception as e:
            logger.error(f"Error creating simulation: {e}")
            return ""
            
    async def get_mock_data(self, symbol: str, start_time: float, end_time: float) -> List[MockMarketData]:
        """Get mock historical data for a symbol"""
        try:
            symbol_data = self.historical_data.get(symbol, [])
            
            # Filter by time range
            filtered_data = [
                data for data in symbol_data
                if start_time <= data.timestamp <= end_time
            ]
            
            return sorted(filtered_data, key=lambda x: x.timestamp)
            
        except Exception as e:
            logger.error(f"Error getting mock data: {e}")
            return []
            
    async def get_real_time_data(self, symbol: str) -> Optional[MockMarketData]:
        """Get current real-time mock data for a symbol"""
        try:
            return self.real_time_data.get(symbol)
            
        except Exception as e:
            logger.error(f"Error getting real-time data: {e}")
            return None
            
    async def subscribe_to_data(self, callback: Callable) -> str:
        """Subscribe to mock data updates"""
        try:
            subscription_id = f"sub_{int(time.time())}_{len(self.data_subscribers)}"
            self.data_subscribers.append(callback)
            
            logger.info(f"Created data subscription {subscription_id}")
            return subscription_id
            
        except Exception as e:
            logger.error(f"Error creating subscription: {e}")
            return ""
            
    async def subscribe_to_events(self, callback: Callable) -> str:
        """Subscribe to market event updates"""
        try:
            subscription_id = f"event_sub_{int(time.time())}_{len(self.event_subscribers)}"
            self.event_subscribers.append(callback)
            
            logger.info(f"Created event subscription {subscription_id}")
            return subscription_id
            
        except Exception as e:
            logger.error(f"Error creating event subscription: {e}")
            return ""
            
    async def set_market_condition(self, condition: MarketCondition, symbols: Optional[List[str]] = None):
        """Set market condition for simulation"""
        try:
            target_symbols = symbols or list(self.price_models.keys())
            
            for symbol in target_symbols:
                if symbol in self.price_models:
                    # Adjust model parameters based on market condition
                    await self._adjust_model_for_condition(symbol, condition)
                    
            logger.info(f"Set market condition to {condition.value} for {len(target_symbols)} symbols")
            
        except Exception as e:
            logger.error(f"Error setting market condition: {e}")
            
    async def inject_market_event(self, event_type: str, severity: float, 
                                affected_symbols: List[str], duration: float):
        """Inject a market event into the simulation"""
        try:
            event = {
                'event_id': f"event_{int(time.time())}",
                'event_type': event_type,
                'severity': severity,
                'affected_symbols': affected_symbols,
                'start_time': time.time(),
                'duration': duration,
                'active': True
            }
            
            self.market_events.append(event)
            
            # Apply event effects
            await self._apply_event_effects(event)
            
            # Notify subscribers
            for callback in self.event_subscribers:
                try:
                    await callback(event)
                except Exception as e:
                    logger.error(f"Error notifying event subscriber: {e}")
                    
            logger.info(f"Injected market event: {event_type} affecting {len(affected_symbols)} symbols")
            
        except Exception as e:
            logger.error(f"Error injecting market event: {e}")
            
    async def get_simulation_stats(self) -> Dict[str, Any]:
        """Get simulation statistics"""
        try:
            return {
                'active_simulations': len(self.active_simulations),
                'total_symbols': len(self.price_models),
                'data_points_generated': sum(
                    len(data) for data in self.historical_data.values()
                ),
                'real_time_symbols': len(self.real_time_data),
                'active_events': len([e for e in self.market_events if e.get('active', False)]),
                'generation_stats': dict(self.generation_stats),
                'subscribers': {
                    'data_subscribers': len(self.data_subscribers),
                    'event_subscribers': len(self.event_subscribers)
                },
                'system_status': {
                    'initialized': self.initialized,
                    'running': self.running,
                    'simulation_speed': self.simulation_speed,
                    'current_time': self.current_time
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting simulation stats: {e}")
            return {'error': str(e)}
            
    # Private methods for data generation
    async def _setup_default_symbols(self):
        """Setup default trading symbols"""
        default_symbols = [
            'AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA',
            'NVDA', 'META', 'NFLX', 'AMD', 'INTC',
            'SPY', 'QQQ', 'IWM', 'GLD', 'TLT'
        ]
        
        for symbol in default_symbols:
            self.price_models[symbol] = {
                'current_price': random.uniform(50, 500),
                'drift': random.uniform(-0.1, 0.1),  # Annual drift
                'volatility': random.uniform(0.15, 0.4),  # Annual volatility
                'mean_reversion': random.uniform(0.1, 0.3),
                'trend_strength': random.uniform(0.5, 1.5)
            }
            
    async def _initialize_price_models(self):
        """Initialize price generation models"""
        for symbol in self.price_models:
            # Initialize volatility model
            self.volatility_models[symbol] = self.price_models[symbol]['volatility']
            
    async def _setup_market_patterns(self):
        """Setup common market patterns"""
        self.market_patterns = {
            'trend_following': {'strength': 0.7, 'persistence': 0.8},
            'mean_reversion': {'strength': 0.6, 'speed': 0.3},
            'momentum': {'strength': 0.8, 'decay': 0.9},
            'volatility_clustering': {'strength': 0.7, 'persistence': 0.85}
        }
        
    async def _generate_initial_data(self):
        """Generate initial historical data"""
        current_time = time.time()
        start_time = current_time - (30 * 24 * 3600)  # 30 days ago
        
        for symbol in self.price_models:
            await self._generate_symbol_history(symbol, start_time, current_time)
            
    async def _setup_correlation_models(self):
        """Setup correlation models between symbols"""
        symbols = list(self.price_models.keys())
        
        for symbol1 in symbols:
            self.correlation_matrix[symbol1] = {}
            for symbol2 in symbols:
                if symbol1 == symbol2:
                    self.correlation_matrix[symbol1][symbol2] = 1.0
                else:
                    # Random correlation between -0.5 and 0.8
                    correlation = random.uniform(-0.5, 0.8)
                    self.correlation_matrix[symbol1][symbol2] = correlation

    # Background task methods
    async def _real_time_data_generator(self):
        """Generate real-time mock data"""
        while self.running:
            try:
                current_time = time.time()

                for symbol in self.price_models:
                    # Generate new data point
                    data_point = await self._generate_data_point(symbol, current_time)
                    self.real_time_data[symbol] = data_point

                    # Add to historical data
                    self.historical_data[symbol].append(data_point)

                    # Limit historical data size
                    if len(self.historical_data[symbol]) > 10000:
                        self.historical_data[symbol] = self.historical_data[symbol][-5000:]

                # Notify subscribers
                for callback in self.data_subscribers:
                    try:
                        await callback(dict(self.real_time_data))
                    except Exception as e:
                        logger.error(f"Error notifying data subscriber: {e}")

                # Wait based on simulation speed
                await asyncio.sleep(1.0 / self.simulation_speed)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in real-time data generator: {e}")

    async def _market_event_generator(self):
        """Generate random market events"""
        while self.running:
            try:
                await asyncio.sleep(random.uniform(300, 1800))  # 5-30 minutes

                # Random chance of event
                if random.random() < 0.1:  # 10% chance
                    event_types = ['earnings', 'news', 'economic_data', 'volatility_spike', 'flash_crash']
                    event_type = random.choice(event_types)
                    severity = random.uniform(0.1, 0.8)

                    # Select affected symbols
                    all_symbols = list(self.price_models.keys())
                    num_affected = random.randint(1, min(5, len(all_symbols)))
                    affected_symbols = random.sample(all_symbols, num_affected)

                    duration = random.uniform(60, 3600)  # 1 minute to 1 hour

                    await self.inject_market_event(event_type, severity, affected_symbols, duration)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in market event generator: {e}")

    async def _volatility_updater(self):
        """Update volatility models"""
        while self.running:
            try:
                await asyncio.sleep(60)  # Update every minute

                for symbol in self.volatility_models:
                    # GARCH-like volatility clustering
                    current_vol = self.volatility_models[symbol]
                    base_vol = self.price_models[symbol]['volatility']

                    # Mean reversion to base volatility
                    new_vol = current_vol * 0.95 + base_vol * 0.05

                    # Add random shock
                    shock = random.gauss(0, 0.01)
                    new_vol = max(0.05, new_vol + shock)

                    self.volatility_models[symbol] = new_vol

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in volatility updater: {e}")

    async def _correlation_updater(self):
        """Update correlation models"""
        while self.running:
            try:
                await asyncio.sleep(300)  # Update every 5 minutes

                # Slightly adjust correlations
                symbols = list(self.correlation_matrix.keys())
                for symbol1 in symbols:
                    for symbol2 in symbols:
                        if symbol1 != symbol2:
                            current_corr = self.correlation_matrix[symbol1][symbol2]
                            # Small random walk
                            change = random.gauss(0, 0.01)
                            new_corr = max(-0.9, min(0.9, current_corr + change))
                            self.correlation_matrix[symbol1][symbol2] = new_corr

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in correlation updater: {e}")

    async def _performance_tracker(self):
        """Track simulation performance"""
        while self.running:
            try:
                await asyncio.sleep(60)  # Update every minute

                # Update generation stats
                for symbol in self.price_models:
                    data_count = len(self.historical_data.get(symbol, []))
                    self.generation_stats[symbol] = {
                        'data_points': data_count,
                        'last_update': time.time(),
                        'current_price': self.real_time_data.get(symbol, {}).close_price if symbol in self.real_time_data else 0,
                        'volatility': self.volatility_models.get(symbol, 0)
                    }

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in performance tracker: {e}")

    # Data generation methods
    async def _generate_data_point(self, symbol: str, timestamp: float) -> MockMarketData:
        """Generate a single data point for a symbol"""
        model = self.price_models[symbol]
        current_vol = self.volatility_models[symbol]

        # Get previous price
        if symbol in self.real_time_data:
            prev_price = self.real_time_data[symbol].close_price
        else:
            prev_price = model['current_price']

        # Generate price movement using geometric Brownian motion
        dt = 1.0 / (365 * 24 * 3600)  # 1 second in years
        drift = model['drift']

        # Add correlation effects
        correlated_return = 0
        for other_symbol in self.correlation_matrix.get(symbol, {}):
            if other_symbol != symbol and other_symbol in self.real_time_data:
                correlation = self.correlation_matrix[symbol][other_symbol]
                other_data = self.real_time_data[other_symbol]
                if hasattr(other_data, 'close_price'):
                    other_return = (other_data.close_price - model['current_price']) / model['current_price']
                    correlated_return += correlation * other_return * 0.1

        # Generate random component
        random_component = random.gauss(0, current_vol * math.sqrt(dt))

        # Calculate price change
        price_change = prev_price * (drift * dt + random_component + correlated_return)
        new_price = max(0.01, prev_price + price_change)

        # Generate OHLC data
        volatility_factor = current_vol * 0.1
        high_price = new_price * (1 + random.uniform(0, volatility_factor))
        low_price = new_price * (1 - random.uniform(0, volatility_factor))
        open_price = prev_price

        # Ensure OHLC consistency
        high_price = max(high_price, open_price, new_price)
        low_price = min(low_price, open_price, new_price)

        # Generate volume
        base_volume = random.randint(100000, 1000000)
        volume_multiplier = 1 + abs(price_change / prev_price) * 10  # Higher volume on big moves
        volume = int(base_volume * volume_multiplier)

        # Generate bid/ask
        spread_pct = random.uniform(0.001, 0.01)  # 0.1% to 1% spread
        spread = new_price * spread_pct
        bid_price = new_price - spread / 2
        ask_price = new_price + spread / 2

        # Determine market condition
        market_condition = await self._determine_market_condition(symbol, new_price, prev_price)

        return MockMarketData(
            symbol=symbol,
            timestamp=timestamp,
            open_price=open_price,
            high_price=high_price,
            low_price=low_price,
            close_price=new_price,
            volume=volume,
            bid_price=bid_price,
            ask_price=ask_price,
            spread=spread,
            volatility=current_vol,
            market_condition=market_condition
        )

    async def _determine_market_condition(self, symbol: str, current_price: float, prev_price: float) -> MarketCondition:
        """Determine market condition based on price action"""
        price_change = (current_price - prev_price) / prev_price
        volatility = self.volatility_models.get(symbol, 0.2)

        if volatility > 0.4:
            return MarketCondition.HIGH_VOLATILITY
        elif volatility < 0.1:
            return MarketCondition.LOW_VOLATILITY
        elif price_change > 0.02:
            return MarketCondition.BULL_MARKET
        elif price_change < -0.02:
            return MarketCondition.BEAR_MARKET
        else:
            return MarketCondition.SIDEWAYS_MARKET

    async def _generate_symbol_history(self, symbol: str, start_time: float, end_time: float):
        """Generate historical data for a symbol"""
        current_time = start_time
        time_step = 60  # 1 minute intervals

        while current_time < end_time:
            data_point = await self._generate_data_point(symbol, current_time)
            self.historical_data[symbol].append(data_point)
            current_time += time_step

    # Simulation mode implementations
    async def _generate_historical_replay(self, simulation_id: str, config: SimulationConfig):
        """Generate historical replay simulation"""
        # Simplified implementation - would replay actual historical patterns
        pass

    async def _generate_synthetic_data(self, simulation_id: str, config: SimulationConfig):
        """Generate synthetic data simulation"""
        # Simplified implementation - would generate synthetic patterns
        pass

    async def _generate_pattern_data(self, simulation_id: str, config: SimulationConfig):
        """Generate pattern-based simulation"""
        # Simplified implementation - would generate specific patterns
        pass

    async def _generate_stress_test_data(self, simulation_id: str, config: SimulationConfig):
        """Generate stress test simulation"""
        # Simplified implementation - would generate stress scenarios
        pass

    async def _generate_regime_data(self, simulation_id: str, config: SimulationConfig):
        """Generate regime-based simulation"""
        # Simplified implementation - would generate regime-specific data
        pass

    async def _adjust_model_for_condition(self, symbol: str, condition: MarketCondition):
        """Adjust price model for market condition"""
        model = self.price_models[symbol]

        if condition == MarketCondition.BULL_MARKET:
            model['drift'] = abs(model['drift']) * 1.5
            model['volatility'] *= 0.8
        elif condition == MarketCondition.BEAR_MARKET:
            model['drift'] = -abs(model['drift']) * 1.5
            model['volatility'] *= 1.2
        elif condition == MarketCondition.HIGH_VOLATILITY:
            model['volatility'] *= 2.0
        elif condition == MarketCondition.LOW_VOLATILITY:
            model['volatility'] *= 0.5
        elif condition == MarketCondition.CRISIS_MODE:
            model['drift'] = -abs(model['drift']) * 3.0
            model['volatility'] *= 3.0

    async def _apply_event_effects(self, event: Dict[str, Any]):
        """Apply market event effects to affected symbols"""
        event_type = event['event_type']
        severity = event['severity']
        affected_symbols = event['affected_symbols']

        for symbol in affected_symbols:
            if symbol in self.price_models:
                model = self.price_models[symbol]

                if event_type == 'flash_crash':
                    # Temporary price drop
                    if symbol in self.real_time_data:
                        current_price = self.real_time_data[symbol].close_price
                        drop_amount = current_price * severity * 0.1
                        model['current_price'] = max(0.01, current_price - drop_amount)

                elif event_type == 'volatility_spike':
                    # Increase volatility
                    self.volatility_models[symbol] *= (1 + severity)

                elif event_type == 'earnings':
                    # Random price jump
                    direction = random.choice([-1, 1])
                    jump = severity * 0.05 * direction
                    if symbol in self.real_time_data:
                        current_price = self.real_time_data[symbol].close_price
                        model['current_price'] = max(0.01, current_price * (1 + jump))

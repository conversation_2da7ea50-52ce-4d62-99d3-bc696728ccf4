# Development Environment Configuration
# Advanced Ollama Trading Agent System

system:
  environment: development
  log_level: DEBUG
  debug: true
  metrics_enabled: true
  profiling_enabled: true

# Database Configuration
database:
  postgres:
    host: "localhost"
    port: 5432
    database: "trading_dev"
    username: "trading_dev"
    password: "dev_password"
    pool_size: 5
    max_overflow: 10
    pool_timeout: 30
    pool_recycle: 3600
    ssl_mode: disable
    
  redis:
    host: "localhost"
    port: 6379
    db: 0
    password: null
    ssl: false
    max_connections: 10

# API Configuration
api:
  host: "127.0.0.1"
  port: 8000
  workers: 1
  max_request_size: 10485760  # 10MB
  request_timeout: 300
  cors_enabled: true
  rate_limiting:
    enabled: false
    requests_per_minute: 10000
    burst_size: 1000

# Security Configuration
security:
  jwt_secret: "dev-secret-key-change-in-production"
  jwt_expiry: 86400  # 24 hours for development
  api_key_required: false
  encryption_enabled: false
  audit_logging: false
  ip_whitelist: []  # Allow all IPs in development

# Ollama Configuration
ollama:
  base_url: "http://localhost:11434"
  timeout: 300
  max_retries: 3
  retry_delay: 2
  connection_pool_size: 5
  load_balancing: false
  health_check_interval: 60

# Market Data Configuration
market_data:
  providers:
    mock:
      enabled: true
      symbols: ["AAPL", "GOOGL", "MSFT", "TSLA"]
      update_interval: 1  # 1 second for testing
      
    alpha_vantage:
      api_key: "demo"  # Use demo key for development
      rate_limit: 5
      timeout: 30
      enabled: false
  
  redis:
    host: "localhost"
    port: 6379
    db: 1
    ttl: 60  # 1 minute for development
  
  update_interval: 5  # seconds
  symbols:
    - "AAPL"
    - "GOOGL"
    - "MSFT"
    - "TSLA"

# Broker Configuration
brokers:
  providers:
    mock:
      enabled: true
      initial_balance: 100000.0
      
    paper_trading:
      enabled: true
      initial_balance: 100000.0
      
    interactive_brokers:
      enabled: false
      host: "localhost"
      port: 7497
      client_id: 1
      timeout: 30
  
  routing:
    default: "mock"

# Agent Configuration
agents:
  max_agents: 10
  heartbeat_interval: 30
  max_memory_usage: *********  # 512MB per agent
  max_cpu_usage: 0.5
  
  defaults:
    model: "llama2:7b"  # Smaller model for development
    temperature: 0.7
    max_tokens: 1024
    timeout: 120
    
  scaling:
    auto_scaling: false
    min_agents: 1
    max_agents: 5
    scale_up_threshold: 0.8
    scale_down_threshold: 0.3
    cooldown_period: 60

# Strategy Configuration
strategies:
  max_active_strategies: 5
  default_capital_allocation: 0.2  # 20% per strategy for testing
  rebalance_frequency: "manual"  # Manual rebalancing in development
  
  risk_limits:
    max_drawdown: 0.20  # 20% - more lenient for testing
    max_leverage: 1.0   # No leverage in development
    position_timeout: 3600  # 1 hour
    
  performance_tracking:
    benchmark: "SPY"
    lookback_period: 30  # 30 days for development
    min_sharpe_ratio: 0.5

# Risk Management
risk:
  max_portfolio_risk: 0.05   # 5% VaR - higher for testing
  max_position_size: 0.25    # 25% of portfolio
  max_sector_exposure: 0.5   # 50% per sector
  max_drawdown: 0.20         # 20%
  
  var_calculation:
    confidence_level: 0.95
    lookback_period: 30
    monte_carlo_simulations: 1000
    
  position_limits:
    max_positions: 10
    max_orders_per_minute: 10
    max_notional_per_order: 10000  # $10K
    
  circuit_breakers:
    daily_loss_limit: 0.10     # 10%
    portfolio_heat: 0.9        # 90%
    correlation_limit: 0.8     # 80%

# Execution Configuration
execution:
  paper_trading: true
  max_orders_per_second: 5
  order_timeout: 60
  slippage_model: "fixed"
  
  order_management:
    smart_routing: false
    dark_pools: false
    iceberg_orders: false
    time_weighted: false
    
  latency_targets:
    market_data: 100    # milliseconds
    order_entry: 500    # milliseconds
    execution: 1000     # milliseconds

# Portfolio Configuration
portfolio:
  initial_capital: 100000.0    # $100K for development
  rebalance_threshold: 0.05    # 5%
  max_positions: 10
  cash_target: 0.1             # 10% cash
  
  optimization:
    method: "equal_weight"  # Simple method for development
    lookback_period: 30
    rebalance_frequency: "manual"
    transaction_costs: 0.0      # No transaction costs in development

# Monitoring Configuration
monitoring:
  metrics:
    collection_interval: 30  # seconds
    retention_period: 86400  # 1 day
    
  alerts:
    email_enabled: false
    slack_enabled: false
    webhook_enabled: false
    console_enabled: true
    
    thresholds:
      cpu_usage: 0.9
      memory_usage: 0.9
      disk_usage: 0.95
      error_rate: 0.1
      
  logging:
    level: "DEBUG"
    format: "text"
    rotation: "never"
    retention: 7  # days
    
    destinations:
      - type: "console"
      - type: "file"
        path: "logs/development.log"

# Performance Configuration
performance:
  caching:
    enabled: true
    backend: "memory"  # Use in-memory cache for development
    default_ttl: 60
    
  connection_pooling:
    database: 5
    redis: 10
    http: 20
    
  async_processing:
    max_workers: 2
    queue_size: 100
    batch_size: 10

# Backup Configuration
backup:
  enabled: false  # Disable backups in development
  schedule: "0 2 * * *"
  retention: 7  # days
  
  destinations:
    - type: "local"
      path: "backup/development"

# Compliance Configuration
compliance:
  enabled: false  # Disable compliance in development
  reporting:
    daily_reports: false
    monthly_reports: false
    regulatory_reports: false
    
  audit_trail:
    enabled: false
    retention: 30
    encryption: false

# Notification Configuration
notifications:
  email:
    enabled: false
    
  slack:
    enabled: false
    
  webhook:
    enabled: false
    
  console:
    enabled: true
    level: "INFO"

# Development-specific Configuration
development:
  hot_reload: true
  auto_restart: true
  debug_toolbar: true
  
  testing:
    mock_external_apis: true
    fast_mode: true
    skip_validations: false
    
  debugging:
    enable_pdb: true
    log_sql_queries: true
    profile_requests: true
    
  data_generation:
    generate_mock_data: true
    mock_data_size: 1000
    historical_data_days: 30

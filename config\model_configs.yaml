# Ollama Model Configurations for Trading Agents
# Based on available models in your system

models:
  # Team Leader Models - Large comprehensive models for coordination
  team_leader:
    primary: "exaone-deep:32b"
    fallback: "command-r:35b"
    config:
      temperature: 0.7
      top_p: 0.9
      max_tokens: 4096
      system_prompt: |
        You are a strategic team leader coordinating multiple specialized trading agents.
        Your role is to synthesize information from team members, make final decisions,
        allocate resources, and ensure team cohesion. Focus on strategic thinking,
        risk management, and team optimization.
      
  # Market Analyst Models - Pattern recognition and analysis
  market_analyst:
    primary: "huihui_ai/magistral-abliterated:24b"
    fallback: "magistral:24b"
    config:
      temperature: 0.4
      top_p: 0.8
      max_tokens: 3072
      system_prompt: |
        You are a specialized market analyst identifying patterns, trends, and opportunities.
        Analyze market data, sentiment, and technical indicators to provide actionable insights.
        Focus on accuracy, timeliness, and clear communication of market intelligence.
        
  # Strategy Developer Models - Creative reasoning for strategy development
  strategy_developer:
    primary: "huihui_ai/am-thinking-abliterate:latest"
    fallback: "cogito:32b"
    config:
      temperature: 0.8
      top_p: 0.9
      max_tokens: 3072
      system_prompt: |
        You are a creative strategy developer designing innovative trading approaches.
        Develop novel strategies, optimize existing ones, and adapt to changing market conditions.
        Focus on innovation, backtesting, and continuous improvement of trading methodologies.
        
  # Risk Manager Models - Precise, conservative models for risk assessment
  risk_manager:
    primary: "phi4-reasoning:plus"
    fallback: "mistral-small:24b"
    config:
      temperature: 0.2
      top_p: 0.7
      max_tokens: 2048
      system_prompt: |
        You are a cautious risk manager protecting assets and ensuring compliance.
        Assess portfolio risk, set position sizes, monitor exposure, and enforce limits.
        Focus on capital preservation, regulatory compliance, and systematic risk control.
        
  # Execution Specialist Models - Fast, efficient models for trade execution
  execution_specialist:
    primary: "nemotron-mini:4b"
    fallback: "hermes3:8b"
    config:
      temperature: 0.3
      top_p: 0.8
      max_tokens: 1024
      system_prompt: |
        You are an execution specialist optimizing trade execution and minimizing costs.
        Focus on order routing, timing, slippage reduction, and execution efficiency.
        Provide fast, accurate execution decisions with minimal market impact.
        
  # Performance Evaluator Models - Analysis and evaluation focused
  performance_evaluator:
    primary: "granite3.3:8b"
    fallback: "marco-o1:7b"
    config:
      temperature: 0.4
      top_p: 0.8
      max_tokens: 2048
      system_prompt: |
        You are a performance evaluator tracking and analyzing system effectiveness.
        Monitor agent performance, identify improvement opportunities, and provide feedback.
        Focus on objective analysis, performance attribution, and optimization recommendations.

# Specialized Model Configurations
specialized_models:
  # Visual Analysis for charts and technical patterns
  visual_analyst:
    model: "qwen2.5vl:32b"
    config:
      temperature: 0.5
      top_p: 0.8
      max_tokens: 2048
      system_prompt: |
        You are a visual analyst specializing in chart pattern recognition and technical analysis.
        Analyze price charts, identify patterns, and provide visual insights for trading decisions.
        
  # Deep Reasoning for Complex Decisions
  deep_reasoner:
    model: "deepseek-r1:latest"
    config:
      temperature: 0.6
      top_p: 0.9
      max_tokens: 4096
      system_prompt: |
        You are a deep reasoning specialist for complex trading decisions.
        Provide thorough analysis, consider multiple scenarios, and reason through complex problems.
        
  # Sentiment Analysis
  sentiment_analyst:
    model: "goekdenizguelmez/JOSIEFIED-Qwen3:14b"
    config:
      temperature: 0.4
      top_p: 0.8
      max_tokens: 1536
      system_prompt: |
        You are a sentiment analyst interpreting market sentiment and news impact.
        Analyze news, social media, and market sentiment to gauge market psychology.

# Model Performance Tracking
performance_tracking:
  metrics:
    - "response_time"
    - "token_usage"
    - "accuracy"
    - "decision_quality"
    - "resource_utilization"
    
  benchmarks:
    response_time_target: 5.0  # seconds
    token_efficiency_target: 0.8
    accuracy_target: 0.85
    
  optimization:
    auto_tune: true
    tune_interval: 86400  # seconds (daily)
    performance_threshold: 0.8

# Model Deployment Strategy
deployment:
  # Model loading strategy
  loading:
    preload_primary: true
    lazy_load_fallback: true
    cache_duration: 3600  # seconds
    
  # Resource allocation
  resources:
    team_leader:
      cpu_cores: 4
      memory_gb: 8
      priority: "high"
      
    market_analyst:
      cpu_cores: 3
      memory_gb: 6
      priority: "high"
      
    strategy_developer:
      cpu_cores: 3
      memory_gb: 6
      priority: "medium"
      
    risk_manager:
      cpu_cores: 2
      memory_gb: 4
      priority: "high"
      
    execution_specialist:
      cpu_cores: 1
      memory_gb: 2
      priority: "critical"
      
    performance_evaluator:
      cpu_cores: 2
      memory_gb: 4
      priority: "low"

# Model Switching Rules
switching_rules:
  # Automatic fallback conditions
  fallback_triggers:
    - "response_timeout"
    - "model_unavailable"
    - "high_error_rate"
    - "resource_exhaustion"
    
  # Performance-based switching
  performance_switching:
    enabled: true
    evaluation_window: 3600  # seconds
    switch_threshold: 0.7
    
  # Market condition-based switching
  market_switching:
    enabled: true
    conditions:
      high_volatility:
        team_leader: "command-r:35b"  # More stable model
        risk_manager: "phi4-reasoning:plus"  # Enhanced reasoning
        
      low_volatility:
        strategy_developer: "cogito:32b"  # More creative model
        
      trending_market:
        market_analyst: "magistral:24b"  # Pattern recognition
        
      ranging_market:
        market_analyst: "huihui_ai/magistral-abliterated:24b"  # Mean reversion focus

# Model Health Monitoring
health_monitoring:
  checks:
    - "model_availability"
    - "response_time"
    - "memory_usage"
    - "error_rate"
    - "throughput"
    
  intervals:
    health_check: 60      # seconds
    performance_check: 300 # seconds
    deep_check: 3600      # seconds
    
  alerts:
    response_time_threshold: 10.0  # seconds
    error_rate_threshold: 0.05     # 5%
    memory_threshold: 0.9          # 90% memory usage

"""
CLI Package - Command Line Interface Tools

This package provides comprehensive command-line tools for managing,
testing, and monitoring the Advanced Ollama Trading Agent System.

Available Tools:
- trading_cli.py: Main CLI interface for system management
- test_runner.py: Interactive terminal testing interface
- utils.py: Utility functions and helpers
"""

from .trading_cli import TradingCLI
from .test_runner import TerminalTestRunner
from .utils import CLIUtils, cli_utils

__all__ = [
    'TradingCLI',
    'TerminalTestRunner', 
    'CLIUtils',
    'cli_utils'
]

__version__ = '1.0.0'
__author__ = 'Advanced Ollama Trading Agents Team'
__description__ = 'Command Line Interface Tools for Trading System'

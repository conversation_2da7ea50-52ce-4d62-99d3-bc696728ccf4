"""
Redis Manager - Handles Redis operations for caching and real-time data
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any, Union
import redis.asyncio as redis
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class CacheStats:
    """Cache statistics"""
    total_operations: int
    cache_hits: int
    cache_misses: int
    hit_rate: float
    total_keys: int
    memory_usage: str


class RedisManager:
    """
    Manages Redis operations for caching, sessions, and real-time data.
    
    Features:
    - High-performance caching
    - Session management
    - Real-time data storage
    - Pub/Sub messaging
    - Data expiration and cleanup
    - Connection pooling
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Connection configuration
        self.host = config.get('host', 'localhost')
        self.port = config.get('port', 6379)
        self.database = config.get('database', 0)
        self.password = config.get('password')
        self.max_connections = config.get('max_connections', 100)
        
        # Redis clients
        self.redis_client: Optional[redis.Redis] = None
        self.pubsub_client: Optional[redis.Redis] = None
        
        # Cache configuration
        self.default_ttl = config.get('default_ttl', 3600)  # 1 hour
        self.key_prefix = config.get('key_prefix', 'trading_system:')
        
        # Statistics
        self.cache_stats = {
            'total_operations': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'total_sets': 0,
            'total_gets': 0,
            'total_deletes': 0
        }
        
        # State
        self.initialized = False
        self.running = False
    
    async def initialize(self) -> bool:
        """Initialize Redis manager"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Redis Manager...")
            
            # Create Redis connections
            await self._create_connections()
            
            # Test connections
            await self._test_connections()
            
            self.initialized = True
            logger.info("✓ Redis Manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis Manager: {e}")
            return False
    
    async def start(self) -> bool:
        """Start Redis manager"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        self.running = True
        logger.info("✓ Redis Manager started")
        return True
    
    async def stop(self) -> bool:
        """Stop Redis manager"""
        if self.redis_client:
            await self.redis_client.close()
        
        if self.pubsub_client:
            await self.pubsub_client.close()
        
        self.running = False
        logger.info("✓ Redis Manager stopped")
        return True
    
    async def health_check(self) -> bool:
        """Check Redis health"""
        try:
            if not self.redis_client:
                return False
            
            result = await self.redis_client.ping()
            return result is True
            
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return False
    
    # Caching Operations
    
    async def cache_data(self, key: str, data: Any, ttl: int = None) -> bool:
        """Cache data with optional TTL"""
        try:
            full_key = self._get_full_key(key)
            ttl = ttl or self.default_ttl
            
            # Serialize data
            if isinstance(data, (dict, list)):
                serialized_data = json.dumps(data)
            else:
                serialized_data = str(data)
            
            # Store in Redis
            await self.redis_client.setex(full_key, ttl, serialized_data)
            
            # Update statistics
            self.cache_stats['total_operations'] += 1
            self.cache_stats['total_sets'] += 1
            
            logger.debug(f"Cached data for key: {key}")
            return True
            
        except Exception as e:
            logger.error(f"Error caching data for key {key}: {e}")
            return False
    
    async def get_cached_data(self, key: str) -> Optional[Any]:
        """Get cached data"""
        try:
            full_key = self._get_full_key(key)
            
            # Get from Redis
            data = await self.redis_client.get(full_key)
            
            # Update statistics
            self.cache_stats['total_operations'] += 1
            self.cache_stats['total_gets'] += 1
            
            if data is not None:
                self.cache_stats['cache_hits'] += 1
                
                # Deserialize data
                try:
                    return json.loads(data)
                except json.JSONDecodeError:
                    return data
            else:
                self.cache_stats['cache_misses'] += 1
                return None
                
        except Exception as e:
            logger.error(f"Error getting cached data for key {key}: {e}")
            self.cache_stats['cache_misses'] += 1
            return None
    
    async def delete_cached_data(self, key: str) -> bool:
        """Delete cached data"""
        try:
            full_key = self._get_full_key(key)
            
            result = await self.redis_client.delete(full_key)
            
            # Update statistics
            self.cache_stats['total_operations'] += 1
            self.cache_stats['total_deletes'] += 1
            
            return result > 0
            
        except Exception as e:
            logger.error(f"Error deleting cached data for key {key}: {e}")
            return False
    
    async def cache_exists(self, key: str) -> bool:
        """Check if cached data exists"""
        try:
            full_key = self._get_full_key(key)
            result = await self.redis_client.exists(full_key)
            return result > 0
            
        except Exception as e:
            logger.error(f"Error checking cache existence for key {key}: {e}")
            return False
    
    async def set_cache_ttl(self, key: str, ttl: int) -> bool:
        """Set TTL for cached data"""
        try:
            full_key = self._get_full_key(key)
            result = await self.redis_client.expire(full_key, ttl)
            return result
            
        except Exception as e:
            logger.error(f"Error setting TTL for key {key}: {e}")
            return False
    
    # Session Management
    
    async def create_session(self, session_id: str, data: Dict[str, Any], ttl: int = 3600) -> bool:
        """Create session"""
        try:
            session_key = f"session:{session_id}"
            return await self.cache_data(session_key, data, ttl)
            
        except Exception as e:
            logger.error(f"Error creating session {session_id}: {e}")
            return False
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data"""
        try:
            session_key = f"session:{session_id}"
            return await self.get_cached_data(session_key)
            
        except Exception as e:
            logger.error(f"Error getting session {session_id}: {e}")
            return None
    
    async def update_session(self, session_id: str, data: Dict[str, Any]) -> bool:
        """Update session data"""
        try:
            session_key = f"session:{session_id}"
            
            # Get current session data
            current_data = await self.get_session(session_id)
            if current_data is None:
                return False
            
            # Merge with new data
            current_data.update(data)
            
            # Get current TTL
            full_key = self._get_full_key(session_key)
            ttl = await self.redis_client.ttl(full_key)
            
            # Update session
            return await self.cache_data(session_key, current_data, ttl if ttl > 0 else 3600)
            
        except Exception as e:
            logger.error(f"Error updating session {session_id}: {e}")
            return False
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete session"""
        try:
            session_key = f"session:{session_id}"
            return await self.delete_cached_data(session_key)
            
        except Exception as e:
            logger.error(f"Error deleting session {session_id}: {e}")
            return False
    
    # Real-time Data Operations
    
    async def store_real_time_data(self, data_type: str, symbol: str, data: Dict[str, Any]) -> bool:
        """Store real-time data"""
        try:
            key = f"realtime:{data_type}:{symbol}"
            
            # Add timestamp
            data_with_timestamp = data.copy()
            data_with_timestamp['timestamp'] = time.time()
            
            # Store with short TTL for real-time data
            return await self.cache_data(key, data_with_timestamp, ttl=300)  # 5 minutes
            
        except Exception as e:
            logger.error(f"Error storing real-time data for {symbol}: {e}")
            return False
    
    async def get_real_time_data(self, data_type: str, symbol: str) -> Optional[Dict[str, Any]]:
        """Get real-time data"""
        try:
            key = f"realtime:{data_type}:{symbol}"
            return await self.get_cached_data(key)
            
        except Exception as e:
            logger.error(f"Error getting real-time data for {symbol}: {e}")
            return None
    
    # Pub/Sub Operations
    
    async def publish_message(self, channel: str, message: Any) -> bool:
        """Publish message to channel"""
        try:
            if isinstance(message, (dict, list)):
                message = json.dumps(message)
            
            result = await self.redis_client.publish(channel, message)
            return result > 0
            
        except Exception as e:
            logger.error(f"Error publishing message to channel {channel}: {e}")
            return False
    
    async def subscribe_to_channel(self, channel: str, callback):
        """Subscribe to channel with callback"""
        try:
            pubsub = self.redis_client.pubsub()
            await pubsub.subscribe(channel)
            
            async for message in pubsub.listen():
                if message['type'] == 'message':
                    try:
                        data = json.loads(message['data'])
                    except json.JSONDecodeError:
                        data = message['data']
                    
                    await callback(channel, data)
                    
        except Exception as e:
            logger.error(f"Error subscribing to channel {channel}: {e}")
    
    # Batch Operations
    
    async def cache_batch(self, data_dict: Dict[str, Any], ttl: int = None) -> Dict[str, bool]:
        """Cache multiple items in batch"""
        try:
            results = {}
            
            # Use pipeline for batch operations
            pipe = self.redis_client.pipeline()
            
            for key, data in data_dict.items():
                full_key = self._get_full_key(key)
                ttl_value = ttl or self.default_ttl
                
                if isinstance(data, (dict, list)):
                    serialized_data = json.dumps(data)
                else:
                    serialized_data = str(data)
                
                pipe.setex(full_key, ttl_value, serialized_data)
            
            # Execute pipeline
            pipeline_results = await pipe.execute()
            
            # Process results
            for i, key in enumerate(data_dict.keys()):
                results[key] = pipeline_results[i] is True
            
            # Update statistics
            self.cache_stats['total_operations'] += len(data_dict)
            self.cache_stats['total_sets'] += len(data_dict)
            
            return results
            
        except Exception as e:
            logger.error(f"Error in batch cache operation: {e}")
            return {key: False for key in data_dict.keys()}
    
    async def get_batch(self, keys: List[str]) -> Dict[str, Any]:
        """Get multiple cached items in batch"""
        try:
            full_keys = [self._get_full_key(key) for key in keys]
            
            # Use pipeline for batch operations
            pipe = self.redis_client.pipeline()
            for full_key in full_keys:
                pipe.get(full_key)
            
            # Execute pipeline
            pipeline_results = await pipe.execute()
            
            # Process results
            results = {}
            for i, key in enumerate(keys):
                data = pipeline_results[i]
                if data is not None:
                    try:
                        results[key] = json.loads(data)
                    except json.JSONDecodeError:
                        results[key] = data
                    
                    self.cache_stats['cache_hits'] += 1
                else:
                    self.cache_stats['cache_misses'] += 1
            
            # Update statistics
            self.cache_stats['total_operations'] += len(keys)
            self.cache_stats['total_gets'] += len(keys)
            
            return results
            
        except Exception as e:
            logger.error(f"Error in batch get operation: {e}")
            self.cache_stats['cache_misses'] += len(keys)
            return {}
    
    # Statistics and Monitoring
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get Redis statistics"""
        try:
            # Get Redis info
            redis_info = await self.redis_client.info()
            
            # Calculate hit rate
            total_cache_ops = self.cache_stats['cache_hits'] + self.cache_stats['cache_misses']
            hit_rate = (self.cache_stats['cache_hits'] / total_cache_ops * 100) if total_cache_ops > 0 else 0
            
            # Get key count
            key_count = await self.redis_client.dbsize()
            
            stats = {
                'cache_stats': self.cache_stats.copy(),
                'hit_rate_percent': hit_rate,
                'total_keys': key_count,
                'memory_usage': redis_info.get('used_memory_human', 'Unknown'),
                'connected_clients': redis_info.get('connected_clients', 0),
                'total_commands_processed': redis_info.get('total_commands_processed', 0),
                'keyspace_hits': redis_info.get('keyspace_hits', 0),
                'keyspace_misses': redis_info.get('keyspace_misses', 0),
                'uptime_seconds': redis_info.get('uptime_in_seconds', 0)
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting Redis stats: {e}")
            return self.cache_stats.copy()
    
    async def clear_cache(self, pattern: str = None) -> bool:
        """Clear cache (optionally by pattern)"""
        try:
            if pattern:
                # Clear keys matching pattern
                full_pattern = self._get_full_key(pattern)
                keys = await self.redis_client.keys(full_pattern)
                if keys:
                    await self.redis_client.delete(*keys)
                    return True
            else:
                # Clear all keys with our prefix
                pattern = f"{self.key_prefix}*"
                keys = await self.redis_client.keys(pattern)
                if keys:
                    await self.redis_client.delete(*keys)
                    return True
            
            return True
            
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return False
    
    # Private methods
    
    async def _create_connections(self):
        """Create Redis connections"""
        try:
            # Main Redis client
            self.redis_client = redis.Redis(
                host=self.host,
                port=self.port,
                db=self.database,
                password=self.password,
                max_connections=self.max_connections,
                decode_responses=True
            )
            
            # Pub/Sub client (separate connection)
            self.pubsub_client = redis.Redis(
                host=self.host,
                port=self.port,
                db=self.database,
                password=self.password,
                decode_responses=True
            )
            
            logger.info(f"✓ Redis connections created (host: {self.host}:{self.port}, db: {self.database})")
            
        except Exception as e:
            logger.error(f"Failed to create Redis connections: {e}")
            raise
    
    async def _test_connections(self):
        """Test Redis connections"""
        try:
            # Test main client
            await self.redis_client.ping()
            
            # Test pub/sub client
            await self.pubsub_client.ping()
            
            logger.info("✓ Redis connections tested successfully")
            
        except Exception as e:
            logger.error(f"Redis connection test failed: {e}")
            raise
    
    def _get_full_key(self, key: str) -> str:
        """Get full key with prefix"""
        return f"{self.key_prefix}{key}"

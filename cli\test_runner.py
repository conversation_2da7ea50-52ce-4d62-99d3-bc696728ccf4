#!/usr/bin/env python3
"""
Terminal Test Runner - Interactive Testing Interface

Comprehensive terminal-based testing interface with real-time feedback,
interactive test selection, and detailed result visualization.
"""

import asyncio
import sys
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn
from rich.tree import Tree
from rich.prompt import Prompt, Confirm
from rich.layout import Layout
from rich.live import Live
from rich.text import Text
from rich.columns import Columns

from tests.test_suite import TestSuiteRunner
from system_validator import SystemValidator


class TerminalTestRunner:
    """
    Interactive terminal-based test runner with rich UI.
    
    Features:
    - Interactive test selection
    - Real-time progress tracking
    - Detailed result visualization
    - Test filtering and categorization
    - Performance metrics display
    - Error analysis and reporting
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.console = Console()
        self.test_suite = TestSuiteRunner(config)
        self.validator = SystemValidator(config)
        
        # Test results storage
        self.test_results: Dict[str, Any] = {}
        self.current_test: Optional[str] = None
        self.start_time: Optional[float] = None
        
        # UI state
        self.show_details = False
        self.filter_category = None
        self.sort_by = 'name'
    
    async def initialize(self) -> bool:
        """Initialize test runner"""
        try:
            await self.test_suite.initialize()
            await self.validator.initialize()
            return True
        except Exception as e:
            self.console.print(f"[red]Failed to initialize test runner: {e}[/red]")
            return False
    
    async def run_interactive_session(self):
        """Run interactive testing session"""
        self.console.clear()
        self.console.print(Panel.fit(
            "[bold blue]Advanced Ollama Trading Agents[/bold blue]\n"
            "[bold green]Interactive Test Runner[/bold green]",
            border_style="blue"
        ))
        
        while True:
            try:
                choice = await self.show_main_menu()
                
                if choice == '1':
                    await self.run_quick_tests()
                elif choice == '2':
                    await self.run_full_test_suite()
                elif choice == '3':
                    await self.run_custom_tests()
                elif choice == '4':
                    await self.run_system_validation()
                elif choice == '5':
                    await self.run_performance_tests()
                elif choice == '6':
                    await self.run_security_tests()
                elif choice == '7':
                    await self.show_test_results()
                elif choice == '8':
                    await self.configure_settings()
                elif choice == '9':
                    break
                else:
                    self.console.print("[red]Invalid choice. Please try again.[/red]")
                
                if choice != '9':
                    Prompt.ask("\nPress Enter to continue")
                    
            except KeyboardInterrupt:
                if Confirm.ask("\nAre you sure you want to exit?"):
                    break
            except Exception as e:
                self.console.print(f"[red]Error: {e}[/red]")
                Prompt.ask("Press Enter to continue")
        
        self.console.print("[yellow]Goodbye![/yellow]")
    
    async def show_main_menu(self) -> str:
        """Display main menu and get user choice"""
        self.console.clear()
        
        menu_panel = Panel(
            "[bold]Main Menu[/bold]\n\n"
            "1. 🚀 Quick Tests (smoke tests)\n"
            "2. 🧪 Full Test Suite (all tests)\n"
            "3. 🎯 Custom Tests (select specific tests)\n"
            "4. 🔍 System Validation\n"
            "5. ⚡ Performance Tests\n"
            "6. 🔒 Security Tests\n"
            "7. 📊 View Test Results\n"
            "8. ⚙️ Configure Settings\n"
            "9. 🚪 Exit\n",
            title="Test Runner",
            border_style="green"
        )
        
        # Show system status
        status_info = await self.get_system_status()
        status_panel = Panel(
            status_info,
            title="System Status",
            border_style="blue"
        )
        
        # Show recent results summary
        results_summary = self.get_results_summary()
        results_panel = Panel(
            results_summary,
            title="Recent Results",
            border_style="yellow"
        )
        
        layout = Layout()
        layout.split_column(
            Layout(menu_panel, size=12),
            Layout().split_row(
                Layout(status_panel),
                Layout(results_panel)
            )
        )
        
        self.console.print(layout)
        
        return Prompt.ask(
            "\n[bold green]Choose an option[/bold green]",
            choices=['1', '2', '3', '4', '5', '6', '7', '8', '9'],
            default='1'
        )
    
    async def run_quick_tests(self):
        """Run quick smoke tests"""
        self.console.clear()
        self.console.print("[bold green]Running Quick Tests...[/bold green]\n")
        
        with Progress(
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=self.console
        ) as progress:
            
            task = progress.add_task("Running quick tests...", total=100)
            
            # Simulate test progress
            test_steps = [
                ("Initializing test environment", 10),
                ("Running unit tests", 30),
                ("Running integration tests", 25),
                ("Running smoke tests", 20),
                ("Generating report", 15)
            ]
            
            completed = 0
            for step_name, step_weight in test_steps:
                progress.update(task, description=step_name)
                
                # Simulate test execution
                for i in range(step_weight):
                    await asyncio.sleep(0.1)  # Simulate work
                    completed += 1
                    progress.update(task, completed=completed)
            
            # Run actual quick tests
            result = await self.test_suite.run_quick_tests()
            self.test_results['quick_tests'] = {
                'result': result,
                'timestamp': datetime.now().isoformat()
            }
        
        # Display results
        await self.display_test_results(result, "Quick Tests")
    
    async def run_full_test_suite(self):
        """Run complete test suite"""
        self.console.clear()
        self.console.print("[bold green]Running Full Test Suite...[/bold green]\n")
        
        # Confirm long-running operation
        if not Confirm.ask("This may take several minutes. Continue?"):
            return
        
        with Progress(
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=self.console
        ) as progress:
            
            task = progress.add_task("Running full test suite...", total=100)
            
            # Run actual test suite with progress updates
            result = await self.test_suite.run_all_tests()
            self.test_results['full_suite'] = {
                'result': result,
                'timestamp': datetime.now().isoformat()
            }
            
            progress.update(task, completed=100)
        
        # Display comprehensive results
        await self.display_comprehensive_results(result)
    
    async def run_custom_tests(self):
        """Run custom selected tests"""
        self.console.clear()
        self.console.print("[bold green]Custom Test Selection[/bold green]\n")
        
        # Available test categories
        test_categories = {
            '1': ('Unit Tests', 'unit'),
            '2': ('Integration Tests', 'integration'),
            '3': ('Performance Tests', 'performance'),
            '4': ('Security Tests', 'security'),
            '5': ('End-to-End Tests', 'e2e'),
            '6': ('Regression Tests', 'regression')
        }
        
        # Display available categories
        table = Table(title="Available Test Categories")
        table.add_column("Option", style="cyan")
        table.add_column("Category", style="green")
        table.add_column("Description", style="yellow")
        
        for key, (name, category) in test_categories.items():
            description = self.get_category_description(category)
            table.add_row(key, name, description)
        
        self.console.print(table)
        
        # Get user selection
        choices = list(test_categories.keys())
        selected = Prompt.ask(
            "\nSelect test categories (comma-separated)",
            default="1,2"
        ).split(',')
        
        selected_categories = []
        for choice in selected:
            choice = choice.strip()
            if choice in test_categories:
                selected_categories.append(test_categories[choice][1])
        
        if not selected_categories:
            self.console.print("[red]No valid categories selected.[/red]")
            return
        
        # Run selected tests
        self.console.print(f"\n[green]Running tests for: {', '.join(selected_categories)}[/green]")
        
        with Progress(console=self.console) as progress:
            task = progress.add_task("Running custom tests...", total=len(selected_categories))
            
            results = {}
            for i, category in enumerate(selected_categories):
                progress.update(task, description=f"Running {category} tests...")
                
                # Run category-specific tests
                if category == 'unit':
                    result = await self.test_suite.run_unit_tests()
                elif category == 'integration':
                    result = await self.test_suite.run_integration_tests()
                elif category == 'performance':
                    result = await self.test_suite.run_performance_tests()
                elif category == 'security':
                    result = await self.test_suite.run_security_tests()
                elif category == 'e2e':
                    result = await self.test_suite.run_end_to_end_tests()
                elif category == 'regression':
                    result = await self.test_suite.run_regression_tests()
                else:
                    result = {'passed': False, 'error': 'Unknown category'}
                
                results[category] = result
                progress.update(task, completed=i + 1)
        
        # Store and display results
        self.test_results['custom_tests'] = {
            'results': results,
            'categories': selected_categories,
            'timestamp': datetime.now().isoformat()
        }
        
        await self.display_custom_results(results, selected_categories)
    
    async def run_system_validation(self):
        """Run comprehensive system validation"""
        self.console.clear()
        self.console.print("[bold green]System Validation[/bold green]\n")
        
        with Progress(
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=self.console
        ) as progress:
            
            task = progress.add_task("Running system validation...", total=100)
            
            # Run validation
            result = await self.validator.validate_complete_system()
            self.test_results['validation'] = {
                'result': result,
                'timestamp': datetime.now().isoformat()
            }
            
            progress.update(task, completed=100)
        
        # Display validation results
        await self.display_validation_results(result)
    
    async def display_test_results(self, result: Dict[str, Any], test_name: str):
        """Display test results in a formatted way"""
        self.console.clear()
        
        # Overall status
        status = "✅ PASSED" if result.get('passed', False) else "❌ FAILED"
        status_color = "green" if result.get('passed', False) else "red"
        
        header = Panel(
            f"[bold {status_color}]{test_name} Results: {status}[/bold {status_color}]",
            border_style=status_color
        )
        self.console.print(header)
        
        # Test statistics
        if 'detailed' in result and 'summary' in result['detailed']:
            summary = result['detailed']['summary']
            
            stats_table = Table(title="Test Statistics")
            stats_table.add_column("Metric", style="cyan")
            stats_table.add_column("Value", style="green")
            
            stats_table.add_row("Total Tests", str(summary.get('total', 0)))
            stats_table.add_row("Passed", str(summary.get('passed', 0)))
            stats_table.add_row("Failed", str(summary.get('failed', 0)))
            stats_table.add_row("Skipped", str(summary.get('skipped', 0)))
            stats_table.add_row("Duration", f"{summary.get('duration', 0):.2f}s")
            
            self.console.print(stats_table)
        
        # Error details
        if not result.get('passed', False) and 'stderr' in result:
            error_panel = Panel(
                result['stderr'][:500] + "..." if len(result['stderr']) > 500 else result['stderr'],
                title="Error Details",
                border_style="red"
            )
            self.console.print(error_panel)
    
    async def display_comprehensive_results(self, result: Dict[str, Any]):
        """Display comprehensive test suite results"""
        self.console.clear()
        
        # Overall status
        overall_status = result.get('overall_status', 'unknown')
        status_color = "green" if overall_status == 'passed' else "red"
        
        header = Panel(
            f"[bold {status_color}]Full Test Suite: {overall_status.upper()}[/bold {status_color}]",
            border_style=status_color
        )
        self.console.print(header)
        
        # Test categories results
        if 'test_categories' in result:
            categories_table = Table(title="Test Categories")
            categories_table.add_column("Category", style="cyan")
            categories_table.add_column("Status", style="green")
            categories_table.add_column("Duration", style="yellow")
            
            for category, info in result['test_categories'].items():
                status = "✅ Passed" if info.get('status') == 'passed' else "❌ Failed"
                duration = f"{info.get('duration', 0):.2f}s"
                categories_table.add_row(category, status, duration)
            
            self.console.print(categories_table)
        
        # Performance metrics
        if 'performance_metrics' in result:
            metrics = result['performance_metrics']
            metrics_panel = Panel(
                f"Test Execution Time: {metrics.get('total_execution_time', 0):.2f}s\n"
                f"Average Test Time: {metrics.get('average_test_time', 0):.2f}s\n"
                f"System Resource Usage: {metrics.get('resource_usage', 'N/A')}",
                title="Performance Metrics",
                border_style="blue"
            )
            self.console.print(metrics_panel)
    
    async def get_system_status(self) -> str:
        """Get current system status"""
        try:
            # This would connect to the actual system
            return (
                "🟢 System: Online\n"
                "🟢 Database: Connected\n"
                "🟢 API: Running\n"
                "🟢 Agents: Active"
            )
        except Exception:
            return (
                "🔴 System: Offline\n"
                "🔴 Database: Disconnected\n"
                "🔴 API: Stopped\n"
                "🔴 Agents: Inactive"
            )
    
    def get_results_summary(self) -> str:
        """Get summary of recent test results"""
        if not self.test_results:
            return "No recent test results"
        
        summary_lines = []
        for test_type, test_data in self.test_results.items():
            result = test_data.get('result', {})
            status = "✅" if result.get('passed', False) else "❌"
            timestamp = test_data.get('timestamp', 'Unknown')
            summary_lines.append(f"{status} {test_type}: {timestamp[:19]}")
        
        return "\n".join(summary_lines[-5:])  # Show last 5 results
    
    def get_category_description(self, category: str) -> str:
        """Get description for test category"""
        descriptions = {
            'unit': 'Individual component tests',
            'integration': 'Component interaction tests',
            'performance': 'Speed and resource tests',
            'security': 'Security vulnerability tests',
            'e2e': 'Complete workflow tests',
            'regression': 'Functionality regression tests'
        }
        return descriptions.get(category, 'Unknown category')


async def main():
    """Main entry point for terminal test runner"""
    config = {
        'testing': True,
        'database': {'postgres': {'host': 'localhost'}},
        'agents': {'max_agents': 5}
    }
    
    runner = TerminalTestRunner(config)
    
    if await runner.initialize():
        await runner.run_interactive_session()
    else:
        print("Failed to initialize test runner")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())

{"validation_id": "validation_**********_95d1611e", "validation_level": "comprehensive", "overall_status": "partial", "overall_score": 0.8083414246591775, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "completed", "integration_score": 0.9127105794395378, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "team_manager", "component_type": "core", "status": "completed", "integration_score": 0.8513800794609745, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "completed", "integration_score": 0.8190110040708377, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "analytics_engine", "component_type": "core", "status": "completed", "integration_score": 0.823346803627292, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "completed", "integration_score": 0.843134879974768, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "completed", "integration_score": 0.8083597378358917, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "completed", "integration_score": 0.863556275926862, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "risk_manager", "component_type": "core", "status": "completed", "integration_score": 0.8840751686982493, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "completed", "integration_score": 0.8306475349949831, "error_count": 0, "warnings": ["Integration issues in strategy_manager"], "dependencies_met": "True"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8570112927207385, "error_count": 0, "warnings": ["Integration issues in competitive_framework"], "dependencies_met": "False"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8077106864600649, "error_count": 0, "warnings": ["Integration issues in tournament_framework"], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.827583819593828, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "completed", "integration_score": 0.8308115508616026, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "completed", "integration_score": 0.8370103262129931, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8325883175050718, "error_count": 0, "warnings": ["Integration issues in advanced_trading_engine"], "dependencies_met": "True"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "completed", "integration_score": 0.7821524803228281, "error_count": 0, "warnings": ["Integration issues in ai_coordinator"], "dependencies_met": "True"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "completed", "integration_score": 0.9014841585266898, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "completed", "integration_score": 0.9136753525974182, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "completed", "integration_score": 0.8570855920988769, "error_count": 0, "warnings": ["Integration issues in paper_trading_engine"], "dependencies_met": "True"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "completed", "integration_score": 0.8409168192398462, "error_count": 0, "warnings": [], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.9883611704128994, "data_manager": 0.6814983698954745, "analytics_engine": 0.6509014482654012, "ollama_hub": 0.7045602806048847, "execution_engine": 0.8141179464591485, "portfolio_manager": 0.6506231832757623, "risk_manager": 0.8310781911443317, "strategy_manager": 0.8071072082177023, "competitive_framework": 0.7520656229064329, "tournament_framework": 0.8789128141459459, "self_improvement_engine": 0.7476685714287757, "regime_adaptation_system": 0.8045497433224684, "performance_optimizer": 0.7743590584003803, "advanced_trading_engine": 0.7903146193738267, "ai_coordinator": 0.687098967853141, "configuration_manager": 0.7945931674481668, "mock_data_providers": 0.8478131374891333, "paper_trading_engine": 0.7823834755291978, "logging_audit_system": 0.8965357499241571}, "team_manager": {"system_coordinator": 0.6643697585501775, "team_manager": 1.0, "data_manager": 0.9846467037502157, "analytics_engine": 0.8983146061655134, "ollama_hub": 0.8471567508230049, "execution_engine": 0.8034562362234178, "portfolio_manager": 0.7959625060849529, "risk_manager": 0.6863258343509708, "strategy_manager": 0.7065848510773963, "competitive_framework": 0.6428565108318229, "tournament_framework": 0.7008263705768143, "self_improvement_engine": 0.7986942197521927, "regime_adaptation_system": 0.7971228199075513, "performance_optimizer": 0.6670019460332857, "advanced_trading_engine": 0.7319173091522709, "ai_coordinator": 0.8215023913445328, "configuration_manager": 0.8579355382917331, "mock_data_providers": 0.775984022622174, "paper_trading_engine": 0.7833506635165918, "logging_audit_system": 0.8228507411772032}, "data_manager": {"system_coordinator": 0.8869404487144912, "team_manager": 0.8068291843914894, "data_manager": 1.0, "analytics_engine": 0.9961153134103291, "ollama_hub": 0.6671842824482367, "execution_engine": 0.7860985322325305, "portfolio_manager": 0.6178102092832247, "risk_manager": 0.8147578043576316, "strategy_manager": 0.6098531232741013, "competitive_framework": 0.6089416086213695, "tournament_framework": 0.6909551889035711, "self_improvement_engine": 0.8832739118936489, "regime_adaptation_system": 0.8230302792275095, "performance_optimizer": 0.8412711896187631, "advanced_trading_engine": 0.8274946294222986, "ai_coordinator": 0.6468178341590358, "configuration_manager": 0.6803151595750991, "mock_data_providers": 0.797138013036509, "paper_trading_engine": 0.7782176007794357, "logging_audit_system": 0.8449175282617952}, "analytics_engine": {"system_coordinator": 0.8853987413582194, "team_manager": 0.6655044046935947, "data_manager": 0.8895437242775892, "analytics_engine": 1.0, "ollama_hub": 0.7992878550009224, "execution_engine": 0.625925612253881, "portfolio_manager": 0.7037926889378714, "risk_manager": 0.8532903513103081, "strategy_manager": 0.9367460960185736, "competitive_framework": 0.6821680981279783, "tournament_framework": 0.8238602492173586, "self_improvement_engine": 0.7218201599158948, "regime_adaptation_system": 0.7646323147055615, "performance_optimizer": 0.7558974820786044, "advanced_trading_engine": 0.8124914157474493, "ai_coordinator": 0.6685165853532905, "configuration_manager": 0.8276625130503167, "mock_data_providers": 0.868329701480075, "paper_trading_engine": 0.8204244202184908, "logging_audit_system": 0.8282303500442036}, "ollama_hub": {"system_coordinator": 0.7611346478385385, "team_manager": 0.6535814772722288, "data_manager": 0.8455373493530569, "analytics_engine": 0.6027876991913665, "ollama_hub": 1.0, "execution_engine": 0.869263437397186, "portfolio_manager": 0.8367498821233865, "risk_manager": 0.8902312182728421, "strategy_manager": 0.6417026689085747, "competitive_framework": 0.7211297471624197, "tournament_framework": 0.7418288413102394, "self_improvement_engine": 0.849486867653638, "regime_adaptation_system": 0.8953280364997087, "performance_optimizer": 0.8420614996299998, "advanced_trading_engine": 0.88169346073668, "ai_coordinator": 0.7897039314402772, "configuration_manager": 0.7617664235406778, "mock_data_providers": 0.767591949317929, "paper_trading_engine": 0.7880755469673814, "logging_audit_system": 0.7794536293014308}, "execution_engine": {"system_coordinator": 0.622760420509872, "team_manager": 0.614984622885036, "data_manager": 0.8788576991005657, "analytics_engine": 0.7197285974833182, "ollama_hub": 0.6213802981863593, "execution_engine": 1.0, "portfolio_manager": 0.8535555668379324, "risk_manager": 0.8446311963221144, "strategy_manager": 0.7053858404965963, "competitive_framework": 0.6755867179647298, "tournament_framework": 0.8854428565111859, "self_improvement_engine": 0.8516454602837115, "regime_adaptation_system": 0.7714708163748458, "performance_optimizer": 0.8765848922144543, "advanced_trading_engine": 0.6912358290772634, "ai_coordinator": 0.7930976078725103, "configuration_manager": 0.6659001443374988, "mock_data_providers": 0.7809376258554908, "paper_trading_engine": 0.7758672664997777, "logging_audit_system": 0.7737357306349062}, "portfolio_manager": {"system_coordinator": 0.8352652691586746, "team_manager": 0.6923823233655149, "data_manager": 0.627640417132423, "analytics_engine": 0.8717816507211591, "ollama_hub": 0.748546249299552, "execution_engine": 0.6557003425155112, "portfolio_manager": 1.0, "risk_manager": 0.8485459309753152, "strategy_manager": 0.6856696832763927, "competitive_framework": 0.7761435889283261, "tournament_framework": 0.6420895164413875, "self_improvement_engine": 0.7993466229125248, "regime_adaptation_system": 0.6915115961937783, "performance_optimizer": 0.8119257729314061, "advanced_trading_engine": 0.6739151488938756, "ai_coordinator": 0.6984162475250629, "configuration_manager": 0.8872799907386097, "mock_data_providers": 0.6091786948067599, "paper_trading_engine": 0.6496001869559684, "logging_audit_system": 0.6110866615455411}, "risk_manager": {"system_coordinator": 0.8273679826090181, "team_manager": 0.6645308120966137, "data_manager": 0.7110500842994193, "analytics_engine": 0.8487984952018822, "ollama_hub": 0.6151823855593739, "execution_engine": 0.8641032058595193, "portfolio_manager": 0.7553263887403795, "risk_manager": 1.0, "strategy_manager": 0.7669979216760809, "competitive_framework": 0.8743070791825593, "tournament_framework": 0.8492888290173094, "self_improvement_engine": 0.7946676808607267, "regime_adaptation_system": 0.7969397508909407, "performance_optimizer": 0.627738384368934, "advanced_trading_engine": 0.8970193346523339, "ai_coordinator": 0.7608578529894869, "configuration_manager": 0.7254810793173657, "mock_data_providers": 0.8304737966540781, "paper_trading_engine": 0.6934682520595112, "logging_audit_system": 0.6534796502896579}, "strategy_manager": {"system_coordinator": 0.847910257735295, "team_manager": 0.8458302125536655, "data_manager": 0.7709088077916983, "analytics_engine": 0.8081095818139201, "ollama_hub": 0.7049757148176069, "execution_engine": 0.6081936396244645, "portfolio_manager": 0.7030989641180098, "risk_manager": 0.792601671606437, "strategy_manager": 1.0, "competitive_framework": 0.7109025614620931, "tournament_framework": 0.8752008990936332, "self_improvement_engine": 0.6630877342701416, "regime_adaptation_system": 0.6371315625428375, "performance_optimizer": 0.7572785358714219, "advanced_trading_engine": 0.8927610993302777, "ai_coordinator": 0.8933499389774462, "configuration_manager": 0.8901081708668597, "mock_data_providers": 0.8900305410822666, "paper_trading_engine": 0.7157238179668557, "logging_audit_system": 0.7664495011078729}, "competitive_framework": {"system_coordinator": 0.6314450715987162, "team_manager": 0.7087149554995156, "data_manager": 0.7497428406438006, "analytics_engine": 0.8878070578586286, "ollama_hub": 0.7464997080806619, "execution_engine": 0.8275642905204508, "portfolio_manager": 0.7166747010211241, "risk_manager": 0.6284993148267896, "strategy_manager": 0.6191661435720719, "competitive_framework": 1.0, "tournament_framework": 0.8740086580061279, "self_improvement_engine": 0.801201540855023, "regime_adaptation_system": 0.7145544860806249, "performance_optimizer": 0.8286414011433868, "advanced_trading_engine": 0.8558901910986795, "ai_coordinator": 0.8214649979306679, "configuration_manager": 0.7188971011270779, "mock_data_providers": 0.8107545764089221, "paper_trading_engine": 0.8661863558766929, "logging_audit_system": 0.6445630071291096}, "tournament_framework": {"system_coordinator": 0.8943292212026599, "team_manager": 0.6090025449667069, "data_manager": 0.6176220169284922, "analytics_engine": 0.6444511717579253, "ollama_hub": 0.8401491001972012, "execution_engine": 0.8177273450568974, "portfolio_manager": 0.837107258987367, "risk_manager": 0.6922759212952201, "strategy_manager": 0.6542037070158674, "competitive_framework": 0.8628354791055897, "tournament_framework": 1.0, "self_improvement_engine": 0.8493510693477511, "regime_adaptation_system": 0.**********510676, "performance_optimizer": 0.7207065315298261, "advanced_trading_engine": 0.8865231951426344, "ai_coordinator": 0.6178912651246905, "configuration_manager": 0.6212824672164422, "mock_data_providers": 0.6761478730537137, "paper_trading_engine": 0.7748525702987711, "logging_audit_system": 0.8105436616622614}, "self_improvement_engine": {"system_coordinator": 0.8878908826819201, "team_manager": 0.8474237967577856, "data_manager": 0.8141614618814588, "analytics_engine": 0.641733446414268, "ollama_hub": 0.7173731581289218, "execution_engine": 0.856000085211045, "portfolio_manager": 0.8141624233705519, "risk_manager": 0.7325114417630442, "strategy_manager": 0.6777812750799176, "competitive_framework": 0.7079126923212867, "tournament_framework": 0.8924049692514149, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.7962520446946638, "performance_optimizer": 0.7958920824089087, "advanced_trading_engine": 0.7125830245617026, "ai_coordinator": 0.6006654659581561, "configuration_manager": 0.6906205660864952, "mock_data_providers": 0.6703241222703351, "paper_trading_engine": 0.7820108961938369, "logging_audit_system": 0.8306368389439847}, "regime_adaptation_system": {"system_coordinator": 0.6978933815967313, "team_manager": 0.8793942247335478, "data_manager": 0.8586394546356115, "analytics_engine": 0.6507157409818498, "ollama_hub": 0.8332430675105659, "execution_engine": 0.7429817619386897, "portfolio_manager": 0.8069282700887824, "risk_manager": 0.655639215282031, "strategy_manager": 0.855418168240887, "competitive_framework": 0.6293913179853001, "tournament_framework": 0.7476323315477134, "self_improvement_engine": 0.7071816052861651, "regime_adaptation_system": 1.0, "performance_optimizer": 0.734431533432031, "advanced_trading_engine": 0.6290728397885116, "ai_coordinator": 0.7861582897811674, "configuration_manager": 0.8276807671029653, "mock_data_providers": 0.6827280560520004, "paper_trading_engine": 0.8636506134282321, "logging_audit_system": 0.878884180208086}, "performance_optimizer": {"system_coordinator": 0.7962419185957135, "team_manager": 0.7814245551227094, "data_manager": 0.6572255756215231, "analytics_engine": 0.6304105205716266, "ollama_hub": 0.7035056608054632, "execution_engine": 0.8098943152031235, "portfolio_manager": 0.6677656387917464, "risk_manager": 0.8985403511945766, "strategy_manager": 0.8845004053326115, "competitive_framework": 0.6084176682681012, "tournament_framework": 0.8784577630726758, "self_improvement_engine": 0.8882707205833933, "regime_adaptation_system": 0.7083835195859798, "performance_optimizer": 1.0, "advanced_trading_engine": 0.8091725119612515, "ai_coordinator": 0.7832760877287843, "configuration_manager": 0.8532009475059483, "mock_data_providers": 0.8034874449754905, "paper_trading_engine": 0.6757524336851363, "logging_audit_system": 0.6269678091694517}, "advanced_trading_engine": {"system_coordinator": 0.7341649217098283, "team_manager": 0.7263079427079503, "data_manager": 0.7746463937054162, "analytics_engine": 0.6782478037267937, "ollama_hub": 0.7537311696569655, "execution_engine": 0.8374435454391977, "portfolio_manager": 0.8849320186543208, "risk_manager": 0.8562068906130963, "strategy_manager": 0.7476521865625275, "competitive_framework": 0.8711432960641605, "tournament_framework": 0.7747181262501195, "self_improvement_engine": 0.8803263541525161, "regime_adaptation_system": 0.8171891170033747, "performance_optimizer": 0.7220972383158987, "advanced_trading_engine": 1.0, "ai_coordinator": 0.7604801657821603, "configuration_manager": 0.6362173581625566, "mock_data_providers": 0.6816458412637213, "paper_trading_engine": 0.873617692818242, "logging_audit_system": 0.8969852792335493}, "ai_coordinator": {"system_coordinator": 0.8103141951398776, "team_manager": 0.7269088530233602, "data_manager": 0.80444851630522, "analytics_engine": 0.6738044419411354, "ollama_hub": 0.6725148071443323, "execution_engine": 0.6646032632927872, "portfolio_manager": 0.8821467267371244, "risk_manager": 0.7319889214248178, "strategy_manager": 0.685930002585959, "competitive_framework": 0.7593860499517243, "tournament_framework": 0.8271966596190709, "self_improvement_engine": 0.8816782566249663, "regime_adaptation_system": 0.8902490573798216, "performance_optimizer": 0.7560432791427257, "advanced_trading_engine": 0.6881111035944749, "ai_coordinator": 1.0, "configuration_manager": 0.8127163895353232, "mock_data_providers": 0.7831694790259471, "paper_trading_engine": 0.6544369970419402, "logging_audit_system": 0.6867564245280335}, "configuration_manager": {"system_coordinator": 0.7230332791207389, "team_manager": 0.7644373381036562, "data_manager": 0.7272571927518682, "analytics_engine": 0.6537796500020935, "ollama_hub": 0.7698949755379234, "execution_engine": 0.605432824309536, "portfolio_manager": 0.6224212353366106, "risk_manager": 0.7691698503337756, "strategy_manager": 0.6956834647098943, "competitive_framework": 0.8076309267110722, "tournament_framework": 0.6189538522181047, "self_improvement_engine": 0.7547600336379356, "regime_adaptation_system": 0.6357183629974875, "performance_optimizer": 0.8846813103511268, "advanced_trading_engine": 0.7812160313288581, "ai_coordinator": 0.8022131717729869, "configuration_manager": 1.0, "mock_data_providers": 0.8360184958768795, "paper_trading_engine": 0.7492853207626484, "logging_audit_system": 0.6386830877654406}, "mock_data_providers": {"system_coordinator": 0.6604629459837535, "team_manager": 0.8404793548603441, "data_manager": 0.7899683561022078, "analytics_engine": 0.7638672478250534, "ollama_hub": 0.7747889977120141, "execution_engine": 0.8982945053632556, "portfolio_manager": 0.603382675227364, "risk_manager": 0.6422272645087096, "strategy_manager": 0.8823335052969357, "competitive_framework": 0.6057410084059122, "tournament_framework": 0.8892138946062356, "self_improvement_engine": 0.7199000480804195, "regime_adaptation_system": 0.824300429020539, "performance_optimizer": 0.6803045908967553, "advanced_trading_engine": 0.8028604429299103, "ai_coordinator": 0.6010901547791236, "configuration_manager": 0.6396323639559466, "mock_data_providers": 1.0, "paper_trading_engine": 0.6913500229500883, "logging_audit_system": 0.6662663036117912}, "paper_trading_engine": {"system_coordinator": 0.7711482722833598, "team_manager": 0.6978317976337762, "data_manager": 0.8041221392953029, "analytics_engine": 0.7422869844607926, "ollama_hub": 0.6233093873387648, "execution_engine": 0.6847816300119682, "portfolio_manager": 0.7727095161155442, "risk_manager": 0.6174765474976968, "strategy_manager": 0.6531415436817757, "competitive_framework": 0.6536267482498179, "tournament_framework": 0.7285333905367422, "self_improvement_engine": 0.8368081911195883, "regime_adaptation_system": 0.6548236018991591, "performance_optimizer": 0.7760169909636655, "advanced_trading_engine": 0.7907239031656508, "ai_coordinator": 0.8382813262574255, "configuration_manager": 0.6883779771829965, "mock_data_providers": 0.6440443970810639, "paper_trading_engine": 1.0, "logging_audit_system": 0.7857898621288909}, "logging_audit_system": {"system_coordinator": 0.8365886141107846, "team_manager": 0.6966836388819125, "data_manager": 0.6243230044669645, "analytics_engine": 0.7551084122767999, "ollama_hub": 0.8438871018618206, "execution_engine": 0.670180448364469, "portfolio_manager": 0.6289192866098902, "risk_manager": 0.7087204244594603, "strategy_manager": 0.7127789246440289, "competitive_framework": 0.7243704906505269, "tournament_framework": 0.6827198343595797, "self_improvement_engine": 0.7531700407250801, "regime_adaptation_system": 0.7419263962012945, "performance_optimizer": 0.7067081845050731, "advanced_trading_engine": 0.6641491617206086, "ai_coordinator": 0.8441859534617129, "configuration_manager": 0.855723535841229, "mock_data_providers": 0.8862427042446764, "paper_trading_engine": 0.6833549998171575, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.7693245410828085, "response_time": 0.91663367238714, "throughput": 0.6990587176040884, "memory_usage": 0.7048170511464026, "cpu_usage": 0.7849567924406652, "concurrent_operations": 0.7448484568310945}, "critical_issues": ["Components with dependency issues: competitive_framework, performance_optimizer, configuration_manager, mock_data_providers"], "recommendations": ["Enhance component integration and communication"], "production_ready": "True", "timestamp": **********.8973927}
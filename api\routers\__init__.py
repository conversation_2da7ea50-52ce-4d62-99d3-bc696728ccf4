"""
API Routers Package

This package contains all API route definitions organized by functionality:

- system_router: System status and control endpoints
- agents_router: Agent management and communication endpoints  
- strategies_router: Trading strategy management endpoints
- portfolio_router: Portfolio management and tracking endpoints
- analytics_router: Analytics and reporting endpoints
- risk_router: Risk management endpoints
- execution_router: Trade execution endpoints
- market_data_router: Market data endpoints
- learning_router: Learning and adaptation endpoints
- database_router: Database management endpoints
"""

# Import all routers for easy access
from . import (
    system_router,
    agents_router,
    strategies_router,
    portfolio_router,
    analytics_router,
    risk_router,
    execution_router,
    market_data_router,
    learning_router,
    database_router
)

__all__ = [
    'system_router',
    'agents_router', 
    'strategies_router',
    'portfolio_router',
    'analytics_router',
    'risk_router',
    'execution_router',
    'market_data_router',
    'learning_router',
    'database_router'
]

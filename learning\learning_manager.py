"""
Learning Manager - Central coordinator for all learning and adaptation activities
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import numpy as np
import pandas as pd

from .strategy_repository import StrategyRepository
from .performance_learner import PerformanceLearner
from .model_tuner import ModelTuner
from .knowledge_manager import KnowledgeManager
from .adaptation_engine import AdaptationEngine

logger = logging.getLogger(__name__)


@dataclass
class LearningEvent:
    """Learning event data structure"""
    event_type: str
    source: str
    data: Dict[str, Any]
    timestamp: float
    priority: int = 1


@dataclass
class AdaptationResult:
    """Adaptation result data structure"""
    success: bool
    adaptation_type: str
    changes_made: List[str]
    performance_impact: Optional[float]
    confidence: float
    timestamp: float


class LearningManager:
    """
    Central coordinator for all learning and adaptation activities.
    
    Responsibilities:
    - Coordinate learning across all system components
    - Manage strategy repository and knowledge base
    - Orchestrate performance-based learning
    - Control model fine-tuning and optimization
    - Implement continuous improvement mechanisms
    - Manage knowledge transfer between components
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.learning_config = config.get('learning', {})
        
        # Core learning components
        self.strategy_repository: Optional[StrategyRepository] = None
        self.performance_learner: Optional[PerformanceLearner] = None
        self.model_tuner: Optional[ModelTuner] = None
        self.knowledge_manager: Optional[KnowledgeManager] = None
        self.adaptation_engine: Optional[AdaptationEngine] = None
        
        # Learning state
        self.learning_events: List[LearningEvent] = []
        self.adaptation_history: List[AdaptationResult] = []
        self.learning_metrics: Dict[str, Any] = {}
        
        # Configuration
        self.learning_enabled = self.learning_config.get('enabled', True)
        self.adaptation_frequency = self.learning_config.get('adaptation_frequency', 3600)  # 1 hour
        self.min_performance_samples = self.learning_config.get('min_performance_samples', 100)
        self.adaptation_threshold = self.learning_config.get('adaptation_threshold', 0.05)  # 5%
        
        # Integration points
        self.strategy_manager = None
        self.risk_manager = None
        self.portfolio_manager = None
        self.execution_engine = None
        
        # State flags
        self.initialized = False
        self.running = False
        
        # Background tasks
        self.learning_task: Optional[asyncio.Task] = None
        self.adaptation_task: Optional[asyncio.Task] = None
        
    async def initialize(self) -> bool:
        """Initialize the learning management system"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Learning Manager...")
            
            # Initialize core components
            self.strategy_repository = StrategyRepository(self.config)
            await self.strategy_repository.initialize()
            
            self.performance_learner = PerformanceLearner(self.config)
            await self.performance_learner.initialize()
            
            self.model_tuner = ModelTuner(self.config)
            await self.model_tuner.initialize()
            
            self.knowledge_manager = KnowledgeManager(self.config)
            await self.knowledge_manager.initialize()
            
            self.adaptation_engine = AdaptationEngine(self.config)
            await self.adaptation_engine.initialize()
            
            # Setup learning metrics
            await self._initialize_learning_metrics()
            
            self.initialized = True
            logger.info("✓ Learning Manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Learning Manager: {e}")
            return False
    
    async def start(self) -> bool:
        """Start the learning management system"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        if self.running:
            return True
            
        try:
            logger.info("Starting Learning Manager...")
            
            # Start all components
            await asyncio.gather(
                self.strategy_repository.start(),
                self.performance_learner.start(),
                self.model_tuner.start(),
                self.knowledge_manager.start(),
                self.adaptation_engine.start()
            )
            
            # Start background tasks
            if self.learning_enabled:
                self.learning_task = asyncio.create_task(self._learning_loop())
                self.adaptation_task = asyncio.create_task(self._adaptation_loop())
            
            self.running = True
            logger.info("✓ Learning Manager started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Learning Manager: {e}")
            return False
    
    async def stop(self) -> bool:
        """Stop the learning management system"""
        if not self.running:
            return True
            
        try:
            logger.info("Stopping Learning Manager...")
            self.running = False
            
            # Cancel background tasks
            if self.learning_task:
                self.learning_task.cancel()
            if self.adaptation_task:
                self.adaptation_task.cancel()
            
            # Stop all components
            await asyncio.gather(
                self.strategy_repository.stop(),
                self.performance_learner.stop(),
                self.model_tuner.stop(),
                self.knowledge_manager.stop(),
                self.adaptation_engine.stop(),
                return_exceptions=True
            )
            
            logger.info("✓ Learning Manager stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping Learning Manager: {e}")
            return False
    
    async def set_integration_points(self, 
                                   strategy_manager=None,
                                   risk_manager=None,
                                   portfolio_manager=None,
                                   execution_engine=None):
        """Set integration points with other systems"""
        self.strategy_manager = strategy_manager
        self.risk_manager = risk_manager
        self.portfolio_manager = portfolio_manager
        self.execution_engine = execution_engine
        
        # Pass integration points to components
        if self.performance_learner:
            await self.performance_learner.set_integration_points(
                strategy_manager, risk_manager, portfolio_manager, execution_engine
            )
        
        if self.adaptation_engine:
            await self.adaptation_engine.set_integration_points(
                strategy_manager, risk_manager, portfolio_manager, execution_engine
            )
        
        logger.info("Learning Manager integration points configured")

    async def set_database_coordinator(self, database_coordinator):
        """Set database coordinator for data persistence"""
        self.database_coordinator = database_coordinator

        # Pass database coordinator to learning components
        if self.strategy_repository:
            if hasattr(self.strategy_repository, 'set_database_coordinator'):
                await self.strategy_repository.set_database_coordinator(database_coordinator)

        if self.performance_learner:
            if hasattr(self.performance_learner, 'set_database_coordinator'):
                await self.performance_learner.set_database_coordinator(database_coordinator)

        if self.model_tuner:
            if hasattr(self.model_tuner, 'set_database_coordinator'):
                await self.model_tuner.set_database_coordinator(database_coordinator)

        if self.knowledge_manager:
            if hasattr(self.knowledge_manager, 'set_database_coordinator'):
                await self.knowledge_manager.set_database_coordinator(database_coordinator)

        if self.adaptation_engine:
            if hasattr(self.adaptation_engine, 'set_database_coordinator'):
                await self.adaptation_engine.set_database_coordinator(database_coordinator)

        logger.info("Learning Manager database coordinator configured")

    async def record_learning_event(self, event_type: str, source: str,
                                  data: Dict[str, Any], priority: int = 1) -> None:
        """Record a learning event"""
        try:
            event = LearningEvent(
                event_type=event_type,
                source=source,
                data=data,
                timestamp=time.time(),
                priority=priority
            )
            
            self.learning_events.append(event)
            
            # Limit event history
            if len(self.learning_events) > 10000:
                self.learning_events = self.learning_events[-10000:]
            
            # Process high-priority events immediately
            if priority == 1:
                await self._process_learning_event(event)
            
            logger.debug(f"Learning event recorded: {event_type} from {source}")
            
        except Exception as e:
            logger.error(f"Error recording learning event: {e}")
    
    async def trigger_adaptation(self, component: str, reason: str, 
                               context: Dict[str, Any]) -> AdaptationResult:
        """Trigger adaptation for a specific component"""
        try:
            logger.info(f"Triggering adaptation for {component}: {reason}")
            
            # Use adaptation engine to perform adaptation
            adaptation_result = await self.adaptation_engine.adapt_component(
                component, reason, context
            )
            
            # Record adaptation result
            result = AdaptationResult(
                success=adaptation_result.get('success', False),
                adaptation_type=adaptation_result.get('type', 'unknown'),
                changes_made=adaptation_result.get('changes', []),
                performance_impact=adaptation_result.get('performance_impact'),
                confidence=adaptation_result.get('confidence', 0.5),
                timestamp=time.time()
            )
            
            self.adaptation_history.append(result)
            
            # Update learning metrics
            await self._update_learning_metrics()
            
            return result
            
        except Exception as e:
            logger.error(f"Error triggering adaptation for {component}: {e}")
            return AdaptationResult(
                success=False,
                adaptation_type='error',
                changes_made=[],
                performance_impact=None,
                confidence=0.0,
                timestamp=time.time()
            )
    
    async def learn_from_performance(self, component: str, 
                                   performance_data: Dict[str, Any]) -> bool:
        """Learn from performance data"""
        try:
            # Use performance learner to extract insights
            learning_result = await self.performance_learner.learn_from_data(
                component, performance_data
            )
            
            if learning_result.get('success', False):
                # Store insights in knowledge manager
                insights = learning_result.get('insights', {})
                await self.knowledge_manager.store_insights(component, insights)
                
                # Check if adaptation is needed
                if learning_result.get('adaptation_needed', False):
                    await self.trigger_adaptation(
                        component, 
                        'performance_learning',
                        learning_result
                    )
                
                logger.info(f"✓ Learned from performance data for {component}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error learning from performance for {component}: {e}")
            return False
    
    async def optimize_strategy(self, strategy_id: str, 
                              optimization_type: str = 'parameters') -> Dict[str, Any]:
        """Optimize a strategy using learning insights"""
        try:
            # Get strategy from repository
            strategy_info = await self.strategy_repository.get_strategy(strategy_id)
            
            if not strategy_info:
                return {'success': False, 'error': 'Strategy not found'}
            
            # Use model tuner for optimization
            optimization_result = await self.model_tuner.optimize_strategy(
                strategy_id, strategy_info, optimization_type
            )
            
            if optimization_result.get('success', False):
                # Update strategy in repository
                await self.strategy_repository.update_strategy(
                    strategy_id, optimization_result.get('optimized_strategy')
                )
                
                # Record learning event
                await self.record_learning_event(
                    'strategy_optimization',
                    'learning_manager',
                    optimization_result,
                    priority=2
                )
            
            return optimization_result
            
        except Exception as e:
            logger.error(f"Error optimizing strategy {strategy_id}: {e}")
            return {'success': False, 'error': str(e)}
    
    async def get_learning_insights(self, component: Optional[str] = None) -> Dict[str, Any]:
        """Get learning insights for component or system-wide"""
        try:
            if component:
                return await self.knowledge_manager.get_insights(component)
            else:
                return await self.knowledge_manager.get_all_insights()
        except Exception as e:
            logger.error(f"Error getting learning insights: {e}")
            return {}
    
    async def get_learning_metrics(self) -> Dict[str, Any]:
        """Get current learning metrics"""
        return self.learning_metrics.copy()
    
    # Private methods
    
    async def _initialize_learning_metrics(self):
        """Initialize learning metrics"""
        self.learning_metrics = {
            'total_learning_events': 0,
            'total_adaptations': 0,
            'successful_adaptations': 0,
            'adaptation_success_rate': 0.0,
            'average_performance_improvement': 0.0,
            'strategies_optimized': 0,
            'models_tuned': 0,
            'knowledge_items': 0,
            'last_adaptation': None,
            'learning_efficiency': 0.0
        }
    
    async def _update_learning_metrics(self):
        """Update learning metrics"""
        try:
            # Basic counts
            self.learning_metrics['total_learning_events'] = len(self.learning_events)
            self.learning_metrics['total_adaptations'] = len(self.adaptation_history)
            
            # Success rates
            successful_adaptations = sum(1 for a in self.adaptation_history if a.success)
            self.learning_metrics['successful_adaptations'] = successful_adaptations
            
            if self.adaptation_history:
                self.learning_metrics['adaptation_success_rate'] = (
                    successful_adaptations / len(self.adaptation_history)
                )
                
                # Average performance improvement
                improvements = [
                    a.performance_impact for a in self.adaptation_history 
                    if a.performance_impact is not None and a.success
                ]
                if improvements:
                    self.learning_metrics['average_performance_improvement'] = np.mean(improvements)
                
                # Last adaptation
                self.learning_metrics['last_adaptation'] = self.adaptation_history[-1].timestamp
            
            # Component-specific metrics
            if self.strategy_repository:
                repo_metrics = await self.strategy_repository.get_metrics()
                self.learning_metrics['strategies_optimized'] = repo_metrics.get('optimized_count', 0)
            
            if self.model_tuner:
                tuner_metrics = await self.model_tuner.get_metrics()
                self.learning_metrics['models_tuned'] = tuner_metrics.get('tuned_count', 0)
            
            if self.knowledge_manager:
                knowledge_metrics = await self.knowledge_manager.get_metrics()
                self.learning_metrics['knowledge_items'] = knowledge_metrics.get('total_items', 0)
            
            # Learning efficiency (simplified metric)
            if self.learning_metrics['total_learning_events'] > 0:
                self.learning_metrics['learning_efficiency'] = (
                    successful_adaptations / self.learning_metrics['total_learning_events']
                )
            
        except Exception as e:
            logger.error(f"Error updating learning metrics: {e}")
    
    async def _learning_loop(self):
        """Background learning loop"""
        while self.running:
            try:
                await asyncio.sleep(60)  # Process every minute
                
                if self.running:
                    # Process pending learning events
                    await self._process_pending_events()
                    
                    # Update metrics
                    await self._update_learning_metrics()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in learning loop: {e}")
    
    async def _adaptation_loop(self):
        """Background adaptation loop"""
        while self.running:
            try:
                await asyncio.sleep(self.adaptation_frequency)
                
                if self.running:
                    # Check for adaptation opportunities
                    await self._check_adaptation_opportunities()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in adaptation loop: {e}")
    
    async def _process_learning_event(self, event: LearningEvent):
        """Process a single learning event"""
        try:
            event_type = event.event_type
            
            if event_type == 'performance_update':
                await self.learn_from_performance(event.source, event.data)
            elif event_type == 'strategy_signal':
                await self._process_strategy_signal(event)
            elif event_type == 'risk_alert':
                await self._process_risk_alert(event)
            elif event_type == 'execution_feedback':
                await self._process_execution_feedback(event)
            
        except Exception as e:
            logger.error(f"Error processing learning event: {e}")
    
    async def _process_pending_events(self):
        """Process pending learning events"""
        try:
            # Process events by priority
            events_to_process = sorted(
                [e for e in self.learning_events if e.priority > 1],
                key=lambda x: x.priority
            )
            
            for event in events_to_process[:10]:  # Process up to 10 events per cycle
                await self._process_learning_event(event)
                self.learning_events.remove(event)
                
        except Exception as e:
            logger.error(f"Error processing pending events: {e}")
    
    async def _check_adaptation_opportunities(self):
        """Check for adaptation opportunities"""
        try:
            # Check each component for adaptation needs
            components = ['strategy_manager', 'risk_manager', 'portfolio_manager', 'execution_engine']
            
            for component in components:
                if hasattr(self, component) and getattr(self, component):
                    # Get performance data
                    performance_data = await self._get_component_performance(component)
                    
                    if performance_data:
                        # Check if adaptation is needed
                        needs_adaptation = await self._assess_adaptation_need(component, performance_data)
                        
                        if needs_adaptation:
                            await self.trigger_adaptation(
                                component,
                                'periodic_assessment',
                                performance_data
                            )
                            
        except Exception as e:
            logger.error(f"Error checking adaptation opportunities: {e}")
    
    async def _get_component_performance(self, component: str) -> Optional[Dict[str, Any]]:
        """Get performance data for a component"""
        try:
            component_obj = getattr(self, component, None)
            if component_obj and hasattr(component_obj, 'get_performance_metrics'):
                return await component_obj.get_performance_metrics()
            return None
        except Exception as e:
            logger.error(f"Error getting performance for {component}: {e}")
            return None
    
    async def _assess_adaptation_need(self, component: str, performance_data: Dict[str, Any]) -> bool:
        """Assess if component needs adaptation"""
        try:
            # Simple assessment based on performance degradation
            recent_performance = performance_data.get('recent_performance', 0.0)
            baseline_performance = performance_data.get('baseline_performance', 0.0)
            
            if baseline_performance > 0:
                performance_change = (recent_performance - baseline_performance) / baseline_performance
                return performance_change < -self.adaptation_threshold
            
            return False
            
        except Exception as e:
            logger.error(f"Error assessing adaptation need for {component}: {e}")
            return False
    
    async def _process_strategy_signal(self, event: LearningEvent):
        """Process strategy signal event"""
        # Implementation for strategy signal processing
        pass
    
    async def _process_risk_alert(self, event: LearningEvent):
        """Process risk alert event"""
        # Implementation for risk alert processing
        pass
    
    async def _process_execution_feedback(self, event: LearningEvent):
        """Process execution feedback event"""
        # Implementation for execution feedback processing
        pass

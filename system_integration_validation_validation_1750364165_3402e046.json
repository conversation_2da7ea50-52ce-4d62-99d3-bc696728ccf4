{"validation_id": "validation_**********_3402e046", "validation_level": "standard", "overall_status": "partial", "overall_score": 0.7948371866252264, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "partial", "integration_score": 0.7949641305081996, "error_count": 0, "warnings": ["Integration issues in system_coordinator"], "dependencies_met": "True"}, {"component_name": "team_manager", "component_type": "core", "status": "completed", "integration_score": 0.8075350538428104, "error_count": 0, "warnings": ["Integration issues in team_manager"], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "partial", "integration_score": 0.6924105769698254, "error_count": 0, "warnings": ["Functionality concerns in data_manager", "Integration issues in data_manager"], "dependencies_met": "True"}, {"component_name": "analytics_engine", "component_type": "core", "status": "partial", "integration_score": 0.7980629618602936, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "completed", "integration_score": 0.805743515505951, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "partial", "integration_score": 0.7499266105440501, "error_count": 0, "warnings": ["Functionality concerns in execution_engine", "Integration issues in execution_engine"], "dependencies_met": "True"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "completed", "integration_score": 0.8313256194148976, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "risk_manager", "component_type": "core", "status": "completed", "integration_score": 0.8338021838086965, "error_count": 0, "warnings": ["Integration issues in risk_manager"], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "partial", "integration_score": 0.7977337216358201, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8171701799122363, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8275577279220137, "error_count": 0, "warnings": ["Integration issues in tournament_framework"], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8040312910366579, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "partial", "integration_score": 0.7836759285199467, "error_count": 0, "warnings": ["Integration issues in regime_adaptation_system"], "dependencies_met": "False"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "partial", "integration_score": 0.7691536420410092, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8068945037007538, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "partial", "integration_score": 0.7903203808234806, "error_count": 0, "warnings": ["Functionality concerns in ai_coordinator"], "dependencies_met": "False"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "partial", "integration_score": 0.7968619487916544, "error_count": 0, "warnings": ["Integration issues in configuration_manager"], "dependencies_met": "True"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "completed", "integration_score": 0.8790730570707094, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "completed", "integration_score": 0.8578840921827079, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "partial", "integration_score": 0.746903405539064, "error_count": 0, "warnings": ["Functionality concerns in logging_audit_system"], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.8922393250747543, "data_manager": 0.7053999976846146, "analytics_engine": 0.8205214029732472, "ollama_hub": 0.8544979962205181, "execution_engine": 0.855752634943447, "portfolio_manager": 0.8689793081881751, "risk_manager": 0.8671214943521484, "strategy_manager": 0.7374429323057254, "competitive_framework": 0.6287319034278147, "tournament_framework": 0.8430410802349111, "self_improvement_engine": 0.8858000119136399, "regime_adaptation_system": 0.8505383667165836, "performance_optimizer": 0.8148794120061813, "advanced_trading_engine": 0.873413591461615, "ai_coordinator": 0.6762677189593044, "configuration_manager": 0.8914089508395042, "mock_data_providers": 0.730810504340895, "paper_trading_engine": 0.756407436840956, "logging_audit_system": 0.7183552726569973}, "team_manager": {"system_coordinator": 0.7674365315383541, "team_manager": 1.0, "data_manager": 0.9578870472500698, "analytics_engine": 0.7633548350239189, "ollama_hub": 0.6426437789894114, "execution_engine": 0.6465634649802152, "portfolio_manager": 0.670152226701257, "risk_manager": 0.7243746376977339, "strategy_manager": 0.6018435866011322, "competitive_framework": 0.7415069202253822, "tournament_framework": 0.632699440538361, "self_improvement_engine": 0.6763404668572663, "regime_adaptation_system": 0.8129362536892091, "performance_optimizer": 0.7860890921181993, "advanced_trading_engine": 0.7581650255622818, "ai_coordinator": 0.815243011808613, "configuration_manager": 0.7919169054754552, "mock_data_providers": 0.8116327404026876, "paper_trading_engine": 0.747700104560329, "logging_audit_system": 0.7146119746061168}, "data_manager": {"system_coordinator": 0.6113218665096416, "team_manager": 0.8457793074668405, "data_manager": 1.0, "analytics_engine": 0.9718065323239959, "ollama_hub": 0.6254543080487256, "execution_engine": 0.614941950792965, "portfolio_manager": 0.6279398908961408, "risk_manager": 0.7238245135321792, "strategy_manager": 0.8312546215954933, "competitive_framework": 0.8390952839191759, "tournament_framework": 0.844057941265738, "self_improvement_engine": 0.7146142543640536, "regime_adaptation_system": 0.8684220872872526, "performance_optimizer": 0.8066831274106708, "advanced_trading_engine": 0.6814802342879329, "ai_coordinator": 0.8866178849280982, "configuration_manager": 0.8341740342267896, "mock_data_providers": 0.8327274913313462, "paper_trading_engine": 0.7442440779578143, "logging_audit_system": 0.8409974115925771}, "analytics_engine": {"system_coordinator": 0.6405116789673947, "team_manager": 0.8185072962579811, "data_manager": 0.8078345438869006, "analytics_engine": 1.0, "ollama_hub": 0.7904602995449143, "execution_engine": 0.8273097400327037, "portfolio_manager": 0.6222442767224693, "risk_manager": 0.7281215631451589, "strategy_manager": 0.9900514199679757, "competitive_framework": 0.7743742531034749, "tournament_framework": 0.8919951904260939, "self_improvement_engine": 0.7052449871200104, "regime_adaptation_system": 0.8768155194756608, "performance_optimizer": 0.7269525840133457, "advanced_trading_engine": 0.8125703889864779, "ai_coordinator": 0.7921100352405014, "configuration_manager": 0.7673763473124892, "mock_data_providers": 0.7922497438506512, "paper_trading_engine": 0.8431005573482793, "logging_audit_system": 0.8300328680651702}, "ollama_hub": {"system_coordinator": 0.6363714458094434, "team_manager": 0.74437840261944, "data_manager": 0.808856480821534, "analytics_engine": 0.8370004902247001, "ollama_hub": 1.0, "execution_engine": 0.6614115523131305, "portfolio_manager": 0.678940517902713, "risk_manager": 0.7583534308939847, "strategy_manager": 0.7950560742274524, "competitive_framework": 0.6801142448729791, "tournament_framework": 0.6237158012703017, "self_improvement_engine": 0.6272653857529615, "regime_adaptation_system": 0.71876594528835, "performance_optimizer": 0.6677830230923245, "advanced_trading_engine": 0.6655802285226406, "ai_coordinator": 0.8448573188408096, "configuration_manager": 0.8456270186036047, "mock_data_providers": 0.8358675609350257, "paper_trading_engine": 0.6273699903284541, "logging_audit_system": 0.6564693453009777}, "execution_engine": {"system_coordinator": 0.8413958806449289, "team_manager": 0.8268722532319228, "data_manager": 0.6553814448067474, "analytics_engine": 0.6805431290906586, "ollama_hub": 0.7705556148160263, "execution_engine": 1.0, "portfolio_manager": 0.8076468026255387, "risk_manager": 0.6069548262005641, "strategy_manager": 0.7186168138626379, "competitive_framework": 0.6605239972570082, "tournament_framework": 0.6356688838935536, "self_improvement_engine": 0.8465890577019912, "regime_adaptation_system": 0.8657733955291722, "performance_optimizer": 0.6843716241653129, "advanced_trading_engine": 0.7810753728208656, "ai_coordinator": 0.611238467693951, "configuration_manager": 0.7539685458199128, "mock_data_providers": 0.746532112180034, "paper_trading_engine": 0.7771689477102487, "logging_audit_system": 0.7879615125845221}, "portfolio_manager": {"system_coordinator": 0.8072731221999021, "team_manager": 0.7094547480607857, "data_manager": 0.7821406953780292, "analytics_engine": 0.6940990520635328, "ollama_hub": 0.743485793720927, "execution_engine": 0.8423338917610386, "portfolio_manager": 1.0, "risk_manager": 0.8493059163277178, "strategy_manager": 0.7174451099839557, "competitive_framework": 0.7457629563703321, "tournament_framework": 0.7647333466066569, "self_improvement_engine": 0.6600390569713342, "regime_adaptation_system": 0.859131715858567, "performance_optimizer": 0.7632548872294451, "advanced_trading_engine": 0.8785412237995542, "ai_coordinator": 0.832359918799787, "configuration_manager": 0.6514096993891183, "mock_data_providers": 0.7078599915163603, "paper_trading_engine": 0.6734948159924431, "logging_audit_system": 0.8307593098923319}, "risk_manager": {"system_coordinator": 0.8307918754791621, "team_manager": 0.8212061485289707, "data_manager": 0.7206202148112215, "analytics_engine": 0.8187003863885275, "ollama_hub": 0.8629978141893702, "execution_engine": 0.7798259611229, "portfolio_manager": 0.8971927668666428, "risk_manager": 1.0, "strategy_manager": 0.8944356672171024, "competitive_framework": 0.8564854008888467, "tournament_framework": 0.8980202459997311, "self_improvement_engine": 0.601061646128593, "regime_adaptation_system": 0.6789278183489744, "performance_optimizer": 0.8837641270818378, "advanced_trading_engine": 0.8927207694003383, "ai_coordinator": 0.6336473993661857, "configuration_manager": 0.6085356396162558, "mock_data_providers": 0.8304922145069007, "paper_trading_engine": 0.6933401402009933, "logging_audit_system": 0.8504894789314059}, "strategy_manager": {"system_coordinator": 0.881290710810006, "team_manager": 0.6001537383839439, "data_manager": 0.7072695044635707, "analytics_engine": 0.6094220211936465, "ollama_hub": 0.736437821336413, "execution_engine": 0.7818585318256607, "portfolio_manager": 0.8540305119235692, "risk_manager": 0.7565895665376969, "strategy_manager": 1.0, "competitive_framework": 0.840987141674561, "tournament_framework": 0.6010308766853087, "self_improvement_engine": 0.8812468282725949, "regime_adaptation_system": 0.8945702789292916, "performance_optimizer": 0.8465258139839167, "advanced_trading_engine": 0.6119906204793192, "ai_coordinator": 0.8599909515978179, "configuration_manager": 0.7761351580469966, "mock_data_providers": 0.6064410450323594, "paper_trading_engine": 0.843233545561941, "logging_audit_system": 0.7074437793579986}, "competitive_framework": {"system_coordinator": 0.8288631237815793, "team_manager": 0.758831502570531, "data_manager": 0.8225719728565782, "analytics_engine": 0.6546823014558204, "ollama_hub": 0.8785716384691369, "execution_engine": 0.8015571407124289, "portfolio_manager": 0.736001701601113, "risk_manager": 0.807903611192075, "strategy_manager": 0.6493763914445497, "competitive_framework": 1.0, "tournament_framework": 0.6834211522873284, "self_improvement_engine": 0.6339279404408058, "regime_adaptation_system": 0.6402261353385764, "performance_optimizer": 0.8906922289566646, "advanced_trading_engine": 0.7505663392390582, "ai_coordinator": 0.7380522978976938, "configuration_manager": 0.6479649327843876, "mock_data_providers": 0.8533952061910535, "paper_trading_engine": 0.6956739338408656, "logging_audit_system": 0.6919108792574653}, "tournament_framework": {"system_coordinator": 0.8679905733871156, "team_manager": 0.6658008880332442, "data_manager": 0.604578340692238, "analytics_engine": 0.8453228634270147, "ollama_hub": 0.8720783980574489, "execution_engine": 0.8194381200681886, "portfolio_manager": 0.8050316862121695, "risk_manager": 0.6285088234698221, "strategy_manager": 0.821522883257664, "competitive_framework": 0.7498318325204422, "tournament_framework": 1.0, "self_improvement_engine": 0.8018561735342206, "regime_adaptation_system": 0.7436268736264733, "performance_optimizer": 0.8015993455118575, "advanced_trading_engine": 0.7418648874392826, "ai_coordinator": 0.6904726098881047, "configuration_manager": 0.8797156237874091, "mock_data_providers": 0.887343066256035, "paper_trading_engine": 0.741526026731271, "logging_audit_system": 0.8486110860591259}, "self_improvement_engine": {"system_coordinator": 0.893667793701741, "team_manager": 0.7947432449413944, "data_manager": 0.8990081730608714, "analytics_engine": 0.6880578817209337, "ollama_hub": 0.7502952271566776, "execution_engine": 0.709075871784016, "portfolio_manager": 0.8406584587615546, "risk_manager": 0.7377666779522747, "strategy_manager": 0.6334382885999065, "competitive_framework": 0.6405012145913247, "tournament_framework": 0.8489105283428906, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.7546985008786838, "performance_optimizer": 0.7245388448944502, "advanced_trading_engine": 0.601094228401766, "ai_coordinator": 0.6311954620184569, "configuration_manager": 0.6994761950874431, "mock_data_providers": 0.8954632875706132, "paper_trading_engine": 0.684406946704035, "logging_audit_system": 0.6853220154915575}, "regime_adaptation_system": {"system_coordinator": 0.755057725598628, "team_manager": 0.7686897845972492, "data_manager": 0.8760352049408863, "analytics_engine": 0.7400859116826237, "ollama_hub": 0.7388074695923122, "execution_engine": 0.6385987110435236, "portfolio_manager": 0.7108867801725605, "risk_manager": 0.7391966662180394, "strategy_manager": 0.7121541844433732, "competitive_framework": 0.6405949419901981, "tournament_framework": 0.7192579386566329, "self_improvement_engine": 0.6329098433394921, "regime_adaptation_system": 1.0, "performance_optimizer": 0.7617061579471198, "advanced_trading_engine": 0.8924506612581546, "ai_coordinator": 0.8328879837988887, "configuration_manager": 0.6150646795004676, "mock_data_providers": 0.6655565754644713, "paper_trading_engine": 0.8956088058582661, "logging_audit_system": 0.8477446333144708}, "performance_optimizer": {"system_coordinator": 0.6584448279351678, "team_manager": 0.8048279263626095, "data_manager": 0.7241302810508612, "analytics_engine": 0.7452825767706702, "ollama_hub": 0.8788469646695762, "execution_engine": 0.8626647383511821, "portfolio_manager": 0.6500002133022824, "risk_manager": 0.7940275837802342, "strategy_manager": 0.6825033825857107, "competitive_framework": 0.6126275017056091, "tournament_framework": 0.6963048048623708, "self_improvement_engine": 0.729417747427833, "regime_adaptation_system": 0.6484105471459967, "performance_optimizer": 1.0, "advanced_trading_engine": 0.795597350481134, "ai_coordinator": 0.8427556870382473, "configuration_manager": 0.6569188083905364, "mock_data_providers": 0.8411379340415915, "paper_trading_engine": 0.6558307200164749, "logging_audit_system": 0.7226422201443314}, "advanced_trading_engine": {"system_coordinator": 0.6373078196526181, "team_manager": 0.7952815826194422, "data_manager": 0.8388637434405689, "analytics_engine": 0.6833614836999276, "ollama_hub": 0.8667850528297405, "execution_engine": 0.8259835932055486, "portfolio_manager": 0.7783005778312677, "risk_manager": 0.769214669048347, "strategy_manager": 0.6254116101010182, "competitive_framework": 0.7856919682217539, "tournament_framework": 0.8157163679966686, "self_improvement_engine": 0.7145423673138342, "regime_adaptation_system": 0.8194743513009339, "performance_optimizer": 0.6045397934814338, "advanced_trading_engine": 1.0, "ai_coordinator": 0.6218439356048546, "configuration_manager": 0.6789240934885842, "mock_data_providers": 0.7289673349250335, "paper_trading_engine": 0.8601793635169361, "logging_audit_system": 0.8798754318548918}, "ai_coordinator": {"system_coordinator": 0.8965449189559269, "team_manager": 0.8572392545343315, "data_manager": 0.7589973392377861, "analytics_engine": 0.7889627272902762, "ollama_hub": 0.6102814652050093, "execution_engine": 0.778531161031764, "portfolio_manager": 0.6937485194380648, "risk_manager": 0.7434957674179101, "strategy_manager": 0.8102888413279038, "competitive_framework": 0.863372678482605, "tournament_framework": 0.6379464657683197, "self_improvement_engine": 0.8536835967670275, "regime_adaptation_system": 0.6789626474769619, "performance_optimizer": 0.6886865547080635, "advanced_trading_engine": 0.7504778551494128, "ai_coordinator": 1.0, "configuration_manager": 0.6930409582014614, "mock_data_providers": 0.6694651112752409, "paper_trading_engine": 0.6844903630156389, "logging_audit_system": 0.7536402953992987}, "configuration_manager": {"system_coordinator": 0.612399048662445, "team_manager": 0.8229861282778157, "data_manager": 0.6187651279037305, "analytics_engine": 0.6360079088239842, "ollama_hub": 0.8864143148112615, "execution_engine": 0.818566920407578, "portfolio_manager": 0.8912120191063644, "risk_manager": 0.7915195785471443, "strategy_manager": 0.6375360566083291, "competitive_framework": 0.8504486471310686, "tournament_framework": 0.7004274051841977, "self_improvement_engine": 0.7984033280300362, "regime_adaptation_system": 0.7135712372045275, "performance_optimizer": 0.6922685339854152, "advanced_trading_engine": 0.6782955843830906, "ai_coordinator": 0.6124982699270688, "configuration_manager": 1.0, "mock_data_providers": 0.8911723544496173, "paper_trading_engine": 0.77205340846574, "logging_audit_system": 0.7017972743938811}, "mock_data_providers": {"system_coordinator": 0.6307008199374927, "team_manager": 0.8662452730865792, "data_manager": 0.6988421699111859, "analytics_engine": 0.8396867575333035, "ollama_hub": 0.6724646950657225, "execution_engine": 0.6108780502406707, "portfolio_manager": 0.7589398689011991, "risk_manager": 0.6545795537371352, "strategy_manager": 0.6056077445122091, "competitive_framework": 0.6730971138539129, "tournament_framework": 0.8193021307747678, "self_improvement_engine": 0.7485674921042799, "regime_adaptation_system": 0.7700525457130548, "performance_optimizer": 0.7165258571507801, "advanced_trading_engine": 0.759630136073915, "ai_coordinator": 0.6776931698646498, "configuration_manager": 0.7650411502608504, "mock_data_providers": 1.0, "paper_trading_engine": 0.7978402673005163, "logging_audit_system": 0.6789244400878653}, "paper_trading_engine": {"system_coordinator": 0.8487952176948971, "team_manager": 0.6212265145855415, "data_manager": 0.6516763253282545, "analytics_engine": 0.8659775637491784, "ollama_hub": 0.7045956264664528, "execution_engine": 0.7234398130473778, "portfolio_manager": 0.6965434145916788, "risk_manager": 0.768354949363671, "strategy_manager": 0.8815960267379972, "competitive_framework": 0.6870152354649464, "tournament_framework": 0.8881728775702561, "self_improvement_engine": 0.6569987314551953, "regime_adaptation_system": 0.7334676328511527, "performance_optimizer": 0.6960170182658797, "advanced_trading_engine": 0.6147409272426769, "ai_coordinator": 0.7183284823531895, "configuration_manager": 0.729627677700808, "mock_data_providers": 0.8133758601365295, "paper_trading_engine": 1.0, "logging_audit_system": 0.8711754038025552}, "logging_audit_system": {"system_coordinator": 0.6512993354756649, "team_manager": 0.8715896603502922, "data_manager": 0.7156523183559331, "analytics_engine": 0.7096862385577148, "ollama_hub": 0.8318216167527431, "execution_engine": 0.8978166003329816, "portfolio_manager": 0.8921684779124068, "risk_manager": 0.8152917091396804, "strategy_manager": 0.7807407307029953, "competitive_framework": 0.8113284896615771, "tournament_framework": 0.7276021434209462, "self_improvement_engine": 0.7521284739096386, "regime_adaptation_system": 0.8095151967044327, "performance_optimizer": 0.8429540190167703, "advanced_trading_engine": 0.89655651435742, "ai_coordinator": 0.6877022486117553, "configuration_manager": 0.7580142732482655, "mock_data_providers": 0.8578561376919184, "paper_trading_engine": 0.7983289770720635, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.7235045209716646, "response_time": 0.821029971957985, "throughput": 0.6411293067408772, "memory_usage": 0.8527889299666702, "cpu_usage": 0.8620909192807834, "concurrent_operations": 0.7289113754434104}, "critical_issues": ["Components with dependency issues: regime_adaptation_system, ai_coordinator"], "recommendations": ["Improve 10 partially working components", "Improve overall system performance and stability", "Enhance component integration and communication"], "production_ready": "True", "timestamp": **********.1035802}
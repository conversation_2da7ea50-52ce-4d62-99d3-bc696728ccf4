#!/bin/bash

# Advanced Ollama Trading Agents - Test Runner Script
# Comprehensive testing script with multiple test modes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
VENV_PATH="$PROJECT_ROOT/venv"
PYTHON_CMD="python3"
PIP_CMD="pip3"

# Test configuration
TEST_TIMEOUT=300
COVERAGE_MIN=80
PARALLEL_WORKERS=4

# Functions
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# Check if virtual environment exists
check_venv() {
    if [ ! -d "$VENV_PATH" ]; then
        print_warning "Virtual environment not found. Creating..."
        $PYTHON_CMD -m venv "$VENV_PATH"
        source "$VENV_PATH/bin/activate"
        $PIP_CMD install --upgrade pip
        $PIP_CMD install -r requirements.txt
        $PIP_CMD install -r requirements-dev.txt
        print_success "Virtual environment created and dependencies installed"
    else
        source "$VENV_PATH/bin/activate"
        print_success "Virtual environment activated"
    fi
}

# Install test dependencies
install_test_deps() {
    print_info "Installing test dependencies..."
    $PIP_CMD install pytest pytest-asyncio pytest-cov pytest-xdist pytest-html pytest-json-report
    $PIP_CMD install coverage bandit safety mypy black flake8 isort
    print_success "Test dependencies installed"
}

# Run linting and code quality checks
run_linting() {
    print_header "Running Code Quality Checks"
    
    # Black formatting check
    print_info "Checking code formatting with Black..."
    if black --check --diff .; then
        print_success "Code formatting is correct"
    else
        print_error "Code formatting issues found. Run 'black .' to fix"
        return 1
    fi
    
    # Import sorting check
    print_info "Checking import sorting with isort..."
    if isort --check-only --diff .; then
        print_success "Import sorting is correct"
    else
        print_error "Import sorting issues found. Run 'isort .' to fix"
        return 1
    fi
    
    # Flake8 linting
    print_info "Running flake8 linting..."
    if flake8 --max-line-length=100 --exclude=venv,__pycache__,.git .; then
        print_success "Flake8 linting passed"
    else
        print_error "Flake8 linting issues found"
        return 1
    fi
    
    # MyPy type checking
    print_info "Running MyPy type checking..."
    if mypy --ignore-missing-imports .; then
        print_success "MyPy type checking passed"
    else
        print_warning "MyPy type checking found issues (non-blocking)"
    fi
    
    print_success "Code quality checks completed"
}

# Run security checks
run_security_checks() {
    print_header "Running Security Checks"
    
    # Bandit security linting
    print_info "Running Bandit security analysis..."
    if bandit -r . -x venv,tests; then
        print_success "Bandit security analysis passed"
    else
        print_warning "Bandit found potential security issues"
    fi
    
    # Safety dependency check
    print_info "Checking dependencies for known vulnerabilities..."
    if safety check; then
        print_success "No known vulnerabilities found in dependencies"
    else
        print_warning "Potential vulnerabilities found in dependencies"
    fi
    
    print_success "Security checks completed"
}

# Run unit tests
run_unit_tests() {
    print_header "Running Unit Tests"
    
    pytest tests/unit/ \
        -v \
        --tb=short \
        --cov=. \
        --cov-report=html:htmlcov \
        --cov-report=xml:coverage.xml \
        --cov-report=term-missing \
        --cov-fail-under=$COVERAGE_MIN \
        --html=reports/unit_tests.html \
        --self-contained-html \
        --json-report --json-report-file=reports/unit_tests.json \
        --timeout=$TEST_TIMEOUT \
        -n $PARALLEL_WORKERS
    
    if [ $? -eq 0 ]; then
        print_success "Unit tests passed"
    else
        print_error "Unit tests failed"
        return 1
    fi
}

# Run integration tests
run_integration_tests() {
    print_header "Running Integration Tests"
    
    pytest tests/integration/ \
        -v \
        --tb=short \
        --html=reports/integration_tests.html \
        --self-contained-html \
        --json-report --json-report-file=reports/integration_tests.json \
        --timeout=$((TEST_TIMEOUT * 2))
    
    if [ $? -eq 0 ]; then
        print_success "Integration tests passed"
    else
        print_error "Integration tests failed"
        return 1
    fi
}

# Run performance tests
run_performance_tests() {
    print_header "Running Performance Tests"
    
    pytest tests/performance/ \
        -v \
        --tb=short \
        -m performance \
        --html=reports/performance_tests.html \
        --self-contained-html \
        --json-report --json-report-file=reports/performance_tests.json \
        --timeout=$((TEST_TIMEOUT * 3))
    
    if [ $? -eq 0 ]; then
        print_success "Performance tests passed"
    else
        print_error "Performance tests failed"
        return 1
    fi
}

# Run end-to-end tests
run_e2e_tests() {
    print_header "Running End-to-End Tests"
    
    pytest tests/integration/test_end_to_end.py \
        -v \
        --tb=short \
        --html=reports/e2e_tests.html \
        --self-contained-html \
        --json-report --json-report-file=reports/e2e_tests.json \
        --timeout=$((TEST_TIMEOUT * 4))
    
    if [ $? -eq 0 ]; then
        print_success "End-to-end tests passed"
    else
        print_error "End-to-end tests failed"
        return 1
    fi
}

# Run all tests
run_all_tests() {
    print_header "Running Complete Test Suite"
    
    # Create reports directory
    mkdir -p reports
    
    # Run all test categories
    run_linting || exit 1
    run_security_checks
    run_unit_tests || exit 1
    run_integration_tests || exit 1
    run_performance_tests
    run_e2e_tests
    
    print_success "Complete test suite finished"
}

# Run quick tests (smoke tests)
run_quick_tests() {
    print_header "Running Quick Tests"
    
    mkdir -p reports
    
    pytest tests/unit/ tests/integration/ \
        -v \
        --tb=short \
        -m "smoke or critical" \
        --html=reports/quick_tests.html \
        --self-contained-html \
        --json-report --json-report-file=reports/quick_tests.json \
        --timeout=60 \
        -x  # Stop on first failure
    
    if [ $? -eq 0 ]; then
        print_success "Quick tests passed"
    else
        print_error "Quick tests failed"
        return 1
    fi
}

# Generate test report
generate_report() {
    print_header "Generating Test Report"
    
    # Combine all JSON reports
    python3 -c "
import json
import glob
from datetime import datetime

reports = []
for file in glob.glob('reports/*.json'):
    try:
        with open(file, 'r') as f:
            data = json.load(f)
            reports.append({
                'file': file,
                'data': data
            })
    except:
        pass

summary = {
    'timestamp': datetime.now().isoformat(),
    'reports': reports,
    'total_tests': sum(r['data'].get('summary', {}).get('total', 0) for r in reports),
    'passed_tests': sum(r['data'].get('summary', {}).get('passed', 0) for r in reports),
    'failed_tests': sum(r['data'].get('summary', {}).get('failed', 0) for r in reports)
}

with open('reports/test_summary.json', 'w') as f:
    json.dump(summary, f, indent=2)

print(f'Test Summary:')
print(f'Total Tests: {summary[\"total_tests\"]}')
print(f'Passed: {summary[\"passed_tests\"]}')
print(f'Failed: {summary[\"failed_tests\"]}')
print(f'Success Rate: {(summary[\"passed_tests\"] / max(summary[\"total_tests\"], 1) * 100):.1f}%')
"
    
    print_success "Test report generated in reports/test_summary.json"
}

# Clean up test artifacts
cleanup() {
    print_info "Cleaning up test artifacts..."
    rm -rf .pytest_cache
    rm -rf __pycache__
    find . -name "*.pyc" -delete
    find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    print_success "Cleanup completed"
}

# Main script logic
main() {
    cd "$PROJECT_ROOT"
    
    # Parse command line arguments
    case "${1:-all}" in
        "quick")
            check_venv
            install_test_deps
            run_quick_tests
            ;;
        "unit")
            check_venv
            install_test_deps
            run_unit_tests
            ;;
        "integration")
            check_venv
            install_test_deps
            run_integration_tests
            ;;
        "performance")
            check_venv
            install_test_deps
            run_performance_tests
            ;;
        "e2e")
            check_venv
            install_test_deps
            run_e2e_tests
            ;;
        "security")
            check_venv
            install_test_deps
            run_security_checks
            ;;
        "lint")
            check_venv
            install_test_deps
            run_linting
            ;;
        "all")
            check_venv
            install_test_deps
            run_all_tests
            generate_report
            ;;
        "clean")
            cleanup
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  quick       Run quick smoke tests"
            echo "  unit        Run unit tests only"
            echo "  integration Run integration tests only"
            echo "  performance Run performance tests only"
            echo "  e2e         Run end-to-end tests only"
            echo "  security    Run security checks only"
            echo "  lint        Run code quality checks only"
            echo "  all         Run complete test suite (default)"
            echo "  clean       Clean up test artifacts"
            echo "  help        Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown command: $1"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"

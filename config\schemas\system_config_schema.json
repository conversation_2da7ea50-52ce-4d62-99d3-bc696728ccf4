{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Advanced Ollama Trading Agent System Configuration Schema", "type": "object", "required": ["system", "database", "api", "ollama", "agents"], "properties": {"system": {"type": "object", "required": ["environment", "log_level"], "properties": {"name": {"type": "string", "minLength": 1}, "version": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "environment": {"type": "string", "enum": ["development", "testing", "staging", "production"]}, "log_level": {"type": "string", "enum": ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]}, "debug": {"type": "boolean"}, "metrics_enabled": {"type": "boolean"}, "profiling_enabled": {"type": "boolean"}}}, "database": {"type": "object", "required": ["postgres"], "properties": {"postgres": {"type": "object", "required": ["host", "port", "database", "username"], "properties": {"host": {"type": "string", "minLength": 1}, "port": {"type": "integer", "minimum": 1, "maximum": 65535}, "database": {"type": "string", "minLength": 1}, "username": {"type": "string", "minLength": 1}, "password": {"type": "string"}, "pool_size": {"type": "integer", "minimum": 1, "maximum": 100}, "max_overflow": {"type": "integer", "minimum": 0, "maximum": 100}, "pool_timeout": {"type": "integer", "minimum": 1}, "pool_recycle": {"type": "integer", "minimum": -1}, "ssl_mode": {"type": "string", "enum": ["disable", "allow", "prefer", "require", "verify-ca", "verify-full"]}}}, "redis": {"type": "object", "properties": {"host": {"type": "string", "minLength": 1}, "port": {"type": "integer", "minimum": 1, "maximum": 65535}, "db": {"type": "integer", "minimum": 0, "maximum": 15}, "password": {"type": ["string", "null"]}, "ssl": {"type": "boolean"}, "max_connections": {"type": "integer", "minimum": 1}}}}}, "api": {"type": "object", "required": ["host", "port"], "properties": {"host": {"type": "string", "minLength": 1}, "port": {"type": "integer", "minimum": 1024, "maximum": 65535}, "workers": {"type": "integer", "minimum": 1, "maximum": 32}, "max_request_size": {"type": "integer", "minimum": 1024}, "request_timeout": {"type": "integer", "minimum": 1}, "cors_enabled": {"type": "boolean"}, "rate_limiting": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "requests_per_minute": {"type": "integer", "minimum": 1}, "burst_size": {"type": "integer", "minimum": 1}}}}}, "security": {"type": "object", "properties": {"jwt_secret": {"type": "string", "minLength": 32}, "jwt_expiry": {"type": "integer", "minimum": 300, "maximum": 86400}, "api_key_required": {"type": "boolean"}, "encryption_enabled": {"type": "boolean"}, "audit_logging": {"type": "boolean"}, "ip_whitelist": {"type": "array", "items": {"type": "string"}}}}, "ollama": {"type": "object", "required": ["base_url"], "properties": {"base_url": {"type": "string", "format": "uri"}, "timeout": {"type": "integer", "minimum": 1, "maximum": 3600}, "max_retries": {"type": "integer", "minimum": 0, "maximum": 10}, "retry_delay": {"type": "integer", "minimum": 1, "maximum": 60}, "connection_pool_size": {"type": "integer", "minimum": 1, "maximum": 100}, "load_balancing": {"type": "boolean"}, "health_check_interval": {"type": "integer", "minimum": 10}}}, "market_data": {"type": "object", "properties": {"providers": {"type": "object", "properties": {"alpha_vantage": {"type": "object", "properties": {"api_key": {"type": "string"}, "rate_limit": {"type": "integer", "minimum": 1}, "timeout": {"type": "integer", "minimum": 1}, "enabled": {"type": "boolean"}}}, "mock": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "symbols": {"type": "array", "items": {"type": "string"}}, "update_interval": {"type": "integer", "minimum": 1}}}}}, "update_interval": {"type": "integer", "minimum": 1, "maximum": 3600}, "symbols": {"type": "array", "items": {"type": "string", "pattern": "^[A-Z]{1,5}$"}, "minItems": 1}}}, "brokers": {"type": "object", "properties": {"providers": {"type": "object"}, "routing": {"type": "object", "properties": {"default": {"type": "string"}, "symbols": {"type": "object"}}}}}, "agents": {"type": "object", "required": ["max_agents", "defaults"], "properties": {"max_agents": {"type": "integer", "minimum": 1, "maximum": 1000}, "heartbeat_interval": {"type": "integer", "minimum": 10, "maximum": 300}, "max_memory_usage": {"type": "integer", "minimum": *********}, "max_cpu_usage": {"type": "number", "minimum": 0.1, "maximum": 1.0}, "defaults": {"type": "object", "required": ["model", "temperature", "max_tokens"], "properties": {"model": {"type": "string", "minLength": 1}, "temperature": {"type": "number", "minimum": 0.0, "maximum": 2.0}, "max_tokens": {"type": "integer", "minimum": 1, "maximum": 8192}, "timeout": {"type": "integer", "minimum": 1, "maximum": 3600}}}, "scaling": {"type": "object", "properties": {"auto_scaling": {"type": "boolean"}, "min_agents": {"type": "integer", "minimum": 1}, "max_agents": {"type": "integer", "minimum": 1}, "scale_up_threshold": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "scale_down_threshold": {"type": "number", "minimum": 0.0, "maximum": 1.0}, "cooldown_period": {"type": "integer", "minimum": 60}}}}}, "strategies": {"type": "object", "properties": {"max_active_strategies": {"type": "integer", "minimum": 1, "maximum": 100}, "default_capital_allocation": {"type": "number", "minimum": 0.01, "maximum": 1.0}, "rebalance_frequency": {"type": "string", "enum": ["manual", "hourly", "daily", "weekly", "monthly"]}, "risk_limits": {"type": "object", "properties": {"max_drawdown": {"type": "number", "minimum": 0.01, "maximum": 1.0}, "max_leverage": {"type": "number", "minimum": 1.0, "maximum": 10.0}, "position_timeout": {"type": "integer", "minimum": 60}}}}}, "risk": {"type": "object", "required": ["max_portfolio_risk", "max_position_size", "max_drawdown"], "properties": {"max_portfolio_risk": {"type": "number", "minimum": 0.001, "maximum": 0.1}, "max_position_size": {"type": "number", "minimum": 0.01, "maximum": 1.0}, "max_sector_exposure": {"type": "number", "minimum": 0.1, "maximum": 1.0}, "max_drawdown": {"type": "number", "minimum": 0.01, "maximum": 1.0}, "var_calculation": {"type": "object", "properties": {"confidence_level": {"type": "number", "minimum": 0.9, "maximum": 0.99}, "lookback_period": {"type": "integer", "minimum": 30, "maximum": 1000}, "monte_carlo_simulations": {"type": "integer", "minimum": 1000, "maximum": 100000}}}}}, "execution": {"type": "object", "required": ["paper_trading"], "properties": {"paper_trading": {"type": "boolean"}, "max_orders_per_second": {"type": "integer", "minimum": 1, "maximum": 1000}, "order_timeout": {"type": "integer", "minimum": 1, "maximum": 3600}, "slippage_model": {"type": "string", "enum": ["fixed", "linear", "square_root", "impact"]}}}, "portfolio": {"type": "object", "required": ["initial_capital"], "properties": {"initial_capital": {"type": "number", "minimum": 1000.0}, "rebalance_threshold": {"type": "number", "minimum": 0.01, "maximum": 0.5}, "max_positions": {"type": "integer", "minimum": 1, "maximum": 1000}, "cash_target": {"type": "number", "minimum": 0.0, "maximum": 1.0}}}, "monitoring": {"type": "object", "properties": {"metrics": {"type": "object", "properties": {"collection_interval": {"type": "integer", "minimum": 1, "maximum": 3600}, "retention_period": {"type": "integer", "minimum": 3600}}}, "logging": {"type": "object", "properties": {"level": {"type": "string", "enum": ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]}, "format": {"type": "string", "enum": ["text", "json"]}, "rotation": {"type": "string", "enum": ["never", "daily", "weekly", "monthly"]}, "retention": {"type": "integer", "minimum": 1}}}}}}}
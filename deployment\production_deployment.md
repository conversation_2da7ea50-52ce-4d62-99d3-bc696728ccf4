# 🚀 Production Deployment Guide

## Overview
This guide covers deploying the Advanced Ollama Trading Agent System for live trading operations.

## Prerequisites

### System Requirements
- **CPU**: 16+ cores (32+ recommended for high-frequency trading)
- **RAM**: 64GB+ (128GB+ recommended)
- **Storage**: 1TB+ NVMe SSD
- **GPU**: NVIDIA RTX 4090 or better (for large models)
- **Network**: Low-latency connection to exchanges

### Software Requirements
- **OS**: Ubuntu 22.04 LTS or CentOS 8+
- **Python**: 3.11+
- **Ollama**: Latest version
- **Docker**: 24.0+
- **PostgreSQL**: 15+
- **Redis**: 7.0+
- **ClickHouse**: 23.0+

## Installation Steps

### 1. System Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install -y python3.11 python3.11-venv python3.11-dev
sudo apt install -y build-essential curl wget git
sudo apt install -y postgresql-15 redis-server

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

### 2. Ollama Installation
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Start Ollama service
sudo systemctl enable ollama
sudo systemctl start ollama

# Pull required models
ollama pull exaone-deep:32b
ollama pull magistral-abliterated:24b
ollama pull am-thinking-abliterate:latest
ollama pull phi4-reasoning:plus
ollama pull nemotron-mini:4b
ollama pull granite3.3:8b
```

### 3. Database Setup
```bash
# PostgreSQL setup
sudo -u postgres createuser trading_user
sudo -u postgres createdb trading_agents
sudo -u postgres psql -c "ALTER USER trading_user PASSWORD 'secure_password';"

# Redis configuration
sudo systemctl enable redis-server
sudo systemctl start redis-server

# ClickHouse installation
sudo apt install -y clickhouse-server clickhouse-client
sudo systemctl enable clickhouse-server
sudo systemctl start clickhouse-server
```

### 4. Application Deployment
```bash
# Clone repository
git clone <repository-url> /opt/trading-agents
cd /opt/trading-agents

# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp config/system_config.yaml.example config/system_config.yaml
cp config/model_configs.yaml.example config/model_configs.yaml

# Edit configurations
nano config/system_config.yaml
nano config/model_configs.yaml
```

## Configuration

### Production Configuration
```yaml
# config/system_config.yaml
environment: production
logging:
  level: INFO
  file: /var/log/trading-agents/system.log
  max_size: 100MB
  backup_count: 10

databases:
  postgres:
    host: localhost
    port: 5432
    database: trading_agents
    username: trading_user
    password: ${POSTGRES_PASSWORD}
    pool_size: 50
    
  redis:
    host: localhost
    port: 6379
    database: 0
    password: ${REDIS_PASSWORD}
    max_connections: 200
    
  clickhouse:
    host: localhost
    port: 9000
    database: analytics
    username: default
    password: ${CLICKHOUSE_PASSWORD}

ollama:
  host: localhost
  port: 11434
  timeout: 300
  max_retries: 3

agents:
  max_agents: 100
  heartbeat_interval: 30
  task_timeout: 300

communication:
  max_queue_size: 50000
  message_ttl: 3600
  
market_data:
  providers:
    - name: alpha_vantage
      enabled: true
      api_key: ${ALPHA_VANTAGE_API_KEY}
    - name: polygon
      enabled: true
      api_key: ${POLYGON_API_KEY}
      
trading:
  paper_trading: false  # Set to true for paper trading
  brokers:
    - name: interactive_brokers
      enabled: true
      account: ${IB_ACCOUNT}
      gateway_host: localhost
      gateway_port: 4001
```

### Environment Variables
```bash
# Create .env file
cat > .env << EOF
POSTGRES_PASSWORD=your_secure_postgres_password
REDIS_PASSWORD=your_secure_redis_password
CLICKHOUSE_PASSWORD=your_secure_clickhouse_password
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
POLYGON_API_KEY=your_polygon_key
IB_ACCOUNT=your_ib_account_number
EOF

# Secure the file
chmod 600 .env
```

## Service Configuration

### Systemd Service
```bash
# Create service file
sudo tee /etc/systemd/system/trading-agents.service << EOF
[Unit]
Description=Advanced Ollama Trading Agent System
After=network.target postgresql.service redis.service clickhouse-server.service ollama.service
Requires=postgresql.service redis.service ollama.service

[Service]
Type=simple
User=trading
Group=trading
WorkingDirectory=/opt/trading-agents
Environment=PATH=/opt/trading-agents/venv/bin
EnvironmentFile=/opt/trading-agents/.env
ExecStart=/opt/trading-agents/venv/bin/python main.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=trading-agents

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/trading-agents /var/log/trading-agents

[Install]
WantedBy=multi-user.target
EOF

# Create user and directories
sudo useradd -r -s /bin/false trading
sudo mkdir -p /var/log/trading-agents
sudo chown trading:trading /var/log/trading-agents
sudo chown -R trading:trading /opt/trading-agents

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable trading-agents
sudo systemctl start trading-agents
```

## Monitoring and Logging

### Log Configuration
```bash
# Configure log rotation
sudo tee /etc/logrotate.d/trading-agents << EOF
/var/log/trading-agents/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 trading trading
    postrotate
        systemctl reload trading-agents
    endscript
}
EOF
```

### Health Checks
```bash
# Create health check script
sudo tee /usr/local/bin/trading-agents-health << EOF
#!/bin/bash
curl -f http://localhost:8080/health || exit 1
EOF

sudo chmod +x /usr/local/bin/trading-agents-health
```

## Security

### Firewall Configuration
```bash
# Configure UFW
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH
sudo ufw allow ssh

# Allow application ports
sudo ufw allow 8080/tcp  # Dashboard
sudo ufw allow 11434/tcp # Ollama (if external access needed)

# Database ports (only if external access needed)
# sudo ufw allow 5432/tcp  # PostgreSQL
# sudo ufw allow 6379/tcp  # Redis
# sudo ufw allow 9000/tcp  # ClickHouse
```

### SSL/TLS Configuration
```bash
# Generate SSL certificates
sudo mkdir -p /etc/ssl/trading-agents
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/ssl/trading-agents/private.key \
    -out /etc/ssl/trading-agents/certificate.crt

sudo chown -R trading:trading /etc/ssl/trading-agents
sudo chmod 600 /etc/ssl/trading-agents/private.key
```

## Backup and Recovery

### Database Backup
```bash
# Create backup script
sudo tee /usr/local/bin/backup-trading-db << EOF
#!/bin/bash
BACKUP_DIR="/opt/backups/trading-agents"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# PostgreSQL backup
pg_dump -h localhost -U trading_user trading_agents > $BACKUP_DIR/postgres_$DATE.sql

# Redis backup
redis-cli --rdb $BACKUP_DIR/redis_$DATE.rdb

# ClickHouse backup
clickhouse-client --query "BACKUP DATABASE analytics TO Disk('backups', '$BACKUP_DIR/clickhouse_$DATE')"

# Compress backups
tar -czf $BACKUP_DIR/trading_agents_backup_$DATE.tar.gz $BACKUP_DIR/*_$DATE.*

# Clean up old backups (keep 30 days)
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
EOF

sudo chmod +x /usr/local/bin/backup-trading-db

# Schedule daily backups
echo "0 2 * * * /usr/local/bin/backup-trading-db" | sudo crontab -
```

## Performance Optimization

### System Tuning
```bash
# Optimize kernel parameters
sudo tee -a /etc/sysctl.conf << EOF
# Network optimizations
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.ipv4.tcp_rmem = 4096 87380 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728

# Memory optimizations
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5

# File system optimizations
fs.file-max = 2097152
EOF

sudo sysctl -p
```

### Database Optimization
```sql
-- PostgreSQL optimizations
ALTER SYSTEM SET shared_buffers = '16GB';
ALTER SYSTEM SET effective_cache_size = '48GB';
ALTER SYSTEM SET maintenance_work_mem = '2GB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
SELECT pg_reload_conf();
```

## Troubleshooting

### Common Issues
1. **Ollama Connection Issues**
   ```bash
   sudo systemctl status ollama
   sudo journalctl -u ollama -f
   ```

2. **Database Connection Issues**
   ```bash
   sudo systemctl status postgresql redis clickhouse-server
   sudo -u postgres psql -c "SELECT 1;"
   redis-cli ping
   clickhouse-client --query "SELECT 1"
   ```

3. **Memory Issues**
   ```bash
   free -h
   sudo dmesg | grep -i "killed process"
   ```

### Log Analysis
```bash
# View system logs
sudo journalctl -u trading-agents -f

# View application logs
tail -f /var/log/trading-agents/system.log

# Check for errors
grep -i error /var/log/trading-agents/system.log
```

## Scaling

### Horizontal Scaling
- Deploy multiple instances with load balancing
- Use Redis Cluster for distributed caching
- Implement database sharding for large datasets

### Vertical Scaling
- Increase CPU cores and memory
- Use faster storage (NVMe SSDs)
- Optimize model deployment across GPUs

## Maintenance

### Regular Maintenance Tasks
1. **Daily**: Check system health, review logs
2. **Weekly**: Update models, review performance
3. **Monthly**: Security updates, backup verification
4. **Quarterly**: Full system review, capacity planning

### Update Procedure
```bash
# Stop services
sudo systemctl stop trading-agents

# Backup current version
sudo cp -r /opt/trading-agents /opt/trading-agents.backup

# Update code
cd /opt/trading-agents
git pull origin main

# Update dependencies
source venv/bin/activate
pip install -r requirements.txt

# Run migrations if needed
python scripts/migrate_database.py

# Start services
sudo systemctl start trading-agents

# Verify deployment
python scripts/health_check.py
```

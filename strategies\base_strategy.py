"""
Base Strategy Framework - Foundation classes for all trading strategies
"""

import asyncio
import logging
import time
import uuid
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union, Tuple
from enum import Enum
from dataclasses import dataclass, field
import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)


class StrategyType(Enum):
    """Types of trading strategies"""
    MOMENTUM = "momentum"
    MEAN_REVERSION = "mean_reversion"
    VOLATILITY_EXPLOITATION = "volatility_exploitation"
    CARRY_TRADE = "carry_trade"
    ARBITRAGE = "arbitrage"
    MULTI_TIMEFRAME = "multi_timeframe"
    ADAPTIVE = "adaptive"
    ENSEMBLE = "ensemble"


class StrategyState(Enum):
    """Strategy lifecycle states"""
    CREATED = "created"
    INITIALIZING = "initializing"
    ACTIVE = "active"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"


class StrategySignal(Enum):
    """Trading signals generated by strategies"""
    STRONG_BUY = "strong_buy"
    BUY = "buy"
    WEAK_BUY = "weak_buy"
    HOLD = "hold"
    WEAK_SELL = "weak_sell"
    SELL = "sell"
    STRONG_SELL = "strong_sell"
    NO_SIGNAL = "no_signal"


@dataclass
class StrategyResult:
    """Result of strategy analysis"""
    strategy_id: str
    symbol: str
    signal: StrategySignal
    confidence: float  # 0.0 to 1.0
    strength: float    # Signal strength
    timestamp: float
    
    # Position sizing recommendations
    position_size: Optional[float] = None
    max_position_size: Optional[float] = None
    
    # Risk metrics
    expected_return: Optional[float] = None
    expected_risk: Optional[float] = None
    risk_reward_ratio: Optional[float] = None
    
    # Strategy-specific data
    indicators: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Execution parameters
    entry_price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    time_horizon: Optional[int] = None  # seconds


@dataclass
class StrategyMetrics:
    """Performance metrics for strategies"""
    strategy_id: str
    
    # Return metrics
    total_return: float = 0.0
    annualized_return: float = 0.0
    excess_return: float = 0.0
    
    # Risk metrics
    volatility: float = 0.0
    max_drawdown: float = 0.0
    var_95: float = 0.0  # Value at Risk 95%
    
    # Risk-adjusted metrics
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0
    
    # Trade statistics
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    profit_factor: float = 0.0
    
    # Timing metrics
    avg_holding_period: float = 0.0
    avg_time_to_profit: float = 0.0
    
    # Updated timestamp
    last_updated: float = field(default_factory=time.time)


class BaseStrategy(ABC):
    """
    Abstract base class for all trading strategies.
    
    Provides core functionality including:
    - Strategy lifecycle management
    - Market data processing
    - Signal generation
    - Performance tracking
    - Risk management integration
    """
    
    def __init__(self, 
                 strategy_id: str = None,
                 name: str = None,
                 strategy_type: StrategyType = None,
                 config: Dict[str, Any] = None):
        
        # Identity
        self.strategy_id = strategy_id or str(uuid.uuid4())
        self.name = name or f"Strategy_{self.strategy_id[:8]}"
        self.strategy_type = strategy_type
        self.config = config or {}
        
        # State management
        self.state = StrategyState.CREATED
        self.created_at = time.time()
        self.last_update = time.time()
        
        # Performance tracking
        self.metrics = StrategyMetrics(strategy_id=self.strategy_id)
        self.trade_history: List[Dict[str, Any]] = []
        self.signal_history: List[StrategyResult] = []
        
        # Configuration parameters
        self.symbols: List[str] = self.config.get('symbols', [])
        self.timeframes: List[str] = self.config.get('timeframes', ['1h'])
        self.lookback_period = self.config.get('lookback_period', 100)
        self.min_confidence = self.config.get('min_confidence', 0.6)
        
        # Risk parameters
        self.max_position_size = self.config.get('max_position_size', 0.05)
        self.stop_loss_pct = self.config.get('stop_loss_pct', 0.02)
        self.take_profit_pct = self.config.get('take_profit_pct', 0.04)
        
        # Data storage
        self.market_data: Dict[str, pd.DataFrame] = {}
        self.indicators: Dict[str, Dict[str, Any]] = {}
        
        # State flags
        self.initialized = False
        self.running = False
        
    async def initialize(self) -> bool:
        """Initialize the strategy"""
        if self.initialized:
            return True
            
        try:
            logger.info(f"Initializing strategy {self.name}")
            self.state = StrategyState.INITIALIZING
            
            # Strategy-specific initialization
            await self._initialize_strategy()
            
            self.initialized = True
            self.state = StrategyState.ACTIVE
            logger.info(f"✓ Strategy {self.name} initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize strategy {self.name}: {e}")
            self.state = StrategyState.ERROR
            return False
    
    async def start(self) -> bool:
        """Start the strategy"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        if self.running:
            return True
            
        try:
            logger.info(f"Starting strategy {self.name}")
            self.running = True
            
            # Start strategy-specific processes
            await self._start_strategy()
            
            logger.info(f"✓ Strategy {self.name} started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start strategy {self.name}: {e}")
            self.state = StrategyState.ERROR
            return False
    
    async def stop(self) -> bool:
        """Stop the strategy"""
        if not self.running:
            return True
            
        try:
            logger.info(f"Stopping strategy {self.name}")
            self.running = False
            self.state = StrategyState.STOPPED
            
            # Stop strategy-specific processes
            await self._stop_strategy()
            
            logger.info(f"✓ Strategy {self.name} stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping strategy {self.name}: {e}")
            return False
    
    async def update_market_data(self, symbol: str, data: pd.DataFrame) -> bool:
        """Update market data for a symbol"""
        try:
            self.market_data[symbol] = data
            self.last_update = time.time()
            
            # Update indicators
            await self._update_indicators(symbol, data)
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating market data for {symbol} in strategy {self.name}: {e}")
            return False
    
    async def generate_signal(self, symbol: str) -> Optional[StrategyResult]:
        """Generate trading signal for a symbol"""
        if not self.running or symbol not in self.market_data:
            return None
            
        try:
            # Generate signal using strategy-specific logic
            result = await self._generate_signal(symbol)
            
            if result and result.confidence >= self.min_confidence:
                # Add to signal history
                self.signal_history.append(result)
                
                # Limit history size
                if len(self.signal_history) > 1000:
                    self.signal_history = self.signal_history[-1000:]
                    
                return result
                
            return None
            
        except Exception as e:
            logger.error(f"Error generating signal for {symbol} in strategy {self.name}: {e}")
            return None
    
    async def update_metrics(self, trade_result: Dict[str, Any]) -> None:
        """Update strategy performance metrics"""
        try:
            self.trade_history.append(trade_result)
            
            # Calculate updated metrics
            await self._calculate_metrics()
            
        except Exception as e:
            logger.error(f"Error updating metrics for strategy {self.name}: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current strategy status"""
        return {
            'strategy_id': self.strategy_id,
            'name': self.name,
            'type': self.strategy_type.value if self.strategy_type else None,
            'state': self.state.value,
            'running': self.running,
            'symbols': self.symbols,
            'last_update': self.last_update,
            'metrics': {
                'total_return': self.metrics.total_return,
                'sharpe_ratio': self.metrics.sharpe_ratio,
                'max_drawdown': self.metrics.max_drawdown,
                'win_rate': self.metrics.win_rate,
                'total_trades': self.metrics.total_trades
            }
        }
    
    # Abstract methods to be implemented by subclasses
    
    @abstractmethod
    async def _initialize_strategy(self) -> None:
        """Strategy-specific initialization"""
        pass
    
    @abstractmethod
    async def _start_strategy(self) -> None:
        """Strategy-specific startup"""
        pass
    
    @abstractmethod
    async def _stop_strategy(self) -> None:
        """Strategy-specific shutdown"""
        pass
    
    @abstractmethod
    async def _update_indicators(self, symbol: str, data: pd.DataFrame) -> None:
        """Update technical indicators"""
        pass
    
    @abstractmethod
    async def _generate_signal(self, symbol: str) -> Optional[StrategyResult]:
        """Generate trading signal (strategy-specific logic)"""
        pass
    
    @abstractmethod
    async def _calculate_metrics(self) -> None:
        """Calculate performance metrics"""
        pass

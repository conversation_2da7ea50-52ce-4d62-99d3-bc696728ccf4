#!/usr/bin/env python3
"""
Agent Capabilities Demonstration Script
Shows the capabilities of each specialized agent
"""

import asyncio
import logging
import sys
import json
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from main import TradingAgentSystem
import yaml

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AgentDemo:
    """Demonstrates agent capabilities"""
    
    def __init__(self):
        self.system = None
        
    async def initialize_system(self):
        """Initialize the trading system"""
        logger.info("🚀 Initializing Trading Agent System for Demo...")
        
        # Load configuration
        config_path = Path("config/system_config.yaml")
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
            
        self.system = TradingAgentSystem(config)
        await self.system.initialize_components()
        await self.system.start_components()
        
        # Create default agents
        await self.system.agent_manager.create_default_agents()
        
        logger.info("✅ System initialized and ready for demo")
        
    async def demo_team_leader(self):
        """Demonstrate Team Leader Agent capabilities"""
        logger.info("\n" + "="*60)
        logger.info("🧠 TEAM LEADER AGENT DEMONSTRATION")
        logger.info("="*60)
        
        # Get team leader agent
        team_leaders = await self.system.agent_manager.get_agents_by_role(
            self.system.agent_manager.AgentRole.TEAM_LEADER
        )
        
        if not team_leaders:
            logger.error("No Team Leader agent found")
            return
            
        team_leader = team_leaders[0]
        
        # Demo 1: Team Formation
        logger.info("📋 Demo 1: Strategic Team Formation")
        team_formation_task = {
            'type': 'form_team',
            'requirements': {
                'strategy_type': 'momentum_trading',
                'risk_level': 'medium',
                'time_horizon': 'short_term'
            },
            'market_conditions': {
                'volatility': 'high',
                'trend': 'bullish',
                'liquidity': 'good'
            }
        }
        
        await team_leader.submit_task(team_formation_task)
        await asyncio.sleep(2)  # Allow processing
        
        # Demo 2: Strategic Decision Making
        logger.info("🎯 Demo 2: Strategic Decision Making")
        strategic_decision_task = {
            'type': 'strategic_decision',
            'context': {
                'market_situation': 'earnings_season',
                'portfolio_status': 'underperforming',
                'risk_budget': 'available'
            },
            'options': [
                'increase_position_sizes',
                'diversify_strategies',
                'reduce_risk_exposure',
                'maintain_current_allocation'
            ],
            'urgency': 'high'
        }
        
        await team_leader.submit_task(strategic_decision_task)
        await asyncio.sleep(3)  # Allow processing
        
        # Get team status
        status = await team_leader.get_team_status()
        logger.info(f"📊 Team Leader Status: {json.dumps(status, indent=2)}")
        
    async def demo_market_analyst(self):
        """Demonstrate Market Analyst Agent capabilities"""
        logger.info("\n" + "="*60)
        logger.info("📊 MARKET ANALYST AGENT DEMONSTRATION")
        logger.info("="*60)
        
        # Get market analyst agent
        analysts = await self.system.agent_manager.get_agents_by_role(
            self.system.agent_manager.AgentRole.MARKET_ANALYST
        )
        
        if not analysts:
            logger.error("No Market Analyst agent found")
            return
            
        analyst = analysts[0]
        
        # Demo 1: Technical Analysis
        logger.info("📈 Demo 1: Technical Analysis")
        technical_analysis_task = {
            'type': 'technical_analysis',
            'symbol': 'AAPL',
            'timeframe': '1h',
            'indicators': ['sma', 'rsi', 'macd', 'bollinger_bands']
        }
        
        await analyst.submit_task(technical_analysis_task)
        await asyncio.sleep(3)  # Allow processing
        
        # Demo 2: Pattern Recognition
        logger.info("🔍 Demo 2: Pattern Recognition")
        pattern_recognition_task = {
            'type': 'pattern_recognition',
            'symbol': 'TSLA',
            'timeframe': '4h',
            'pattern_types': ['head_and_shoulders', 'triangle', 'flag']
        }
        
        await analyst.submit_task(pattern_recognition_task)
        await asyncio.sleep(3)  # Allow processing
        
        # Demo 3: Sentiment Analysis
        logger.info("💭 Demo 3: Market Sentiment Analysis")
        sentiment_analysis_task = {
            'type': 'sentiment_analysis',
            'symbol': 'SPY',
            'sources': ['market_data', 'technical_indicators']
        }
        
        await analyst.submit_task(sentiment_analysis_task)
        await asyncio.sleep(2)  # Allow processing
        
        logger.info("✅ Market Analyst demonstrations complete")
        
    async def demo_strategy_developer(self):
        """Demonstrate Strategy Developer Agent capabilities"""
        logger.info("\n" + "="*60)
        logger.info("🔬 STRATEGY DEVELOPER AGENT DEMONSTRATION")
        logger.info("="*60)
        
        # Get strategy developer agent
        developers = await self.system.agent_manager.get_agents_by_role(
            self.system.agent_manager.AgentRole.STRATEGY_DEVELOPER
        )
        
        if not developers:
            logger.error("No Strategy Developer agent found")
            return
            
        developer = developers[0]
        
        # Demo 1: Strategy Development
        logger.info("💡 Demo 1: Creative Strategy Development")
        strategy_development_task = {
            'type': 'develop_strategy',
            'strategy_type': 'mean_reversion',
            'market_conditions': {
                'volatility': 'medium',
                'trend': 'sideways',
                'regime': 'range_bound'
            },
            'constraints': {
                'max_drawdown': 0.10,
                'min_sharpe': 1.2,
                'max_leverage': 2.0
            },
            'objectives': {
                'target_return': 0.15,
                'risk_level': 'medium'
            }
        }
        
        await developer.submit_task(strategy_development_task)
        await asyncio.sleep(4)  # Allow processing for creative thinking
        
        # Demo 2: Strategy Backtesting
        logger.info("📊 Demo 2: Strategy Backtesting")
        backtest_task = {
            'type': 'backtest_strategy',
            'strategy_id': 'demo_strategy',  # Would use actual strategy ID
            'period': '2y',
            'symbols': ['AAPL', 'GOOGL', 'MSFT', 'TSLA']
        }
        
        await developer.submit_task(backtest_task)
        await asyncio.sleep(3)  # Allow processing
        
        # Demo 3: Strategy Optimization
        logger.info("⚙️ Demo 3: Strategy Optimization")
        optimization_task = {
            'type': 'optimize_strategy',
            'strategy_id': 'demo_strategy',
            'method': 'genetic_algorithm',
            'objective': 'sharpe_ratio'
        }
        
        await developer.submit_task(optimization_task)
        await asyncio.sleep(3)  # Allow processing
        
        logger.info("✅ Strategy Developer demonstrations complete")
        
    async def demo_risk_manager(self):
        """Demonstrate Risk Manager Agent capabilities"""
        logger.info("\n" + "="*60)
        logger.info("⚖️ RISK MANAGER AGENT DEMONSTRATION")
        logger.info("="*60)
        
        # Get risk manager agent
        risk_managers = await self.system.agent_manager.get_agents_by_role(
            self.system.agent_manager.AgentRole.RISK_MANAGER
        )
        
        if not risk_managers:
            logger.error("No Risk Manager agent found")
            return
            
        risk_manager = risk_managers[0]
        
        # Demo 1: Portfolio Risk Assessment
        logger.info("🔍 Demo 1: Portfolio Risk Assessment")
        risk_assessment_task = {
            'type': 'assess_portfolio_risk',
            'portfolio': {
                'AAPL': {'position': 1000, 'value': 150000},
                'GOOGL': {'position': 500, 'value': 100000},
                'TSLA': {'position': 800, 'value': 200000},
                'SPY': {'position': 2000, 'value': 400000}
            },
            'market_data': {
                'volatilities': {'AAPL': 0.25, 'GOOGL': 0.30, 'TSLA': 0.45, 'SPY': 0.18},
                'correlations': {'AAPL_GOOGL': 0.65, 'AAPL_TSLA': 0.55, 'GOOGL_TSLA': 0.60}
            },
            'type': 'comprehensive'
        }
        
        await risk_manager.submit_task(risk_assessment_task)
        await asyncio.sleep(3)  # Allow processing
        
        # Demo 2: Position Sizing
        logger.info("📏 Demo 2: Optimal Position Sizing")
        position_sizing_task = {
            'type': 'calculate_position_size',
            'symbol': 'NVDA',
            'strategy_risk': 0.02,
            'portfolio_value': 1000000,
            'volatility': 0.35,
            'correlation': 0.45
        }
        
        await risk_manager.submit_task(position_sizing_task)
        await asyncio.sleep(2)  # Allow processing
        
        # Demo 3: Stress Testing
        logger.info("💥 Demo 3: Portfolio Stress Testing")
        stress_test_task = {
            'type': 'stress_test',
            'portfolio': {
                'AAPL': {'position': 1000, 'value': 150000},
                'GOOGL': {'position': 500, 'value': 100000},
                'TSLA': {'position': 800, 'value': 200000}
            },
            'scenarios': ['market_crash', 'volatility_spike', 'correlation_breakdown']
        }
        
        await risk_manager.submit_task(stress_test_task)
        await asyncio.sleep(3)  # Allow processing
        
        logger.info("✅ Risk Manager demonstrations complete")
        
    async def demo_execution_specialist(self):
        """Demonstrate Execution Specialist Agent capabilities"""
        logger.info("\n" + "="*60)
        logger.info("⚡ EXECUTION SPECIALIST AGENT DEMONSTRATION")
        logger.info("="*60)
        
        # Get execution specialist agent
        specialists = await self.system.agent_manager.get_agents_by_role(
            self.system.agent_manager.AgentRole.EXECUTION_SPECIALIST
        )
        
        if not specialists:
            logger.error("No Execution Specialist agent found")
            return
            
        specialist = specialists[0]
        
        # Demo 1: Order Execution Optimization
        logger.info("🎯 Demo 1: Order Execution Optimization")
        execution_task = {
            'type': 'execute_order',
            'order': {
                'symbol': 'AAPL',
                'side': 'buy',
                'quantity': 5000,
                'type': 'market'
            },
            'execution_params': {
                'max_participation_rate': 0.10,
                'time_horizon': 3600  # 1 hour
            },
            'urgency': 'normal'
        }
        
        await specialist.submit_task(execution_task)
        await asyncio.sleep(3)  # Allow processing
        
        # Demo 2: Market Impact Analysis
        logger.info("📊 Demo 2: Market Impact Analysis")
        impact_analysis_task = {
            'type': 'analyze_market_impact',
            'symbol': 'TSLA',
            'quantity': 10000,
            'side': 'sell',
            'market_data': {
                'bid_ask_spread': 0.05,
                'market_depth': 50000,
                'average_volume': 25000000
            }
        }
        
        await specialist.submit_task(impact_analysis_task)
        await asyncio.sleep(2)  # Allow processing
        
        # Demo 3: Algorithm Selection
        logger.info("🤖 Demo 3: Execution Algorithm Selection")
        algorithm_selection_task = {
            'type': 'select_algorithm',
            'order_characteristics': {
                'size': 'large',
                'urgency': 'medium',
                'symbol_liquidity': 'high'
            },
            'market_conditions': {
                'volatility': 'medium',
                'volume': 'above_average',
                'spread': 'tight'
            },
            'objectives': {
                'minimize_cost': 0.7,
                'minimize_risk': 0.3
            }
        }
        
        await specialist.submit_task(algorithm_selection_task)
        await asyncio.sleep(2)  # Allow processing
        
        logger.info("✅ Execution Specialist demonstrations complete")
        
    async def demo_performance_evaluator(self):
        """Demonstrate Performance Evaluator Agent capabilities"""
        logger.info("\n" + "="*60)
        logger.info("📈 PERFORMANCE EVALUATOR AGENT DEMONSTRATION")
        logger.info("="*60)
        
        # Get performance evaluator agent
        evaluators = await self.system.agent_manager.get_agents_by_role(
            self.system.agent_manager.AgentRole.PERFORMANCE_EVALUATOR
        )
        
        if not evaluators:
            logger.error("No Performance Evaluator agent found")
            return
            
        evaluator = evaluators[0]
        
        # Demo 1: Performance Evaluation
        logger.info("📊 Demo 1: Comprehensive Performance Evaluation")
        performance_evaluation_task = {
            'type': 'evaluate_performance',
            'entity_type': 'strategy',
            'entity_id': 'momentum_strategy_v1',
            'performance_data': {
                'returns': [0.02, -0.01, 0.03, 0.01, -0.02, 0.04, 0.02],
                'benchmark_returns': [0.015, -0.005, 0.025, 0.008, -0.015, 0.035, 0.018],
                'volatility': 0.15,
                'max_drawdown': 0.08,
                'sharpe_ratio': 1.25
            },
            'period': '6M'
        }
        
        await evaluator.submit_task(performance_evaluation_task)
        await asyncio.sleep(3)  # Allow processing
        
        # Demo 2: Optimization Recommendations
        logger.info("💡 Demo 2: Optimization Recommendations")
        optimization_recommendations_task = {
            'type': 'optimization_recommendations',
            'target_entity': {
                'type': 'portfolio',
                'id': 'main_portfolio'
            },
            'performance_issues': [
                'high_volatility',
                'poor_risk_adjusted_returns',
                'concentration_risk'
            ],
            'constraints': {
                'max_leverage': 2.0,
                'min_liquidity': 0.10
            },
            'objectives': {
                'target_sharpe': 1.5,
                'max_drawdown': 0.12
            }
        }
        
        await evaluator.submit_task(optimization_recommendations_task)
        await asyncio.sleep(3)  # Allow processing
        
        logger.info("✅ Performance Evaluator demonstrations complete")
        
    async def run_full_demo(self):
        """Run complete agent demonstration"""
        try:
            await self.initialize_system()
            
            logger.info("\n🎭 STARTING COMPREHENSIVE AGENT DEMONSTRATIONS")
            logger.info("This will showcase the capabilities of all 6 specialized agents")
            
            # Run all demonstrations
            await self.demo_team_leader()
            await self.demo_market_analyst()
            await self.demo_strategy_developer()
            await self.demo_risk_manager()
            await self.demo_execution_specialist()
            await self.demo_performance_evaluator()
            
            # System statistics
            logger.info("\n" + "="*60)
            logger.info("📊 SYSTEM PERFORMANCE SUMMARY")
            logger.info("="*60)
            
            system_stats = await self.system.agent_manager.get_system_stats()
            logger.info(f"System Statistics: {json.dumps(system_stats, indent=2)}")
            
            logger.info("\n🎉 ALL AGENT DEMONSTRATIONS COMPLETED SUCCESSFULLY!")
            logger.info("The system is ready for live trading operations.")
            
        except Exception as e:
            logger.error(f"Demo failed: {e}")
            raise
        finally:
            if self.system:
                await self.system.stop_components()


async def main():
    """Main demo function"""
    demo = AgentDemo()
    await demo.run_full_demo()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("\nDemo cancelled by user")
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        sys.exit(1)

# Deployment Guide

This guide covers deploying the Advanced Ollama Trading Agent System in various environments.

## 🚀 Deployment Options

### 1. Local Development
### 2. Docker Deployment
### 3. Cloud Deployment (AWS/GCP/Azure)
### 4. Kubernetes Deployment
### 5. Production Deployment

## 📋 Prerequisites

### System Requirements

**Minimum Requirements:**
- CPU: 4 cores
- RAM: 8GB
- Storage: 50GB SSD
- Network: 100 Mbps

**Recommended Requirements:**
- CPU: 8+ cores
- RAM: 16GB+
- Storage: 100GB+ NVMe SSD
- Network: 1 Gbps

### Software Dependencies

- Python 3.9+
- PostgreSQL 12+
- Redis 6+
- Ollama
- Docker (for containerized deployment)
- Kubernetes (for K8s deployment)

## 🐳 Docker Deployment

### Quick Start with Docker Compose

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd advanced-ollama-trading-agents
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Deploy with <PERSON>er Compose**
   ```bash
   docker-compose up -d
   ```

### Docker Compose Configuration

```yaml
version: '3.8'

services:
  # Trading System
  trading-system:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DB_HOST=postgres
      - REDIS_HOST=redis
      - OLLAMA_HOST=ollama
    depends_on:
      - postgres
      - redis
      - ollama
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    restart: unless-stopped

  # PostgreSQL Database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: trading_agents
      POSTGRES_USER: trading_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Ollama AI Models
  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    restart: unless-stopped

  # TimescaleDB (Optional)
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    environment:
      POSTGRES_DB: timeseries_data
      POSTGRES_USER: timescale_user
      POSTGRES_PASSWORD: timescale_password
    volumes:
      - timescale_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - trading-system
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  ollama_data:
  timescale_data:
```

### Environment Configuration (.env)

```bash
# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=trading_agents
DB_USER=trading_user
DB_PASSWORD=secure_password

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# Ollama Configuration
OLLAMA_HOST=ollama
OLLAMA_PORT=11434

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_SECRET_KEY=your-secret-key-here

# Security
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRY=3600

# Trading Configuration
PAPER_TRADING=true
MAX_POSITION_SIZE=0.1
MAX_DAILY_LOSS=0.05

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
```

## ☁️ Cloud Deployment

### AWS Deployment

#### Using AWS ECS

1. **Create ECS Cluster**
   ```bash
   aws ecs create-cluster --cluster-name trading-agents-cluster
   ```

2. **Build and Push Docker Image**
   ```bash
   # Build image
   docker build -t trading-agents .
   
   # Tag for ECR
   docker tag trading-agents:latest 123456789012.dkr.ecr.us-west-2.amazonaws.com/trading-agents:latest
   
   # Push to ECR
   docker push 123456789012.dkr.ecr.us-west-2.amazonaws.com/trading-agents:latest
   ```

3. **Create Task Definition**
   ```json
   {
     "family": "trading-agents-task",
     "networkMode": "awsvpc",
     "requiresCompatibilities": ["FARGATE"],
     "cpu": "2048",
     "memory": "4096",
     "executionRoleArn": "arn:aws:iam::123456789012:role/ecsTaskExecutionRole",
     "containerDefinitions": [
       {
         "name": "trading-system",
         "image": "123456789012.dkr.ecr.us-west-2.amazonaws.com/trading-agents:latest",
         "portMappings": [
           {
             "containerPort": 8000,
             "protocol": "tcp"
           }
         ],
         "environment": [
           {
             "name": "DB_HOST",
             "value": "your-rds-endpoint"
           }
         ],
         "logConfiguration": {
           "logDriver": "awslogs",
           "options": {
             "awslogs-group": "/ecs/trading-agents",
             "awslogs-region": "us-west-2",
             "awslogs-stream-prefix": "ecs"
           }
         }
       }
     ]
   }
   ```

4. **Create Service**
   ```bash
   aws ecs create-service \
     --cluster trading-agents-cluster \
     --service-name trading-agents-service \
     --task-definition trading-agents-task \
     --desired-count 2 \
     --launch-type FARGATE \
     --network-configuration "awsvpcConfiguration={subnets=[subnet-12345,subnet-67890],securityGroups=[sg-abcdef],assignPublicIp=ENABLED}"
   ```

#### Infrastructure as Code (Terraform)

```hcl
# main.tf
provider "aws" {
  region = var.aws_region
}

# VPC and Networking
module "vpc" {
  source = "terraform-aws-modules/vpc/aws"
  
  name = "trading-agents-vpc"
  cidr = "10.0.0.0/16"
  
  azs             = ["${var.aws_region}a", "${var.aws_region}b"]
  private_subnets = ["********/24", "********/24"]
  public_subnets  = ["**********/24", "**********/24"]
  
  enable_nat_gateway = true
  enable_vpn_gateway = true
}

# RDS Database
resource "aws_db_instance" "postgres" {
  identifier = "trading-agents-db"
  
  engine         = "postgres"
  engine_version = "15.4"
  instance_class = "db.t3.medium"
  
  allocated_storage     = 100
  max_allocated_storage = 1000
  storage_type         = "gp3"
  storage_encrypted    = true
  
  db_name  = "trading_agents"
  username = "trading_user"
  password = var.db_password
  
  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.main.name
  
  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  skip_final_snapshot = false
  final_snapshot_identifier = "trading-agents-final-snapshot"
  
  tags = {
    Name = "trading-agents-db"
  }
}

# ElastiCache Redis
resource "aws_elasticache_subnet_group" "main" {
  name       = "trading-agents-cache-subnet"
  subnet_ids = module.vpc.private_subnets
}

resource "aws_elasticache_cluster" "redis" {
  cluster_id           = "trading-agents-redis"
  engine               = "redis"
  node_type            = "cache.t3.micro"
  num_cache_nodes      = 1
  parameter_group_name = "default.redis7"
  port                 = 6379
  subnet_group_name    = aws_elasticache_subnet_group.main.name
  security_group_ids   = [aws_security_group.redis.id]
}

# ECS Cluster
resource "aws_ecs_cluster" "main" {
  name = "trading-agents-cluster"
  
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

# Application Load Balancer
resource "aws_lb" "main" {
  name               = "trading-agents-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets            = module.vpc.public_subnets
  
  enable_deletion_protection = false
}
```

### Google Cloud Platform (GCP)

#### Using Google Kubernetes Engine (GKE)

1. **Create GKE Cluster**
   ```bash
   gcloud container clusters create trading-agents-cluster \
     --zone=us-central1-a \
     --num-nodes=3 \
     --machine-type=e2-standard-4 \
     --enable-autoscaling \
     --min-nodes=1 \
     --max-nodes=10
   ```

2. **Deploy Application**
   ```bash
   kubectl apply -f k8s/
   ```

### Microsoft Azure

#### Using Azure Container Instances (ACI)

```bash
# Create resource group
az group create --name trading-agents-rg --location eastus

# Create container group
az container create \
  --resource-group trading-agents-rg \
  --name trading-agents \
  --image your-registry/trading-agents:latest \
  --cpu 2 \
  --memory 4 \
  --ports 8000 \
  --environment-variables \
    DB_HOST=your-postgres-server \
    REDIS_HOST=your-redis-cache
```

## ⚙️ Kubernetes Deployment

### Kubernetes Manifests

#### Namespace
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: trading-agents
```

#### ConfigMap
```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: trading-agents-config
  namespace: trading-agents
data:
  config.yaml: |
    database:
      postgres:
        host: postgres-service
        port: 5432
        database: trading_agents
    api:
      host: 0.0.0.0
      port: 8000
    agents:
      max_agents: 20
```

#### Secret
```yaml
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: trading-agents-secret
  namespace: trading-agents
type: Opaque
data:
  db-password: <base64-encoded-password>
  jwt-secret: <base64-encoded-jwt-secret>
```

#### Deployment
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trading-agents
  namespace: trading-agents
spec:
  replicas: 3
  selector:
    matchLabels:
      app: trading-agents
  template:
    metadata:
      labels:
        app: trading-agents
    spec:
      containers:
      - name: trading-system
        image: trading-agents:latest
        ports:
        - containerPort: 8000
        env:
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: trading-agents-secret
              key: db-password
        volumeMounts:
        - name: config
          mountPath: /app/config
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config
        configMap:
          name: trading-agents-config
```

#### Service
```yaml
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: trading-agents-service
  namespace: trading-agents
spec:
  selector:
    app: trading-agents
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
```

#### Ingress
```yaml
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: trading-agents-ingress
  namespace: trading-agents
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - trading.yourdomain.com
    secretName: trading-agents-tls
  rules:
  - host: trading.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: trading-agents-service
            port:
              number: 80
```

### Helm Chart

```yaml
# helm/Chart.yaml
apiVersion: v2
name: trading-agents
description: Advanced Ollama Trading Agent System
type: application
version: 1.0.0
appVersion: "1.0.0"
```

```yaml
# helm/values.yaml
replicaCount: 3

image:
  repository: trading-agents
  pullPolicy: IfNotPresent
  tag: "latest"

service:
  type: LoadBalancer
  port: 80

ingress:
  enabled: true
  className: "nginx"
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - host: trading.yourdomain.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: trading-agents-tls
      hosts:
        - trading.yourdomain.com

resources:
  limits:
    cpu: 2000m
    memory: 4Gi
  requests:
    cpu: 1000m
    memory: 2Gi

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80

postgresql:
  enabled: true
  auth:
    postgresPassword: "secure-password"
    database: "trading_agents"

redis:
  enabled: true
  auth:
    enabled: false
```

## 🔧 Production Configuration

### Security Hardening

1. **SSL/TLS Configuration**
   ```nginx
   # nginx/nginx.conf
   server {
       listen 443 ssl http2;
       server_name trading.yourdomain.com;
       
       ssl_certificate /etc/nginx/ssl/cert.pem;
       ssl_certificate_key /etc/nginx/ssl/key.pem;
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
       
       location / {
           proxy_pass http://trading-system:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }
   ```

2. **Database Security**
   ```sql
   -- Create dedicated user
   CREATE USER trading_app WITH PASSWORD 'secure_password';
   
   -- Grant minimal permissions
   GRANT CONNECT ON DATABASE trading_agents TO trading_app;
   GRANT USAGE ON SCHEMA public TO trading_app;
   GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO trading_app;
   ```

3. **Environment Variables**
   ```bash
   # Use secrets management
   export DB_PASSWORD=$(aws secretsmanager get-secret-value --secret-id prod/db/password --query SecretString --output text)
   export JWT_SECRET=$(aws secretsmanager get-secret-value --secret-id prod/jwt/secret --query SecretString --output text)
   ```

### Monitoring and Logging

1. **Prometheus Monitoring**
   ```yaml
   # monitoring/prometheus.yaml
   global:
     scrape_interval: 15s
   
   scrape_configs:
     - job_name: 'trading-agents'
       static_configs:
         - targets: ['trading-agents:8000']
   ```

2. **Grafana Dashboard**
   ```json
   {
     "dashboard": {
       "title": "Trading Agents Dashboard",
       "panels": [
         {
           "title": "System Health",
           "type": "stat",
           "targets": [
             {
               "expr": "up{job=\"trading-agents\"}"
             }
           ]
         }
       ]
     }
   }
   ```

3. **Log Aggregation**
   ```yaml
   # logging/fluentd.yaml
   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: fluentd-config
   data:
     fluent.conf: |
       <source>
         @type tail
         path /var/log/containers/*trading-agents*.log
         pos_file /var/log/fluentd-containers.log.pos
         tag kubernetes.*
         format json
       </source>
       
       <match kubernetes.**>
         @type elasticsearch
         host elasticsearch
         port 9200
         index_name trading-agents
       </match>
   ```

### Backup and Disaster Recovery

1. **Database Backups**
   ```bash
   #!/bin/bash
   # backup.sh
   
   BACKUP_DIR="/backups"
   DATE=$(date +%Y%m%d_%H%M%S)
   
   # PostgreSQL backup
   pg_dump -h $DB_HOST -U $DB_USER -d trading_agents > $BACKUP_DIR/postgres_$DATE.sql
   
   # Upload to S3
   aws s3 cp $BACKUP_DIR/postgres_$DATE.sql s3://trading-agents-backups/
   
   # Cleanup old backups
   find $BACKUP_DIR -name "postgres_*.sql" -mtime +7 -delete
   ```

2. **Configuration Backup**
   ```bash
   # Backup configuration
   kubectl get configmap trading-agents-config -o yaml > config-backup.yaml
   kubectl get secret trading-agents-secret -o yaml > secret-backup.yaml
   ```

## 🚀 Deployment Checklist

### Pre-Deployment
- [ ] Environment configured
- [ ] Secrets and credentials secured
- [ ] Database initialized
- [ ] SSL certificates obtained
- [ ] Monitoring setup
- [ ] Backup strategy implemented

### Deployment
- [ ] Application deployed
- [ ] Health checks passing
- [ ] Load balancer configured
- [ ] DNS configured
- [ ] SSL/TLS working

### Post-Deployment
- [ ] Monitoring alerts configured
- [ ] Log aggregation working
- [ ] Backup jobs scheduled
- [ ] Performance testing completed
- [ ] Security scan completed
- [ ] Documentation updated

## 🔍 Troubleshooting

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Check connectivity
   telnet $DB_HOST $DB_PORT
   
   # Test credentials
   psql -h $DB_HOST -U $DB_USER -d trading_agents
   ```

2. **Ollama Model Issues**
   ```bash
   # Check Ollama status
   curl http://ollama:11434/api/tags
   
   # Pull missing models
   docker exec ollama ollama pull llama2
   ```

3. **Memory Issues**
   ```bash
   # Check memory usage
   kubectl top pods -n trading-agents
   
   # Increase memory limits
   kubectl patch deployment trading-agents -p '{"spec":{"template":{"spec":{"containers":[{"name":"trading-system","resources":{"limits":{"memory":"8Gi"}}}]}}}}'
   ```

### Log Analysis

```bash
# View application logs
kubectl logs -f deployment/trading-agents -n trading-agents

# Search for errors
kubectl logs deployment/trading-agents -n trading-agents | grep ERROR

# View specific container logs
docker logs trading-agents_trading-system_1
```

## 📞 Support

For deployment support:
- Check the [troubleshooting guide](troubleshooting.md)
- Review [monitoring documentation](monitoring.md)
- Contact support team
- Create GitHub issue

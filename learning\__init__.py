"""
Learning and Adaptation Package

This package provides comprehensive learning and adaptation capabilities for the
Advanced Ollama Trading Agent System. It includes:

- Strategy repository and management
- Performance-based learning and adaptation
- Model fine-tuning and optimization
- Continuous improvement mechanisms
- Knowledge management and transfer
- Adaptive parameter optimization
"""

from .learning_manager import LearningManager
from .strategy_repository import StrategyRepository
from .performance_learner import PerformanceLearner
from .model_tuner import ModelTuner
from .knowledge_manager import KnowledgeManager
from .adaptation_engine import AdaptationEngine

__all__ = [
    'LearningManager',
    'StrategyRepository',
    'PerformanceLearner',
    'ModelTuner',
    'KnowledgeManager',
    'AdaptationEngine'
]

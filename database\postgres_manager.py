"""
PostgreSQL Manager - Handles PostgreSQL operations and schema management
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import asyncpg
from datetime import datetime, timezone

logger = logging.getLogger(__name__)


@dataclass
class QueryResult:
    """Query result wrapper"""
    success: bool
    data: Optional[Union[List[Dict[str, Any]], Dict[str, Any]]]
    affected_rows: int
    execution_time_ms: float
    error_message: Optional[str] = None


class PostgreSQLManager:
    """
    Manages PostgreSQL database operations.
    
    Features:
    - Connection pooling and management
    - Schema creation and migration
    - CRUD operations for trading data
    - Transaction management
    - Query optimization and monitoring
    - Data archiving and cleanup
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Connection configuration
        self.host = config.get('host', 'localhost')
        self.port = config.get('port', 5432)
        self.database = config.get('database', 'trading_system')
        self.username = config.get('username', 'trading_user')
        self.password = config.get('password', 'trading_password')
        
        # Pool configuration
        self.min_pool_size = config.get('min_pool_size', 5)
        self.max_pool_size = config.get('max_pool_size', 20)
        self.pool_timeout = config.get('pool_timeout', 10)
        
        # Connection pool
        self.pool: Optional[asyncpg.Pool] = None
        
        # Query statistics
        self.query_stats: Dict[str, Any] = {
            'total_queries': 0,
            'successful_queries': 0,
            'failed_queries': 0,
            'average_execution_time': 0.0,
            'last_query_time': None
        }
        
        # State
        self.initialized = False
        self.running = False
    
    async def initialize(self) -> bool:
        """Initialize PostgreSQL manager"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing PostgreSQL Manager...")
            
            # Create connection pool
            await self._create_connection_pool()
            
            # Create schemas
            await self._create_schemas()
            
            self.initialized = True
            logger.info("✓ PostgreSQL Manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL Manager: {e}")
            return False
    
    async def start(self) -> bool:
        """Start PostgreSQL manager"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        self.running = True
        logger.info("✓ PostgreSQL Manager started")
        return True
    
    async def stop(self) -> bool:
        """Stop PostgreSQL manager"""
        if self.pool:
            await self.pool.close()
        
        self.running = False
        logger.info("✓ PostgreSQL Manager stopped")
        return True
    
    async def health_check(self) -> bool:
        """Check PostgreSQL health"""
        try:
            if not self.pool:
                return False
            
            async with self.pool.acquire() as conn:
                result = await conn.fetchval("SELECT 1")
                return result == 1
                
        except Exception as e:
            logger.error(f"PostgreSQL health check failed: {e}")
            return False
    
    # Strategy Operations
    
    async def store_strategy(self, strategy_id: str, data: Dict[str, Any]) -> bool:
        """Store strategy data"""
        try:
            query = """
                INSERT INTO strategies (id, name, type, parameters, performance_metrics, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                ON CONFLICT (id) DO UPDATE SET
                    name = EXCLUDED.name,
                    type = EXCLUDED.type,
                    parameters = EXCLUDED.parameters,
                    performance_metrics = EXCLUDED.performance_metrics,
                    updated_at = EXCLUDED.updated_at
            """
            
            params = [
                strategy_id,
                data.get('name', ''),
                data.get('type', ''),
                json.dumps(data.get('parameters', {})),
                json.dumps(data.get('performance_metrics', {})),
                datetime.now(timezone.utc),
                datetime.now(timezone.utc)
            ]
            
            result = await self._execute_query(query, params)
            return result.success
            
        except Exception as e:
            logger.error(f"Error storing strategy {strategy_id}: {e}")
            return False
    
    async def get_strategy(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """Get strategy data"""
        try:
            query = "SELECT * FROM strategies WHERE id = $1"
            result = await self._execute_query(query, [strategy_id])
            
            if result.success and result.data:
                strategy_data = result.data[0] if isinstance(result.data, list) else result.data
                
                # Parse JSON fields
                if 'parameters' in strategy_data:
                    strategy_data['parameters'] = json.loads(strategy_data['parameters'])
                if 'performance_metrics' in strategy_data:
                    strategy_data['performance_metrics'] = json.loads(strategy_data['performance_metrics'])
                
                return strategy_data
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting strategy {strategy_id}: {e}")
            return None
    
    async def list_strategies(self, strategy_type: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """List strategies"""
        try:
            if strategy_type:
                query = "SELECT * FROM strategies WHERE type = $1 ORDER BY updated_at DESC LIMIT $2"
                params = [strategy_type, limit]
            else:
                query = "SELECT * FROM strategies ORDER BY updated_at DESC LIMIT $1"
                params = [limit]
            
            result = await self._execute_query(query, params)
            
            if result.success and result.data:
                strategies = result.data if isinstance(result.data, list) else [result.data]
                
                # Parse JSON fields
                for strategy in strategies:
                    if 'parameters' in strategy:
                        strategy['parameters'] = json.loads(strategy['parameters'])
                    if 'performance_metrics' in strategy:
                        strategy['performance_metrics'] = json.loads(strategy['performance_metrics'])
                
                return strategies
            
            return []
            
        except Exception as e:
            logger.error(f"Error listing strategies: {e}")
            return []
    
    # Portfolio Operations
    
    async def store_portfolio_state(self, portfolio_id: str, state: Dict[str, Any]) -> bool:
        """Store portfolio state"""
        try:
            query = """
                INSERT INTO portfolio_states (id, portfolio_data, timestamp, created_at)
                VALUES ($1, $2, $3, $4)
            """
            
            params = [
                portfolio_id,
                json.dumps(state),
                datetime.now(timezone.utc),
                datetime.now(timezone.utc)
            ]
            
            result = await self._execute_query(query, params)
            return result.success
            
        except Exception as e:
            logger.error(f"Error storing portfolio state {portfolio_id}: {e}")
            return False
    
    async def get_portfolio_history(self, portfolio_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get portfolio history"""
        try:
            query = """
                SELECT * FROM portfolio_states 
                WHERE id = $1 
                ORDER BY timestamp DESC 
                LIMIT $2
            """
            
            result = await self._execute_query(query, [portfolio_id, limit])
            
            if result.success and result.data:
                history = result.data if isinstance(result.data, list) else [result.data]
                
                # Parse JSON fields
                for entry in history:
                    if 'portfolio_data' in entry:
                        entry['portfolio_data'] = json.loads(entry['portfolio_data'])
                
                return history
            
            return []
            
        except Exception as e:
            logger.error(f"Error getting portfolio history {portfolio_id}: {e}")
            return []
    
    # Trade Operations
    
    async def store_trade(self, trade_data: Dict[str, Any]) -> bool:
        """Store trade data"""
        try:
            query = """
                INSERT INTO trades (id, strategy_id, symbol, side, quantity, price, 
                                  timestamp, status, metadata, created_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            """
            
            params = [
                trade_data.get('id'),
                trade_data.get('strategy_id'),
                trade_data.get('symbol'),
                trade_data.get('side'),
                trade_data.get('quantity'),
                trade_data.get('price'),
                datetime.fromtimestamp(trade_data.get('timestamp', time.time()), timezone.utc),
                trade_data.get('status', 'pending'),
                json.dumps(trade_data.get('metadata', {})),
                datetime.now(timezone.utc)
            ]
            
            result = await self._execute_query(query, params)
            return result.success
            
        except Exception as e:
            logger.error(f"Error storing trade: {e}")
            return False
    
    async def get_trades(self, strategy_id: str = None, symbol: str = None, 
                        limit: int = 100) -> List[Dict[str, Any]]:
        """Get trades"""
        try:
            conditions = []
            params = []
            param_count = 0
            
            if strategy_id:
                param_count += 1
                conditions.append(f"strategy_id = ${param_count}")
                params.append(strategy_id)
            
            if symbol:
                param_count += 1
                conditions.append(f"symbol = ${param_count}")
                params.append(symbol)
            
            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
            
            param_count += 1
            query = f"""
                SELECT * FROM trades 
                {where_clause}
                ORDER BY timestamp DESC 
                LIMIT ${param_count}
            """
            params.append(limit)
            
            result = await self._execute_query(query, params)
            
            if result.success and result.data:
                trades = result.data if isinstance(result.data, list) else [result.data]
                
                # Parse JSON fields
                for trade in trades:
                    if 'metadata' in trade:
                        trade['metadata'] = json.loads(trade['metadata'])
                
                return trades
            
            return []
            
        except Exception as e:
            logger.error(f"Error getting trades: {e}")
            return []
    
    # Statistics and Monitoring
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get PostgreSQL statistics"""
        try:
            stats = self.query_stats.copy()
            
            # Get database size
            size_query = "SELECT pg_size_pretty(pg_database_size(current_database())) as size"
            size_result = await self._execute_query(size_query)
            
            if size_result.success and size_result.data:
                stats['database_size'] = size_result.data[0]['size']
            
            # Get table counts
            count_queries = [
                ("strategies_count", "SELECT COUNT(*) as count FROM strategies"),
                ("trades_count", "SELECT COUNT(*) as count FROM trades"),
                ("portfolio_states_count", "SELECT COUNT(*) as count FROM portfolio_states")
            ]
            
            for stat_name, query in count_queries:
                result = await self._execute_query(query)
                if result.success and result.data:
                    stats[stat_name] = result.data[0]['count']
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting PostgreSQL stats: {e}")
            return self.query_stats.copy()
    
    # Private methods
    
    async def _create_connection_pool(self):
        """Create PostgreSQL connection pool"""
        try:
            self.pool = await asyncpg.create_pool(
                host=self.host,
                port=self.port,
                database=self.database,
                user=self.username,
                password=self.password,
                min_size=self.min_pool_size,
                max_size=self.max_pool_size,
                command_timeout=self.pool_timeout
            )
            
            logger.info(f"✓ PostgreSQL connection pool created ({self.min_pool_size}-{self.max_pool_size} connections)")
            
        except Exception as e:
            logger.error(f"Failed to create PostgreSQL connection pool: {e}")
            raise
    
    async def _create_schemas(self):
        """Create database schemas"""
        try:
            schemas = [
                # Strategies table
                """
                CREATE TABLE IF NOT EXISTS strategies (
                    id VARCHAR(255) PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    type VARCHAR(100) NOT NULL,
                    parameters JSONB NOT NULL,
                    performance_metrics JSONB,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
                """,
                
                # Portfolio states table
                """
                CREATE TABLE IF NOT EXISTS portfolio_states (
                    id VARCHAR(255) NOT NULL,
                    portfolio_data JSONB NOT NULL,
                    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (id, timestamp)
                )
                """,
                
                # Trades table
                """
                CREATE TABLE IF NOT EXISTS trades (
                    id VARCHAR(255) PRIMARY KEY,
                    strategy_id VARCHAR(255),
                    symbol VARCHAR(50) NOT NULL,
                    side VARCHAR(10) NOT NULL,
                    quantity DECIMAL(20, 8) NOT NULL,
                    price DECIMAL(20, 8) NOT NULL,
                    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
                    status VARCHAR(50) NOT NULL,
                    metadata JSONB,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
                """,
                
                # Risk assessments table
                """
                CREATE TABLE IF NOT EXISTS risk_assessments (
                    id SERIAL PRIMARY KEY,
                    portfolio_id VARCHAR(255) NOT NULL,
                    assessment_data JSONB NOT NULL,
                    risk_score DECIMAL(5, 4),
                    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
                """,
                
                # Learning insights table
                """
                CREATE TABLE IF NOT EXISTS learning_insights (
                    id SERIAL PRIMARY KEY,
                    component VARCHAR(100) NOT NULL,
                    insight_type VARCHAR(100) NOT NULL,
                    insight_data JSONB NOT NULL,
                    confidence DECIMAL(3, 2),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
                """
            ]
            
            # Create indexes
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_strategies_type ON strategies(type)",
                "CREATE INDEX IF NOT EXISTS idx_strategies_updated_at ON strategies(updated_at)",
                "CREATE INDEX IF NOT EXISTS idx_portfolio_states_timestamp ON portfolio_states(timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_trades_strategy_id ON trades(strategy_id)",
                "CREATE INDEX IF NOT EXISTS idx_trades_symbol ON trades(symbol)",
                "CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_risk_assessments_portfolio_id ON risk_assessments(portfolio_id)",
                "CREATE INDEX IF NOT EXISTS idx_risk_assessments_timestamp ON risk_assessments(timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_learning_insights_component ON learning_insights(component)",
                "CREATE INDEX IF NOT EXISTS idx_learning_insights_type ON learning_insights(insight_type)"
            ]
            
            async with self.pool.acquire() as conn:
                # Create tables
                for schema in schemas:
                    await conn.execute(schema)
                
                # Create indexes
                for index in indexes:
                    await conn.execute(index)
            
            logger.info("✓ PostgreSQL schemas created")
            
        except Exception as e:
            logger.error(f"Error creating PostgreSQL schemas: {e}")
            raise
    
    async def _execute_query(self, query: str, params: List[Any] = None) -> QueryResult:
        """Execute a query with monitoring"""
        start_time = time.time()
        
        try:
            async with self.pool.acquire() as conn:
                if params:
                    if query.strip().upper().startswith('SELECT'):
                        result = await conn.fetch(query, *params)
                        data = [dict(row) for row in result]
                        affected_rows = len(data)
                    else:
                        result = await conn.execute(query, *params)
                        data = None
                        affected_rows = int(result.split()[-1]) if result else 0
                else:
                    if query.strip().upper().startswith('SELECT'):
                        result = await conn.fetch(query)
                        data = [dict(row) for row in result]
                        affected_rows = len(data)
                    else:
                        result = await conn.execute(query)
                        data = None
                        affected_rows = int(result.split()[-1]) if result else 0
            
            execution_time = (time.time() - start_time) * 1000
            
            # Update statistics
            self._update_query_stats(True, execution_time)
            
            return QueryResult(
                success=True,
                data=data,
                affected_rows=affected_rows,
                execution_time_ms=execution_time
            )
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            self._update_query_stats(False, execution_time)
            
            return QueryResult(
                success=False,
                data=None,
                affected_rows=0,
                execution_time_ms=execution_time,
                error_message=str(e)
            )
    
    def _update_query_stats(self, success: bool, execution_time: float):
        """Update query statistics"""
        self.query_stats['total_queries'] += 1
        
        if success:
            self.query_stats['successful_queries'] += 1
        else:
            self.query_stats['failed_queries'] += 1
        
        # Update average execution time
        total_queries = self.query_stats['total_queries']
        current_avg = self.query_stats['average_execution_time']
        self.query_stats['average_execution_time'] = (
            (current_avg * (total_queries - 1) + execution_time) / total_queries
        )
        
        self.query_stats['last_query_time'] = time.time()

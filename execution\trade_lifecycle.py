"""
Trade Lifecycle Manager - Manages the complete lifecycle of trades
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

from .order_types import Order, OrderStatus

logger = logging.getLogger(__name__)


class TradeState(Enum):
    """States in the trade lifecycle"""
    CREATED = "created"
    VALIDATED = "validated"
    SUBMITTED = "submitted"
    ACKNOWLEDGED = "acknowledged"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"
    SETTLED = "settled"


@dataclass
class TradeEvent:
    """Event in the trade lifecycle"""
    trade_id: str
    event_type: str
    timestamp: float
    data: Dict[str, Any]
    source: str


@dataclass
class TradeRecord:
    """Complete trade record"""
    trade_id: str
    order: Order
    state: TradeState
    events: List[TradeEvent]
    created_at: float
    updated_at: float
    metadata: Dict[str, Any]


class TradeLifecycleManager:
    """
    Manages the complete lifecycle of trades from creation to settlement.
    Tracks all events and state transitions.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.lifecycle_config = config.get('trade_lifecycle', {})
        
        # Trade tracking
        self.active_trades: Dict[str, TradeRecord] = {}
        self.completed_trades: Dict[str, TradeRecord] = {}
        self.trade_events: List[TradeEvent] = []
        
        # State machine
        self.valid_transitions = {
            TradeState.CREATED: [TradeState.VALIDATED, TradeState.SUBMITTED, TradeState.REJECTED],  # Allow direct submission
            TradeState.VALIDATED: [TradeState.SUBMITTED, TradeState.CANCELLED],
            TradeState.SUBMITTED: [TradeState.ACKNOWLEDGED, TradeState.REJECTED, TradeState.CANCELLED],
            TradeState.ACKNOWLEDGED: [TradeState.PARTIALLY_FILLED, TradeState.FILLED, TradeState.CANCELLED, TradeState.EXPIRED],
            TradeState.PARTIALLY_FILLED: [TradeState.FILLED, TradeState.CANCELLED, TradeState.EXPIRED],
            TradeState.FILLED: [TradeState.SETTLED],
            TradeState.CANCELLED: [],
            TradeState.REJECTED: [],
            TradeState.EXPIRED: [],
            TradeState.SETTLED: []
        }
        
        # State
        self.initialized = False
        self.running = False
        
        # Configuration
        self.auto_settlement = self.lifecycle_config.get('auto_settlement', True)
        self.settlement_delay = self.lifecycle_config.get('settlement_delay', 2)  # T+2
        self.max_trade_age = self.lifecycle_config.get('max_trade_age', 86400)  # 24 hours
        
    async def initialize(self):
        """Initialize the trade lifecycle manager"""
        if self.initialized:
            return
            
        logger.info("Initializing Trade Lifecycle Manager...")
        
        try:
            # Initialize state tracking
            self._init_state_tracking()
            
            self.initialized = True
            logger.info("✓ Trade Lifecycle Manager initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Trade Lifecycle Manager: {e}")
            raise
            
    def _init_state_tracking(self):
        """Initialize state tracking"""
        logger.info("Trade lifecycle state tracking initialized")
        
    async def start(self):
        """Start the trade lifecycle manager"""
        if not self.initialized:
            await self.initialize()
            
        if self.running:
            return
            
        logger.info("Starting Trade Lifecycle Manager...")
        
        # Start background tasks
        asyncio.create_task(self._lifecycle_monitoring_loop())
        asyncio.create_task(self._settlement_processing_loop())
        
        self.running = True
        logger.info("✓ Trade Lifecycle Manager started")
        
    async def stop(self):
        """Stop the trade lifecycle manager"""
        if not self.running:
            return
            
        logger.info("Stopping Trade Lifecycle Manager...")
        self.running = False
        logger.info("✓ Trade Lifecycle Manager stopped")
        
    async def create_trade(self, order: Order) -> str:
        """Create a new trade record"""
        try:
            trade_id = f"trade_{order.order_id}_{int(time.time())}"
            
            # Create trade record
            trade_record = TradeRecord(
                trade_id=trade_id,
                order=order,
                state=TradeState.CREATED,
                events=[],
                created_at=time.time(),
                updated_at=time.time(),
                metadata={}
            )
            
            # Add creation event
            await self._add_event(trade_record, "trade_created", {
                'order_id': order.order_id,
                'symbol': order.symbol,
                'quantity': order.quantity
            }, "lifecycle_manager")
            
            # Store trade
            self.active_trades[trade_id] = trade_record
            
            logger.info(f"Created trade {trade_id} for order {order.order_id}")
            return trade_id
            
        except Exception as e:
            logger.error(f"Error creating trade for order {order.order_id}: {e}")
            raise
            
    async def update_trade_state(self, trade_id: str, new_state: TradeState, event_data: Dict[str, Any] = None, source: str = "unknown") -> bool:
        """Update trade state"""
        try:
            if trade_id not in self.active_trades:
                logger.warning(f"Trade {trade_id} not found")
                return False
                
            trade_record = self.active_trades[trade_id]
            current_state = trade_record.state
            
            # Validate state transition
            if new_state not in self.valid_transitions.get(current_state, []):
                logger.warning(f"Invalid state transition for trade {trade_id}: {current_state} -> {new_state}")
                return False
            
            # Update state
            trade_record.state = new_state
            trade_record.updated_at = time.time()
            
            # Add state change event
            await self._add_event(trade_record, "state_changed", {
                'previous_state': current_state.value,
                'new_state': new_state.value,
                **(event_data or {})
            }, source)
            
            # Handle terminal states
            if new_state in [TradeState.FILLED, TradeState.CANCELLED, TradeState.REJECTED, TradeState.EXPIRED, TradeState.SETTLED]:
                await self._handle_terminal_state(trade_record)
            
            logger.info(f"Updated trade {trade_id} state: {current_state.value} -> {new_state.value}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating trade state for {trade_id}: {e}")
            return False
            
    async def _add_event(self, trade_record: TradeRecord, event_type: str, data: Dict[str, Any], source: str):
        """Add event to trade record"""
        try:
            event = TradeEvent(
                trade_id=trade_record.trade_id,
                event_type=event_type,
                timestamp=time.time(),
                data=data,
                source=source
            )
            
            trade_record.events.append(event)
            self.trade_events.append(event)
            
            # Keep only recent events
            if len(self.trade_events) > 10000:
                self.trade_events = self.trade_events[-10000:]
                
        except Exception as e:
            logger.error(f"Error adding event to trade {trade_record.trade_id}: {e}")
            
    async def _handle_terminal_state(self, trade_record: TradeRecord):
        """Handle trade reaching terminal state"""
        try:
            # Move to completed trades if truly terminal
            if trade_record.state in [TradeState.CANCELLED, TradeState.REJECTED, TradeState.EXPIRED, TradeState.SETTLED]:
                self.completed_trades[trade_record.trade_id] = trade_record
                if trade_record.trade_id in self.active_trades:
                    del self.active_trades[trade_record.trade_id]
                    
            # Schedule settlement for filled trades
            elif trade_record.state == TradeState.FILLED and self.auto_settlement:
                asyncio.create_task(self._schedule_settlement(trade_record))
                
        except Exception as e:
            logger.error(f"Error handling terminal state for trade {trade_record.trade_id}: {e}")
            
    async def _schedule_settlement(self, trade_record: TradeRecord):
        """Schedule trade settlement"""
        try:
            # Wait for settlement delay
            settlement_time = self.settlement_delay * 24 * 3600  # Convert days to seconds
            await asyncio.sleep(min(settlement_time, 60))  # Cap at 1 minute for testing
            
            # Update to settled state
            await self.update_trade_state(
                trade_record.trade_id,
                TradeState.SETTLED,
                {'settlement_date': time.time()},
                'settlement_processor'
            )
            
        except Exception as e:
            logger.error(f"Error scheduling settlement for trade {trade_record.trade_id}: {e}")
            
    async def get_trade_status(self, trade_id: str) -> Optional[Dict[str, Any]]:
        """Get trade status"""
        try:
            # Check active trades first
            if trade_id in self.active_trades:
                trade_record = self.active_trades[trade_id]
            elif trade_id in self.completed_trades:
                trade_record = self.completed_trades[trade_id]
            else:
                return None
                
            return {
                'trade_id': trade_record.trade_id,
                'order_id': trade_record.order.order_id,
                'symbol': trade_record.order.symbol,
                'state': trade_record.state.value,
                'created_at': trade_record.created_at,
                'updated_at': trade_record.updated_at,
                'events_count': len(trade_record.events),
                'metadata': trade_record.metadata
            }
            
        except Exception as e:
            logger.error(f"Error getting trade status for {trade_id}: {e}")
            return None
            
    async def get_trade_events(self, trade_id: str) -> List[TradeEvent]:
        """Get trade events"""
        try:
            if trade_id in self.active_trades:
                return self.active_trades[trade_id].events.copy()
            elif trade_id in self.completed_trades:
                return self.completed_trades[trade_id].events.copy()
            else:
                return []
                
        except Exception as e:
            logger.error(f"Error getting trade events for {trade_id}: {e}")
            return []
            
    async def get_active_trades(self) -> List[Dict[str, Any]]:
        """Get all active trades"""
        try:
            return [
                {
                    'trade_id': trade_record.trade_id,
                    'order_id': trade_record.order.order_id,
                    'symbol': trade_record.order.symbol,
                    'state': trade_record.state.value,
                    'created_at': trade_record.created_at,
                    'updated_at': trade_record.updated_at
                }
                for trade_record in self.active_trades.values()
            ]
            
        except Exception as e:
            logger.error(f"Error getting active trades: {e}")
            return []
            
    async def _lifecycle_monitoring_loop(self):
        """Background lifecycle monitoring loop"""
        while self.running:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                if self.running:
                    await self._check_trade_timeouts()
                    await self._cleanup_old_trades()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in lifecycle monitoring loop: {e}")
                
    async def _settlement_processing_loop(self):
        """Background settlement processing loop"""
        while self.running:
            try:
                await asyncio.sleep(300)  # Check every 5 minutes
                
                if self.running:
                    await self._process_pending_settlements()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in settlement processing loop: {e}")
                
    async def _check_trade_timeouts(self):
        """Check for trade timeouts"""
        try:
            current_time = time.time()
            timeout_threshold = current_time - self.max_trade_age
            
            for trade_id, trade_record in list(self.active_trades.items()):
                if trade_record.created_at < timeout_threshold:
                    if trade_record.state in [TradeState.CREATED, TradeState.VALIDATED, TradeState.SUBMITTED]:
                        await self.update_trade_state(
                            trade_id,
                            TradeState.EXPIRED,
                            {'reason': 'timeout'},
                            'lifecycle_monitor'
                        )
                        
        except Exception as e:
            logger.error(f"Error checking trade timeouts: {e}")
            
    async def _cleanup_old_trades(self):
        """Cleanup old completed trades"""
        try:
            current_time = time.time()
            cleanup_threshold = current_time - (self.max_trade_age * 7)  # Keep for 7x max age
            
            trades_to_remove = []
            for trade_id, trade_record in self.completed_trades.items():
                if trade_record.updated_at < cleanup_threshold:
                    trades_to_remove.append(trade_id)
                    
            for trade_id in trades_to_remove:
                del self.completed_trades[trade_id]
                
            if trades_to_remove:
                logger.info(f"Cleaned up {len(trades_to_remove)} old trades")
                
        except Exception as e:
            logger.error(f"Error cleaning up old trades: {e}")
            
    async def _process_pending_settlements(self):
        """Process pending settlements"""
        try:
            # This would handle any pending settlement processing
            # For now, just a placeholder
            pass
            
        except Exception as e:
            logger.error(f"Error processing pending settlements: {e}")
            
    async def get_stats(self) -> Dict[str, Any]:
        """Get trade lifecycle statistics"""
        try:
            state_counts = {}
            for trade_record in self.active_trades.values():
                state = trade_record.state.value
                state_counts[state] = state_counts.get(state, 0) + 1
                
            return {
                'running': self.running,
                'active_trades': len(self.active_trades),
                'completed_trades': len(self.completed_trades),
                'total_events': len(self.trade_events),
                'state_distribution': state_counts,
                'auto_settlement': self.auto_settlement,
                'settlement_delay': self.settlement_delay
            }
            
        except Exception as e:
            logger.error(f"Error getting lifecycle stats: {e}")
            return {}

    async def start_order_lifecycle(self, order: Order) -> str:
        """Start lifecycle management for an order"""
        try:
            trade_id = await self.create_trade(order)
            await self.update_trade_state(trade_id, TradeState.SUBMITTED, {
                'venue': 'default',
                'order_type': order.order_type.value if hasattr(order.order_type, 'value') else str(order.order_type)
            }, 'execution_engine')
            return trade_id
        except Exception as e:
            logger.error(f"Error starting order lifecycle: {e}")
            return ""

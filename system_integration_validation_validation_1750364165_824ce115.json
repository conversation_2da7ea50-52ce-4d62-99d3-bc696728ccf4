{"validation_id": "validation_**********_824ce115", "validation_level": "production", "overall_status": "failed", "overall_score": 0.7865024000523235, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "partial", "integration_score": 0.7941185072241529, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "team_manager", "component_type": "core", "status": "partial", "integration_score": 0.781219702577032, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "partial", "integration_score": 0.7866943410913646, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "analytics_engine", "component_type": "core", "status": "partial", "integration_score": 0.7868590375358767, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "partial", "integration_score": 0.773306505922678, "error_count": 0, "warnings": ["Functionality concerns in ollama_hub"], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "partial", "integration_score": 0.7607658303425411, "error_count": 0, "warnings": ["Functionality concerns in execution_engine", "Integration issues in execution_engine"], "dependencies_met": "True"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "completed", "integration_score": 0.8053929251774636, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "risk_manager", "component_type": "core", "status": "completed", "integration_score": 0.8165695419916212, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "partial", "integration_score": 0.7373344647782853, "error_count": 0, "warnings": ["Functionality concerns in strategy_manager"], "dependencies_met": "True"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8085970427257256, "error_count": 0, "warnings": ["Integration issues in competitive_framework"], "dependencies_met": "True"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.82150112424138, "error_count": 0, "warnings": ["Integration issues in tournament_framework"], "dependencies_met": "False"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.796485058599043, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "partial", "integration_score": 0.7298341621448634, "error_count": 0, "warnings": ["Integration issues in regime_adaptation_system"], "dependencies_met": "True"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "partial", "integration_score": 0.7569625403356002, "error_count": 0, "warnings": ["Functionality concerns in performance_optimizer"], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.7899867510005011, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "completed", "integration_score": 0.8161341419031443, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "completed", "integration_score": 0.8127587495972396, "error_count": 0, "warnings": ["Functionality concerns in configuration_manager"], "dependencies_met": "True"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "completed", "integration_score": 0.8146965029028013, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "completed", "integration_score": 0.8192299896330746, "error_count": 0, "warnings": ["Integration issues in paper_trading_engine"], "dependencies_met": "True"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "partial", "integration_score": 0.7539034218188185, "error_count": 0, "warnings": ["Functionality concerns in logging_audit_system"], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.9693201950439335, "data_manager": 0.8453485842369555, "analytics_engine": 0.8462524697683078, "ollama_hub": 0.6364544200048755, "execution_engine": 0.6986856682462034, "portfolio_manager": 0.7411044664774802, "risk_manager": 0.8159793803082174, "strategy_manager": 0.8791742266349909, "competitive_framework": 0.7709165536221234, "tournament_framework": 0.7454829223695313, "self_improvement_engine": 0.8027007466190669, "regime_adaptation_system": 0.8743482494321209, "performance_optimizer": 0.7041850483917445, "advanced_trading_engine": 0.7392594186633192, "ai_coordinator": 0.610154022319194, "configuration_manager": 0.653733160212419, "mock_data_providers": 0.8701209520815257, "paper_trading_engine": 0.7183002232014956, "logging_audit_system": 0.8089168570995334}, "team_manager": {"system_coordinator": 0.8561915813755305, "team_manager": 1.0, "data_manager": 0.9672526313595606, "analytics_engine": 0.7203759798098942, "ollama_hub": 0.632849612168945, "execution_engine": 0.7495088357927862, "portfolio_manager": 0.7043454220494717, "risk_manager": 0.674964323669756, "strategy_manager": 0.6966710476790295, "competitive_framework": 0.8070931549634193, "tournament_framework": 0.6453723384197253, "self_improvement_engine": 0.6792876523436214, "regime_adaptation_system": 0.8121843455315307, "performance_optimizer": 0.8781481321203112, "advanced_trading_engine": 0.7473967135983175, "ai_coordinator": 0.6950695993583025, "configuration_manager": 0.8767583074201319, "mock_data_providers": 0.6480924152891671, "paper_trading_engine": 0.8879477434882868, "logging_audit_system": 0.8524549078171446}, "data_manager": {"system_coordinator": 0.8530147845917471, "team_manager": 0.6832884096671015, "data_manager": 1.0, "analytics_engine": 0.9847555773063659, "ollama_hub": 0.7340588031448414, "execution_engine": 0.6729522550195859, "portfolio_manager": 0.7727911138314139, "risk_manager": 0.7270912340746567, "strategy_manager": 0.6902172365162289, "competitive_framework": 0.8298295369414386, "tournament_framework": 0.6091675634344427, "self_improvement_engine": 0.7566405491044421, "regime_adaptation_system": 0.8814965479838366, "performance_optimizer": 0.7669584592121881, "advanced_trading_engine": 0.8238871260486642, "ai_coordinator": 0.8123486450442372, "configuration_manager": 0.602050406600767, "mock_data_providers": 0.7091617304190605, "paper_trading_engine": 0.8902871561590181, "logging_audit_system": 0.8167558734931777}, "analytics_engine": {"system_coordinator": 0.7102823722611318, "team_manager": 0.6279035386557363, "data_manager": 0.8212404796380068, "analytics_engine": 1.0, "ollama_hub": 0.8407075401620415, "execution_engine": 0.8565340872324672, "portfolio_manager": 0.8482615998121851, "risk_manager": 0.8370555314633011, "strategy_manager": 0.9094063456848279, "competitive_framework": 0.625156716326755, "tournament_framework": 0.856513074723621, "self_improvement_engine": 0.8907502707643707, "regime_adaptation_system": 0.866331395868994, "performance_optimizer": 0.6306761874503202, "advanced_trading_engine": 0.8423743684760581, "ai_coordinator": 0.8950385817027502, "configuration_manager": 0.8272844843300987, "mock_data_providers": 0.8586440867917611, "paper_trading_engine": 0.8637049734161075, "logging_audit_system": 0.6903992649557459}, "ollama_hub": {"system_coordinator": 0.8871808169252704, "team_manager": 0.7635172063515305, "data_manager": 0.7321914805843706, "analytics_engine": 0.7924283908121558, "ollama_hub": 1.0, "execution_engine": 0.6048848136033105, "portfolio_manager": 0.6528632199792859, "risk_manager": 0.8881503614088475, "strategy_manager": 0.8673814103947102, "competitive_framework": 0.6943306616434151, "tournament_framework": 0.7461181173667057, "self_improvement_engine": 0.6387139129956445, "regime_adaptation_system": 0.6947218266608053, "performance_optimizer": 0.8237519892476144, "advanced_trading_engine": 0.7471008050809372, "ai_coordinator": 0.6741632106101821, "configuration_manager": 0.6669050028828862, "mock_data_providers": 0.7513568088301651, "paper_trading_engine": 0.7967678684313029, "logging_audit_system": 0.7260985699565706}, "execution_engine": {"system_coordinator": 0.6548985416174414, "team_manager": 0.7428792034637923, "data_manager": 0.7295738339708615, "analytics_engine": 0.8168699995464846, "ollama_hub": 0.6691450468240518, "execution_engine": 1.0, "portfolio_manager": 0.9680791147751309, "risk_manager": 0.629264283018463, "strategy_manager": 0.7717694208916358, "competitive_framework": 0.7285432120813358, "tournament_framework": 0.77476316263873, "self_improvement_engine": 0.8826067108810693, "regime_adaptation_system": 0.739503068852464, "performance_optimizer": 0.8995864883579912, "advanced_trading_engine": 0.7587711716792158, "ai_coordinator": 0.7051406713960273, "configuration_manager": 0.7154104515535042, "mock_data_providers": 0.7048169829241889, "paper_trading_engine": 0.8674549901547902, "logging_audit_system": 0.8858171982276122}, "portfolio_manager": {"system_coordinator": 0.7222743351133292, "team_manager": 0.8578107469054357, "data_manager": 0.8899537850867879, "analytics_engine": 0.7313446499598817, "ollama_hub": 0.6487802739357222, "execution_engine": 0.7031737975017853, "portfolio_manager": 1.0, "risk_manager": 0.7981128991786003, "strategy_manager": 0.6794672329580275, "competitive_framework": 0.8196457589272095, "tournament_framework": 0.8259131569979309, "self_improvement_engine": 0.7514903480786276, "regime_adaptation_system": 0.8820557664113364, "performance_optimizer": 0.6797134656645303, "advanced_trading_engine": 0.6541444827708567, "ai_coordinator": 0.6941376521474112, "configuration_manager": 0.6642387227323887, "mock_data_providers": 0.8568402524705087, "paper_trading_engine": 0.8452964744327646, "logging_audit_system": 0.7873579425127601}, "risk_manager": {"system_coordinator": 0.8930705327309691, "team_manager": 0.7918731166189381, "data_manager": 0.8095653951458623, "analytics_engine": 0.8015085322085657, "ollama_hub": 0.7325598552178928, "execution_engine": 0.8860961147847896, "portfolio_manager": 0.8852583228335613, "risk_manager": 1.0, "strategy_manager": 0.7037961891275998, "competitive_framework": 0.8140241321259121, "tournament_framework": 0.8308943229864216, "self_improvement_engine": 0.6271410694922016, "regime_adaptation_system": 0.7007395809454658, "performance_optimizer": 0.8857148192999464, "advanced_trading_engine": 0.6869244105896666, "ai_coordinator": 0.8001184217638767, "configuration_manager": 0.8992774166256401, "mock_data_providers": 0.730506215679956, "paper_trading_engine": 0.8234542889373286, "logging_audit_system": 0.6364799842805563}, "strategy_manager": {"system_coordinator": 0.6311964457259666, "team_manager": 0.6374225243626638, "data_manager": 0.7833200807547367, "analytics_engine": 0.727002331964579, "ollama_hub": 0.700959751454317, "execution_engine": 0.7770485467139059, "portfolio_manager": 0.6356091737854036, "risk_manager": 0.7168930788824395, "strategy_manager": 1.0, "competitive_framework": 0.6576720025574246, "tournament_framework": 0.6131609543658962, "self_improvement_engine": 0.608197674613603, "regime_adaptation_system": 0.6102800087973624, "performance_optimizer": 0.8909593368390823, "advanced_trading_engine": 0.6047590549826781, "ai_coordinator": 0.6103495815822473, "configuration_manager": 0.6206496713869352, "mock_data_providers": 0.8359663435384795, "paper_trading_engine": 0.6539448214097296, "logging_audit_system": 0.7327977673290846}, "competitive_framework": {"system_coordinator": 0.6815491095213113, "team_manager": 0.6726430175877847, "data_manager": 0.704059813612627, "analytics_engine": 0.7053731814362962, "ollama_hub": 0.7027858525247532, "execution_engine": 0.6600711172145377, "portfolio_manager": 0.8630718577743346, "risk_manager": 0.6742061388627038, "strategy_manager": 0.7310576563851864, "competitive_framework": 1.0, "tournament_framework": 0.8383130594250323, "self_improvement_engine": 0.6850943618251829, "regime_adaptation_system": 0.6156535608345632, "performance_optimizer": 0.8981355692417596, "advanced_trading_engine": 0.7078520572436746, "ai_coordinator": 0.7379694146577905, "configuration_manager": 0.6347098442497214, "mock_data_providers": 0.7130405891029821, "paper_trading_engine": 0.6962509504680046, "logging_audit_system": 0.767518341442349}, "tournament_framework": {"system_coordinator": 0.8656639221521107, "team_manager": 0.6179166813849735, "data_manager": 0.811201587963873, "analytics_engine": 0.8318204745325393, "ollama_hub": 0.7473225950606709, "execution_engine": 0.6774321407409385, "portfolio_manager": 0.6499211688376606, "risk_manager": 0.7953776028667287, "strategy_manager": 0.6667747479433436, "competitive_framework": 0.6955922267492921, "tournament_framework": 1.0, "self_improvement_engine": 0.7595618934616146, "regime_adaptation_system": 0.8145277544213873, "performance_optimizer": 0.8728284539761038, "advanced_trading_engine": 0.8903369087424609, "ai_coordinator": 0.6963904317340154, "configuration_manager": 0.6109478665492123, "mock_data_providers": 0.775111870228865, "paper_trading_engine": 0.8972454364443221, "logging_audit_system": 0.7658607191313108}, "self_improvement_engine": {"system_coordinator": 0.648254140646014, "team_manager": 0.781092690125548, "data_manager": 0.8606115056219974, "analytics_engine": 0.6798188364113548, "ollama_hub": 0.6517072312798998, "execution_engine": 0.6247115281925691, "portfolio_manager": 0.7218556207894405, "risk_manager": 0.8940876857840363, "strategy_manager": 0.8953020416931678, "competitive_framework": 0.6803586013137966, "tournament_framework": 0.8850552350592943, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.6440766169438307, "performance_optimizer": 0.8184689050881202, "advanced_trading_engine": 0.6110405717416996, "ai_coordinator": 0.7226386400416942, "configuration_manager": 0.6205054374119013, "mock_data_providers": 0.7777157444196227, "paper_trading_engine": 0.6620973348652341, "logging_audit_system": 0.6694457961965785}, "regime_adaptation_system": {"system_coordinator": 0.6753078329676447, "team_manager": 0.8383940864425006, "data_manager": 0.8879669990785337, "analytics_engine": 0.6021678372125878, "ollama_hub": 0.8935168425945127, "execution_engine": 0.6657752216313977, "portfolio_manager": 0.8933281725597428, "risk_manager": 0.7956698089932741, "strategy_manager": 0.7800546862599212, "competitive_framework": 0.831974744323678, "tournament_framework": 0.7753969604706523, "self_improvement_engine": 0.899000577978024, "regime_adaptation_system": 1.0, "performance_optimizer": 0.779535006947748, "advanced_trading_engine": 0.7316758758383597, "ai_coordinator": 0.6615516793817835, "configuration_manager": 0.7990609726901082, "mock_data_providers": 0.8113781259790336, "paper_trading_engine": 0.6107208833272817, "logging_audit_system": 0.6904955046056743}, "performance_optimizer": {"system_coordinator": 0.7178380732178846, "team_manager": 0.6358635103433133, "data_manager": 0.7624225084981833, "analytics_engine": 0.6681408275381678, "ollama_hub": 0.6332025398235426, "execution_engine": 0.812927704093853, "portfolio_manager": 0.8767062634648637, "risk_manager": 0.7878097240152424, "strategy_manager": 0.6927363941351384, "competitive_framework": 0.6731675651196438, "tournament_framework": 0.8795805976372291, "self_improvement_engine": 0.6498623380932479, "regime_adaptation_system": 0.7666583064583832, "performance_optimizer": 1.0, "advanced_trading_engine": 0.6104645502977036, "ai_coordinator": 0.6126939808036496, "configuration_manager": 0.7957605404670898, "mock_data_providers": 0.6422176190861216, "paper_trading_engine": 0.7723360983957586, "logging_audit_system": 0.6674608108298798}, "advanced_trading_engine": {"system_coordinator": 0.7925878804906289, "team_manager": 0.7497883786749193, "data_manager": 0.8017748786983874, "analytics_engine": 0.8298105096209353, "ollama_hub": 0.7170156565089407, "execution_engine": 0.8156615105824966, "portfolio_manager": 0.7722159138402789, "risk_manager": 0.844778972467958, "strategy_manager": 0.8467028791096073, "competitive_framework": 0.8111871435361007, "tournament_framework": 0.8511044151406001, "self_improvement_engine": 0.6684419135617155, "regime_adaptation_system": 0.7991671989117022, "performance_optimizer": 0.63965304827245, "advanced_trading_engine": 1.0, "ai_coordinator": 0.872014193442017, "configuration_manager": 0.63034958243642, "mock_data_providers": 0.6913465493104347, "paper_trading_engine": 0.8667602072752237, "logging_audit_system": 0.7434623322408958}, "ai_coordinator": {"system_coordinator": 0.8066847760737538, "team_manager": 0.6273483022931763, "data_manager": 0.7640198412978483, "analytics_engine": 0.8865647070909546, "ollama_hub": 0.8124761144222455, "execution_engine": 0.7472667277745778, "portfolio_manager": 0.8783152354387924, "risk_manager": 0.614854384383167, "strategy_manager": 0.8897701722335833, "competitive_framework": 0.8307212807049804, "tournament_framework": 0.6104222653845638, "self_improvement_engine": 0.7576950685528182, "regime_adaptation_system": 0.6090333716936741, "performance_optimizer": 0.7136448221830853, "advanced_trading_engine": 0.8903267972751021, "ai_coordinator": 1.0, "configuration_manager": 0.8924885825037502, "mock_data_providers": 0.8433625653198848, "paper_trading_engine": 0.7346074892624848, "logging_audit_system": 0.7661977442813678}, "configuration_manager": {"system_coordinator": 0.8153161573408637, "team_manager": 0.7109562993399676, "data_manager": 0.7138591858920076, "analytics_engine": 0.6452935195162645, "ollama_hub": 0.729276570916936, "execution_engine": 0.7522008526016762, "portfolio_manager": 0.8549377953796964, "risk_manager": 0.6244823871487201, "strategy_manager": 0.7368018933637993, "competitive_framework": 0.6181109385686779, "tournament_framework": 0.6989433263032141, "self_improvement_engine": 0.7042281382962995, "regime_adaptation_system": 0.7693957493154454, "performance_optimizer": 0.6404964711222989, "advanced_trading_engine": 0.8825417713345352, "ai_coordinator": 0.8574835950459285, "configuration_manager": 1.0, "mock_data_providers": 0.6369312760986081, "paper_trading_engine": 0.6303914649920466, "logging_audit_system": 0.6450705312256928}, "mock_data_providers": {"system_coordinator": 0.6724072351210332, "team_manager": 0.7129913574109569, "data_manager": 0.7032435101364195, "analytics_engine": 0.6965450840968713, "ollama_hub": 0.7198259820465813, "execution_engine": 0.7953904428032738, "portfolio_manager": 0.7961636139348774, "risk_manager": 0.8941145223150977, "strategy_manager": 0.7302436699454202, "competitive_framework": 0.835777837999519, "tournament_framework": 0.6624350720745343, "self_improvement_engine": 0.870744335032931, "regime_adaptation_system": 0.6880990500743609, "performance_optimizer": 0.790344567134866, "advanced_trading_engine": 0.7936380210817697, "ai_coordinator": 0.8371610582592155, "configuration_manager": 0.7358991505349877, "mock_data_providers": 1.0, "paper_trading_engine": 0.7728971497870613, "logging_audit_system": 0.7651566620030184}, "paper_trading_engine": {"system_coordinator": 0.7341028579296563, "team_manager": 0.6224875904513447, "data_manager": 0.8901126660051706, "analytics_engine": 0.7862112639998293, "ollama_hub": 0.6853879728872279, "execution_engine": 0.8253053833562042, "portfolio_manager": 0.7651881216800626, "risk_manager": 0.7260479942642531, "strategy_manager": 0.6993221115450549, "competitive_framework": 0.7130351335513737, "tournament_framework": 0.6764353674951094, "self_improvement_engine": 0.8428254924369407, "regime_adaptation_system": 0.6534356927779945, "performance_optimizer": 0.7737263351902224, "advanced_trading_engine": 0.8618763615836116, "ai_coordinator": 0.7249914797174837, "configuration_manager": 0.8637422538814774, "mock_data_providers": 0.7115822713677995, "paper_trading_engine": 1.0, "logging_audit_system": 0.6468345718174713}, "logging_audit_system": {"system_coordinator": 0.7524056145840915, "team_manager": 0.8977923569213838, "data_manager": 0.8234689815617696, "analytics_engine": 0.710779288203904, "ollama_hub": 0.7765880507714887, "execution_engine": 0.611019656525536, "portfolio_manager": 0.802063307572474, "risk_manager": 0.7592538541553817, "strategy_manager": 0.804335395107755, "competitive_framework": 0.6585532457496545, "tournament_framework": 0.8279887642879209, "self_improvement_engine": 0.7836323538268561, "regime_adaptation_system": 0.8013364942350057, "performance_optimizer": 0.7478188911100314, "advanced_trading_engine": 0.639824633549253, "ai_coordinator": 0.7802858180573919, "configuration_manager": 0.7031511892449898, "mock_data_providers": 0.8904408727063895, "paper_trading_engine": 0.7076505916284086, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.7629547820023728, "response_time": 0.8347437292808284, "throughput": 0.6027161829085613, "memory_usage": 0.7767901593717642, "cpu_usage": 0.8300646692775455, "concurrent_operations": 0.677836143524954}, "critical_issues": ["Components with dependency issues: system_coordinator, tournament_framework, mock_data_providers"], "recommendations": ["Improve 12 partially working components", "Improve overall system performance and stability", "Enhance component integration and communication", "Address all issues before production deployment"], "production_ready": "False", "timestamp": **********.1712174}
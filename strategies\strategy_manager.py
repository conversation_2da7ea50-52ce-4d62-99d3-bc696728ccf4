"""
Strategy Manager - Central management for all trading strategies
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Type
from collections import defaultdict

from .base_strategy import BaseStrategy, StrategyType, StrategyState, StrategyResult
from .momentum_strategies import MomentumStrategy, TrendFollowingStrategy, BreakoutStrategy
from .mean_reversion_strategies import MeanReversionStrategy, PairsTradingStrategy
from .volatility_strategies import VolatilityExploitationStrategy
from .carry_strategies import CarryTradeStrategy
from .adaptive_strategies import AdaptiveStrategy

logger = logging.getLogger(__name__)


class StrategyManager:
    """
    Central manager for all trading strategies.
    
    Responsibilities:
    - Strategy lifecycle management
    - Strategy registration and discovery
    - Market data distribution
    - Signal aggregation and coordination
    - Performance monitoring
    - Strategy optimization
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Strategy storage
        self.strategies: Dict[str, BaseStrategy] = {}
        self.strategies_by_type: Dict[StrategyType, List[str]] = defaultdict(list)
        self.strategies_by_symbol: Dict[str, List[str]] = defaultdict(list)
        
        # Strategy registry
        self.strategy_classes: Dict[str, Type[BaseStrategy]] = {
            'momentum': MomentumStrategy,
            'trend_following': TrendFollowingStrategy,
            'breakout': BreakoutStrategy,
            'mean_reversion': MeanReversionStrategy,
            'pairs_trading': PairsTradingStrategy,
            'volatility_exploitation': VolatilityExploitationStrategy,
            'carry_trade': CarryTradeStrategy,
            'adaptive': AdaptiveStrategy
        }
        
        # Configuration
        self.max_strategies = self.config.get('max_strategies', 100)
        self.update_interval = self.config.get('update_interval', 60)  # seconds
        self.signal_aggregation_method = self.config.get('signal_aggregation', 'weighted_average')
        
        # State
        self.initialized = False
        self.running = False
        
        # Background tasks
        self.monitoring_task: Optional[asyncio.Task] = None
        self.optimization_task: Optional[asyncio.Task] = None
        
        # Performance tracking
        self.global_metrics = {
            'total_strategies': 0,
            'active_strategies': 0,
            'total_signals': 0,
            'successful_signals': 0,
            'signal_accuracy': 0.0,
            'avg_strategy_performance': 0.0
        }
        
    async def initialize(self) -> bool:
        """Initialize the strategy manager"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Strategy Manager...")
            
            # Load strategy configurations
            await self._load_strategy_configs()
            
            # Initialize default strategies if configured
            await self._initialize_default_strategies()
            
            self.initialized = True
            logger.info("✓ Strategy Manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Strategy Manager: {e}")
            return False
    
    async def start(self) -> bool:
        """Start the strategy manager"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        if self.running:
            return True
            
        try:
            logger.info("Starting Strategy Manager...")
            self.running = True
            
            # Start all strategies
            for strategy in self.strategies.values():
                await strategy.start()
            
            # Start background tasks
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            self.optimization_task = asyncio.create_task(self._optimization_loop())
            
            logger.info("✓ Strategy Manager started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Strategy Manager: {e}")
            return False

    async def set_integration_points(self,
                                   risk_manager=None,
                                   execution_engine=None,
                                   portfolio_manager=None,
                                   market_data_manager=None,
                                   message_broker=None):
        """Set integration points with other systems"""
        self.risk_manager = risk_manager
        self.execution_engine = execution_engine
        self.portfolio_manager = portfolio_manager
        self.market_data_manager = market_data_manager
        self.message_broker = message_broker

        # Pass integration points to all strategies
        for strategy in self.strategies.values():
            if hasattr(strategy, 'set_integration_points'):
                await strategy.set_integration_points(
                    risk_manager=risk_manager,
                    execution_engine=execution_engine,
                    portfolio_manager=portfolio_manager,
                    market_data_manager=market_data_manager,
                    message_broker=message_broker
                )

        logger.info("Strategy Manager integration points configured")

    async def set_database_coordinator(self, database_coordinator):
        """Set database coordinator for data persistence"""
        self.database_coordinator = database_coordinator

        # Pass database coordinator to all strategies
        for strategy in self.strategies.values():
            if hasattr(strategy, 'set_database_coordinator'):
                await strategy.set_database_coordinator(database_coordinator)

        logger.info("Strategy Manager database coordinator configured")

    async def stop(self) -> bool:
        """Stop the strategy manager"""
        if not self.running:
            return True
            
        try:
            logger.info("Stopping Strategy Manager...")
            self.running = False
            
            # Cancel background tasks
            if self.monitoring_task:
                self.monitoring_task.cancel()
            if self.optimization_task:
                self.optimization_task.cancel()
            
            # Stop all strategies
            for strategy in self.strategies.values():
                await strategy.stop()
            
            logger.info("✓ Strategy Manager stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping Strategy Manager: {e}")
            return False

    async def set_analytics_engine(self, analytics_engine):
        """Set analytics engine for advanced analytics"""
        self.analytics_engine = analytics_engine

        # Pass analytics engine to all strategies
        for strategy in self.strategies.values():
            if hasattr(strategy, 'set_analytics_engine'):
                await strategy.set_analytics_engine(analytics_engine)

        logger.info("Strategy Manager analytics engine configured")

    async def create_strategy(self,
                             strategy_type: str,
                             name: str = None,
                             config: Dict[str, Any] = None) -> Optional[str]:
        """Create a new strategy"""
        try:
            if len(self.strategies) >= self.max_strategies:
                logger.error(f"Maximum strategies ({self.max_strategies}) reached")
                return None
                
            if strategy_type not in self.strategy_classes:
                logger.error(f"Unknown strategy type: {strategy_type}")
                return None
            
            # Create strategy instance
            strategy_class = self.strategy_classes[strategy_type]
            strategy = strategy_class(
                name=name,
                config=config or {}
            )
            
            # Initialize strategy
            if await strategy.initialize():
                # Register strategy
                self.strategies[strategy.strategy_id] = strategy
                
                # Update indices
                if strategy.strategy_type:
                    self.strategies_by_type[strategy.strategy_type].append(strategy.strategy_id)
                
                for symbol in strategy.symbols:
                    self.strategies_by_symbol[symbol].append(strategy.strategy_id)
                
                # Start strategy if manager is running
                if self.running:
                    await strategy.start()
                
                logger.info(f"✓ Created strategy {strategy.name} ({strategy_type})")
                return strategy.strategy_id
            else:
                logger.error(f"Failed to initialize strategy {strategy.name}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating strategy: {e}")
            return None
    
    async def remove_strategy(self, strategy_id: str) -> bool:
        """Remove a strategy"""
        try:
            if strategy_id not in self.strategies:
                logger.error(f"Strategy {strategy_id} not found")
                return False
            
            strategy = self.strategies[strategy_id]
            
            # Stop strategy
            await strategy.stop()
            
            # Remove from indices
            if strategy.strategy_type:
                if strategy_id in self.strategies_by_type[strategy.strategy_type]:
                    self.strategies_by_type[strategy.strategy_type].remove(strategy_id)
            
            for symbol in strategy.symbols:
                if strategy_id in self.strategies_by_symbol[symbol]:
                    self.strategies_by_symbol[symbol].remove(strategy_id)
            
            # Remove from main storage
            del self.strategies[strategy_id]
            
            logger.info(f"✓ Removed strategy {strategy.name}")
            return True
            
        except Exception as e:
            logger.error(f"Error removing strategy {strategy_id}: {e}")
            return False

    async def start_strategy(self, strategy_id: str) -> bool:
        """Start a specific strategy"""
        try:
            if strategy_id not in self.strategies:
                logger.error(f"Strategy {strategy_id} not found")
                return False

            strategy = self.strategies[strategy_id]
            await strategy.start()
            logger.info(f"✓ Started strategy {strategy.name}")
            return True

        except Exception as e:
            logger.error(f"Error starting strategy {strategy_id}: {e}")
            return False
    
    async def update_market_data(self, symbol: str, data) -> None:
        """Update market data for all strategies tracking this symbol"""
        try:
            strategy_ids = self.strategies_by_symbol.get(symbol, [])
            
            for strategy_id in strategy_ids:
                if strategy_id in self.strategies:
                    strategy = self.strategies[strategy_id]
                    await strategy.update_market_data(symbol, data)
                    
        except Exception as e:
            logger.error(f"Error updating market data for {symbol}: {e}")
    
    async def generate_signals(self, symbol: str) -> List[StrategyResult]:
        """Generate signals from all strategies for a symbol"""
        signals = []
        
        try:
            strategy_ids = self.strategies_by_symbol.get(symbol, [])
            
            for strategy_id in strategy_ids:
                if strategy_id in self.strategies:
                    strategy = self.strategies[strategy_id]
                    signal = await strategy.generate_signal(symbol)
                    if signal:
                        signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating signals for {symbol}: {e}")
            return []
    
    async def aggregate_signals(self, signals: List[StrategyResult]) -> Optional[StrategyResult]:
        """Aggregate multiple signals into a single signal"""
        if not signals:
            return None
            
        try:
            if self.signal_aggregation_method == 'weighted_average':
                return await self._weighted_average_aggregation(signals)
            elif self.signal_aggregation_method == 'majority_vote':
                return await self._majority_vote_aggregation(signals)
            elif self.signal_aggregation_method == 'confidence_weighted':
                return await self._confidence_weighted_aggregation(signals)
            else:
                # Default to simple average
                return await self._simple_average_aggregation(signals)
                
        except Exception as e:
            logger.error(f"Error aggregating signals: {e}")
            return None
    
    def get_strategy_status(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific strategy"""
        if strategy_id in self.strategies:
            return self.strategies[strategy_id].get_status()
        return None
    
    def get_all_strategies_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all strategies"""
        return {
            strategy_id: strategy.get_status()
            for strategy_id, strategy in self.strategies.items()
        }
    
    def get_global_metrics(self) -> Dict[str, Any]:
        """Get global strategy manager metrics"""
        return {
            **self.global_metrics,
            'total_strategies': len(self.strategies),
            'active_strategies': sum(1 for s in self.strategies.values() if s.running),
            'strategies_by_type': {
                strategy_type.value: len(strategy_ids)
                for strategy_type, strategy_ids in self.strategies_by_type.items()
            }
        }
    
    # Private methods
    
    async def _load_strategy_configs(self):
        """Load strategy configurations"""
        # Implementation for loading strategy configs from files/database
        pass
    
    async def _initialize_default_strategies(self):
        """Initialize default strategies based on configuration"""
        default_strategies = self.config.get('default_strategies', [])
        
        for strategy_config in default_strategies:
            await self.create_strategy(
                strategy_type=strategy_config['type'],
                name=strategy_config.get('name'),
                config=strategy_config.get('config', {})
            )
    
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.running:
            try:
                await asyncio.sleep(self.update_interval)
                if self.running:
                    await self._update_global_metrics()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
    
    async def _optimization_loop(self):
        """Background optimization loop"""
        while self.running:
            try:
                await asyncio.sleep(3600)  # Run every hour
                if self.running:
                    await self._optimize_strategies()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in optimization loop: {e}")
    
    async def _update_global_metrics(self):
        """Update global performance metrics"""
        try:
            total_signals = sum(len(s.signal_history) for s in self.strategies.values())
            total_return = sum(s.metrics.total_return for s in self.strategies.values())
            
            self.global_metrics.update({
                'total_signals': total_signals,
                'avg_strategy_performance': total_return / len(self.strategies) if self.strategies else 0.0
            })
            
        except Exception as e:
            logger.error(f"Error updating global metrics: {e}")
    
    async def _optimize_strategies(self):
        """Optimize strategy parameters"""
        # Implementation for strategy optimization
        pass
    
    async def _weighted_average_aggregation(self, signals: List[StrategyResult]) -> StrategyResult:
        """Aggregate signals using weighted average"""
        # Implementation for weighted average aggregation
        pass
    
    async def _majority_vote_aggregation(self, signals: List[StrategyResult]) -> StrategyResult:
        """Aggregate signals using majority vote"""
        # Implementation for majority vote aggregation
        pass
    
    async def _confidence_weighted_aggregation(self, signals: List[StrategyResult]) -> StrategyResult:
        """Aggregate signals using confidence weighting"""
        # Implementation for confidence weighted aggregation
        pass
    
    async def _simple_average_aggregation(self, signals: List[StrategyResult]) -> StrategyResult:
        """Aggregate signals using simple average"""
        # Implementation for simple average aggregation
        pass

    async def get_active_strategies(self) -> List[str]:
        """Get list of active strategy IDs"""
        return [
            strategy_id for strategy_id, strategy in self.strategies.items()
            if strategy.state == StrategyState.RUNNING
        ]

    async def pause_all_strategies(self) -> bool:
        """Pause all active strategies"""
        try:
            for strategy in self.strategies.values():
                if strategy.state == StrategyState.RUNNING:
                    await strategy.pause()
            return True
        except Exception as e:
            logger.error(f"Error pausing strategies: {e}")
            return False

    async def resume_all_strategies(self) -> bool:
        """Resume all paused strategies"""
        try:
            for strategy in self.strategies.values():
                if strategy.state == StrategyState.PAUSED:
                    await strategy.resume()
            return True
        except Exception as e:
            logger.error(f"Error resuming strategies: {e}")
            return False

    async def get_stats(self) -> Dict[str, Any]:
        """Get strategy manager statistics"""
        return {
            'running': self.running,
            'total_strategies': len(self.strategies),
            'active_strategies': len(await self.get_active_strategies()),
            'strategies_by_type': {
                strategy_type.value: len(strategy_ids)
                for strategy_type, strategy_ids in self.strategies_by_type.items()
            }
        }

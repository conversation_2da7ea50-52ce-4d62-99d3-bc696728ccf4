"""
Strategy Manager - Central management for all trading strategies
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Type
from collections import defaultdict

from .base_strategy import BaseStrategy, StrategyType, StrategyState, StrategyResult
from .momentum_strategies import MomentumStrategy, TrendFollowingStrategy, BreakoutStrategy
from .mean_reversion_strategies import MeanReversionStrategy, PairsTradingStrategy
from .volatility_strategies import VolatilityExploitationStrategy
from .carry_strategies import CarryTradeStrategy
from .adaptive_strategies import AdaptiveStrategy

logger = logging.getLogger(__name__)


class StrategyManager:
    """
    Central manager for all trading strategies.
    
    Responsibilities:
    - Strategy lifecycle management
    - Strategy registration and discovery
    - Market data distribution
    - Signal aggregation and coordination
    - Performance monitoring
    - Strategy optimization
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Strategy storage
        self.strategies: Dict[str, BaseStrategy] = {}
        self.strategies_by_type: Dict[StrategyType, List[str]] = defaultdict(list)
        self.strategies_by_symbol: Dict[str, List[str]] = defaultdict(list)
        
        # Strategy registry
        self.strategy_classes: Dict[str, Type[BaseStrategy]] = {
            'momentum': MomentumStrategy,
            'trend_following': TrendFollowingStrategy,
            'breakout': BreakoutStrategy,
            'mean_reversion': MeanReversionStrategy,
            'pairs_trading': PairsTradingStrategy,
            'volatility_exploitation': VolatilityExploitationStrategy,
            'carry_trade': CarryTradeStrategy,
            'adaptive': AdaptiveStrategy
        }

        # AI-powered strategies
        self.ai_strategies: Dict[str, Dict[str, Any]] = {}
        self.ai_models: Dict[str, Any] = {}

        # Configuration
        self.max_strategies = self.config.get('max_strategies', 100)
        self.update_interval = self.config.get('update_interval', 60)  # seconds
        self.signal_aggregation_method = self.config.get('signal_aggregation', 'weighted_average')
        self.ai_enabled = self.config.get('ai_enabled', True)
        
        # State
        self.initialized = False
        self.running = False

        # AI integration
        self.ollama_hub = None
        self.agent_manager = None

        # Background tasks
        self.monitoring_task: Optional[asyncio.Task] = None
        self.optimization_task: Optional[asyncio.Task] = None
        self.ai_optimization_task: Optional[asyncio.Task] = None
        
        # Performance tracking
        self.global_metrics = {
            'total_strategies': 0,
            'active_strategies': 0,
            'total_signals': 0,
            'successful_signals': 0,
            'signal_accuracy': 0.0,
            'avg_strategy_performance': 0.0
        }
        
    async def initialize(self) -> bool:
        """Initialize the strategy manager"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Strategy Manager...")
            
            # Load strategy configurations
            await self._load_strategy_configs()
            
            # Initialize default strategies if configured
            await self._initialize_default_strategies()
            
            self.initialized = True
            logger.info("✓ Strategy Manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Strategy Manager: {e}")
            return False
    
    async def start(self) -> bool:
        """Start the strategy manager"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        if self.running:
            return True
            
        try:
            logger.info("Starting Strategy Manager...")
            self.running = True
            
            # Start all strategies
            for strategy in self.strategies.values():
                await strategy.start()
            
            # Start background tasks
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            self.optimization_task = asyncio.create_task(self._optimization_loop())
            
            logger.info("✓ Strategy Manager started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Strategy Manager: {e}")
            return False

    async def set_integration_points(self,
                                   risk_manager=None,
                                   execution_engine=None,
                                   portfolio_manager=None,
                                   market_data_manager=None,
                                   message_broker=None):
        """Set integration points with other systems"""
        self.risk_manager = risk_manager
        self.execution_engine = execution_engine
        self.portfolio_manager = portfolio_manager
        self.market_data_manager = market_data_manager
        self.message_broker = message_broker

        # Pass integration points to all strategies
        for strategy in self.strategies.values():
            if hasattr(strategy, 'set_integration_points'):
                await strategy.set_integration_points(
                    risk_manager=risk_manager,
                    execution_engine=execution_engine,
                    portfolio_manager=portfolio_manager,
                    market_data_manager=market_data_manager,
                    message_broker=message_broker
                )

        logger.info("Strategy Manager integration points configured")

    async def set_database_coordinator(self, database_coordinator):
        """Set database coordinator for data persistence"""
        self.database_coordinator = database_coordinator

        # Pass database coordinator to all strategies
        for strategy in self.strategies.values():
            if hasattr(strategy, 'set_database_coordinator'):
                await strategy.set_database_coordinator(database_coordinator)

        logger.info("Strategy Manager database coordinator configured")

    async def stop(self) -> bool:
        """Stop the strategy manager"""
        if not self.running:
            return True
            
        try:
            logger.info("Stopping Strategy Manager...")
            self.running = False
            
            # Cancel background tasks
            if self.monitoring_task:
                self.monitoring_task.cancel()
            if self.optimization_task:
                self.optimization_task.cancel()
            
            # Stop all strategies
            for strategy in self.strategies.values():
                await strategy.stop()
            
            logger.info("✓ Strategy Manager stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping Strategy Manager: {e}")
            return False

    async def set_analytics_engine(self, analytics_engine):
        """Set analytics engine for advanced analytics"""
        self.analytics_engine = analytics_engine

        # Pass analytics engine to all strategies
        for strategy in self.strategies.values():
            if hasattr(strategy, 'set_analytics_engine'):
                await strategy.set_analytics_engine(analytics_engine)

        logger.info("Strategy Manager analytics engine configured")

    async def set_ai_integration(self, ollama_hub, agent_manager):
        """Set AI integration components"""
        self.ollama_hub = ollama_hub
        self.agent_manager = agent_manager

        # Pass AI components to all strategies
        for strategy in self.strategies.values():
            if hasattr(strategy, 'set_ai_integration'):
                await strategy.set_ai_integration(ollama_hub, agent_manager)

        logger.info("Strategy Manager AI integration configured")

    async def create_strategy(self,
                             strategy_type: str,
                             name: str = None,
                             config: Dict[str, Any] = None) -> Optional[str]:
        """Create a new strategy with optional AI integration"""
        try:
            if len(self.strategies) >= self.max_strategies:
                logger.error(f"Maximum strategies ({self.max_strategies}) reached")
                return None

            if strategy_type not in self.strategy_classes:
                logger.error(f"Unknown strategy type: {strategy_type}")
                return None

            # Prepare configuration
            strategy_config = config or {}

            # Check if this should be an AI-powered strategy
            ai_enabled = strategy_config.get('ai_enabled', False)
            ai_model = strategy_config.get('ai_model')

            # Create strategy instance
            strategy_class = self.strategy_classes[strategy_type]
            strategy = strategy_class(
                name=name,
                config=strategy_config
            )

            # Set up AI integration if enabled
            if ai_enabled and self.ai_enabled and self.ollama_hub:
                await self._setup_ai_strategy(strategy, ai_model, strategy_config)

            # Initialize strategy
            if await strategy.initialize():
                # Register strategy
                self.strategies[strategy.strategy_id] = strategy

                # Track AI strategies separately
                if ai_enabled:
                    self.ai_strategies[strategy.strategy_id] = {
                        'ai_model': ai_model,
                        'ai_config': strategy_config,
                        'performance_metrics': {},
                        'decision_history': []
                    }

                # Update indices
                if strategy.strategy_type:
                    self.strategies_by_type[strategy.strategy_type].append(strategy.strategy_id)

                for symbol in strategy.symbols:
                    self.strategies_by_symbol[symbol].append(strategy.strategy_id)

                # Start strategy if manager is running
                if self.running:
                    await strategy.start()

                ai_suffix = " (AI-powered)" if ai_enabled else ""
                logger.info(f"✓ Created strategy {strategy.name} ({strategy_type}){ai_suffix}")
                return strategy.strategy_id
            else:
                logger.error(f"Failed to initialize strategy {strategy.name}")
                return None

        except Exception as e:
            logger.error(f"Error creating strategy: {e}")
            return None
    
    async def remove_strategy(self, strategy_id: str) -> bool:
        """Remove a strategy"""
        try:
            if strategy_id not in self.strategies:
                logger.error(f"Strategy {strategy_id} not found")
                return False
            
            strategy = self.strategies[strategy_id]
            
            # Stop strategy
            await strategy.stop()
            
            # Remove from indices
            if strategy.strategy_type:
                if strategy_id in self.strategies_by_type[strategy.strategy_type]:
                    self.strategies_by_type[strategy.strategy_type].remove(strategy_id)
            
            for symbol in strategy.symbols:
                if strategy_id in self.strategies_by_symbol[symbol]:
                    self.strategies_by_symbol[symbol].remove(strategy_id)
            
            # Remove from main storage
            del self.strategies[strategy_id]
            
            logger.info(f"✓ Removed strategy {strategy.name}")
            return True
            
        except Exception as e:
            logger.error(f"Error removing strategy {strategy_id}: {e}")
            return False

    async def start_strategy(self, strategy_id: str) -> bool:
        """Start a specific strategy"""
        try:
            if strategy_id not in self.strategies:
                logger.error(f"Strategy {strategy_id} not found")
                return False

            strategy = self.strategies[strategy_id]
            await strategy.start()
            logger.info(f"✓ Started strategy {strategy.name}")
            return True

        except Exception as e:
            logger.error(f"Error starting strategy {strategy_id}: {e}")
            return False
    
    async def update_market_data(self, symbol: str, data) -> None:
        """Update market data for all strategies tracking this symbol"""
        try:
            strategy_ids = self.strategies_by_symbol.get(symbol, [])
            
            for strategy_id in strategy_ids:
                if strategy_id in self.strategies:
                    strategy = self.strategies[strategy_id]
                    await strategy.update_market_data(symbol, data)
                    
        except Exception as e:
            logger.error(f"Error updating market data for {symbol}: {e}")
    
    async def generate_signals(self, symbol: str) -> List[StrategyResult]:
        """Generate signals from all strategies for a symbol"""
        signals = []
        
        try:
            strategy_ids = self.strategies_by_symbol.get(symbol, [])
            
            for strategy_id in strategy_ids:
                if strategy_id in self.strategies:
                    strategy = self.strategies[strategy_id]
                    signal = await strategy.generate_signal(symbol)
                    if signal:
                        signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating signals for {symbol}: {e}")
            return []
    
    async def aggregate_signals(self, signals: List[StrategyResult]) -> Optional[StrategyResult]:
        """Aggregate multiple signals into a single signal"""
        if not signals:
            return None
            
        try:
            if self.signal_aggregation_method == 'weighted_average':
                return await self._weighted_average_aggregation(signals)
            elif self.signal_aggregation_method == 'majority_vote':
                return await self._majority_vote_aggregation(signals)
            elif self.signal_aggregation_method == 'confidence_weighted':
                return await self._confidence_weighted_aggregation(signals)
            else:
                # Default to simple average
                return await self._simple_average_aggregation(signals)
                
        except Exception as e:
            logger.error(f"Error aggregating signals: {e}")
            return None
    
    def get_strategy_status(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific strategy"""
        if strategy_id in self.strategies:
            return self.strategies[strategy_id].get_status()
        return None
    
    def get_all_strategies_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all strategies"""
        return {
            strategy_id: strategy.get_status()
            for strategy_id, strategy in self.strategies.items()
        }
    
    def get_global_metrics(self) -> Dict[str, Any]:
        """Get global strategy manager metrics"""
        return {
            **self.global_metrics,
            'total_strategies': len(self.strategies),
            'active_strategies': sum(1 for s in self.strategies.values() if s.running),
            'strategies_by_type': {
                strategy_type.value: len(strategy_ids)
                for strategy_type, strategy_ids in self.strategies_by_type.items()
            }
        }
    
    # Private methods
    
    async def _load_strategy_configs(self):
        """Load strategy configurations"""
        # Implementation for loading strategy configs from files/database
        pass
    
    async def _initialize_default_strategies(self):
        """Initialize default strategies based on configuration"""
        default_strategies = self.config.get('default_strategies', [])
        
        for strategy_config in default_strategies:
            await self.create_strategy(
                strategy_type=strategy_config['type'],
                name=strategy_config.get('name'),
                config=strategy_config.get('config', {})
            )
    
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.running:
            try:
                await asyncio.sleep(self.update_interval)
                if self.running:
                    await self._update_global_metrics()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
    
    async def _optimization_loop(self):
        """Background optimization loop"""
        while self.running:
            try:
                await asyncio.sleep(3600)  # Run every hour
                if self.running:
                    await self._optimize_strategies()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in optimization loop: {e}")
    
    async def _update_global_metrics(self):
        """Update global performance metrics"""
        try:
            total_signals = sum(len(s.signal_history) for s in self.strategies.values())
            total_return = sum(s.metrics.total_return for s in self.strategies.values())
            
            self.global_metrics.update({
                'total_signals': total_signals,
                'avg_strategy_performance': total_return / len(self.strategies) if self.strategies else 0.0
            })
            
        except Exception as e:
            logger.error(f"Error updating global metrics: {e}")
    
    async def _optimize_strategies(self):
        """Optimize strategy parameters"""
        # Implementation for strategy optimization
        pass
    
    async def _weighted_average_aggregation(self, signals: List[StrategyResult]) -> StrategyResult:
        """Aggregate signals using weighted average"""
        # Implementation for weighted average aggregation
        pass
    
    async def _majority_vote_aggregation(self, signals: List[StrategyResult]) -> StrategyResult:
        """Aggregate signals using majority vote"""
        # Implementation for majority vote aggregation
        pass
    
    async def _confidence_weighted_aggregation(self, signals: List[StrategyResult]) -> StrategyResult:
        """Aggregate signals using confidence weighting"""
        # Implementation for confidence weighted aggregation
        pass
    
    async def _simple_average_aggregation(self, signals: List[StrategyResult]) -> StrategyResult:
        """Aggregate signals using simple average"""
        # Implementation for simple average aggregation
        pass

    async def get_active_strategies(self) -> List[str]:
        """Get list of active strategy IDs"""
        return [
            strategy_id for strategy_id, strategy in self.strategies.items()
            if strategy.state == StrategyState.RUNNING
        ]

    async def pause_all_strategies(self) -> bool:
        """Pause all active strategies"""
        try:
            for strategy in self.strategies.values():
                if strategy.state == StrategyState.RUNNING:
                    await strategy.pause()
            return True
        except Exception as e:
            logger.error(f"Error pausing strategies: {e}")
            return False

    async def resume_all_strategies(self) -> bool:
        """Resume all paused strategies"""
        try:
            for strategy in self.strategies.values():
                if strategy.state == StrategyState.PAUSED:
                    await strategy.resume()
            return True
        except Exception as e:
            logger.error(f"Error resuming strategies: {e}")
            return False

    async def get_stats(self) -> Dict[str, Any]:
        """Get strategy manager statistics"""
        return {
            'running': self.running,
            'total_strategies': len(self.strategies),
            'active_strategies': len(await self.get_active_strategies()),
            'ai_strategies': len(self.ai_strategies),
            'strategies_by_type': {
                strategy_type.value: len(strategy_ids)
                for strategy_type, strategy_ids in self.strategies_by_type.items()
            }
        }

    async def _setup_ai_strategy(self, strategy, ai_model: str, config: Dict[str, Any]):
        """Set up AI integration for a strategy"""
        try:
            # Deploy AI model for this strategy
            model_instance = await self.ollama_hub.deploy_model_for_agent(
                agent_name=f"strategy_{strategy.strategy_id}",
                role="strategy_developer",
                model_name=ai_model
            )

            if model_instance:
                # Store model instance
                self.ai_models[strategy.strategy_id] = model_instance

                # Configure strategy for AI
                strategy.ai_enabled = True
                strategy.ai_model = model_instance
                strategy.ai_config = config

                logger.info(f"✓ AI integration set up for strategy {strategy.name} with model {ai_model}")
            else:
                logger.warning(f"Failed to deploy AI model {ai_model} for strategy {strategy.name}")

        except Exception as e:
            logger.error(f"Error setting up AI strategy: {e}")

    async def get_ai_strategy_performance(self, strategy_id: str) -> Dict[str, Any]:
        """Get AI-specific performance metrics for a strategy"""
        try:
            if strategy_id not in self.ai_strategies:
                return {}

            ai_strategy = self.ai_strategies[strategy_id]
            strategy = self.strategies.get(strategy_id)

            if not strategy:
                return {}

            # Calculate AI-specific metrics
            decision_history = ai_strategy.get('decision_history', [])

            ai_metrics = {
                'ai_model': ai_strategy.get('ai_model'),
                'total_ai_decisions': len(decision_history),
                'ai_decision_accuracy': 0.0,
                'avg_ai_confidence': 0.0,
                'ai_response_time': 0.0,
                'model_performance': {}
            }

            if decision_history:
                # Calculate accuracy
                correct_decisions = sum(1 for d in decision_history if d.get('correct', False))
                ai_metrics['ai_decision_accuracy'] = correct_decisions / len(decision_history)

                # Calculate average confidence
                confidences = [d.get('confidence', 0.0) for d in decision_history]
                ai_metrics['avg_ai_confidence'] = sum(confidences) / len(confidences)

                # Calculate average response time
                response_times = [d.get('response_time', 0.0) for d in decision_history]
                ai_metrics['ai_response_time'] = sum(response_times) / len(response_times)

            # Get model performance if available
            if strategy_id in self.ai_models:
                model_instance = self.ai_models[strategy_id]
                ai_metrics['model_performance'] = model_instance.get_performance_metrics()

            return ai_metrics

        except Exception as e:
            logger.error(f"Error getting AI strategy performance for {strategy_id}: {e}")
            return {}

    async def optimize_ai_strategies(self) -> Dict[str, Any]:
        """Optimize AI-powered strategies"""
        try:
            logger.info("🧠 Optimizing AI strategies...")

            optimization_results = {}

            for strategy_id, ai_strategy in self.ai_strategies.items():
                try:
                    strategy = self.strategies.get(strategy_id)
                    if not strategy:
                        continue

                    # Get current performance
                    performance = await self.get_ai_strategy_performance(strategy_id)

                    # Check if optimization is needed
                    accuracy = performance.get('ai_decision_accuracy', 1.0)
                    response_time = performance.get('ai_response_time', 0.0)

                    optimization_needed = False
                    optimization_actions = []

                    # Check accuracy threshold
                    if accuracy < 0.7:  # Less than 70% accuracy
                        optimization_needed = True
                        optimization_actions.append('model_switch')

                    # Check response time threshold
                    if response_time > 5.0:  # More than 5 seconds
                        optimization_needed = True
                        optimization_actions.append('model_optimization')

                    if optimization_needed:
                        # Perform optimization
                        optimization_result = await self._optimize_strategy_ai(
                            strategy_id, optimization_actions
                        )
                        optimization_results[strategy_id] = optimization_result
                    else:
                        optimization_results[strategy_id] = {
                            'optimized': False,
                            'reason': 'performance_acceptable'
                        }

                except Exception as e:
                    logger.error(f"Error optimizing AI strategy {strategy_id}: {e}")
                    optimization_results[strategy_id] = {
                        'optimized': False,
                        'error': str(e)
                    }

            return {
                'success': True,
                'optimized_strategies': len([r for r in optimization_results.values() if r.get('optimized', False)]),
                'total_ai_strategies': len(self.ai_strategies),
                'optimization_results': optimization_results
            }

        except Exception as e:
            logger.error(f"Error optimizing AI strategies: {e}")
            return {'success': False, 'error': str(e)}

    async def _optimize_strategy_ai(self, strategy_id: str, actions: List[str]) -> Dict[str, Any]:
        """Optimize AI for a specific strategy"""
        try:
            optimization_result = {
                'optimized': False,
                'actions_taken': [],
                'improvements': {}
            }

            strategy = self.strategies.get(strategy_id)
            ai_strategy = self.ai_strategies.get(strategy_id)

            if not strategy or not ai_strategy:
                return optimization_result

            for action in actions:
                if action == 'model_switch':
                    # Try to switch to a better model
                    current_model = ai_strategy.get('ai_model')

                    # Get alternative models for strategy development
                    alternative_models = [
                        'phi4-reasoning:plus',
                        'exaone-deep:32b',
                        'granite3.3:8b'
                    ]

                    for alt_model in alternative_models:
                        if alt_model != current_model:
                            # Try to deploy alternative model
                            new_model_instance = await self.ollama_hub.deploy_model_for_agent(
                                agent_name=f"strategy_{strategy_id}_optimized",
                                role="strategy_developer",
                                model_name=alt_model
                            )

                            if new_model_instance:
                                # Switch to new model
                                old_model = self.ai_models.get(strategy_id)
                                self.ai_models[strategy_id] = new_model_instance
                                ai_strategy['ai_model'] = alt_model
                                strategy.ai_model = new_model_instance

                                optimization_result['actions_taken'].append(f'switched_to_{alt_model}')
                                optimization_result['optimized'] = True

                                # Clean up old model
                                if old_model:
                                    await self.ollama_hub.deployment_manager.undeploy_model(
                                        f"strategy_{strategy_id}"
                                    )

                                break

                elif action == 'model_optimization':
                    # Optimize model parameters
                    if strategy_id in self.ai_models:
                        model_instance = self.ai_models[strategy_id]

                        # Adjust model parameters for better performance
                        optimized_config = {
                            'temperature': 0.3,  # Lower temperature for more consistent decisions
                            'top_p': 0.8,
                            'max_tokens': 1024  # Shorter responses for faster execution
                        }

                        # Update model configuration
                        model_instance.config.update(optimized_config)
                        ai_strategy['ai_config'].update(optimized_config)

                        optimization_result['actions_taken'].append('optimized_parameters')
                        optimization_result['optimized'] = True

            return optimization_result

        except Exception as e:
            logger.error(f"Error optimizing strategy AI for {strategy_id}: {e}")
            return {'optimized': False, 'error': str(e)}

    async def get_ai_strategies_summary(self) -> Dict[str, Any]:
        """Get summary of all AI strategies"""
        try:
            summary = {
                'total_ai_strategies': len(self.ai_strategies),
                'ai_models_in_use': {},
                'performance_summary': {
                    'avg_accuracy': 0.0,
                    'avg_response_time': 0.0,
                    'total_decisions': 0
                },
                'strategies': {}
            }

            total_accuracy = 0.0
            total_response_time = 0.0
            total_decisions = 0
            strategies_with_data = 0

            for strategy_id, ai_strategy in self.ai_strategies.items():
                strategy = self.strategies.get(strategy_id)
                if not strategy:
                    continue

                # Get performance metrics
                performance = await self.get_ai_strategy_performance(strategy_id)

                # Track model usage
                ai_model = ai_strategy.get('ai_model', 'unknown')
                summary['ai_models_in_use'][ai_model] = summary['ai_models_in_use'].get(ai_model, 0) + 1

                # Aggregate performance
                accuracy = performance.get('ai_decision_accuracy', 0.0)
                response_time = performance.get('ai_response_time', 0.0)
                decisions = performance.get('total_ai_decisions', 0)

                if decisions > 0:
                    total_accuracy += accuracy
                    total_response_time += response_time
                    total_decisions += decisions
                    strategies_with_data += 1

                # Store individual strategy info
                summary['strategies'][strategy_id] = {
                    'name': strategy.name,
                    'type': strategy.strategy_type.value if strategy.strategy_type else 'unknown',
                    'ai_model': ai_model,
                    'performance': performance
                }

            # Calculate averages
            if strategies_with_data > 0:
                summary['performance_summary']['avg_accuracy'] = total_accuracy / strategies_with_data
                summary['performance_summary']['avg_response_time'] = total_response_time / strategies_with_data

            summary['performance_summary']['total_decisions'] = total_decisions

            return summary

        except Exception as e:
            logger.error(f"Error getting AI strategies summary: {e}")
            return {}

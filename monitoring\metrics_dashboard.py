"""
Metrics Dashboard - Real-time Performance Metrics Visualization

Interactive dashboard for visualizing system performance metrics,
alerts, and business KPIs in real-time.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.layout import Layout
from rich.live import Live
from rich.text import Text
from rich.columns import Columns
from rich.progress import Progress, BarColumn, TextColumn
from rich.tree import Tree

from .performance_monitor import PerformanceMonitor, PerformanceMetric


class MetricsDashboard:
    """
    Real-time metrics dashboard with rich terminal interface.
    
    Features:
    - Real-time metrics display
    - Alert status monitoring
    - Performance trends
    - System health overview
    - Interactive navigation
    """
    
    def __init__(self, performance_monitor: PerformanceMonitor):
        self.performance_monitor = performance_monitor
        self.console = Console()
        
        # Dashboard state
        self.current_view = 'overview'
        self.refresh_interval = 2  # seconds
        self.running = False
        
        # Display settings
        self.show_details = False
        self.selected_metric = None
        
        # Views
        self.views = {
            'overview': self.create_overview_layout,
            'system': self.create_system_layout,
            'application': self.create_application_layout,
            'business': self.create_business_layout,
            'alerts': self.create_alerts_layout,
            'trends': self.create_trends_layout
        }
    
    async def start_dashboard(self):
        """Start the real-time dashboard"""
        self.running = True
        
        with Live(self.create_layout(), refresh_per_second=1/self.refresh_interval) as live:
            while self.running:
                try:
                    layout = self.create_layout()
                    live.update(layout)
                    await asyncio.sleep(self.refresh_interval)
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    self.console.print(f"[red]Dashboard error: {e}[/red]")
                    await asyncio.sleep(self.refresh_interval)
        
        self.running = False
    
    def stop_dashboard(self):
        """Stop the dashboard"""
        self.running = False
    
    def create_layout(self) -> Layout:
        """Create the main dashboard layout"""
        layout = Layout()
        
        # Split into header, body, and footer
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="body"),
            Layout(name="footer", size=3)
        )
        
        # Header
        layout["header"].update(self.create_header())
        
        # Body - use current view
        view_func = self.views.get(self.current_view, self.create_overview_layout)
        layout["body"].update(view_func())
        
        # Footer
        layout["footer"].update(self.create_footer())
        
        return layout
    
    def create_header(self) -> Panel:
        """Create dashboard header"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Get system status
        metrics = self.performance_monitor.get_current_metrics()
        active_alerts = self.performance_monitor.get_active_alerts()
        
        status_color = "red" if active_alerts else "green"
        status_text = f"{len(active_alerts)} alerts" if active_alerts else "All systems normal"
        
        header_text = (
            f"[bold blue]Advanced Ollama Trading Agents - Performance Dashboard[/bold blue]\n"
            f"Time: {current_time} | View: {self.current_view.title()} | "
            f"Status: [{status_color}]{status_text}[/{status_color}]"
        )
        
        return Panel(header_text, border_style="blue")
    
    def create_footer(self) -> Panel:
        """Create dashboard footer"""
        footer_text = (
            "[dim]Navigation: [1]Overview [2]System [3]Application [4]Business [5]Alerts [6]Trends | "
            "[Q]uit [R]efresh[/dim]"
        )
        return Panel(footer_text, border_style="dim")
    
    def create_overview_layout(self) -> Layout:
        """Create overview layout"""
        layout = Layout()
        layout.split_row(
            Layout(name="left"),
            Layout(name="right")
        )
        
        # Left side - key metrics
        layout["left"].split_column(
            Layout(self.create_key_metrics_panel(), size=8),
            Layout(self.create_system_health_panel())
        )
        
        # Right side - alerts and trends
        layout["right"].split_column(
            Layout(self.create_alerts_summary_panel(), size=8),
            Layout(self.create_trends_summary_panel())
        )
        
        return layout
    
    def create_system_layout(self) -> Layout:
        """Create system metrics layout"""
        layout = Layout()
        layout.split_column(
            Layout(self.create_system_metrics_table(), size=10),
            Layout(self.create_system_charts())
        )
        return layout
    
    def create_application_layout(self) -> Layout:
        """Create application metrics layout"""
        layout = Layout()
        layout.split_column(
            Layout(self.create_application_metrics_table(), size=10),
            Layout(self.create_application_charts())
        )
        return layout
    
    def create_business_layout(self) -> Layout:
        """Create business metrics layout"""
        layout = Layout()
        layout.split_column(
            Layout(self.create_business_metrics_table(), size=10),
            Layout(self.create_business_charts())
        )
        return layout
    
    def create_alerts_layout(self) -> Layout:
        """Create alerts layout"""
        return Layout(self.create_alerts_detail_panel())
    
    def create_trends_layout(self) -> Layout:
        """Create trends layout"""
        return Layout(self.create_trends_detail_panel())
    
    def create_key_metrics_panel(self) -> Panel:
        """Create key metrics panel"""
        metrics = self.performance_monitor.get_current_metrics()
        
        table = Table(title="Key Metrics")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        table.add_column("Status", style="yellow")
        
        # System metrics
        cpu_metric = metrics.get('system.cpu_usage')
        if cpu_metric:
            status = "🔴" if cpu_metric.value > 80 else "🟡" if cpu_metric.value > 60 else "🟢"
            table.add_row("CPU Usage", f"{cpu_metric.value:.1f}%", status)
        
        memory_metric = metrics.get('system.memory_usage')
        if memory_metric:
            status = "🔴" if memory_metric.value > 85 else "🟡" if memory_metric.value > 70 else "🟢"
            table.add_row("Memory Usage", f"{memory_metric.value:.1f}%", status)
        
        # Application metrics
        response_time_metric = metrics.get('app.response_time')
        if response_time_metric:
            status = "🔴" if response_time_metric.value > 2.0 else "🟡" if response_time_metric.value > 1.0 else "🟢"
            table.add_row("Response Time", f"{response_time_metric.value:.2f}s", status)
        
        throughput_metric = metrics.get('app.throughput')
        if throughput_metric:
            status = "🟢" if throughput_metric.value > 50 else "🟡" if throughput_metric.value > 20 else "🔴"
            table.add_row("Throughput", f"{throughput_metric.value:.1f} ops/s", status)
        
        return Panel(table, title="Key Performance Indicators", border_style="green")
    
    def create_system_health_panel(self) -> Panel:
        """Create system health panel"""
        metrics = self.performance_monitor.get_current_metrics()
        
        health_items = []
        
        # Check CPU
        cpu_metric = metrics.get('system.cpu_usage')
        if cpu_metric:
            if cpu_metric.value > 80:
                health_items.append("🔴 CPU usage high")
            elif cpu_metric.value > 60:
                health_items.append("🟡 CPU usage elevated")
            else:
                health_items.append("🟢 CPU usage normal")
        
        # Check Memory
        memory_metric = metrics.get('system.memory_usage')
        if memory_metric:
            if memory_metric.value > 85:
                health_items.append("🔴 Memory usage critical")
            elif memory_metric.value > 70:
                health_items.append("🟡 Memory usage high")
            else:
                health_items.append("🟢 Memory usage normal")
        
        # Check Application
        error_rate_metric = metrics.get('app.error_rate')
        if error_rate_metric:
            if error_rate_metric.value > 5:
                health_items.append("🔴 High error rate")
            elif error_rate_metric.value > 1:
                health_items.append("🟡 Elevated error rate")
            else:
                health_items.append("🟢 Error rate normal")
        
        health_text = "\n".join(health_items) if health_items else "🟢 All systems operational"
        
        return Panel(health_text, title="System Health", border_style="blue")
    
    def create_alerts_summary_panel(self) -> Panel:
        """Create alerts summary panel"""
        active_alerts = self.performance_monitor.get_active_alerts()
        
        if not active_alerts:
            alert_text = "🟢 No active alerts"
        else:
            alert_lines = []
            for alert_name, alert in active_alerts.items():
                severity = alert['rule']['severity']
                icon = "🔴" if severity == 'critical' else "🟡" if severity == 'warning' else "🔵"
                alert_lines.append(f"{icon} {alert_name}")
            
            alert_text = "\n".join(alert_lines[:5])  # Show max 5 alerts
            if len(active_alerts) > 5:
                alert_text += f"\n... and {len(active_alerts) - 5} more"
        
        return Panel(alert_text, title="Active Alerts", border_style="red" if active_alerts else "green")
    
    def create_trends_summary_panel(self) -> Panel:
        """Create trends summary panel"""
        # Get metrics statistics for the last hour
        summary = self.performance_monitor.get_metrics_summary(timedelta(hours=1))
        
        trend_lines = []
        
        # CPU trend
        cpu_stats = summary.get('system.cpu_usage')
        if cpu_stats:
            trend = "📈" if cpu_stats['mean'] > 70 else "📊" if cpu_stats['mean'] > 40 else "📉"
            trend_lines.append(f"{trend} CPU: {cpu_stats['mean']:.1f}% avg")
        
        # Memory trend
        memory_stats = summary.get('system.memory_usage')
        if memory_stats:
            trend = "📈" if memory_stats['mean'] > 70 else "📊" if memory_stats['mean'] > 40 else "📉"
            trend_lines.append(f"{trend} Memory: {memory_stats['mean']:.1f}% avg")
        
        # Response time trend
        response_stats = summary.get('app.response_time')
        if response_stats:
            trend = "📈" if response_stats['mean'] > 1.0 else "📊" if response_stats['mean'] > 0.5 else "📉"
            trend_lines.append(f"{trend} Response: {response_stats['mean']:.2f}s avg")
        
        trend_text = "\n".join(trend_lines) if trend_lines else "No trend data available"
        
        return Panel(trend_text, title="Performance Trends (1h)", border_style="yellow")
    
    def create_system_metrics_table(self) -> Panel:
        """Create detailed system metrics table"""
        metrics = self.performance_monitor.get_current_metrics()
        
        table = Table(title="System Metrics")
        table.add_column("Metric", style="cyan")
        table.add_column("Current", style="green")
        table.add_column("Unit", style="yellow")
        table.add_column("Timestamp", style="dim")
        
        system_metrics = {k: v for k, v in metrics.items() if k.startswith('system.')}
        
        for name, metric in system_metrics.items():
            display_name = name.replace('system.', '').replace('_', ' ').title()
            timestamp = metric.timestamp.strftime("%H:%M:%S")
            table.add_row(display_name, f"{metric.value:.2f}", metric.unit, timestamp)
        
        return Panel(table, border_style="blue")
    
    def create_application_metrics_table(self) -> Panel:
        """Create detailed application metrics table"""
        metrics = self.performance_monitor.get_current_metrics()
        
        table = Table(title="Application Metrics")
        table.add_column("Metric", style="cyan")
        table.add_column("Current", style="green")
        table.add_column("Unit", style="yellow")
        table.add_column("Timestamp", style="dim")
        
        app_metrics = {k: v for k, v in metrics.items() if k.startswith('app.')}
        
        for name, metric in app_metrics.items():
            display_name = name.replace('app.', '').replace('_', ' ').title()
            timestamp = metric.timestamp.strftime("%H:%M:%S")
            table.add_row(display_name, f"{metric.value:.2f}", metric.unit, timestamp)
        
        return Panel(table, border_style="green")
    
    def create_business_metrics_table(self) -> Panel:
        """Create detailed business metrics table"""
        metrics = self.performance_monitor.get_current_metrics()
        
        table = Table(title="Business Metrics")
        table.add_column("Metric", style="cyan")
        table.add_column("Current", style="green")
        table.add_column("Unit", style="yellow")
        table.add_column("Timestamp", style="dim")
        
        business_metrics = {k: v for k, v in metrics.items() if k.startswith('business.')}
        
        for name, metric in business_metrics.items():
            display_name = name.replace('business.', '').replace('_', ' ').title()
            timestamp = metric.timestamp.strftime("%H:%M:%S")
            
            # Format value based on metric type
            if 'value' in name or 'pnl' in name:
                value_str = f"${metric.value:,.2f}"
            else:
                value_str = f"{metric.value:.0f}"
            
            table.add_row(display_name, value_str, metric.unit, timestamp)
        
        return Panel(table, border_style="yellow")
    
    def create_system_charts(self) -> Panel:
        """Create system performance charts (ASCII)"""
        # This would create ASCII charts of system metrics over time
        chart_text = "📊 System performance charts would be displayed here\n"
        chart_text += "CPU, Memory, Disk usage trends over time"
        return Panel(chart_text, title="System Charts", border_style="blue")
    
    def create_application_charts(self) -> Panel:
        """Create application performance charts (ASCII)"""
        chart_text = "📊 Application performance charts would be displayed here\n"
        chart_text += "Response time, throughput, error rate trends"
        return Panel(chart_text, title="Application Charts", border_style="green")
    
    def create_business_charts(self) -> Panel:
        """Create business metrics charts (ASCII)"""
        chart_text = "📊 Business metrics charts would be displayed here\n"
        chart_text += "Portfolio value, P&L, trading activity trends"
        return Panel(chart_text, title="Business Charts", border_style="yellow")
    
    def create_alerts_detail_panel(self) -> Panel:
        """Create detailed alerts panel"""
        active_alerts = self.performance_monitor.get_active_alerts()
        
        if not active_alerts:
            content = Text("🟢 No active alerts\n\nAll systems are operating normally.", style="green")
        else:
            tree = Tree("🚨 Active Alerts")
            
            for alert_name, alert in active_alerts.items():
                rule = alert['rule']
                metric = alert['metric']
                
                severity_icon = "🔴" if rule['severity'] == 'critical' else "🟡"
                alert_node = tree.add(f"{severity_icon} {alert_name}")
                
                alert_node.add(f"Metric: {metric.name}")
                alert_node.add(f"Value: {metric.value:.2f} {metric.unit}")
                alert_node.add(f"Threshold: {rule['threshold']}")
                alert_node.add(f"Triggered: {alert['triggered_at'].strftime('%H:%M:%S')}")
            
            content = tree
        
        return Panel(content, title="Alert Details", border_style="red" if active_alerts else "green")
    
    def create_trends_detail_panel(self) -> Panel:
        """Create detailed trends panel"""
        summary = self.performance_monitor.get_metrics_summary(timedelta(hours=1))
        
        if not summary:
            content = Text("No trend data available", style="dim")
        else:
            tree = Tree("📈 Performance Trends (Last Hour)")
            
            for metric_name, stats in summary.items():
                display_name = metric_name.replace('_', ' ').title()
                metric_node = tree.add(f"📊 {display_name}")
                
                metric_node.add(f"Average: {stats['mean']:.2f}")
                metric_node.add(f"Min: {stats['min']:.2f}")
                metric_node.add(f"Max: {stats['max']:.2f}")
                metric_node.add(f"95th percentile: {stats['p95']:.2f}")
                metric_node.add(f"Samples: {stats['count']}")
            
            content = tree
        
        return Panel(content, title="Trend Analysis", border_style="yellow")

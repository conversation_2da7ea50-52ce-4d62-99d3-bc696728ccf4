"""
Risk Parity Optimizer - Equal risk contribution portfolio optimization
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class RiskParityOptimizer:
    """
    Risk Parity portfolio optimization that allocates capital such that
    each asset contributes equally to the portfolio's total risk.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Model parameters
        self.target_risk_contributions: Optional[np.ndarray] = None
        self.risk_budget_tolerance = 1e-6
        self.max_iterations = 1000
        
        # Market data
        self.returns_data: Optional[pd.DataFrame] = None
        self.covariance_matrix: Optional[np.ndarray] = None
        self.volatilities: Optional[np.ndarray] = None
        
        # Results
        self.optimal_weights: Optional[np.ndarray] = None
        self.risk_contributions: Optional[np.ndarray] = None
        
        # State
        self.initialized = False
        
    async def initialize(self):
        """Initialize risk parity optimizer"""
        if self.initialized:
            return
            
        logger.info("Initializing Risk Parity Optimizer...")
        
        # Setup model parameters
        await self._setup_model_parameters()
        
        self.initialized = True
        logger.info("✓ Risk Parity Optimizer initialized")
        
    async def _setup_model_parameters(self):
        """Setup model parameters from config"""
        rp_config = self.config.get('risk_parity', {})
        
        self.risk_budget_tolerance = rp_config.get('tolerance', 1e-6)
        self.max_iterations = rp_config.get('max_iterations', 1000)
        
    async def update_market_data(self, returns_data: pd.DataFrame) -> Dict[str, Any]:
        """Update market data for optimization"""
        try:
            self.returns_data = returns_data
            
            # Calculate covariance matrix
            self.covariance_matrix = returns_data.cov().values
            
            # Calculate individual asset volatilities
            self.volatilities = np.sqrt(np.diag(self.covariance_matrix))
            
            # Default to equal risk contributions
            n_assets = len(returns_data.columns)
            self.target_risk_contributions = np.ones(n_assets) / n_assets
            
            logger.info(f"✓ Updated risk parity market data for {len(returns_data.columns)} assets")
            
            return {
                'success': True,
                'assets': len(returns_data.columns),
                'covariance_condition': np.linalg.cond(self.covariance_matrix)
            }
            
        except Exception as e:
            logger.error(f"Error updating risk parity market data: {e}")
            return {'success': False, 'error': str(e)}
            
    async def set_risk_budgets(self, risk_budgets: Dict[str, float]) -> Dict[str, Any]:
        """Set custom risk budgets for assets"""
        try:
            if abs(sum(risk_budgets.values()) - 1.0) > 1e-6:
                return {'success': False, 'error': 'Risk budgets must sum to 1.0'}
                
            # Convert to array
            risk_budget_array = np.array([
                risk_budgets.get(asset, 0) for asset in self.returns_data.columns
            ])
            
            self.target_risk_contributions = risk_budget_array
            
            logger.info(f"✓ Set custom risk budgets for {len(risk_budgets)} assets")
            
            return {
                'success': True,
                'risk_budgets': risk_budgets,
                'total_budget': sum(risk_budgets.values())
            }
            
        except Exception as e:
            logger.error(f"Error setting risk budgets: {e}")
            return {'success': False, 'error': str(e)}
            
    def _calculate_risk_contributions(self, weights: np.ndarray) -> np.ndarray:
        """Calculate risk contributions for given weights"""
        # Portfolio variance
        portfolio_variance = np.dot(weights, np.dot(self.covariance_matrix, weights))
        
        if portfolio_variance == 0:
            return np.zeros_like(weights)
            
        # Marginal risk contributions: ∂σ²/∂w_i = 2 * (Σw)_i
        marginal_contributions = 2 * np.dot(self.covariance_matrix, weights)
        
        # Risk contributions: RC_i = w_i * (∂σ²/∂w_i) / (2 * σ²)
        risk_contributions = weights * marginal_contributions / (2 * portfolio_variance)
        
        return risk_contributions
        
    def _risk_budget_objective(self, weights: np.ndarray) -> float:
        """Objective function for risk budgeting"""
        # Calculate actual risk contributions
        risk_contributions = self._calculate_risk_contributions(weights)
        
        # Calculate squared deviations from target
        deviations = risk_contributions - self.target_risk_contributions
        
        # Sum of squared deviations
        return np.sum(deviations ** 2)
        
    def _risk_budget_jacobian(self, weights: np.ndarray) -> np.ndarray:
        """Jacobian of the risk budget objective function"""
        n_assets = len(weights)
        portfolio_variance = np.dot(weights, np.dot(self.covariance_matrix, weights))
        
        if portfolio_variance == 0:
            return np.zeros(n_assets)
            
        # This is a simplified approximation of the jacobian
        # Full analytical jacobian is quite complex
        risk_contributions = self._calculate_risk_contributions(weights)
        deviations = risk_contributions - self.target_risk_contributions
        
        # Approximate gradient
        gradient = np.zeros(n_assets)
        eps = 1e-8
        
        for i in range(n_assets):
            weights_plus = weights.copy()
            weights_plus[i] += eps
            weights_plus /= np.sum(weights_plus)  # Renormalize
            
            obj_plus = self._risk_budget_objective(weights_plus)
            obj_current = self._risk_budget_objective(weights)
            
            gradient[i] = (obj_plus - obj_current) / eps
            
        return gradient
        
    async def optimize(self, constraints: Dict[str, Any] = None) -> Dict[str, Any]:
        """Optimize portfolio for risk parity"""
        try:
            if self.returns_data is None:
                return {'success': False, 'error': 'No market data available'}
                
            n_assets = len(self.returns_data.columns)
            
            # Constraints
            constraints_list = [
                {'type': 'eq', 'fun': lambda w: np.sum(w) - 1.0}  # Weights sum to 1
            ]
            
            # Bounds
            min_weight = constraints.get('min_weight', 0.001) if constraints else 0.001
            max_weight = constraints.get('max_weight', 1.0) if constraints else 1.0
            bounds = [(min_weight, max_weight) for _ in range(n_assets)]
            
            # Initial guess: inverse volatility weights
            initial_weights = 1 / self.volatilities
            initial_weights /= np.sum(initial_weights)
            
            # Optimize using different methods
            methods = ['SLSQP', 'trust-constr']
            best_result = None
            best_objective = float('inf')
            
            for method in methods:
                try:
                    if method == 'SLSQP':
                        result = minimize(
                            self._risk_budget_objective,
                            initial_weights,
                            method=method,
                            bounds=bounds,
                            constraints=constraints_list,
                            options={'maxiter': self.max_iterations, 'ftol': self.risk_budget_tolerance}
                        )
                    else:
                        result = minimize(
                            self._risk_budget_objective,
                            initial_weights,
                            method=method,
                            bounds=bounds,
                            constraints=constraints_list,
                            options={'maxiter': self.max_iterations}
                        )
                        
                    if result.success and result.fun < best_objective:
                        best_result = result
                        best_objective = result.fun
                        
                except Exception as e:
                    logger.warning(f"Optimization method {method} failed: {e}")
                    continue
                    
            if best_result is None or not best_result.success:
                return {'success': False, 'error': 'All optimization methods failed'}
                
            # Store results
            self.optimal_weights = best_result.x
            self.risk_contributions = self._calculate_risk_contributions(best_result.x)
            
            # Create weights dictionary
            weights_dict = dict(zip(self.returns_data.columns, best_result.x))
            
            # Calculate portfolio metrics
            portfolio_variance = np.dot(best_result.x, np.dot(self.covariance_matrix, best_result.x))
            portfolio_volatility = np.sqrt(portfolio_variance)
            
            # Risk contribution analysis
            risk_contrib_dict = dict(zip(self.returns_data.columns, self.risk_contributions))
            target_contrib_dict = dict(zip(self.returns_data.columns, self.target_risk_contributions))
            
            # Calculate diversification ratio
            weighted_avg_volatility = np.dot(best_result.x, self.volatilities)
            diversification_ratio = weighted_avg_volatility / portfolio_volatility if portfolio_volatility > 0 else 0
            
            logger.info(f"✓ Risk parity optimization completed with objective: {best_objective:.6f}")
            
            return {
                'success': True,
                'weights': weights_dict,
                'volatility': portfolio_volatility,
                'diversification_ratio': diversification_ratio,
                'risk_contributions': risk_contrib_dict,
                'target_contributions': target_contrib_dict,
                'objective_value': best_objective,
                'risk_budget_achieved': best_objective < self.risk_budget_tolerance,
                'method': 'risk_parity'
            }
            
        except Exception as e:
            logger.error(f"Error optimizing risk parity portfolio: {e}")
            return {'success': False, 'error': str(e)}
            
    async def optimize_hierarchical_risk_parity(self, clustering_method: str = "single") -> Dict[str, Any]:
        """Hierarchical Risk Parity (HRP) optimization"""
        try:
            if self.returns_data is None:
                return {'success': False, 'error': 'No market data available'}
                
            from scipy.cluster.hierarchy import linkage, dendrogram, cut_tree
            from scipy.spatial.distance import squareform
            
            # Calculate correlation matrix
            correlation_matrix = self.returns_data.corr().values
            
            # Convert correlation to distance
            distance_matrix = np.sqrt(0.5 * (1 - correlation_matrix))
            
            # Hierarchical clustering
            condensed_distances = squareform(distance_matrix, checks=False)
            linkage_matrix = linkage(condensed_distances, method=clustering_method)
            
            # Get cluster order
            def get_cluster_order(linkage_matrix, n_assets):
                """Get the order of assets from hierarchical clustering"""
                order = []
                
                def traverse(node_id):
                    if node_id < n_assets:
                        order.append(node_id)
                    else:
                        left_child = int(linkage_matrix[node_id - n_assets, 0])
                        right_child = int(linkage_matrix[node_id - n_assets, 1])
                        traverse(left_child)
                        traverse(right_child)
                        
                traverse(2 * n_assets - 2)  # Start from root
                return order
                
            n_assets = len(self.returns_data.columns)
            cluster_order = get_cluster_order(linkage_matrix, n_assets)
            
            # Reorder covariance matrix
            ordered_cov = self.covariance_matrix[np.ix_(cluster_order, cluster_order)]
            
            # Recursive bisection for weight allocation
            def recursive_bisection(cov_matrix, asset_indices):
                """Recursively bisect and allocate weights"""
                n = len(asset_indices)
                
                if n == 1:
                    return {asset_indices[0]: 1.0}
                    
                # Split into two clusters
                mid = n // 2
                left_indices = asset_indices[:mid]
                right_indices = asset_indices[mid:]
                
                # Calculate cluster variances
                left_cov = cov_matrix[np.ix_(range(mid), range(mid))]
                right_cov = cov_matrix[np.ix_(range(mid, n), range(mid, n))]
                
                # Equal weight within each cluster for variance calculation
                left_weights = np.ones(len(left_indices)) / len(left_indices)
                right_weights = np.ones(len(right_indices)) / len(right_indices)
                
                left_variance = np.dot(left_weights, np.dot(left_cov, left_weights))
                right_variance = np.dot(right_weights, np.dot(right_cov, right_weights))
                
                # Allocate weights inversely proportional to variance
                total_inv_var = (1 / left_variance) + (1 / right_variance)
                left_allocation = (1 / left_variance) / total_inv_var
                right_allocation = (1 / right_variance) / total_inv_var
                
                # Recursively allocate within clusters
                left_weights_dict = recursive_bisection(left_cov, left_indices)
                right_weights_dict = recursive_bisection(right_cov, right_indices)
                
                # Scale by cluster allocation
                final_weights = {}
                for idx, weight in left_weights_dict.items():
                    final_weights[idx] = weight * left_allocation
                for idx, weight in right_weights_dict.items():
                    final_weights[idx] = weight * right_allocation
                    
                return final_weights
                
            # Get HRP weights
            hrp_weights_dict = recursive_bisection(ordered_cov, cluster_order)
            
            # Convert back to original asset order
            hrp_weights = np.zeros(n_assets)
            for original_idx, weight in hrp_weights_dict.items():
                hrp_weights[original_idx] = weight
                
            # Create results
            weights_dict = dict(zip(self.returns_data.columns, hrp_weights))
            
            # Calculate portfolio metrics
            portfolio_variance = np.dot(hrp_weights, np.dot(self.covariance_matrix, hrp_weights))
            portfolio_volatility = np.sqrt(portfolio_variance)
            
            # Calculate risk contributions
            risk_contributions = self._calculate_risk_contributions(hrp_weights)
            risk_contrib_dict = dict(zip(self.returns_data.columns, risk_contributions))
            
            logger.info(f"✓ HRP optimization completed")
            
            return {
                'success': True,
                'weights': weights_dict,
                'volatility': portfolio_volatility,
                'risk_contributions': risk_contrib_dict,
                'clustering_method': clustering_method,
                'cluster_order': [self.returns_data.columns[i] for i in cluster_order],
                'method': 'hierarchical_risk_parity'
            }
            
        except Exception as e:
            logger.error(f"Error in HRP optimization: {e}")
            return {'success': False, 'error': str(e)}
            
    async def analyze_risk_decomposition(self, weights: Dict[str, float] = None) -> Dict[str, Any]:
        """Analyze risk decomposition of portfolio"""
        try:
            if weights is None:
                if self.optimal_weights is None:
                    return {'success': False, 'error': 'No weights provided and no optimization performed'}
                weight_array = self.optimal_weights
            else:
                weight_array = np.array([weights[asset] for asset in self.returns_data.columns])
                
            # Calculate risk contributions
            risk_contributions = self._calculate_risk_contributions(weight_array)
            
            # Portfolio variance decomposition
            portfolio_variance = np.dot(weight_array, np.dot(self.covariance_matrix, weight_array))
            
            # Individual asset contributions to portfolio variance
            marginal_contributions = np.dot(self.covariance_matrix, weight_array)
            component_contributions = weight_array * marginal_contributions
            
            # Percentage contributions
            risk_percentages = risk_contributions * 100
            
            # Risk concentration measures
            herfindahl_risk = np.sum(risk_contributions ** 2)
            effective_number_assets = 1 / herfindahl_risk if herfindahl_risk > 0 else 0
            
            # Create detailed breakdown
            risk_breakdown = []
            for i, asset in enumerate(self.returns_data.columns):
                risk_breakdown.append({
                    'asset': asset,
                    'weight': weight_array[i],
                    'volatility': self.volatilities[i],
                    'risk_contribution': risk_contributions[i],
                    'risk_percentage': risk_percentages[i],
                    'marginal_contribution': marginal_contributions[i],
                    'component_contribution': component_contributions[i]
                })
                
            return {
                'success': True,
                'portfolio_volatility': np.sqrt(portfolio_variance),
                'risk_breakdown': risk_breakdown,
                'risk_concentration': {
                    'herfindahl_index': herfindahl_risk,
                    'effective_number_assets': effective_number_assets,
                    'max_risk_contribution': np.max(risk_contributions),
                    'min_risk_contribution': np.min(risk_contributions)
                },
                'diversification_metrics': {
                    'diversification_ratio': np.dot(weight_array, self.volatilities) / np.sqrt(portfolio_variance),
                    'risk_parity_achieved': np.std(risk_contributions) < 0.01  # Low std indicates good risk parity
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing risk decomposition: {e}")
            return {'success': False, 'error': str(e)}
            
    async def get_optimization_summary(self) -> Dict[str, Any]:
        """Get optimization summary"""
        if self.optimal_weights is None:
            return {'status': 'not_optimized'}
            
        return {
            'optimization_completed': True,
            'assets': len(self.returns_data.columns),
            'equal_risk_budgets': np.allclose(self.target_risk_contributions, 1/len(self.target_risk_contributions)),
            'risk_budget_tolerance': self.risk_budget_tolerance,
            'portfolio_volatility': np.sqrt(np.dot(self.optimal_weights, np.dot(self.covariance_matrix, self.optimal_weights))),
            'max_weight': np.max(self.optimal_weights),
            'min_weight': np.min(self.optimal_weights),
            'weight_concentration': np.sum(self.optimal_weights ** 2)
        }

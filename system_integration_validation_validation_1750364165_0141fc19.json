{"validation_id": "validation_**********_0141fc19", "validation_level": "comprehensive", "overall_status": "partial", "overall_score": 0.7772540573350616, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "partial", "integration_score": 0.7361545205385359, "error_count": 0, "warnings": ["Functionality concerns in system_coordinator", "Integration issues in system_coordinator"], "dependencies_met": "False"}, {"component_name": "team_manager", "component_type": "core", "status": "partial", "integration_score": 0.6994024473640937, "error_count": 0, "warnings": ["Functionality concerns in team_manager", "Integration issues in team_manager"], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "completed", "integration_score": 0.8092541507529208, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "analytics_engine", "component_type": "core", "status": "partial", "integration_score": 0.7706587917247264, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "completed", "integration_score": 0.8428632488781739, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "completed", "integration_score": 0.8208409841600623, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "completed", "integration_score": 0.8000404596582787, "error_count": 0, "warnings": ["Integration issues in portfolio_manager"], "dependencies_met": "True"}, {"component_name": "risk_manager", "component_type": "core", "status": "partial", "integration_score": 0.7657537130618131, "error_count": 0, "warnings": ["Functionality concerns in risk_manager"], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "partial", "integration_score": 0.7721300246888517, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "partial", "integration_score": 0.7997245182754926, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8079831356620235, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.7866044969940494, "error_count": 0, "warnings": ["Integration issues in self_improvement_engine"], "dependencies_met": "False"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "partial", "integration_score": 0.76598750164987, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "partial", "integration_score": 0.7269427114275734, "error_count": 0, "warnings": ["Functionality concerns in performance_optimizer"], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.7760452531308858, "error_count": 0, "warnings": ["Functionality concerns in advanced_trading_engine"], "dependencies_met": "False"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "completed", "integration_score": 0.8195006556202736, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "completed", "integration_score": 0.8115303216957579, "error_count": 0, "warnings": ["Integration issues in configuration_manager"], "dependencies_met": "True"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "partial", "integration_score": 0.7801420007072263, "error_count": 0, "warnings": ["Integration issues in mock_data_providers"], "dependencies_met": "True"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "completed", "integration_score": 0.8055617476355705, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "partial", "integration_score": 0.7236386209023751, "error_count": 0, "warnings": ["Integration issues in logging_audit_system"], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.9147907460385772, "data_manager": 0.6795309596875386, "analytics_engine": 0.6430172762299124, "ollama_hub": 0.7747828206082628, "execution_engine": 0.7077048902040226, "portfolio_manager": 0.8391039337703587, "risk_manager": 0.6983598518644955, "strategy_manager": 0.6759596666229835, "competitive_framework": 0.8523431889846906, "tournament_framework": 0.6301883067264593, "self_improvement_engine": 0.7055332926232623, "regime_adaptation_system": 0.6480970407657193, "performance_optimizer": 0.7500144301541868, "advanced_trading_engine": 0.7137649582414571, "ai_coordinator": 0.72945797371689, "configuration_manager": 0.6558031970087613, "mock_data_providers": 0.6611459414500127, "paper_trading_engine": 0.6396485620817888, "logging_audit_system": 0.8305487326562193}, "team_manager": {"system_coordinator": 0.8024908872494245, "team_manager": 1.0, "data_manager": 0.8676344509229014, "analytics_engine": 0.7079186027540547, "ollama_hub": 0.6153042819550977, "execution_engine": 0.7597657396842196, "portfolio_manager": 0.7512557171339853, "risk_manager": 0.8565026969663497, "strategy_manager": 0.7265308604176437, "competitive_framework": 0.8542544447146089, "tournament_framework": 0.8951706545519278, "self_improvement_engine": 0.6844496896452692, "regime_adaptation_system": 0.7046834325782807, "performance_optimizer": 0.8711505319988879, "advanced_trading_engine": 0.7386222263723563, "ai_coordinator": 0.6579805818841918, "configuration_manager": 0.6282853081709958, "mock_data_providers": 0.6577321390720338, "paper_trading_engine": 0.8528092876314641, "logging_audit_system": 0.8822881773404436}, "data_manager": {"system_coordinator": 0.7671347632457063, "team_manager": 0.7009227595945305, "data_manager": 1.0, "analytics_engine": 0.8316400460534169, "ollama_hub": 0.6183259626511564, "execution_engine": 0.6005603590306196, "portfolio_manager": 0.6895940834197659, "risk_manager": 0.6118478494841465, "strategy_manager": 0.6298081317896408, "competitive_framework": 0.7214053485246186, "tournament_framework": 0.8353494489795348, "self_improvement_engine": 0.7990809706239731, "regime_adaptation_system": 0.7755312652182971, "performance_optimizer": 0.6228155999999614, "advanced_trading_engine": 0.6507522995283918, "ai_coordinator": 0.6987329272233813, "configuration_manager": 0.8145164806710952, "mock_data_providers": 0.7645496967368418, "paper_trading_engine": 0.6143792861666808, "logging_audit_system": 0.8610972291325646}, "analytics_engine": {"system_coordinator": 0.6437762484560596, "team_manager": 0.6764132487240092, "data_manager": 0.7208865341382299, "analytics_engine": 1.0, "ollama_hub": 0.7469063190739386, "execution_engine": 0.8133908827925869, "portfolio_manager": 0.7148504775669473, "risk_manager": 0.616552450699264, "strategy_manager": 0.8609054977064285, "competitive_framework": 0.6621478039658191, "tournament_framework": 0.7819398428899151, "self_improvement_engine": 0.7286099181924306, "regime_adaptation_system": 0.659238590152771, "performance_optimizer": 0.8717450888564139, "advanced_trading_engine": 0.8934428226023345, "ai_coordinator": 0.7057427199856432, "configuration_manager": 0.6492818387199443, "mock_data_providers": 0.6459987691796999, "paper_trading_engine": 0.6582328900593052, "logging_audit_system": 0.7994295126200314}, "ollama_hub": {"system_coordinator": 0.6920088105890201, "team_manager": 0.7354077840968162, "data_manager": 0.83669776565421, "analytics_engine": 0.7774493671642693, "ollama_hub": 1.0, "execution_engine": 0.8659327289441319, "portfolio_manager": 0.6419998432879115, "risk_manager": 0.7706842938158396, "strategy_manager": 0.8868699102523756, "competitive_framework": 0.673875064256171, "tournament_framework": 0.8104802893132634, "self_improvement_engine": 0.7195323056087867, "regime_adaptation_system": 0.724668165150151, "performance_optimizer": 0.6710608291563639, "advanced_trading_engine": 0.8804236346601766, "ai_coordinator": 0.6841654116473145, "configuration_manager": 0.8598976673866343, "mock_data_providers": 0.7415216722171021, "paper_trading_engine": 0.65309481666244, "logging_audit_system": 0.8651256122054373}, "execution_engine": {"system_coordinator": 0.8794224835625347, "team_manager": 0.7218144622396914, "data_manager": 0.6624435180200511, "analytics_engine": 0.691596419726321, "ollama_hub": 0.7893474801261353, "execution_engine": 1.0, "portfolio_manager": 0.9635706053509072, "risk_manager": 0.6479131188323947, "strategy_manager": 0.7834574166321715, "competitive_framework": 0.8886738068259097, "tournament_framework": 0.71827379661335, "self_improvement_engine": 0.8898427512683714, "regime_adaptation_system": 0.6856492079140447, "performance_optimizer": 0.7269189143308388, "advanced_trading_engine": 0.6157022205277832, "ai_coordinator": 0.8318376492980213, "configuration_manager": 0.8644674577261935, "mock_data_providers": 0.8891198308033201, "paper_trading_engine": 0.7553154622135273, "logging_audit_system": 0.7073848441760954}, "portfolio_manager": {"system_coordinator": 0.8467267114836174, "team_manager": 0.6349383276049059, "data_manager": 0.8005707455676677, "analytics_engine": 0.6555712568090366, "ollama_hub": 0.7044625765985257, "execution_engine": 0.8683023649537839, "portfolio_manager": 1.0, "risk_manager": 0.6554243264359549, "strategy_manager": 0.6525570031237502, "competitive_framework": 0.716035647613294, "tournament_framework": 0.803749091962715, "self_improvement_engine": 0.893503702941109, "regime_adaptation_system": 0.654437765218018, "performance_optimizer": 0.6915620786886842, "advanced_trading_engine": 0.8820625530025643, "ai_coordinator": 0.8911102658532501, "configuration_manager": 0.729435808375515, "mock_data_providers": 0.8588850657111677, "paper_trading_engine": 0.8386941468919102, "logging_audit_system": 0.6209272404183726}, "risk_manager": {"system_coordinator": 0.6612404770475178, "team_manager": 0.8370105263568415, "data_manager": 0.8303476498771827, "analytics_engine": 0.855249776279691, "ollama_hub": 0.7717581116792751, "execution_engine": 0.7212582543081846, "portfolio_manager": 0.79400584110261, "risk_manager": 1.0, "strategy_manager": 0.6655455377679881, "competitive_framework": 0.8985587138711102, "tournament_framework": 0.7852471505713625, "self_improvement_engine": 0.6523869143555552, "regime_adaptation_system": 0.6383814963527977, "performance_optimizer": 0.87176948718622, "advanced_trading_engine": 0.6843613140199374, "ai_coordinator": 0.8460143526164952, "configuration_manager": 0.7036609768106111, "mock_data_providers": 0.8912389062016719, "paper_trading_engine": 0.8442325652035317, "logging_audit_system": 0.8211345373773616}, "strategy_manager": {"system_coordinator": 0.7378602415189148, "team_manager": 0.8720895213576504, "data_manager": 0.7372095308727219, "analytics_engine": 0.7996635653229665, "ollama_hub": 0.8184620374467291, "execution_engine": 0.6131395869592499, "portfolio_manager": 0.7498602251398148, "risk_manager": 0.6058721961256137, "strategy_manager": 1.0, "competitive_framework": 0.6117038610446314, "tournament_framework": 0.8011761534937996, "self_improvement_engine": 0.7835959877921044, "regime_adaptation_system": 0.689213093489215, "performance_optimizer": 0.8001083865186318, "advanced_trading_engine": 0.711546576654835, "ai_coordinator": 0.8062914428763459, "configuration_manager": 0.8626363662081026, "mock_data_providers": 0.7257217461087182, "paper_trading_engine": 0.7619312516371852, "logging_audit_system": 0.8992382369451547}, "competitive_framework": {"system_coordinator": 0.8007892096974347, "team_manager": 0.7693269627540376, "data_manager": 0.7047113667305407, "analytics_engine": 0.8682522974278367, "ollama_hub": 0.780582918466589, "execution_engine": 0.7142892551391076, "portfolio_manager": 0.8720807513069735, "risk_manager": 0.6360049680028321, "strategy_manager": 0.6592731927458363, "competitive_framework": 1.0, "tournament_framework": 0.8121345244169003, "self_improvement_engine": 0.6852763288395993, "regime_adaptation_system": 0.639333832609337, "performance_optimizer": 0.7034342134348457, "advanced_trading_engine": 0.7562909537633212, "ai_coordinator": 0.6883925425625955, "configuration_manager": 0.6814197992092299, "mock_data_providers": 0.6952777111159244, "paper_trading_engine": 0.8573621799758337, "logging_audit_system": 0.6498974734325511}, "tournament_framework": {"system_coordinator": 0.8817604979330773, "team_manager": 0.6421108653098241, "data_manager": 0.7898931044602037, "analytics_engine": 0.8527806046088613, "ollama_hub": 0.7493492643594585, "execution_engine": 0.6322551403632184, "portfolio_manager": 0.8095977778049267, "risk_manager": 0.6796303910934477, "strategy_manager": 0.7712281661289785, "competitive_framework": 0.6507310150254525, "tournament_framework": 1.0, "self_improvement_engine": 0.6026231874039928, "regime_adaptation_system": 0.7694011809550083, "performance_optimizer": 0.8989577149083303, "advanced_trading_engine": 0.8474747853411115, "ai_coordinator": 0.6324988974451478, "configuration_manager": 0.6929183834060749, "mock_data_providers": 0.6596675955935009, "paper_trading_engine": 0.8677041074111849, "logging_audit_system": 0.7780883989277267}, "self_improvement_engine": {"system_coordinator": 0.6576224225921969, "team_manager": 0.88112296797621, "data_manager": 0.6435930266735207, "analytics_engine": 0.6503393155044022, "ollama_hub": 0.8922986180018633, "execution_engine": 0.8178053940387968, "portfolio_manager": 0.8769661931230928, "risk_manager": 0.6461071242039852, "strategy_manager": 0.6057160923456552, "competitive_framework": 0.6687703607462993, "tournament_framework": 0.7200480602590028, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.8169038608525809, "performance_optimizer": 0.872396325492115, "advanced_trading_engine": 0.7168998793147715, "ai_coordinator": 0.7273161787145537, "configuration_manager": 0.8886484653903426, "mock_data_providers": 0.7955019286655829, "paper_trading_engine": 0.6792366826146037, "logging_audit_system": 0.6955775736136902}, "regime_adaptation_system": {"system_coordinator": 0.8610926351641675, "team_manager": 0.7584057761243075, "data_manager": 0.8704068424368944, "analytics_engine": 0.6952941321735538, "ollama_hub": 0.6165903401740955, "execution_engine": 0.7910032773031492, "portfolio_manager": 0.6595835289672771, "risk_manager": 0.8098763959730506, "strategy_manager": 0.8057336333647439, "competitive_framework": 0.8958655426342876, "tournament_framework": 0.715740989871799, "self_improvement_engine": 0.7884766416502412, "regime_adaptation_system": 1.0, "performance_optimizer": 0.8814423569780527, "advanced_trading_engine": 0.8344195780364538, "ai_coordinator": 0.7981825786604557, "configuration_manager": 0.770948478762778, "mock_data_providers": 0.7180559500732049, "paper_trading_engine": 0.763315472333053, "logging_audit_system": 0.8989674294827511}, "performance_optimizer": {"system_coordinator": 0.6653568023936212, "team_manager": 0.6868117100701485, "data_manager": 0.8793361249139584, "analytics_engine": 0.8963405819602421, "ollama_hub": 0.6327043038692384, "execution_engine": 0.6055986353671313, "portfolio_manager": 0.8241896400267056, "risk_manager": 0.712480507384378, "strategy_manager": 0.6528011381199079, "competitive_framework": 0.6100346258559043, "tournament_framework": 0.7243718071488752, "self_improvement_engine": 0.6984051926765893, "regime_adaptation_system": 0.7454701407985107, "performance_optimizer": 1.0, "advanced_trading_engine": 0.7006771425931634, "ai_coordinator": 0.8519915003261168, "configuration_manager": 0.7742659948905688, "mock_data_providers": 0.8125934802934257, "paper_trading_engine": 0.6043943037581967, "logging_audit_system": 0.6846212922502005}, "advanced_trading_engine": {"system_coordinator": 0.765055030458165, "team_manager": 0.6592009511521693, "data_manager": 0.6604398050860137, "analytics_engine": 0.620317209834917, "ollama_hub": 0.634245670044475, "execution_engine": 0.6991000196948517, "portfolio_manager": 0.7954240557985693, "risk_manager": 0.8767418741268136, "strategy_manager": 0.6882198419069828, "competitive_framework": 0.7628652754006697, "tournament_framework": 0.8123971489411348, "self_improvement_engine": 0.8882544182660786, "regime_adaptation_system": 0.6523467355134355, "performance_optimizer": 0.7353369527408876, "advanced_trading_engine": 1.0, "ai_coordinator": 0.8960973970241888, "configuration_manager": 0.8492632383541632, "mock_data_providers": 0.872931815608651, "paper_trading_engine": 0.7322473928076212, "logging_audit_system": 0.8987919225040573}, "ai_coordinator": {"system_coordinator": 0.6955106624380242, "team_manager": 0.6269637213342308, "data_manager": 0.8797202484243929, "analytics_engine": 0.738142931428092, "ollama_hub": 0.840310485377834, "execution_engine": 0.6601939593285173, "portfolio_manager": 0.8031192238947311, "risk_manager": 0.6220523801929358, "strategy_manager": 0.6384264966199034, "competitive_framework": 0.6907156114471726, "tournament_framework": 0.8354103079782685, "self_improvement_engine": 0.794959956119441, "regime_adaptation_system": 0.6335426674913098, "performance_optimizer": 0.6172348484306359, "advanced_trading_engine": 0.6977872623429621, "ai_coordinator": 1.0, "configuration_manager": 0.8912602650692936, "mock_data_providers": 0.769180758276591, "paper_trading_engine": 0.6323608865492707, "logging_audit_system": 0.8927371789867247}, "configuration_manager": {"system_coordinator": 0.612521422655296, "team_manager": 0.8989441445029024, "data_manager": 0.7873547404292824, "analytics_engine": 0.8341958502769166, "ollama_hub": 0.6400633729955951, "execution_engine": 0.7308799317985304, "portfolio_manager": 0.8573213846926858, "risk_manager": 0.7631918171312696, "strategy_manager": 0.7503010284397024, "competitive_framework": 0.712940080452812, "tournament_framework": 0.7804985228236944, "self_improvement_engine": 0.7795037766509483, "regime_adaptation_system": 0.6345648642058844, "performance_optimizer": 0.8536385095865535, "advanced_trading_engine": 0.8942828469206693, "ai_coordinator": 0.7579450656858558, "configuration_manager": 1.0, "mock_data_providers": 0.6501729501920134, "paper_trading_engine": 0.6391360480231824, "logging_audit_system": 0.7989241928271245}, "mock_data_providers": {"system_coordinator": 0.7150920972782968, "team_manager": 0.8035113393964166, "data_manager": 0.750767349993512, "analytics_engine": 0.8514444431011894, "ollama_hub": 0.7967200177421172, "execution_engine": 0.7973972803088285, "portfolio_manager": 0.6738009750938896, "risk_manager": 0.6843479194822646, "strategy_manager": 0.7819528818886026, "competitive_framework": 0.8730373019455883, "tournament_framework": 0.8190927443630486, "self_improvement_engine": 0.8575211141171226, "regime_adaptation_system": 0.6980337357415074, "performance_optimizer": 0.6174989731137175, "advanced_trading_engine": 0.6274978797399423, "ai_coordinator": 0.6979973103353212, "configuration_manager": 0.6197817595575643, "mock_data_providers": 1.0, "paper_trading_engine": 0.6369695585262243, "logging_audit_system": 0.6075590829404068}, "paper_trading_engine": {"system_coordinator": 0.6840883484540599, "team_manager": 0.8656320456074064, "data_manager": 0.6433668948240158, "analytics_engine": 0.7042335047058879, "ollama_hub": 0.8050922000654344, "execution_engine": 0.7547783132909965, "portfolio_manager": 0.7611320259372062, "risk_manager": 0.6534005179416367, "strategy_manager": 0.6094680999911147, "competitive_framework": 0.7068444412618204, "tournament_framework": 0.639966270067845, "self_improvement_engine": 0.7980156964783818, "regime_adaptation_system": 0.6807326018134483, "performance_optimizer": 0.6361614990349702, "advanced_trading_engine": 0.7878787693189521, "ai_coordinator": 0.7238521441993909, "configuration_manager": 0.8385341176785663, "mock_data_providers": 0.6858501780688349, "paper_trading_engine": 1.0, "logging_audit_system": 0.7545863685909097}, "logging_audit_system": {"system_coordinator": 0.8819415710323497, "team_manager": 0.7741575390766107, "data_manager": 0.8660425058585222, "analytics_engine": 0.7608484911008634, "ollama_hub": 0.623294190357406, "execution_engine": 0.6403350167489245, "portfolio_manager": 0.6610322193352407, "risk_manager": 0.7580187389830905, "strategy_manager": 0.7580201011467466, "competitive_framework": 0.8139295672641108, "tournament_framework": 0.8875054092633472, "self_improvement_engine": 0.6840314406047597, "regime_adaptation_system": 0.6098033698706204, "performance_optimizer": 0.6815342775254529, "advanced_trading_engine": 0.8855904984396792, "ai_coordinator": 0.6129005787601807, "configuration_manager": 0.6707050618458666, "mock_data_providers": 0.7673194672324808, "paper_trading_engine": 0.6076238626755355, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.7315478438909901, "response_time": 0.874461160626472, "throughput": 0.7914579796331894, "memory_usage": 0.784571314239373, "cpu_usage": 0.7930839232122695, "concurrent_operations": 0.6299224421166657}, "critical_issues": ["Components with dependency issues: system_coordinator, data_manager, self_improvement_engine, advanced_trading_engine"], "recommendations": ["Improve 12 partially working components", "Improve overall system performance and stability", "Enhance component integration and communication"], "production_ready": "True", "timestamp": **********.1648753}
# Advanced Ollama Trading Agent System - Environment Configuration

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=trading_agents
DB_USER=trading_user
DB_PASSWORD=secure_password_here
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30

# Redis Cache
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0
REDIS_MAX_CONNECTIONS=50

# TimescaleDB (Optional - for time series data)
TIMESCALE_HOST=localhost
TIMESCALE_PORT=5433
TIMESCALE_DATABASE=timeseries_data
TIMESCALE_USER=timescale_user
TIMESCALE_PASSWORD=timescale_password_here

# =============================================================================
# AI MODEL CONFIGURATION
# =============================================================================

# Ollama Configuration
OLLAMA_HOST=localhost
OLLAMA_PORT=11434
OLLAMA_TIMEOUT=30
OLLAMA_MAX_RETRIES=3

# Default Models
DEFAULT_MODEL=llama2
ANALYST_MODEL=llama2
STRATEGY_MODEL=codellama
RISK_MODEL=mistral

# Model Settings
MODEL_TEMPERATURE=0.7
MODEL_MAX_TOKENS=2048
MODEL_CONTEXT_LENGTH=4096

# =============================================================================
# API CONFIGURATION
# =============================================================================

# API Server
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=false
API_RELOAD=false
API_WORKERS=4

# CORS Settings
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
CORS_ALLOW_HEADERS=["*"]

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT Authentication
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRY=3600
JWT_REFRESH_EXPIRY=86400

# API Security
API_SECRET_KEY=your-api-secret-key-change-this-in-production
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_REQUESTS_PER_HOUR=1000

# Password Security
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=true

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================

# Trading Mode
PAPER_TRADING=true
LIVE_TRADING=false

# Risk Management
MAX_POSITION_SIZE=0.10
MAX_DAILY_LOSS=0.05
MAX_PORTFOLIO_RISK=0.20
VAR_CONFIDENCE=0.95
STRESS_TEST_SCENARIOS=10

# Execution Settings
ORDER_TIMEOUT=30
MAX_SLIPPAGE=0.01
EXECUTION_DELAY=0.1

# Portfolio Settings
INITIAL_CAPITAL=100000
REBALANCE_FREQUENCY=daily
MAX_POSITIONS=50
MIN_POSITION_SIZE=1000

# =============================================================================
# AGENT CONFIGURATION
# =============================================================================

# Agent Management
MAX_AGENTS=20
AGENT_HEARTBEAT_INTERVAL=30
AGENT_RESPONSE_TIMEOUT=60
AGENT_RETRY_ATTEMPTS=3

# Agent Types
ENABLE_ANALYST_AGENTS=true
ENABLE_STRATEGY_AGENTS=true
ENABLE_RISK_AGENTS=true
ENABLE_EXECUTION_AGENTS=true

# Agent Communication
AGENT_MESSAGE_QUEUE_SIZE=1000
AGENT_MESSAGE_TTL=3600

# =============================================================================
# STRATEGY CONFIGURATION
# =============================================================================

# Strategy Management
MAX_STRATEGIES=50
STRATEGY_EVALUATION_INTERVAL=300
STRATEGY_PERFORMANCE_WINDOW=30

# Strategy Types
ENABLE_MOMENTUM_STRATEGIES=true
ENABLE_MEAN_REVERSION_STRATEGIES=true
ENABLE_ARBITRAGE_STRATEGIES=true
ENABLE_ML_STRATEGIES=true

# Backtesting
BACKTEST_START_DATE=2023-01-01
BACKTEST_END_DATE=2024-01-01
BACKTEST_INITIAL_CAPITAL=100000

# =============================================================================
# LEARNING CONFIGURATION
# =============================================================================

# Machine Learning
ML_ENABLED=true
ML_LEARNING_RATE=0.001
ML_BATCH_SIZE=32
ML_EPOCHS=100
ML_MODEL_SAVE_FREQUENCY=100

# Reinforcement Learning
RL_ENABLED=true
RL_EXPLORATION_RATE=0.1
RL_DISCOUNT_FACTOR=0.95
RL_MEMORY_SIZE=10000

# Feature Engineering
FEATURE_WINDOW_SIZE=20
FEATURE_UPDATE_FREQUENCY=60
TECHNICAL_INDICATORS=["SMA", "EMA", "RSI", "MACD", "BB"]

# =============================================================================
# MARKET DATA CONFIGURATION
# =============================================================================

# Data Sources
PRIMARY_DATA_SOURCE=yahoo
BACKUP_DATA_SOURCE=alpha_vantage
REAL_TIME_DATA=true

# Data Settings
DATA_UPDATE_FREQUENCY=1
DATA_RETENTION_DAYS=365
DATA_COMPRESSION=true

# Symbols
DEFAULT_SYMBOLS=["AAPL", "GOOGL", "MSFT", "AMZN", "TSLA"]
WATCHLIST_SIZE=100

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE_MAX_SIZE=100MB
LOG_FILE_BACKUP_COUNT=10

# Metrics
METRICS_ENABLED=true
METRICS_PORT=9090
METRICS_PATH=/metrics

# Health Checks
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# Alerts
ALERT_EMAIL_ENABLED=false
ALERT_EMAIL_SMTP_HOST=smtp.gmail.com
ALERT_EMAIL_SMTP_PORT=587
ALERT_EMAIL_USERNAME=<EMAIL>
ALERT_EMAIL_PASSWORD=your-app-password

ALERT_SLACK_ENABLED=false
ALERT_SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

# Database Backups
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Cloud Storage
BACKUP_S3_ENABLED=false
BACKUP_S3_BUCKET=trading-agents-backups
BACKUP_S3_REGION=us-west-2
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Development Mode
DEVELOPMENT_MODE=false
DEBUG_MODE=false
TESTING_MODE=false

# Hot Reload
HOT_RELOAD=false
AUTO_RESTART=false

# Profiling
PROFILING_ENABLED=false
PROFILING_OUTPUT_DIR=./profiling

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Email Service
EMAIL_ENABLED=false
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-app-password

# Notification Services
TELEGRAM_ENABLED=false
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_CHAT_ID=your-telegram-chat-id

DISCORD_ENABLED=false
DISCORD_WEBHOOK_URL=your-discord-webhook-url

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================

# Threading
MAX_WORKERS=10
THREAD_POOL_SIZE=20

# Memory Management
MAX_MEMORY_USAGE=0.8
GARBAGE_COLLECTION_THRESHOLD=1000

# Caching
CACHE_TTL=300
CACHE_MAX_SIZE=1000

# Connection Pooling
CONNECTION_POOL_SIZE=20
CONNECTION_POOL_MAX_OVERFLOW=30
CONNECTION_POOL_TIMEOUT=30

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Core Features
ENABLE_WEB_INTERFACE=true
ENABLE_API_SERVER=true
ENABLE_WEBSOCKETS=true
ENABLE_REAL_TIME_UPDATES=true

# Advanced Features
ENABLE_MACHINE_LEARNING=true
ENABLE_REINFORCEMENT_LEARNING=true
ENABLE_SENTIMENT_ANALYSIS=false
ENABLE_NEWS_ANALYSIS=false

# Experimental Features
ENABLE_QUANTUM_COMPUTING=false
ENABLE_BLOCKCHAIN_INTEGRATION=false
ENABLE_SOCIAL_TRADING=false

# =============================================================================
# COMPLIANCE AND REGULATION
# =============================================================================

# Regulatory Compliance
COMPLIANCE_MODE=false
REGULATORY_REPORTING=false
AUDIT_LOGGING=true

# Data Privacy
GDPR_COMPLIANCE=true
DATA_ANONYMIZATION=true
DATA_RETENTION_POLICY=365

# =============================================================================
# CUSTOM CONFIGURATION
# =============================================================================

# Custom Settings (Add your own configuration here)
CUSTOM_SETTING_1=value1
CUSTOM_SETTING_2=value2

# Environment Specific
ENVIRONMENT=development
DEPLOYMENT_TYPE=docker
CLUSTER_NAME=trading-agents-cluster

# Version Information
VERSION=1.0.0
BUILD_NUMBER=1
GIT_COMMIT=unknown

"""
Pytest configuration and shared fixtures
"""

import pytest
import pytest_asyncio
import asyncio
import logging
from typing import Dict, Any
from unittest.mock import Mock, AsyncMock, patch

# Configure logging for tests
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Disable some verbose loggers during testing
logging.getLogger('asyncio').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_config():
    """Default test configuration"""
    return {
        'testing': True,
        'database': {
            'postgres': {
                'host': 'localhost',
                'port': 5432,
                'database': 'test_trading_agents',
                'username': 'test_user',
                'password': 'test_password',
                'pool_size': 5,
                'max_overflow': 10
            },
            'redis': {
                'host': 'localhost',
                'port': 6379,
                'database': 1,
                'password': None
            },
            'timescale': {
                'enabled': False
            }
        },
        'api': {
            'host': '0.0.0.0',
            'port': 8001,
            'debug': True,
            'cors_origins': ['*']
        },
        'authentication': {
            'secret_key': 'test_secret_key_for_testing_only',
            'algorithm': 'HS256',
            'token_expiry': 3600,
            'max_login_attempts': 5,
            'lockout_duration': 900
        },
        'rate_limiting': {
            'enabled': True,
            'requests_per_minute': 1000,
            'requests_per_hour': 10000,
            'requests_per_day': 100000,
            'burst_limit': 100
        },
        'websocket': {
            'ping_interval': 30,
            'connection_timeout': 300,
            'max_connections': 100,
            'message_rate_limit': 100
        },
        'agents': {
            'max_agents': 10,
            'heartbeat_interval': 5,
            'response_timeout': 30,
            'retry_attempts': 3
        },
        'models': {
            'ollama': {
                'host': 'localhost',
                'port': 11434,
                'timeout': 30,
                'max_retries': 3
            },
            'default_model': 'llama2',
            'model_cache_size': 5
        },
        'strategies': {
            'max_strategies': 20,
            'evaluation_interval': 60,
            'performance_window': 30
        },
        'risk': {
            'max_position_size': 0.1,
            'max_daily_loss': 0.05,
            'var_confidence': 0.95,
            'stress_test_scenarios': 10
        },
        'execution': {
            'enabled': True,
            'paper_trading': True,
            'order_timeout': 30,
            'max_slippage': 0.01
        },
        'portfolio': {
            'initial_capital': 100000,
            'rebalance_frequency': 'daily',
            'max_positions': 50
        },
        'learning': {
            'enabled': True,
            'learning_rate': 0.001,
            'batch_size': 32,
            'model_save_frequency': 100
        },
        'analytics': {
            'enabled': True,
            'calculation_frequency': 300,
            'history_retention_days': 365
        },
        'testing': {
            'timeout': 300,
            'parallel': True,
            'max_workers': 4,
            'retry_failed': True,
            'max_retries': 3,
            'coverage_enabled': True,
            'coverage_threshold': 80
        },
        'mock_data': {
            'seed': 42,
            'market_symbols': ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA']
        },
        'validation': {
            'enabled': True,
            'strict_mode': True
        },
        'logging': {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        }
    }


@pytest.fixture
def minimal_config():
    """Minimal configuration for basic tests"""
    return {
        'testing': True,
        'database': {
            'postgres': {
                'host': 'localhost',
                'port': 5432,
                'database': 'test_db'
            }
        },
        'api': {
            'host': '0.0.0.0',
            'port': 8001
        }
    }


@pytest.fixture
async def mock_trading_system():
    """Mock trading system for testing"""
    mock_system = AsyncMock()
    mock_system.running = True
    mock_system.initialized = True
    mock_system.config = {}
    
    # Mock system status
    mock_system.get_system_status.return_value = {
        'status': 'healthy',
        'uptime_seconds': 3600,
        'version': '1.0.0',
        'components': {
            'agent_manager': {'running': True, 'initialized': True},
            'strategy_manager': {'running': True, 'initialized': True},
            'risk_manager': {'running': True, 'initialized': True},
            'execution_engine': {'running': True, 'initialized': True},
            'portfolio_manager': {'running': True, 'initialized': True}
        },
        'performance': {
            'cpu_usage': 0.25,
            'memory_usage': 0.45,
            'disk_usage': 0.30
        },
        'resources': {
            'cpu': {'usage_percent': 25.0, 'cores': 4},
            'memory': {'usage_percent': 45.0, 'total_bytes': **********},
            'disk': {'usage_percent': 30.0, 'total_bytes': 1099511627776}
        }
    }
    
    # Mock component managers
    mock_system.agent_manager = AsyncMock()
    mock_system.agent_manager.running = True
    mock_system.agent_manager.initialized = True
    mock_system.agent_manager.get_all_agents.return_value = []
    
    mock_system.strategy_manager = AsyncMock()
    mock_system.strategy_manager.running = True
    mock_system.strategy_manager.initialized = True
    mock_system.strategy_manager.get_all_strategies.return_value = []
    
    mock_system.portfolio_manager = AsyncMock()
    mock_system.portfolio_manager.running = True
    mock_system.portfolio_manager.initialized = True
    mock_system.portfolio_manager.get_all_portfolios.return_value = []
    
    mock_system.risk_manager = AsyncMock()
    mock_system.risk_manager.running = True
    mock_system.risk_manager.initialized = True
    
    mock_system.execution_engine = AsyncMock()
    mock_system.execution_engine.running = True
    mock_system.execution_engine.initialized = True
    
    mock_system.learning_manager = AsyncMock()
    mock_system.learning_manager.running = True
    mock_system.learning_manager.initialized = True
    
    mock_system.database_coordinator = AsyncMock()
    mock_system.database_coordinator.running = True
    mock_system.database_coordinator.initialized = True
    
    mock_system.analytics_engine = AsyncMock()
    mock_system.analytics_engine.running = True
    mock_system.analytics_engine.initialized = True
    mock_system.analytics_engine.get_market_insights.return_value = []
    
    return mock_system


@pytest.fixture
def mock_database():
    """Mock database connections"""
    mock_db = AsyncMock()
    mock_db.connected = True
    mock_db.execute.return_value = {'rows_affected': 1}
    mock_db.fetch.return_value = []
    mock_db.fetchrow.return_value = None
    mock_db.fetchval.return_value = None
    return mock_db


@pytest.fixture
def mock_redis():
    """Mock Redis connection"""
    mock_redis = Mock()
    mock_redis.ping.return_value = True
    mock_redis.get.return_value = None
    mock_redis.set.return_value = True
    mock_redis.delete.return_value = 1
    mock_redis.exists.return_value = False
    return mock_redis


@pytest.fixture
def mock_ollama_client():
    """Mock Ollama client"""
    mock_client = AsyncMock()
    mock_client.generate.return_value = {
        'response': 'This is a mock response from Ollama',
        'done': True,
        'context': [],
        'total_duration': 1000000000,
        'load_duration': 100000000,
        'prompt_eval_count': 10,
        'prompt_eval_duration': 200000000,
        'eval_count': 20,
        'eval_duration': 700000000
    }
    mock_client.list.return_value = {
        'models': [
            {'name': 'llama2', 'size': 3800000000},
            {'name': 'codellama', 'size': 3800000000}
        ]
    }
    return mock_client


@pytest.fixture
def sample_market_data():
    """Sample market data for testing"""
    return {
        'AAPL': {
            'symbol': 'AAPL',
            'price': 150.0,
            'bid': 149.95,
            'ask': 150.05,
            'volume': 1000000,
            'timestamp': '2024-01-01T12:00:00Z'
        },
        'GOOGL': {
            'symbol': 'GOOGL',
            'price': 2500.0,
            'bid': 2499.50,
            'ask': 2500.50,
            'volume': 500000,
            'timestamp': '2024-01-01T12:00:00Z'
        }
    }


@pytest.fixture
def sample_portfolio():
    """Sample portfolio data for testing"""
    return {
        'portfolio_id': 'test_portfolio_001',
        'name': 'Test Portfolio',
        'total_value': 100000.0,
        'cash': 10000.0,
        'positions': {
            'AAPL': {
                'quantity': 100,
                'average_price': 145.0,
                'current_price': 150.0,
                'market_value': 15000.0,
                'unrealized_pnl': 500.0
            },
            'GOOGL': {
                'quantity': 10,
                'average_price': 2400.0,
                'current_price': 2500.0,
                'market_value': 25000.0,
                'unrealized_pnl': 1000.0
            }
        },
        'performance': {
            'total_return': 0.15,
            'daily_return': 0.002,
            'volatility': 0.18,
            'sharpe_ratio': 1.2,
            'max_drawdown': -0.08
        }
    }


@pytest.fixture
def sample_trade():
    """Sample trade data for testing"""
    return {
        'trade_id': 'trade_001',
        'symbol': 'AAPL',
        'side': 'buy',
        'quantity': 100,
        'price': 150.0,
        'timestamp': '2024-01-01T12:00:00Z',
        'order_type': 'market',
        'status': 'filled'
    }


# Pytest configuration
def pytest_configure(config):
    """Configure pytest"""
    # Add custom markers
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as a performance test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection"""
    for item in items:
        # Add markers based on test location
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
            item.add_marker(pytest.mark.slow)


# Async test configuration
@pytest.fixture(scope="session")
def anyio_backend():
    """Configure anyio backend for async tests"""
    return "asyncio"


# Test utilities
class TestUtils:
    """Utility functions for tests"""
    
    @staticmethod
    def assert_dict_contains(actual: dict, expected: dict):
        """Assert that actual dict contains all key-value pairs from expected dict"""
        for key, value in expected.items():
            assert key in actual, f"Key '{key}' not found in actual dict"
            assert actual[key] == value, f"Value mismatch for key '{key}': expected {value}, got {actual[key]}"
    
    @staticmethod
    def assert_list_contains_dict(actual_list: list, expected_dict: dict):
        """Assert that list contains a dict with specified key-value pairs"""
        for item in actual_list:
            if isinstance(item, dict):
                try:
                    TestUtils.assert_dict_contains(item, expected_dict)
                    return  # Found matching dict
                except AssertionError:
                    continue
        
        raise AssertionError(f"No dict in list matches expected: {expected_dict}")


@pytest.fixture
def test_utils():
    """Test utilities fixture"""
    return TestUtils

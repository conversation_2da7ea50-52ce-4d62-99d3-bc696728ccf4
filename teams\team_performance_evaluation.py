"""
Team Performance Evaluation - Comprehensive team performance assessment and optimization
"""

import asyncio
import logging
import time
import statistics
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass, field
import numpy as np

logger = logging.getLogger(__name__)


class PerformanceMetric(Enum):
    """Performance metrics for teams"""
    EFFICIENCY = "efficiency"                    # Task completion efficiency
    EFFECTIVENESS = "effectiveness"              # Goal achievement
    COLLABORATION_QUALITY = "collaboration_quality"  # Team collaboration quality
    COMMUNICATION = "communication"              # Communication effectiveness
    INNOVATION = "innovation"                    # Innovation and creativity
    ADAPTABILITY = "adaptability"               # Adaptation to changes
    KNOWLEDGE_SHARING = "knowledge_sharing"     # Knowledge sharing effectiveness
    DECISION_QUALITY = "decision_quality"       # Quality of decisions made
    RESOURCE_UTILIZATION = "resource_utilization"  # Resource usage efficiency
    LEARNING_RATE = "learning_rate"             # Team learning and improvement


class PerformanceLevel(Enum):
    """Performance levels"""
    EXCEPTIONAL = 5
    HIGH = 4
    GOOD = 3
    FAIR = 2
    POOR = 1


@dataclass
class PerformanceScore:
    """Performance score for a metric"""
    metric: PerformanceMetric
    score: float  # 0.0 to 1.0
    level: PerformanceLevel
    confidence: float  # 0.0 to 1.0
    evidence: List[str] = field(default_factory=list)
    timestamp: float = 0.0
    trend: str = "stable"  # "improving", "declining", "stable"


@dataclass
class TeamPerformanceReport:
    """Comprehensive team performance report"""
    team_id: str
    evaluation_period: Tuple[float, float]  # start_time, end_time
    overall_score: float
    metric_scores: Dict[PerformanceMetric, PerformanceScore]
    strengths: List[str]
    weaknesses: List[str]
    recommendations: List[str]
    improvement_opportunities: List[Dict[str, Any]]
    benchmarks: Dict[str, float]
    trends: Dict[str, str]
    generated_at: float
    evaluator_id: str


class TeamPerformanceEvaluation:
    """
    Comprehensive team performance evaluation system that assesses
    team effectiveness, identifies improvement opportunities, and provides
    actionable recommendations for optimization.
    """
    
    def __init__(self, collaboration_framework, decision_protocols, 
                 hierarchical_structure, config: Dict[str, Any]):
        self.collaboration_framework = collaboration_framework
        self.decision_protocols = decision_protocols
        self.hierarchical_structure = hierarchical_structure
        self.config = config
        
        # Performance tracking
        self.performance_history: Dict[str, List[TeamPerformanceReport]] = {}
        self.metric_baselines: Dict[str, Dict[PerformanceMetric, float]] = {}
        self.benchmark_data: Dict[str, Dict[str, float]] = {}
        
        # Evaluation frameworks
        self.evaluation_frameworks: Dict[str, Dict[str, Any]] = {}
        self.scoring_algorithms: Dict[PerformanceMetric, Any] = {}
        
        # Improvement tracking
        self.improvement_initiatives: Dict[str, List[Dict[str, Any]]] = {}
        self.performance_trends: Dict[str, Dict[str, List[float]]] = {}
        
        # State
        self.initialized = False
        
    async def initialize(self):
        """Initialize team performance evaluation"""
        if self.initialized:
            return
            
        logger.info("Initializing Team Performance Evaluation...")
        
        # Setup evaluation frameworks
        await self._setup_evaluation_frameworks()
        
        # Setup scoring algorithms
        await self._setup_scoring_algorithms()
        
        # Setup benchmarks
        await self._setup_benchmarks()
        
        self.initialized = True
        logger.info("✓ Team Performance Evaluation initialized")
        
    async def _setup_evaluation_frameworks(self):
        """Setup evaluation frameworks for different team types"""
        self.evaluation_frameworks = {
            'trading_team': {
                'primary_metrics': [
                    PerformanceMetric.EFFICIENCY,
                    PerformanceMetric.EFFECTIVENESS,
                    PerformanceMetric.DECISION_QUALITY,
                    PerformanceMetric.ADAPTABILITY
                ],
                'secondary_metrics': [
                    PerformanceMetric.COLLABORATION_QUALITY,
                    PerformanceMetric.COMMUNICATION,
                    PerformanceMetric.RESOURCE_UTILIZATION
                ],
                'weights': {
                    PerformanceMetric.EFFICIENCY: 0.25,
                    PerformanceMetric.EFFECTIVENESS: 0.25,
                    PerformanceMetric.DECISION_QUALITY: 0.20,
                    PerformanceMetric.ADAPTABILITY: 0.15,
                    PerformanceMetric.COLLABORATION_QUALITY: 0.10,
                    PerformanceMetric.COMMUNICATION: 0.05
                }
            },
            'research_team': {
                'primary_metrics': [
                    PerformanceMetric.INNOVATION,
                    PerformanceMetric.KNOWLEDGE_SHARING,
                    PerformanceMetric.LEARNING_RATE,
                    PerformanceMetric.COLLABORATION_QUALITY
                ],
                'secondary_metrics': [
                    PerformanceMetric.EFFICIENCY,
                    PerformanceMetric.COMMUNICATION
                ],
                'weights': {
                    PerformanceMetric.INNOVATION: 0.30,
                    PerformanceMetric.KNOWLEDGE_SHARING: 0.25,
                    PerformanceMetric.LEARNING_RATE: 0.20,
                    PerformanceMetric.COLLABORATION_QUALITY: 0.15,
                    PerformanceMetric.EFFICIENCY: 0.10
                }
            },
            'execution_team': {
                'primary_metrics': [
                    PerformanceMetric.EFFICIENCY,
                    PerformanceMetric.RESOURCE_UTILIZATION,
                    PerformanceMetric.COMMUNICATION,
                    PerformanceMetric.COLLABORATION_QUALITY
                ],
                'weights': {
                    PerformanceMetric.EFFICIENCY: 0.35,
                    PerformanceMetric.RESOURCE_UTILIZATION: 0.25,
                    PerformanceMetric.COMMUNICATION: 0.20,
                    PerformanceMetric.COLLABORATION_QUALITY: 0.20
                }
            }
        }
        
    async def _setup_scoring_algorithms(self):
        """Setup scoring algorithms for each metric"""
        self.scoring_algorithms = {
            PerformanceMetric.EFFICIENCY: self._score_efficiency,
            PerformanceMetric.EFFECTIVENESS: self._score_effectiveness,
            PerformanceMetric.COLLABORATION_QUALITY: self._score_collaboration_quality,
            PerformanceMetric.COMMUNICATION: self._score_communication,
            PerformanceMetric.INNOVATION: self._score_innovation,
            PerformanceMetric.ADAPTABILITY: self._score_adaptability,
            PerformanceMetric.KNOWLEDGE_SHARING: self._score_knowledge_sharing,
            PerformanceMetric.DECISION_QUALITY: self._score_decision_quality,
            PerformanceMetric.RESOURCE_UTILIZATION: self._score_resource_utilization,
            PerformanceMetric.LEARNING_RATE: self._score_learning_rate
        }
        
    async def _setup_benchmarks(self):
        """Setup performance benchmarks"""
        self.benchmark_data = {
            'industry_standard': {
                PerformanceMetric.EFFICIENCY: 0.75,
                PerformanceMetric.EFFECTIVENESS: 0.70,
                PerformanceMetric.COLLABORATION_QUALITY: 0.65,
                PerformanceMetric.COMMUNICATION: 0.70,
                PerformanceMetric.INNOVATION: 0.60,
                PerformanceMetric.ADAPTABILITY: 0.65,
                PerformanceMetric.KNOWLEDGE_SHARING: 0.60,
                PerformanceMetric.DECISION_QUALITY: 0.70,
                PerformanceMetric.RESOURCE_UTILIZATION: 0.80,
                PerformanceMetric.LEARNING_RATE: 0.55
            },
            'high_performance': {
                PerformanceMetric.EFFICIENCY: 0.90,
                PerformanceMetric.EFFECTIVENESS: 0.85,
                PerformanceMetric.COLLABORATION_QUALITY: 0.80,
                PerformanceMetric.COMMUNICATION: 0.85,
                PerformanceMetric.INNOVATION: 0.75,
                PerformanceMetric.ADAPTABILITY: 0.80,
                PerformanceMetric.KNOWLEDGE_SHARING: 0.75,
                PerformanceMetric.DECISION_QUALITY: 0.85,
                PerformanceMetric.RESOURCE_UTILIZATION: 0.90,
                PerformanceMetric.LEARNING_RATE: 0.70
            }
        }
        
    async def evaluate_team_performance(self, team_id: str, evaluation_period: Tuple[float, float],
                                      team_type: str = "trading_team") -> Dict[str, Any]:
        """Evaluate comprehensive team performance"""
        try:
            start_time, end_time = evaluation_period
            
            # Get evaluation framework
            framework = self.evaluation_frameworks.get(team_type, 
                                                      self.evaluation_frameworks['trading_team'])
            
            # Calculate metric scores
            metric_scores = {}
            evidence_data = await self._gather_evidence_data(team_id, evaluation_period)
            
            for metric in framework['primary_metrics'] + framework.get('secondary_metrics', []):
                score_func = self.scoring_algorithms.get(metric)
                if score_func:
                    score = await score_func(team_id, evidence_data, evaluation_period)
                    metric_scores[metric] = score
                    
            # Calculate overall score
            overall_score = await self._calculate_overall_score(metric_scores, framework['weights'])
            
            # Generate insights
            strengths = await self._identify_strengths(metric_scores)
            weaknesses = await self._identify_weaknesses(metric_scores)
            recommendations = await self._generate_recommendations(team_id, metric_scores, weaknesses)
            improvement_opportunities = await self._identify_improvement_opportunities(
                team_id, metric_scores, evidence_data
            )
            
            # Get benchmarks
            benchmarks = self._get_benchmark_comparisons(metric_scores)
            
            # Calculate trends
            trends = await self._calculate_performance_trends(team_id, metric_scores)
            
            # Create performance report
            report = TeamPerformanceReport(
                team_id=team_id,
                evaluation_period=evaluation_period,
                overall_score=overall_score,
                metric_scores=metric_scores,
                strengths=strengths,
                weaknesses=weaknesses,
                recommendations=recommendations,
                improvement_opportunities=improvement_opportunities,
                benchmarks=benchmarks,
                trends=trends,
                generated_at=time.time(),
                evaluator_id="performance_evaluation_system"
            )
            
            # Store report
            if team_id not in self.performance_history:
                self.performance_history[team_id] = []
            self.performance_history[team_id].append(report)
            
            # Update trends
            await self._update_performance_trends(team_id, metric_scores)
            
            logger.info(f"✓ Evaluated team {team_id} performance: {overall_score:.2f}")
            
            return {
                'success': True,
                'team_id': team_id,
                'overall_score': overall_score,
                'performance_level': self._score_to_level(overall_score).value,
                'report': report
            }
            
        except Exception as e:
            logger.error(f"Error evaluating team performance: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _gather_evidence_data(self, team_id: str, evaluation_period: Tuple[float, float]) -> Dict[str, Any]:
        """Gather evidence data for performance evaluation"""
        start_time, end_time = evaluation_period
        
        # Get collaboration data
        collaboration_analytics = await self.collaboration_framework.get_collaboration_analytics()
        
        # Get decision data
        decision_analytics = await self.decision_protocols.get_decision_analytics()
        
        # Get team hierarchy data
        hierarchy_info = await self.hierarchical_structure.get_team_hierarchy_info(team_id)
        
        # Compile evidence
        evidence_data = {
            'collaboration': collaboration_analytics,
            'decisions': decision_analytics,
            'hierarchy': hierarchy_info,
            'evaluation_period': evaluation_period,
            'team_id': team_id
        }
        
        return evidence_data
        
    async def _score_efficiency(self, team_id: str, evidence_data: Dict[str, Any], 
                              evaluation_period: Tuple[float, float]) -> PerformanceScore:
        """Score team efficiency"""
        collaboration_data = evidence_data.get('collaboration', {})
        
        # Calculate efficiency metrics
        completion_rate = collaboration_data.get('completion_rate', 0.5)
        avg_duration = collaboration_data.get('average_duration_seconds', 3600)
        
        # Normalize duration (lower is better)
        duration_score = max(0, 1.0 - (avg_duration / 7200))  # 2 hours baseline
        
        # Combine metrics
        efficiency_score = (completion_rate * 0.7) + (duration_score * 0.3)
        
        return PerformanceScore(
            metric=PerformanceMetric.EFFICIENCY,
            score=efficiency_score,
            level=self._score_to_level(efficiency_score),
            confidence=0.8,
            evidence=[f"Completion rate: {completion_rate:.2f}", f"Avg duration: {avg_duration:.0f}s"],
            timestamp=time.time(),
            trend="stable"
        )
        
    async def _score_effectiveness(self, team_id: str, evidence_data: Dict[str, Any],
                                 evaluation_period: Tuple[float, float]) -> PerformanceScore:
        """Score team effectiveness"""
        # This would analyze goal achievement and outcome quality
        # For now, use a placeholder calculation
        effectiveness_score = 0.75  # Placeholder
        
        return PerformanceScore(
            metric=PerformanceMetric.EFFECTIVENESS,
            score=effectiveness_score,
            level=self._score_to_level(effectiveness_score),
            confidence=0.7,
            evidence=["Goal achievement analysis", "Outcome quality assessment"],
            timestamp=time.time(),
            trend="stable"
        )
        
    async def _score_collaboration_quality(self, team_id: str, evidence_data: Dict[str, Any],
                                         evaluation_period: Tuple[float, float]) -> PerformanceScore:
        """Score collaboration quality"""
        collaboration_data = evidence_data.get('collaboration', {})
        
        # Analyze collaboration metrics
        knowledge_items = collaboration_data.get('knowledge_metrics', {}).get('total_items', 0)
        workload_distribution = collaboration_data.get('workload_distribution', {})
        
        # Calculate collaboration score
        knowledge_score = min(1.0, knowledge_items / 50)  # 50 items baseline
        
        # Workload balance (lower variance is better)
        if workload_distribution:
            workloads = list(workload_distribution.values())
            workload_variance = statistics.variance(workloads) if len(workloads) > 1 else 0
            balance_score = max(0, 1.0 - (workload_variance / 10))  # 10 tasks variance baseline
        else:
            balance_score = 0.5
            
        collaboration_score = (knowledge_score * 0.6) + (balance_score * 0.4)
        
        return PerformanceScore(
            metric=PerformanceMetric.COLLABORATION_QUALITY,
            score=collaboration_score,
            level=self._score_to_level(collaboration_score),
            confidence=0.8,
            evidence=[f"Knowledge items: {knowledge_items}", f"Workload balance: {balance_score:.2f}"],
            timestamp=time.time(),
            trend="stable"
        )
        
    # Placeholder implementations for other scoring methods
    async def _score_communication(self, team_id: str, evidence_data: Dict[str, Any],
                                 evaluation_period: Tuple[float, float]) -> PerformanceScore:
        """Score communication effectiveness"""
        return PerformanceScore(
            metric=PerformanceMetric.COMMUNICATION,
            score=0.70,
            level=PerformanceLevel.GOOD,
            confidence=0.6,
            evidence=["Communication analysis placeholder"],
            timestamp=time.time(),
            trend="stable"
        )
        
    async def _score_innovation(self, team_id: str, evidence_data: Dict[str, Any],
                              evaluation_period: Tuple[float, float]) -> PerformanceScore:
        """Score innovation and creativity"""
        return PerformanceScore(
            metric=PerformanceMetric.INNOVATION,
            score=0.65,
            level=PerformanceLevel.GOOD,
            confidence=0.6,
            evidence=["Innovation analysis placeholder"],
            timestamp=time.time(),
            trend="stable"
        )
        
    async def _score_adaptability(self, team_id: str, evidence_data: Dict[str, Any],
                                evaluation_period: Tuple[float, float]) -> PerformanceScore:
        """Score team adaptability"""
        return PerformanceScore(
            metric=PerformanceMetric.ADAPTABILITY,
            score=0.72,
            level=PerformanceLevel.GOOD,
            confidence=0.7,
            evidence=["Adaptability analysis placeholder"],
            timestamp=time.time(),
            trend="stable"
        )
        
    async def _score_knowledge_sharing(self, team_id: str, evidence_data: Dict[str, Any],
                                     evaluation_period: Tuple[float, float]) -> PerformanceScore:
        """Score knowledge sharing effectiveness"""
        collaboration_data = evidence_data.get('collaboration', {})
        knowledge_metrics = collaboration_data.get('knowledge_metrics', {})
        
        total_items = knowledge_metrics.get('total_items', 0)
        knowledge_score = min(1.0, total_items / 100)  # 100 items baseline
        
        return PerformanceScore(
            metric=PerformanceMetric.KNOWLEDGE_SHARING,
            score=knowledge_score,
            level=self._score_to_level(knowledge_score),
            confidence=0.8,
            evidence=[f"Knowledge items shared: {total_items}"],
            timestamp=time.time(),
            trend="stable"
        )
        
    async def _score_decision_quality(self, team_id: str, evidence_data: Dict[str, Any],
                                    evaluation_period: Tuple[float, float]) -> PerformanceScore:
        """Score decision quality"""
        decision_data = evidence_data.get('decisions', {})
        
        total_decisions = decision_data.get('total_decisions', 0)
        status_distribution = decision_data.get('status_distribution', {})
        
        if total_decisions > 0:
            approved_decisions = status_distribution.get('approved', 0)
            decision_quality_score = approved_decisions / total_decisions
        else:
            decision_quality_score = 0.5
            
        return PerformanceScore(
            metric=PerformanceMetric.DECISION_QUALITY,
            score=decision_quality_score,
            level=self._score_to_level(decision_quality_score),
            confidence=0.8,
            evidence=[f"Approved decisions: {approved_decisions}/{total_decisions}"],
            timestamp=time.time(),
            trend="stable"
        )
        
    async def _score_resource_utilization(self, team_id: str, evidence_data: Dict[str, Any],
                                        evaluation_period: Tuple[float, float]) -> PerformanceScore:
        """Score resource utilization efficiency"""
        return PerformanceScore(
            metric=PerformanceMetric.RESOURCE_UTILIZATION,
            score=0.78,
            level=PerformanceLevel.GOOD,
            confidence=0.7,
            evidence=["Resource utilization analysis placeholder"],
            timestamp=time.time(),
            trend="stable"
        )
        
    async def _score_learning_rate(self, team_id: str, evidence_data: Dict[str, Any],
                                 evaluation_period: Tuple[float, float]) -> PerformanceScore:
        """Score team learning rate"""
        return PerformanceScore(
            metric=PerformanceMetric.LEARNING_RATE,
            score=0.68,
            level=PerformanceLevel.GOOD,
            confidence=0.6,
            evidence=["Learning rate analysis placeholder"],
            timestamp=time.time(),
            trend="stable"
        )
        
    def _score_to_level(self, score: float) -> PerformanceLevel:
        """Convert score to performance level"""
        if score >= 0.9:
            return PerformanceLevel.EXCEPTIONAL
        elif score >= 0.8:
            return PerformanceLevel.HIGH
        elif score >= 0.6:
            return PerformanceLevel.GOOD
        elif score >= 0.4:
            return PerformanceLevel.FAIR
        else:
            return PerformanceLevel.POOR
            
    async def _calculate_overall_score(self, metric_scores: Dict[PerformanceMetric, PerformanceScore],
                                     weights: Dict[PerformanceMetric, float]) -> float:
        """Calculate weighted overall score"""
        total_score = 0.0
        total_weight = 0.0
        
        for metric, weight in weights.items():
            if metric in metric_scores:
                total_score += metric_scores[metric].score * weight
                total_weight += weight
                
        return total_score / total_weight if total_weight > 0 else 0.0
        
    async def _identify_strengths(self, metric_scores: Dict[PerformanceMetric, PerformanceScore]) -> List[str]:
        """Identify team strengths"""
        strengths = []
        
        for metric, score in metric_scores.items():
            if score.level in [PerformanceLevel.EXCEPTIONAL, PerformanceLevel.HIGH]:
                strengths.append(f"Strong {metric.value}: {score.score:.2f}")
                
        return strengths
        
    async def _identify_weaknesses(self, metric_scores: Dict[PerformanceMetric, PerformanceScore]) -> List[str]:
        """Identify team weaknesses"""
        weaknesses = []
        
        for metric, score in metric_scores.items():
            if score.level in [PerformanceLevel.POOR, PerformanceLevel.FAIR]:
                weaknesses.append(f"Needs improvement in {metric.value}: {score.score:.2f}")
                
        return weaknesses
        
    async def _generate_recommendations(self, team_id: str, 
                                      metric_scores: Dict[PerformanceMetric, PerformanceScore],
                                      weaknesses: List[str]) -> List[str]:
        """Generate improvement recommendations"""
        recommendations = []
        
        # Generate recommendations based on weaknesses
        for metric, score in metric_scores.items():
            if score.level in [PerformanceLevel.POOR, PerformanceLevel.FAIR]:
                if metric == PerformanceMetric.EFFICIENCY:
                    recommendations.append("Implement task automation and streamline processes")
                elif metric == PerformanceMetric.COLLABORATION_QUALITY:
                    recommendations.append("Enhance team communication and knowledge sharing")
                elif metric == PerformanceMetric.DECISION_QUALITY:
                    recommendations.append("Improve decision-making processes and criteria")
                # Add more specific recommendations
                
        return recommendations
        
    async def _identify_improvement_opportunities(self, team_id: str,
                                                metric_scores: Dict[PerformanceMetric, PerformanceScore],
                                                evidence_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify specific improvement opportunities"""
        opportunities = []
        
        for metric, score in metric_scores.items():
            if score.score < 0.8:  # Room for improvement
                opportunity = {
                    'metric': metric.value,
                    'current_score': score.score,
                    'target_score': min(1.0, score.score + 0.2),
                    'improvement_potential': min(0.2, 1.0 - score.score),
                    'priority': 'high' if score.score < 0.6 else 'medium'
                }
                opportunities.append(opportunity)
                
        return opportunities
        
    def _get_benchmark_comparisons(self, metric_scores: Dict[PerformanceMetric, PerformanceScore]) -> Dict[str, float]:
        """Get benchmark comparisons"""
        benchmarks = {}
        
        for benchmark_name, benchmark_values in self.benchmark_data.items():
            total_diff = 0.0
            count = 0
            
            for metric, score in metric_scores.items():
                if metric in benchmark_values:
                    diff = score.score - benchmark_values[metric]
                    total_diff += diff
                    count += 1
                    
            if count > 0:
                benchmarks[benchmark_name] = total_diff / count
                
        return benchmarks
        
    async def _calculate_performance_trends(self, team_id: str,
                                          metric_scores: Dict[PerformanceMetric, PerformanceScore]) -> Dict[str, str]:
        """Calculate performance trends"""
        trends = {}
        
        # Get historical data
        if team_id in self.performance_history and len(self.performance_history[team_id]) > 1:
            previous_report = self.performance_history[team_id][-2]
            
            for metric, current_score in metric_scores.items():
                if metric in previous_report.metric_scores:
                    previous_score = previous_report.metric_scores[metric].score
                    
                    if current_score.score > previous_score + 0.05:
                        trends[metric.value] = "improving"
                    elif current_score.score < previous_score - 0.05:
                        trends[metric.value] = "declining"
                    else:
                        trends[metric.value] = "stable"
                else:
                    trends[metric.value] = "new"
        else:
            # No historical data
            for metric in metric_scores:
                trends[metric.value] = "baseline"
                
        return trends
        
    async def _update_performance_trends(self, team_id: str,
                                       metric_scores: Dict[PerformanceMetric, PerformanceScore]):
        """Update performance trends data"""
        if team_id not in self.performance_trends:
            self.performance_trends[team_id] = {}
            
        for metric, score in metric_scores.items():
            if metric.value not in self.performance_trends[team_id]:
                self.performance_trends[team_id][metric.value] = []
                
            self.performance_trends[team_id][metric.value].append(score.score)
            
            # Keep only last 20 data points
            if len(self.performance_trends[team_id][metric.value]) > 20:
                self.performance_trends[team_id][metric.value] = \
                    self.performance_trends[team_id][metric.value][-20:]
                    
    async def get_team_performance_summary(self, team_id: str) -> Dict[str, Any]:
        """Get team performance summary"""
        if team_id not in self.performance_history:
            return {'team_id': team_id, 'status': 'no_evaluations'}
            
        latest_report = self.performance_history[team_id][-1]
        
        return {
            'team_id': team_id,
            'latest_evaluation': latest_report.generated_at,
            'overall_score': latest_report.overall_score,
            'performance_level': self._score_to_level(latest_report.overall_score).value,
            'total_evaluations': len(self.performance_history[team_id]),
            'strengths': latest_report.strengths,
            'top_recommendations': latest_report.recommendations[:3],
            'trends': latest_report.trends
        }
        
    async def get_performance_analytics(self) -> Dict[str, Any]:
        """Get overall performance analytics"""
        total_teams = len(self.performance_history)
        total_evaluations = sum(len(reports) for reports in self.performance_history.values())
        
        if total_evaluations == 0:
            return {'total_teams': 0, 'total_evaluations': 0}
            
        # Calculate average scores
        all_scores = []
        for reports in self.performance_history.values():
            for report in reports:
                all_scores.append(report.overall_score)
                
        avg_score = statistics.mean(all_scores)
        
        # Performance distribution
        performance_distribution = {}
        for score in all_scores:
            level = self._score_to_level(score).value
            performance_distribution[level] = performance_distribution.get(level, 0) + 1
            
        return {
            'total_teams': total_teams,
            'total_evaluations': total_evaluations,
            'average_score': avg_score,
            'performance_distribution': performance_distribution,
            'teams_evaluated': list(self.performance_history.keys())
        }

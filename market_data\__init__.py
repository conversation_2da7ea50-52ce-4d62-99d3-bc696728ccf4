"""
Market Data Package - Comprehensive Market Data Integration

This package provides unified market data management with support for
multiple data providers, real-time feeds, historical data, and data quality validation.

Components:
- MarketDataManager: Main coordinator for all market data operations
- DataProviderInterface: Abstract interface for data providers
- MockDataProvider: Mock provider for testing
- AlphaVantageProvider: Alpha Vantage API integration
- DataQualityValidator: Data quality validation and scoring

Features:
- Multiple data provider support with failover
- Real-time data streaming and subscriptions
- Historical data retrieval and caching
- Data quality validation and scoring
- Redis caching for performance
- Provider health monitoring
- Configurable data sources
"""

from .data_manager import (
    MarketDataManager,
    DataProviderInterface,
    MockDataProvider,
    AlphaVantageProvider,
    DataQualityValidator,
    MarketDataPoint,
    Quote,
    Bar,
    DataProvider,
    DataType
)

__all__ = [
    'MarketDataManager',
    'DataProviderInterface',
    'MockDataProvider',
    'AlphaVantageProvider',
    'DataQualityValidator',
    'MarketDataPoint',
    'Quote',
    'Bar',
    'DataProvider',
    'DataType'
]

__version__ = '1.0.0'
__author__ = 'Advanced Ollama Trading Agents Team'
__description__ = 'Comprehensive Market Data Integration System'

# Advanced Ollama Trading Agent System

A sophisticated team-based trading system powered by Ollama LLM models with specialized agent roles, dynamic team formation, competitive/cooperative modes, and self-improvement capabilities.

## Architecture Overview

This system implements a hierarchical team-based architecture where specialized AI agents collaborate to make trading decisions. The system features:

- **Dynamic Team Formation**: Teams adapt based on market conditions
- **Specialized Agent Roles**: Team Leaders, Market Analysts, Strategy Developers, Risk Managers, Execution Specialists, and Performance Evaluators
- **Competitive & Cooperative Modes**: Agents can compete or collaborate based on market conditions
- **Self-Improvement**: Continuous learning and adaptation capabilities
- **Multi-Model Support**: Leverages multiple Ollama models optimized for different roles

## Available Ollama Models

The system is designed to work with your available models:
- exaone-deep:32b (19 GB) - Team Leaders
- huihui_ai/am-thinking-abliterate:latest (19 GB) - Strategy Developers
- huihui_ai/magistral-abliterated:24b (14 GB) - Market Analysts
- qwen2.5vl:32b (21 GB) - Visual Analysis
- phi4-reasoning:plus (11 GB) - Risk Managers
- command-r:35b (18 GB) - Team Coordination
- cogito:32b (19 GB) - Creative Strategy Development
- And many more specialized models...

## Project Structure

```
ollama_trading_system/
├── agents/                 # Agent implementations
├── models/                 # Ollama model management
├── teams/                  # Team formation and management
├── communication/          # Inter-agent communication
├── data/                   # Market data and storage
├── strategies/             # Trading strategies
├── risk/                   # Risk management
├── execution/              # Trade execution
├── monitoring/             # Performance monitoring
├── learning/               # Self-improvement systems
├── config/                 # Configuration files
├── tests/                  # Test suites
└── docs/                   # Documentation
```

## Getting Started

1. Ensure Ollama is running with your models loaded
2. Install dependencies: `pip install -r requirements.txt`
3. Configure the system: Edit `config/system_config.yaml`
4. Initialize the database: `python scripts/init_db.py`
5. Start the system: `python main.py`

## Development Status

This is an active development project implementing the advanced architecture described in the documentation files.

## Key Features

### Agent Specializations
- **Team Leaders**: Coordinate team activities using large comprehensive models
- **Market Analysts**: Identify patterns and opportunities with pattern recognition models
- **Strategy Developers**: Create innovative approaches using creative reasoning models
- **Risk Managers**: Protect assets with precise, conservative models
- **Execution Specialists**: Optimize trade execution with fast, efficient models
- **Performance Evaluators**: Track and improve system performance

### Team Dynamics
- Dynamic team formation based on market regimes
- Hierarchical decision-making with clear escalation paths
- Competitive tournaments and performance leagues
- Collaborative problem-solving mechanisms

### Learning Systems
- Individual agent learning through performance-based fine-tuning
- Team learning through collective performance analysis
- Cross-team learning for strategy sharing and best practice propagation
- Centralized learning repository for successful strategies

## Configuration

The system uses YAML configuration files for easy customization:
- `config/system_config.yaml` - Main system configuration
- `config/model_configs.yaml` - Ollama model configurations
- `config/agent_configs.yaml` - Agent-specific settings
- `config/team_configs.yaml` - Team formation rules
- `config/market_configs.yaml` - Market data and trading settings

## Monitoring and Analytics

Built-in monitoring includes:
- Real-time performance dashboards
- Agent and team performance metrics
- System resource utilization
- Model inference performance
- Trading performance analytics

## Security and Compliance

- End-to-end encryption for sensitive data
- Role-based access control
- Audit logging for all decisions
- Compliance monitoring and reporting

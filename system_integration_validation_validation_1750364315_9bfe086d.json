{"validation_id": "validation_**********_9bfe086d", "validation_level": "basic", "overall_status": "partial", "overall_score": 0.804379470679538, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "completed", "integration_score": 0.8818671959323277, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "team_manager", "component_type": "core", "status": "completed", "integration_score": 0.8392635779077849, "error_count": 0, "warnings": ["Integration issues in team_manager"], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "completed", "integration_score": 0.837535386245688, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "analytics_engine", "component_type": "core", "status": "completed", "integration_score": 0.815372766436463, "error_count": 0, "warnings": ["Integration issues in analytics_engine"], "dependencies_met": "False"}, {"component_name": "ollama_hub", "component_type": "core", "status": "completed", "integration_score": 0.8046451616277709, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "completed", "integration_score": 0.8213644355046509, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "completed", "integration_score": 0.8893065846205582, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "risk_manager", "component_type": "core", "status": "completed", "integration_score": 0.8673736205957447, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "completed", "integration_score": 0.7729664992804715, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8924189480084552, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8193785546705126, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8436251140856018, "error_count": 0, "warnings": ["Integration issues in self_improvement_engine"], "dependencies_met": "True"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "completed", "integration_score": 0.8069751040695021, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "completed", "integration_score": 0.8722316825218684, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8261451298663359, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "completed", "integration_score": 0.8371155045097016, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "completed", "integration_score": 0.8883219547139528, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "completed", "integration_score": 0.9339010539461878, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "completed", "integration_score": 0.9049373859111347, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "completed", "integration_score": 0.8423241245346549, "error_count": 0, "warnings": [], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.8267826388524551, "data_manager": 0.6256262454872011, "analytics_engine": 0.8591946691073775, "ollama_hub": 0.7948816382372462, "execution_engine": 0.6833450552154389, "portfolio_manager": 0.8558311814227592, "risk_manager": 0.6900862707276338, "strategy_manager": 0.8169018294177787, "competitive_framework": 0.697939040651276, "tournament_framework": 0.6573892526555265, "self_improvement_engine": 0.7373568258091742, "regime_adaptation_system": 0.839615512508258, "performance_optimizer": 0.7322694414660067, "advanced_trading_engine": 0.6258606634079408, "ai_coordinator": 0.7393064411095643, "configuration_manager": 0.7748261030501935, "mock_data_providers": 0.7947661189585096, "paper_trading_engine": 0.6908493131276942, "logging_audit_system": 0.8234077623500107}, "team_manager": {"system_coordinator": 0.8175476847382502, "team_manager": 1.0, "data_manager": 0.9643494687230958, "analytics_engine": 0.6458543630013648, "ollama_hub": 0.7703170628979167, "execution_engine": 0.7034180655322646, "portfolio_manager": 0.6178050154414398, "risk_manager": 0.6270145837719849, "strategy_manager": 0.7172223401466068, "competitive_framework": 0.6726859995011298, "tournament_framework": 0.6572595184520416, "self_improvement_engine": 0.7152509523835232, "regime_adaptation_system": 0.8834876886143028, "performance_optimizer": 0.7446877610839744, "advanced_trading_engine": 0.7098318927066666, "ai_coordinator": 0.8090427379110634, "configuration_manager": 0.6275253280391798, "mock_data_providers": 0.7555703457168759, "paper_trading_engine": 0.658242813560993, "logging_audit_system": 0.7125561456683025}, "data_manager": {"system_coordinator": 0.810781486040139, "team_manager": 0.8734605888190583, "data_manager": 1.0, "analytics_engine": 0.9379553492356074, "ollama_hub": 0.7252466105695403, "execution_engine": 0.7136465587146992, "portfolio_manager": 0.725536247506485, "risk_manager": 0.7898498578271322, "strategy_manager": 0.6045908740121673, "competitive_framework": 0.7350641536361536, "tournament_framework": 0.7330545937460083, "self_improvement_engine": 0.8446328041247768, "regime_adaptation_system": 0.8338799908829314, "performance_optimizer": 0.6959948096216337, "advanced_trading_engine": 0.8824935463739425, "ai_coordinator": 0.624271107588824, "configuration_manager": 0.6813811010363898, "mock_data_providers": 0.6895406921583357, "paper_trading_engine": 0.8277012542154163, "logging_audit_system": 0.8953560020493302}, "analytics_engine": {"system_coordinator": 0.8915044899097814, "team_manager": 0.7434895760802656, "data_manager": 0.6260173548392205, "analytics_engine": 1.0, "ollama_hub": 0.6061476143801142, "execution_engine": 0.7097297394290464, "portfolio_manager": 0.8388673239134254, "risk_manager": 0.6255613243473191, "strategy_manager": 0.9794391355007606, "competitive_framework": 0.6570279425745762, "tournament_framework": 0.6896200862050651, "self_improvement_engine": 0.8668930717650953, "regime_adaptation_system": 0.8539131624603078, "performance_optimizer": 0.8853122586040152, "advanced_trading_engine": 0.6816912290591559, "ai_coordinator": 0.6512319166401116, "configuration_manager": 0.7265588859772623, "mock_data_providers": 0.8441794140266032, "paper_trading_engine": 0.6032708991330095, "logging_audit_system": 0.6275874705392691}, "ollama_hub": {"system_coordinator": 0.8356636344179286, "team_manager": 0.8675753560708761, "data_manager": 0.8223196510301721, "analytics_engine": 0.8005206248578501, "ollama_hub": 1.0, "execution_engine": 0.6128164705020681, "portfolio_manager": 0.799353733942434, "risk_manager": 0.7762126559421166, "strategy_manager": 0.6598408409507169, "competitive_framework": 0.6767106033268748, "tournament_framework": 0.6848563877503555, "self_improvement_engine": 0.6745844269795036, "regime_adaptation_system": 0.8796521193309528, "performance_optimizer": 0.6155641197946075, "advanced_trading_engine": 0.6361037203927394, "ai_coordinator": 0.7376952881240944, "configuration_manager": 0.7753062479159712, "mock_data_providers": 0.7624066834368483, "paper_trading_engine": 0.600648868510532, "logging_audit_system": 0.7756786683056568}, "execution_engine": {"system_coordinator": 0.8060968865581192, "team_manager": 0.7589437584254852, "data_manager": 0.8214499251521501, "analytics_engine": 0.8849138864630532, "ollama_hub": 0.7163889830824488, "execution_engine": 1.0, "portfolio_manager": 0.9953109086647689, "risk_manager": 0.6649056550024286, "strategy_manager": 0.8168909669653771, "competitive_framework": 0.821032781219118, "tournament_framework": 0.7080139206593954, "self_improvement_engine": 0.8033025112810341, "regime_adaptation_system": 0.6163824903183991, "performance_optimizer": 0.766862566884811, "advanced_trading_engine": 0.8424314385841052, "ai_coordinator": 0.6194522385341124, "configuration_manager": 0.8527186004900924, "mock_data_providers": 0.89854963147735, "paper_trading_engine": 0.60183695796928, "logging_audit_system": 0.7117917634830279}, "portfolio_manager": {"system_coordinator": 0.8131445333750613, "team_manager": 0.884760261623083, "data_manager": 0.7445599253133812, "analytics_engine": 0.8374852873439578, "ollama_hub": 0.713955228307207, "execution_engine": 0.6102719885629059, "portfolio_manager": 1.0, "risk_manager": 0.6060884077294241, "strategy_manager": 0.6442816649597379, "competitive_framework": 0.7792902314108641, "tournament_framework": 0.8252230743858406, "self_improvement_engine": 0.7682809753227864, "regime_adaptation_system": 0.6101033121013567, "performance_optimizer": 0.8296016843212068, "advanced_trading_engine": 0.7322198914393643, "ai_coordinator": 0.805582995052769, "configuration_manager": 0.7545708060292601, "mock_data_providers": 0.7771539078344524, "paper_trading_engine": 0.8261760803997411, "logging_audit_system": 0.7522284359079555}, "risk_manager": {"system_coordinator": 0.7382643776587499, "team_manager": 0.7237272290392763, "data_manager": 0.7341715254895425, "analytics_engine": 0.616309632559124, "ollama_hub": 0.8885874360495223, "execution_engine": 0.6499110978301398, "portfolio_manager": 0.6473972222555957, "risk_manager": 1.0, "strategy_manager": 0.7537834724432763, "competitive_framework": 0.7115617876539092, "tournament_framework": 0.8784356029024045, "self_improvement_engine": 0.6907538912004795, "regime_adaptation_system": 0.8603670757982814, "performance_optimizer": 0.6820121252659564, "advanced_trading_engine": 0.824321468889443, "ai_coordinator": 0.8409748766709791, "configuration_manager": 0.7177574890985409, "mock_data_providers": 0.8840252402350306, "paper_trading_engine": 0.7927145979856721, "logging_audit_system": 0.8692522399943168}, "strategy_manager": {"system_coordinator": 0.6324313344214187, "team_manager": 0.7594109632509728, "data_manager": 0.7505133922023067, "analytics_engine": 0.6276811952146157, "ollama_hub": 0.7857572910879638, "execution_engine": 0.8525548142556802, "portfolio_manager": 0.8780143840250557, "risk_manager": 0.7751263339700157, "strategy_manager": 1.0, "competitive_framework": 0.8593962116669984, "tournament_framework": 0.7739067722822148, "self_improvement_engine": 0.6112370572468317, "regime_adaptation_system": 0.671435221349798, "performance_optimizer": 0.7382915384991915, "advanced_trading_engine": 0.6606036259665239, "ai_coordinator": 0.720902588419337, "configuration_manager": 0.7050204856733144, "mock_data_providers": 0.618373879366886, "paper_trading_engine": 0.8590057736810338, "logging_audit_system": 0.671766776210118}, "competitive_framework": {"system_coordinator": 0.7010804223662399, "team_manager": 0.7443233333480863, "data_manager": 0.7738872638354184, "analytics_engine": 0.8638718391908929, "ollama_hub": 0.7160453874216756, "execution_engine": 0.6833195942298621, "portfolio_manager": 0.8752145799630728, "risk_manager": 0.6184927755487726, "strategy_manager": 0.6521178253899721, "competitive_framework": 1.0, "tournament_framework": 0.6450603444924318, "self_improvement_engine": 0.7444396051827005, "regime_adaptation_system": 0.6295427950267919, "performance_optimizer": 0.706836141699701, "advanced_trading_engine": 0.8691290493842333, "ai_coordinator": 0.8318294702977105, "configuration_manager": 0.7623626184812318, "mock_data_providers": 0.8445867857427702, "paper_trading_engine": 0.7433711407417547, "logging_audit_system": 0.7003311792282924}, "tournament_framework": {"system_coordinator": 0.7289555687439963, "team_manager": 0.8134754370249107, "data_manager": 0.7824555886678973, "analytics_engine": 0.7792531485716893, "ollama_hub": 0.6402905524686419, "execution_engine": 0.6077340224561469, "portfolio_manager": 0.8596051438784569, "risk_manager": 0.8830669276573452, "strategy_manager": 0.6809508537611179, "competitive_framework": 0.6337012314437698, "tournament_framework": 1.0, "self_improvement_engine": 0.7085791162710876, "regime_adaptation_system": 0.682332170941091, "performance_optimizer": 0.8603772116668066, "advanced_trading_engine": 0.8804156890792019, "ai_coordinator": 0.7205969318047273, "configuration_manager": 0.722730207177315, "mock_data_providers": 0.6199473463755495, "paper_trading_engine": 0.8152244004851117, "logging_audit_system": 0.8926720382424895}, "self_improvement_engine": {"system_coordinator": 0.8842083027742829, "team_manager": 0.6681495968302592, "data_manager": 0.6804520293181816, "analytics_engine": 0.7076869821122947, "ollama_hub": 0.6336460701313906, "execution_engine": 0.7675293248608779, "portfolio_manager": 0.7171306344316194, "risk_manager": 0.6897376964198156, "strategy_manager": 0.8421860826202949, "competitive_framework": 0.6116631538681231, "tournament_framework": 0.8730818663972748, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.845232061245484, "performance_optimizer": 0.6017706774463442, "advanced_trading_engine": 0.8776099502636818, "ai_coordinator": 0.6511232758888448, "configuration_manager": 0.7096675350815538, "mock_data_providers": 0.7741119552522807, "paper_trading_engine": 0.8344559126427922, "logging_audit_system": 0.6035215243461483}, "regime_adaptation_system": {"system_coordinator": 0.863798193723379, "team_manager": 0.78464089707981, "data_manager": 0.6317344361382912, "analytics_engine": 0.8362516613677264, "ollama_hub": 0.7503064247441288, "execution_engine": 0.7563858473312414, "portfolio_manager": 0.6853525137170241, "risk_manager": 0.6595882230682926, "strategy_manager": 0.7646449604278084, "competitive_framework": 0.8561733884075149, "tournament_framework": 0.6788741007492098, "self_improvement_engine": 0.6349916863767485, "regime_adaptation_system": 1.0, "performance_optimizer": 0.7474570228764149, "advanced_trading_engine": 0.7062269612166897, "ai_coordinator": 0.8109363926239987, "configuration_manager": 0.8995251296699945, "mock_data_providers": 0.8195660924563355, "paper_trading_engine": 0.6644013319214661, "logging_audit_system": 0.7987517039226857}, "performance_optimizer": {"system_coordinator": 0.6562261665256208, "team_manager": 0.8661570377895154, "data_manager": 0.8653519226462799, "analytics_engine": 0.7621272766831064, "ollama_hub": 0.6592896291756007, "execution_engine": 0.8538295663613108, "portfolio_manager": 0.8545961712012071, "risk_manager": 0.8498363447467645, "strategy_manager": 0.8924724915038242, "competitive_framework": 0.8845611473944962, "tournament_framework": 0.6576566272644159, "self_improvement_engine": 0.6102819141482784, "regime_adaptation_system": 0.7638301328547485, "performance_optimizer": 1.0, "advanced_trading_engine": 0.6440004898582027, "ai_coordinator": 0.6183653896077221, "configuration_manager": 0.8503738552029578, "mock_data_providers": 0.872533287396486, "paper_trading_engine": 0.7034033887402951, "logging_audit_system": 0.7345878308005934}, "advanced_trading_engine": {"system_coordinator": 0.7551749295999892, "team_manager": 0.6258004765011902, "data_manager": 0.6101465912838003, "analytics_engine": 0.7956440511977855, "ollama_hub": 0.6720461005676673, "execution_engine": 0.8047625468621158, "portfolio_manager": 0.8069471488826433, "risk_manager": 0.7188915547446381, "strategy_manager": 0.6303420921323314, "competitive_framework": 0.8243368046342137, "tournament_framework": 0.7468700687614618, "self_improvement_engine": 0.6416049996331338, "regime_adaptation_system": 0.7900236980414231, "performance_optimizer": 0.7080059233906458, "advanced_trading_engine": 1.0, "ai_coordinator": 0.6638623372036369, "configuration_manager": 0.6780357984491662, "mock_data_providers": 0.8419928045079309, "paper_trading_engine": 0.6945436004971508, "logging_audit_system": 0.6544830795075176}, "ai_coordinator": {"system_coordinator": 0.6905504100203973, "team_manager": 0.865909410537246, "data_manager": 0.6007122619764248, "analytics_engine": 0.7363782501199748, "ollama_hub": 0.8759115259792879, "execution_engine": 0.6289076334223501, "portfolio_manager": 0.7366397839974456, "risk_manager": 0.6593029335440947, "strategy_manager": 0.7409477406381169, "competitive_framework": 0.7948125250318466, "tournament_framework": 0.7968203857960118, "self_improvement_engine": 0.7143882287671384, "regime_adaptation_system": 0.8284379457184411, "performance_optimizer": 0.7080261777883186, "advanced_trading_engine": 0.7274100491083828, "ai_coordinator": 1.0, "configuration_manager": 0.6313145011945714, "mock_data_providers": 0.699861856941031, "paper_trading_engine": 0.7367269118237268, "logging_audit_system": 0.6813169933317669}, "configuration_manager": {"system_coordinator": 0.7542676779216586, "team_manager": 0.72442980495218, "data_manager": 0.6066779845490364, "analytics_engine": 0.7262848433386782, "ollama_hub": 0.619911931437804, "execution_engine": 0.6117449917687842, "portfolio_manager": 0.7513150855377199, "risk_manager": 0.6914388147303048, "strategy_manager": 0.7175526303929987, "competitive_framework": 0.8708365079649149, "tournament_framework": 0.8647476333606906, "self_improvement_engine": 0.8358379956461797, "regime_adaptation_system": 0.6771459603748073, "performance_optimizer": 0.8475676044464935, "advanced_trading_engine": 0.642918889774664, "ai_coordinator": 0.6867080885028652, "configuration_manager": 1.0, "mock_data_providers": 0.6176000584854562, "paper_trading_engine": 0.7692813447342194, "logging_audit_system": 0.7966052952998871}, "mock_data_providers": {"system_coordinator": 0.87999334716698, "team_manager": 0.8428577267191346, "data_manager": 0.7283792389204733, "analytics_engine": 0.8278550770029598, "ollama_hub": 0.8949628099483031, "execution_engine": 0.8680685134932526, "portfolio_manager": 0.742432440260065, "risk_manager": 0.7266084957430966, "strategy_manager": 0.7108182129692444, "competitive_framework": 0.6786687992955849, "tournament_framework": 0.8963690471959813, "self_improvement_engine": 0.8084607725749764, "regime_adaptation_system": 0.6338490183926723, "performance_optimizer": 0.6602406936417802, "advanced_trading_engine": 0.8330002404549359, "ai_coordinator": 0.8897076265796362, "configuration_manager": 0.6562615749742722, "mock_data_providers": 1.0, "paper_trading_engine": 0.6467536286186862, "logging_audit_system": 0.773850927959504}, "paper_trading_engine": {"system_coordinator": 0.7911050500187251, "team_manager": 0.7374179575847932, "data_manager": 0.8413239167571668, "analytics_engine": 0.7810394738027588, "ollama_hub": 0.7298592542920315, "execution_engine": 0.6328375785431304, "portfolio_manager": 0.6210160555415627, "risk_manager": 0.625651012910262, "strategy_manager": 0.7942590354708959, "competitive_framework": 0.7347834103929579, "tournament_framework": 0.7511562610051489, "self_improvement_engine": 0.820340754307788, "regime_adaptation_system": 0.7549729700232282, "performance_optimizer": 0.6514845737166115, "advanced_trading_engine": 0.6950466561714115, "ai_coordinator": 0.7729543332828406, "configuration_manager": 0.702726754426058, "mock_data_providers": 0.8800384024766996, "paper_trading_engine": 1.0, "logging_audit_system": 0.6999711776915281}, "logging_audit_system": {"system_coordinator": 0.7612070497573379, "team_manager": 0.6142675854365126, "data_manager": 0.6011749029947295, "analytics_engine": 0.7818295655579209, "ollama_hub": 0.8464947365621418, "execution_engine": 0.876603510821364, "portfolio_manager": 0.8596743444574844, "risk_manager": 0.7937256184135728, "strategy_manager": 0.7257892346765262, "competitive_framework": 0.7115975594768776, "tournament_framework": 0.7059218897470168, "self_improvement_engine": 0.8689098997825102, "regime_adaptation_system": 0.8390692461983109, "performance_optimizer": 0.7677873178067625, "advanced_trading_engine": 0.6210207116472296, "ai_coordinator": 0.8751888036350026, "configuration_manager": 0.7951755028032808, "mock_data_providers": 0.6595615338826866, "paper_trading_engine": 0.6176777568623744, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.8675685533166718, "response_time": 0.931933516586859, "throughput": 0.6955606223189486, "memory_usage": 0.8701136809350786, "cpu_usage": 0.8772260205564945, "concurrent_operations": 0.6435050468989885}, "critical_issues": ["Components with dependency issues: system_coordinator, analytics_engine, execution_engine, portfolio_manager, advanced_trading_engine, ai_coordinator, configuration_manager"], "recommendations": ["Enhance component integration and communication"], "production_ready": "True", "timestamp": **********.7128975}
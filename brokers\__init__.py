"""
Brokers Package - Comprehensive Broker Integration and Connectivity

This package provides unified broker management with support for multiple
broker APIs, order routing, trade execution, and connectivity management.

Components:
- BrokerManager: Main coordinator for all broker operations
- BrokerInterface: Abstract interface for broker implementations
- MockBroker: Mock broker for testing
- PaperTradingBroker: Paper trading simulation
- Order, Position, Account: Core data structures

Features:
- Multiple broker support with failover
- Order routing and execution
- Position aggregation across brokers
- Real-time order monitoring
- Paper trading simulation
- Mock broker for testing
- Comprehensive order management
"""

from .broker_manager import (
    BrokerManager,
    BrokerInterface,
    MockBroker,
    PaperTradingBroker,
    Order,
    Position,
    Account,
    BrokerType,
    OrderType,
    OrderSide,
    OrderStatus,
    TimeInForce
)

__all__ = [
    'BrokerManager',
    'BrokerInterface',
    'MockBroker',
    'PaperTradingBroker',
    'Order',
    'Position',
    'Account',
    'BrokerType',
    'OrderType',
    'OrderSide',
    'OrderStatus',
    'TimeInForce'
]

__version__ = '1.0.0'
__author__ = 'Advanced Ollama Trading Agents Team'
__description__ = 'Comprehensive Broker Integration and Connectivity System'

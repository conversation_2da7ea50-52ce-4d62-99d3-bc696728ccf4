{"validation_id": "validation_**********_e6429d08", "validation_level": "basic", "overall_status": "partial", "overall_score": 0.7904071462407296, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "partial", "integration_score": 0.7640259793324334, "error_count": 0, "warnings": ["Functionality concerns in system_coordinator", "Integration issues in system_coordinator"], "dependencies_met": "True"}, {"component_name": "team_manager", "component_type": "core", "status": "partial", "integration_score": 0.7756198612809797, "error_count": 0, "warnings": ["Functionality concerns in team_manager"], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "completed", "integration_score": 0.8024769405985656, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "analytics_engine", "component_type": "core", "status": "completed", "integration_score": 0.8151508747096954, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "partial", "integration_score": 0.7444889066108801, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "partial", "integration_score": 0.7176018741947197, "error_count": 0, "warnings": ["Functionality concerns in execution_engine"], "dependencies_met": "True"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "completed", "integration_score": 0.8003380574144034, "error_count": 0, "warnings": ["Integration issues in portfolio_manager"], "dependencies_met": "True"}, {"component_name": "risk_manager", "component_type": "core", "status": "partial", "integration_score": 0.7829052606520169, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "partial", "integration_score": 0.753513736397827, "error_count": 0, "warnings": ["Functionality concerns in strategy_manager"], "dependencies_met": "True"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "partial", "integration_score": 0.7315183813424826, "error_count": 0, "warnings": ["Integration issues in competitive_framework"], "dependencies_met": "True"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "partial", "integration_score": 0.7928557026789443, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.7061642044280392, "error_count": 0, "warnings": ["Functionality concerns in self_improvement_engine", "Integration issues in self_improvement_engine"], "dependencies_met": "True"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "partial", "integration_score": 0.7252323944697722, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "completed", "integration_score": 0.80781686665375, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.7998360718721139, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "completed", "integration_score": 0.8011851153023587, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "partial", "integration_score": 0.7814001275198881, "error_count": 0, "warnings": ["Integration issues in configuration_manager"], "dependencies_met": "False"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "completed", "integration_score": 0.8584810738250916, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "completed", "integration_score": 0.8424266768038449, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "partial", "integration_score": 0.7604405122073126, "error_count": 0, "warnings": ["Functionality concerns in logging_audit_system", "Integration issues in logging_audit_system"], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.8445715775305508, "data_manager": 0.7197699364480568, "analytics_engine": 0.6399602821823953, "ollama_hub": 0.6213720212625177, "execution_engine": 0.6565151108427576, "portfolio_manager": 0.6689958400201353, "risk_manager": 0.6316351882938277, "strategy_manager": 0.8618460926900975, "competitive_framework": 0.7709342879112445, "tournament_framework": 0.7703148722778337, "self_improvement_engine": 0.7438658667598756, "regime_adaptation_system": 0.6625155321074693, "performance_optimizer": 0.8765904577834038, "advanced_trading_engine": 0.8511003292831549, "ai_coordinator": 0.6042682520238664, "configuration_manager": 0.6120361562209928, "mock_data_providers": 0.8039035445718521, "paper_trading_engine": 0.692197396039454, "logging_audit_system": 0.6279454485187826}, "team_manager": {"system_coordinator": 0.8840390649318475, "team_manager": 1.0, "data_manager": 0.9440592749532167, "analytics_engine": 0.6272145301241051, "ollama_hub": 0.6266222788035899, "execution_engine": 0.892589990705925, "portfolio_manager": 0.8699999773598048, "risk_manager": 0.7927556673481264, "strategy_manager": 0.6613841053155016, "competitive_framework": 0.7271000399974755, "tournament_framework": 0.8670713506662576, "self_improvement_engine": 0.6331601284159025, "regime_adaptation_system": 0.6268825058353719, "performance_optimizer": 0.6541920156504908, "advanced_trading_engine": 0.746606683498479, "ai_coordinator": 0.8866469856713568, "configuration_manager": 0.6943520260121732, "mock_data_providers": 0.8944968379672471, "paper_trading_engine": 0.8921528046980485, "logging_audit_system": 0.8575603783807901}, "data_manager": {"system_coordinator": 0.859261040990138, "team_manager": 0.8557330960527116, "data_manager": 1.0, "analytics_engine": 0.8750585736891576, "ollama_hub": 0.763310453136675, "execution_engine": 0.7504253459758603, "portfolio_manager": 0.7541468725656668, "risk_manager": 0.6622062840681928, "strategy_manager": 0.7872040638104815, "competitive_framework": 0.812674600687057, "tournament_framework": 0.624273626448593, "self_improvement_engine": 0.6098954427525418, "regime_adaptation_system": 0.6511719762563292, "performance_optimizer": 0.7610607345804277, "advanced_trading_engine": 0.8481590406746565, "ai_coordinator": 0.7703785057190119, "configuration_manager": 0.713905475422036, "mock_data_providers": 0.6163415426888764, "paper_trading_engine": 0.7510660627202507, "logging_audit_system": 0.8920918893839722}, "analytics_engine": {"system_coordinator": 0.6804906434009276, "team_manager": 0.7691893110297905, "data_manager": 0.6149547971432048, "analytics_engine": 1.0, "ollama_hub": 0.6762868494105817, "execution_engine": 0.6718737307678541, "portfolio_manager": 0.6460783898159639, "risk_manager": 0.8780325600814264, "strategy_manager": 0.9956866573946328, "competitive_framework": 0.6587160332809949, "tournament_framework": 0.6582918208717001, "self_improvement_engine": 0.797027657084263, "regime_adaptation_system": 0.8275169762311363, "performance_optimizer": 0.7792840627000236, "advanced_trading_engine": 0.7254284440909502, "ai_coordinator": 0.8824441399931295, "configuration_manager": 0.7449832930937872, "mock_data_providers": 0.7794015675245862, "paper_trading_engine": 0.8642481307854004, "logging_audit_system": 0.8959995704414516}, "ollama_hub": {"system_coordinator": 0.6460487446996658, "team_manager": 0.8672835868914506, "data_manager": 0.6590057252590332, "analytics_engine": 0.7590918889038553, "ollama_hub": 1.0, "execution_engine": 0.82017383253787, "portfolio_manager": 0.7322136273787191, "risk_manager": 0.8898754550760636, "strategy_manager": 0.8659965758777036, "competitive_framework": 0.8223763396458036, "tournament_framework": 0.8965917373681176, "self_improvement_engine": 0.6042273304185213, "regime_adaptation_system": 0.7529089483012665, "performance_optimizer": 0.657661233965731, "advanced_trading_engine": 0.7095444770406346, "ai_coordinator": 0.7010274219264202, "configuration_manager": 0.7621179198504043, "mock_data_providers": 0.6932298970560222, "paper_trading_engine": 0.7052858337018227, "logging_audit_system": 0.8234269343156817}, "execution_engine": {"system_coordinator": 0.7226427639018366, "team_manager": 0.7077282261474502, "data_manager": 0.6856244332090169, "analytics_engine": 0.7274277449363807, "ollama_hub": 0.7017718713009784, "execution_engine": 1.0, "portfolio_manager": 0.877516600794794, "risk_manager": 0.8120438097645135, "strategy_manager": 0.6148701313039169, "competitive_framework": 0.8958788231464507, "tournament_framework": 0.6409848070466853, "self_improvement_engine": 0.8951109633714931, "regime_adaptation_system": 0.6928962925674892, "performance_optimizer": 0.8096092639550425, "advanced_trading_engine": 0.866580724861358, "ai_coordinator": 0.8632395249669398, "configuration_manager": 0.8412264186146003, "mock_data_providers": 0.8195833887968179, "paper_trading_engine": 0.6096808808288522, "logging_audit_system": 0.7191772571817373}, "portfolio_manager": {"system_coordinator": 0.8581672260797433, "team_manager": 0.762141644236894, "data_manager": 0.7435752177596802, "analytics_engine": 0.6004949241575913, "ollama_hub": 0.6608538463789102, "execution_engine": 0.6425743428524593, "portfolio_manager": 1.0, "risk_manager": 0.6104442964319153, "strategy_manager": 0.779154284357011, "competitive_framework": 0.6077571336862742, "tournament_framework": 0.8834765445356706, "self_improvement_engine": 0.7774710689808643, "regime_adaptation_system": 0.8240708413446771, "performance_optimizer": 0.6204944747406052, "advanced_trading_engine": 0.7612067657620231, "ai_coordinator": 0.838781059110141, "configuration_manager": 0.6519351271883083, "mock_data_providers": 0.6469254988478749, "paper_trading_engine": 0.7736742671644145, "logging_audit_system": 0.7738110609348381}, "risk_manager": {"system_coordinator": 0.627445759298582, "team_manager": 0.6268078533955639, "data_manager": 0.7751422975025424, "analytics_engine": 0.6527856303504953, "ollama_hub": 0.7836898310006184, "execution_engine": 0.78849536075759, "portfolio_manager": 0.750852287429599, "risk_manager": 1.0, "strategy_manager": 0.84808435015407, "competitive_framework": 0.7970904002005141, "tournament_framework": 0.8186666491920342, "self_improvement_engine": 0.7252154874116894, "regime_adaptation_system": 0.8529160002634824, "performance_optimizer": 0.6462906621348794, "advanced_trading_engine": 0.8747451640937597, "ai_coordinator": 0.8899023783610369, "configuration_manager": 0.6097701792416813, "mock_data_providers": 0.8985101758907164, "paper_trading_engine": 0.7082827230703606, "logging_audit_system": 0.7336067021184177}, "strategy_manager": {"system_coordinator": 0.6777704296638999, "team_manager": 0.7589233383999437, "data_manager": 0.8273623263808788, "analytics_engine": 0.772286769961209, "ollama_hub": 0.8179682872416536, "execution_engine": 0.6286169803219857, "portfolio_manager": 0.7360481002024776, "risk_manager": 0.7687863356024395, "strategy_manager": 1.0, "competitive_framework": 0.6483727956342301, "tournament_framework": 0.6868339310699062, "self_improvement_engine": 0.7092033488523983, "regime_adaptation_system": 0.6796189950417566, "performance_optimizer": 0.7377007266064766, "advanced_trading_engine": 0.8135228489250408, "ai_coordinator": 0.6611118638502751, "configuration_manager": 0.6056135686156019, "mock_data_providers": 0.7004141187121357, "paper_trading_engine": 0.6374070020212507, "logging_audit_system": 0.849614030646359}, "competitive_framework": {"system_coordinator": 0.761651145245692, "team_manager": 0.6132126652224051, "data_manager": 0.6425121658493914, "analytics_engine": 0.7589402088360674, "ollama_hub": 0.8788977641926365, "execution_engine": 0.8356827310125209, "portfolio_manager": 0.622144687129069, "risk_manager": 0.6088903833119806, "strategy_manager": 0.7587030946603792, "competitive_framework": 1.0, "tournament_framework": 0.6301211421959192, "self_improvement_engine": 0.731569348562205, "regime_adaptation_system": 0.775416655360136, "performance_optimizer": 0.820785097526369, "advanced_trading_engine": 0.7890804024628261, "ai_coordinator": 0.6482096795587082, "configuration_manager": 0.6256931555530768, "mock_data_providers": 0.738552413694306, "paper_trading_engine": 0.7901127486286592, "logging_audit_system": 0.6872455244781259}, "tournament_framework": {"system_coordinator": 0.6576485709407702, "team_manager": 0.7226988857784953, "data_manager": 0.6214861055472374, "analytics_engine": 0.6158574217112126, "ollama_hub": 0.8636204161366043, "execution_engine": 0.7743482387812377, "portfolio_manager": 0.830021807649287, "risk_manager": 0.6664788647160901, "strategy_manager": 0.8655201973524929, "competitive_framework": 0.6771841485636637, "tournament_framework": 1.0, "self_improvement_engine": 0.6007143933965696, "regime_adaptation_system": 0.6735008310710968, "performance_optimizer": 0.7756054125622771, "advanced_trading_engine": 0.8325186233314694, "ai_coordinator": 0.7087350196074836, "configuration_manager": 0.600742088162069, "mock_data_providers": 0.7551703552124569, "paper_trading_engine": 0.8586963694737568, "logging_audit_system": 0.892363241635475}, "self_improvement_engine": {"system_coordinator": 0.7189128915439577, "team_manager": 0.710741311414484, "data_manager": 0.8177180542781528, "analytics_engine": 0.8115847545878043, "ollama_hub": 0.6289588445263564, "execution_engine": 0.6740217724431266, "portfolio_manager": 0.6121630111707261, "risk_manager": 0.7627922777330218, "strategy_manager": 0.7976973888291485, "competitive_framework": 0.7142188599010458, "tournament_framework": 0.7946936663507735, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.6922725446979434, "performance_optimizer": 0.7005919239735021, "advanced_trading_engine": 0.8475656985809825, "ai_coordinator": 0.8782559683391223, "configuration_manager": 0.859228424526491, "mock_data_providers": 0.6756887741984541, "paper_trading_engine": 0.8148459456459459, "logging_audit_system": 0.6866559286055345}, "regime_adaptation_system": {"system_coordinator": 0.8954401525666363, "team_manager": 0.71790945234488, "data_manager": 0.6525325520690679, "analytics_engine": 0.8860430005813644, "ollama_hub": 0.8999020186584595, "execution_engine": 0.6092437624559479, "portfolio_manager": 0.6842381836161046, "risk_manager": 0.8736831275455031, "strategy_manager": 0.7769694817325508, "competitive_framework": 0.7534374884683699, "tournament_framework": 0.7726329458740928, "self_improvement_engine": 0.7845771559664176, "regime_adaptation_system": 1.0, "performance_optimizer": 0.6592110565025965, "advanced_trading_engine": 0.6355490063270182, "ai_coordinator": 0.7289939957127725, "configuration_manager": 0.6947440160991362, "mock_data_providers": 0.6413319092552792, "paper_trading_engine": 0.7863216856042967, "logging_audit_system": 0.8068220263236552}, "performance_optimizer": {"system_coordinator": 0.6625873597057182, "team_manager": 0.6871745252744114, "data_manager": 0.6982016805820233, "analytics_engine": 0.8536241412395693, "ollama_hub": 0.6039568707005026, "execution_engine": 0.7717347376152408, "portfolio_manager": 0.8321609051656426, "risk_manager": 0.6912872044066181, "strategy_manager": 0.7096082532541599, "competitive_framework": 0.7741718525487755, "tournament_framework": 0.8487745523800485, "self_improvement_engine": 0.6320852381503743, "regime_adaptation_system": 0.704826096074732, "performance_optimizer": 1.0, "advanced_trading_engine": 0.7201391610674344, "ai_coordinator": 0.6911285783783749, "configuration_manager": 0.7450001148595624, "mock_data_providers": 0.6703521686035091, "paper_trading_engine": 0.6194445367173731, "logging_audit_system": 0.6853804351740704}, "advanced_trading_engine": {"system_coordinator": 0.8542921991387422, "team_manager": 0.7741551174493881, "data_manager": 0.6262689472167967, "analytics_engine": 0.68650363200429, "ollama_hub": 0.7135594841207189, "execution_engine": 0.818682823930268, "portfolio_manager": 0.8362390663499135, "risk_manager": 0.8841478199969075, "strategy_manager": 0.8117328461360398, "competitive_framework": 0.8123904328249568, "tournament_framework": 0.6956939599084623, "self_improvement_engine": 0.6501618732766254, "regime_adaptation_system": 0.8869240712659419, "performance_optimizer": 0.640219601697001, "advanced_trading_engine": 1.0, "ai_coordinator": 0.6553086788956433, "configuration_manager": 0.8268609431550933, "mock_data_providers": 0.8990420156684263, "paper_trading_engine": 0.7241436699573973, "logging_audit_system": 0.6495339290841476}, "ai_coordinator": {"system_coordinator": 0.8198483401474481, "team_manager": 0.8398362318863936, "data_manager": 0.6136234591425123, "analytics_engine": 0.7367247359990853, "ollama_hub": 0.7219171174585262, "execution_engine": 0.8181036650979586, "portfolio_manager": 0.6400082354388057, "risk_manager": 0.7534451439448164, "strategy_manager": 0.7185923063023433, "competitive_framework": 0.8030367201313935, "tournament_framework": 0.7129351090419047, "self_improvement_engine": 0.855694449393344, "regime_adaptation_system": 0.6193095880816737, "performance_optimizer": 0.6206429696076217, "advanced_trading_engine": 0.773767891076739, "ai_coordinator": 1.0, "configuration_manager": 0.8947902363902989, "mock_data_providers": 0.6147200166841126, "paper_trading_engine": 0.861621972250008, "logging_audit_system": 0.7591778089329471}, "configuration_manager": {"system_coordinator": 0.8766332343232939, "team_manager": 0.8604513863885865, "data_manager": 0.6993534280625744, "analytics_engine": 0.8241530047407419, "ollama_hub": 0.8320405564926603, "execution_engine": 0.6740998250990871, "portfolio_manager": 0.7971026549969977, "risk_manager": 0.8585520937012553, "strategy_manager": 0.7091282430817853, "competitive_framework": 0.7571088317548532, "tournament_framework": 0.7208532528435029, "self_improvement_engine": 0.8938417786607152, "regime_adaptation_system": 0.8578505585088675, "performance_optimizer": 0.8315889436603152, "advanced_trading_engine": 0.7697514751128818, "ai_coordinator": 0.7081331638634172, "configuration_manager": 1.0, "mock_data_providers": 0.681031218950537, "paper_trading_engine": 0.726723897248174, "logging_audit_system": 0.6557634256231064}, "mock_data_providers": {"system_coordinator": 0.7590373907864127, "team_manager": 0.6295011273267799, "data_manager": 0.8828111796772702, "analytics_engine": 0.6078097178611465, "ollama_hub": 0.791018215743426, "execution_engine": 0.6489738295813521, "portfolio_manager": 0.8631964615516662, "risk_manager": 0.7403866092801303, "strategy_manager": 0.8112715284498363, "competitive_framework": 0.7136441544626244, "tournament_framework": 0.6332522609033978, "self_improvement_engine": 0.7386814652318385, "regime_adaptation_system": 0.8901172023866107, "performance_optimizer": 0.7795137992158627, "advanced_trading_engine": 0.8024295496862509, "ai_coordinator": 0.6136762676066451, "configuration_manager": 0.6273233296254972, "mock_data_providers": 1.0, "paper_trading_engine": 0.8081420463072791, "logging_audit_system": 0.6066792397469346}, "paper_trading_engine": {"system_coordinator": 0.7779619412011947, "team_manager": 0.777143302662908, "data_manager": 0.7680910467695303, "analytics_engine": 0.8084273415064055, "ollama_hub": 0.8136361407864021, "execution_engine": 0.7440812808608972, "portfolio_manager": 0.8887118460614472, "risk_manager": 0.7139163467525429, "strategy_manager": 0.7963283033831139, "competitive_framework": 0.8811469207706439, "tournament_framework": 0.7555874619155342, "self_improvement_engine": 0.8570540660940917, "regime_adaptation_system": 0.7078226042896884, "performance_optimizer": 0.6352358459166019, "advanced_trading_engine": 0.6139156892885057, "ai_coordinator": 0.6276622079860232, "configuration_manager": 0.8813800603864116, "mock_data_providers": 0.6961147164313229, "paper_trading_engine": 1.0, "logging_audit_system": 0.759706614321188}, "logging_audit_system": {"system_coordinator": 0.7423546239815633, "team_manager": 0.7945690817091707, "data_manager": 0.6443333706264371, "analytics_engine": 0.7233896998667257, "ollama_hub": 0.8250144539485028, "execution_engine": 0.6942397477190214, "portfolio_manager": 0.8804245380009348, "risk_manager": 0.6166233247688931, "strategy_manager": 0.6744765660506412, "competitive_framework": 0.6505086468958308, "tournament_framework": 0.7701483997252635, "self_improvement_engine": 0.7483446127941381, "regime_adaptation_system": 0.8432458928830797, "performance_optimizer": 0.6224096681156227, "advanced_trading_engine": 0.7102109053583243, "ai_coordinator": 0.710274285824195, "configuration_manager": 0.8193802242354014, "mock_data_providers": 0.7682199170370893, "paper_trading_engine": 0.7549406950115675, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.8887980708661685, "response_time": 0.8638306475931092, "throughput": 0.6507318489727346, "memory_usage": 0.8026432797337106, "cpu_usage": 0.8637978225958666, "concurrent_operations": 0.7751462898077585}, "critical_issues": ["Components with dependency issues: advanced_trading_engine, configuration_manager, paper_trading_engine"], "recommendations": ["Improve 13 partially working components", "Improve overall system performance and stability", "Enhance component integration and communication"], "production_ready": "True", "timestamp": **********.0990748}
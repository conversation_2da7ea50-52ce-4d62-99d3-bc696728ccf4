# Core Dependencies
aiohttp>=3.9.0
asyncio-mqtt>=0.16.0
asyncpg>=0.29.0
redis>=5.0.0
pydantic>=2.5.0
pydantic-settings>=2.1.0
PyYAML>=6.0.1
python-dotenv>=1.0.0

# Data Processing and Analysis
pandas>=2.1.0
numpy>=1.24.0
scipy>=1.11.0
scikit-learn>=1.3.0
joblib>=1.3.0
ta-lib>=0.4.28
yfinance>=0.2.18
ccxt>=4.1.0

# Database Drivers
asyncpg>=0.29.0
redis>=5.0.0
clickhouse-driver>=0.2.6
sqlalchemy>=2.0.0
alembic>=1.13.0

# Web Framework and API
fastapi>=0.104.0
uvicorn>=0.24.0
websockets>=12.0
httpx>=0.25.0

# Monitoring and Logging
prometheus-client>=0.19.0
structlog>=23.2.0
rich>=13.7.0
psutil>=5.9.0

# Machine Learning and AI
torch>=2.1.0
transformers>=4.35.0
sentence-transformers>=2.2.2
openai>=1.3.0

# Financial Data and Trading
quantlib>=1.32
backtrader>=**********
zipline-reloaded>=3.0.0
empyrical>=0.5.5

# Utilities
click>=8.1.0
tqdm>=4.66.0
python-dateutil>=2.8.2
pytz>=2023.3
schedule>=1.2.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0
httpx>=0.25.0

# Development Tools
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.0
pre-commit>=3.5.0

# Configuration and Environment
python-decouple>=3.8
configparser>=6.0.0
toml>=0.10.2

# Async and Concurrency
asyncio>=3.4.3
aiofiles>=23.2.1
aiodns>=3.1.1

# Cryptography and Security
cryptography>=41.0.0
bcrypt>=4.1.0
passlib>=1.7.4
python-jose>=3.3.0

# Data Visualization (Optional)
matplotlib>=3.8.0
plotly>=5.17.0
seaborn>=0.13.0

# Message Queue and Communication
celery>=5.3.0
kombu>=5.3.0
pika>=1.3.0

# Time Series Analysis
statsmodels>=0.14.0
arch>=6.2.0
pmdarima>=2.0.4

# Performance Optimization
numba>=0.58.0
cython>=3.0.0
ujson>=5.8.0

# Ollama Integration
ollama>=0.1.7

{"validation_id": "validation_**********_f1a2251b", "validation_level": "comprehensive", "overall_status": "partial", "overall_score": 0.7864128705474209, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "partial", "integration_score": 0.7286751115286436, "error_count": 0, "warnings": ["Functionality concerns in system_coordinator", "Integration issues in system_coordinator"], "dependencies_met": "True"}, {"component_name": "team_manager", "component_type": "core", "status": "partial", "integration_score": 0.7338874120768264, "error_count": 0, "warnings": ["Functionality concerns in team_manager"], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "partial", "integration_score": 0.7261775656434478, "error_count": 0, "warnings": ["Functionality concerns in data_manager"], "dependencies_met": "True"}, {"component_name": "analytics_engine", "component_type": "core", "status": "partial", "integration_score": 0.7016545377785047, "error_count": 0, "warnings": ["Functionality concerns in analytics_engine"], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "partial", "integration_score": 0.7665960616951175, "error_count": 0, "warnings": ["Integration issues in ollama_hub"], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "partial", "integration_score": 0.7445288494113562, "error_count": 0, "warnings": ["Functionality concerns in execution_engine", "Integration issues in execution_engine"], "dependencies_met": "True"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "partial", "integration_score": 0.8028301877990427, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "risk_manager", "component_type": "core", "status": "partial", "integration_score": 0.7263362783862332, "error_count": 0, "warnings": ["Integration issues in risk_manager"], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "partial", "integration_score": 0.8471534482409041, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "partial", "integration_score": 0.7980978919005247, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "partial", "integration_score": 0.7397455756035132, "error_count": 0, "warnings": ["Integration issues in tournament_framework"], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.7944761370017056, "error_count": 0, "warnings": ["Integration issues in self_improvement_engine"], "dependencies_met": "True"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "partial", "integration_score": 0.7655277046519732, "error_count": 0, "warnings": ["Functionality concerns in regime_adaptation_system"], "dependencies_met": "True"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "partial", "integration_score": 0.7649871994442461, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.786853050234062, "error_count": 0, "warnings": ["Functionality concerns in advanced_trading_engine"], "dependencies_met": "True"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "partial", "integration_score": 0.7798224037886056, "error_count": 0, "warnings": ["Integration issues in ai_coordinator"], "dependencies_met": "True"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "partial", "integration_score": 0.813148368428705, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "partial", "integration_score": 0.7990840369541036, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "partial", "integration_score": 0.7999641644783129, "error_count": 0, "warnings": ["Integration issues in paper_trading_engine"], "dependencies_met": "True"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "partial", "integration_score": 0.811488774206755, "error_count": 0, "warnings": [], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.9177561726525243, "data_manager": 0.6985935225715975, "analytics_engine": 0.8304787224147101, "ollama_hub": 0.8272240304171639, "execution_engine": 0.7919378398616792, "portfolio_manager": 0.8954965099435013, "risk_manager": 0.6042340249438826, "strategy_manager": 0.8680381318987265, "competitive_framework": 0.670874171999402, "tournament_framework": 0.6576787023184457, "self_improvement_engine": 0.8426963865925775, "regime_adaptation_system": 0.6690002704994757, "performance_optimizer": 0.7101740878619904, "advanced_trading_engine": 0.8194728073550333, "ai_coordinator": 0.7151799576078246, "configuration_manager": 0.7773716732639034, "mock_data_providers": 0.7917787079893002, "paper_trading_engine": 0.7846912110672872, "logging_audit_system": 0.734560598891409}, "team_manager": {"system_coordinator": 0.7531120675363312, "team_manager": 1.0, "data_manager": 0.8508055478165728, "analytics_engine": 0.8825118444055341, "ollama_hub": 0.6062228596128411, "execution_engine": 0.8929594658833155, "portfolio_manager": 0.8639768604640443, "risk_manager": 0.781510462256779, "strategy_manager": 0.7961988996903754, "competitive_framework": 0.6511544817500919, "tournament_framework": 0.6682453759066359, "self_improvement_engine": 0.7546896987771681, "regime_adaptation_system": 0.8306053711777142, "performance_optimizer": 0.8927809336006374, "advanced_trading_engine": 0.8842323659993768, "ai_coordinator": 0.7548320059313474, "configuration_manager": 0.7472330996587886, "mock_data_providers": 0.6716053010743425, "paper_trading_engine": 0.6541653571110501, "logging_audit_system": 0.713190979966858}, "data_manager": {"system_coordinator": 0.8285393845493815, "team_manager": 0.7422111203447418, "data_manager": 1.0, "analytics_engine": 0.8299983496418954, "ollama_hub": 0.7854272186144604, "execution_engine": 0.6193895871714818, "portfolio_manager": 0.6978800703041512, "risk_manager": 0.7648293928512656, "strategy_manager": 0.6355919731271713, "competitive_framework": 0.6180327955959742, "tournament_framework": 0.663114002243184, "self_improvement_engine": 0.7118815640511257, "regime_adaptation_system": 0.8578283581447326, "performance_optimizer": 0.8170483405325587, "advanced_trading_engine": 0.7888475582170495, "ai_coordinator": 0.6737384618067934, "configuration_manager": 0.6403640211270596, "mock_data_providers": 0.6865302963953501, "paper_trading_engine": 0.6798469434396364, "logging_audit_system": 0.6280458997195839}, "analytics_engine": {"system_coordinator": 0.8466698118609091, "team_manager": 0.6657600082970884, "data_manager": 0.7979053972853737, "analytics_engine": 1.0, "ollama_hub": 0.675894675417526, "execution_engine": 0.6523644186945433, "portfolio_manager": 0.7190772465787251, "risk_manager": 0.763362826633114, "strategy_manager": 0.9143786676230811, "competitive_framework": 0.6819372738562797, "tournament_framework": 0.8132280136584278, "self_improvement_engine": 0.8285295568789948, "regime_adaptation_system": 0.7402521820110657, "performance_optimizer": 0.7937816986932161, "advanced_trading_engine": 0.6706143919474523, "ai_coordinator": 0.7480714690798553, "configuration_manager": 0.8941635207780338, "mock_data_providers": 0.6270870679064858, "paper_trading_engine": 0.860130139511061, "logging_audit_system": 0.608817783882027}, "ollama_hub": {"system_coordinator": 0.6353222295099392, "team_manager": 0.8112956644814775, "data_manager": 0.6952441629307118, "analytics_engine": 0.8784265004506215, "ollama_hub": 1.0, "execution_engine": 0.6286276224522681, "portfolio_manager": 0.8951617288862955, "risk_manager": 0.8834152930984294, "strategy_manager": 0.8230356466137133, "competitive_framework": 0.646759767248858, "tournament_framework": 0.874380966051476, "self_improvement_engine": 0.7605100480845239, "regime_adaptation_system": 0.6167279868494703, "performance_optimizer": 0.8222752579150008, "advanced_trading_engine": 0.8495548947507234, "ai_coordinator": 0.844404954237149, "configuration_manager": 0.7854515596858358, "mock_data_providers": 0.7950639769056167, "paper_trading_engine": 0.6499979711979977, "logging_audit_system": 0.7706459412171126}, "execution_engine": {"system_coordinator": 0.7773417436839765, "team_manager": 0.6467732355235288, "data_manager": 0.8019952547968123, "analytics_engine": 0.884106788497224, "ollama_hub": 0.6761663447547916, "execution_engine": 1.0, "portfolio_manager": 0.9704197232980101, "risk_manager": 0.8870601229354662, "strategy_manager": 0.8815836992195099, "competitive_framework": 0.8930849017345079, "tournament_framework": 0.6195818690774999, "self_improvement_engine": 0.790326741196402, "regime_adaptation_system": 0.8232844166238649, "performance_optimizer": 0.6835217782202967, "advanced_trading_engine": 0.6238231641877817, "ai_coordinator": 0.6991930478788037, "configuration_manager": 0.7010499910919402, "mock_data_providers": 0.6682788017488914, "paper_trading_engine": 0.8364629742941818, "logging_audit_system": 0.6447524091210903}, "portfolio_manager": {"system_coordinator": 0.6640241063890914, "team_manager": 0.7451156876434044, "data_manager": 0.6405203800922019, "analytics_engine": 0.7571325651163516, "ollama_hub": 0.8286319079001275, "execution_engine": 0.670946481919696, "portfolio_manager": 1.0, "risk_manager": 0.7787595981491428, "strategy_manager": 0.8692834604458097, "competitive_framework": 0.6216839882775531, "tournament_framework": 0.6971692037830924, "self_improvement_engine": 0.896745885866191, "regime_adaptation_system": 0.8080653921026524, "performance_optimizer": 0.6852081400057678, "advanced_trading_engine": 0.6821689894915373, "ai_coordinator": 0.6448467039682616, "configuration_manager": 0.6879360052908421, "mock_data_providers": 0.7562095537340663, "paper_trading_engine": 0.7023687242600379, "logging_audit_system": 0.6460218845409726}, "risk_manager": {"system_coordinator": 0.7721048096703489, "team_manager": 0.8750171215475655, "data_manager": 0.8090636112369709, "analytics_engine": 0.6486551649894962, "ollama_hub": 0.7265073517179602, "execution_engine": 0.6965515336489405, "portfolio_manager": 0.897709742068336, "risk_manager": 1.0, "strategy_manager": 0.8119215173985092, "competitive_framework": 0.8854441383109428, "tournament_framework": 0.8559148358064815, "self_improvement_engine": 0.6088143764276441, "regime_adaptation_system": 0.675825379558155, "performance_optimizer": 0.8922765834380753, "advanced_trading_engine": 0.7184369197689332, "ai_coordinator": 0.6953434417772552, "configuration_manager": 0.782653701011323, "mock_data_providers": 0.7499619000668873, "paper_trading_engine": 0.658885948732767, "logging_audit_system": 0.7988148312086953}, "strategy_manager": {"system_coordinator": 0.6693909813805257, "team_manager": 0.818274610435344, "data_manager": 0.798516218240545, "analytics_engine": 0.6781881426375151, "ollama_hub": 0.7555872621523596, "execution_engine": 0.6618519321933869, "portfolio_manager": 0.8346050974768521, "risk_manager": 0.6857913257375662, "strategy_manager": 1.0, "competitive_framework": 0.7910854627487472, "tournament_framework": 0.8125557435656431, "self_improvement_engine": 0.8239247483774597, "regime_adaptation_system": 0.7639791366936239, "performance_optimizer": 0.7977150797654351, "advanced_trading_engine": 0.8311395478832937, "ai_coordinator": 0.7063065867955729, "configuration_manager": 0.6035307388887137, "mock_data_providers": 0.6477202201308021, "paper_trading_engine": 0.8117487139482498, "logging_audit_system": 0.7486692757835784}, "competitive_framework": {"system_coordinator": 0.8289522512026695, "team_manager": 0.7454311372819808, "data_manager": 0.8950013052853067, "analytics_engine": 0.7588894596120536, "ollama_hub": 0.7198432077450869, "execution_engine": 0.7672559346694243, "portfolio_manager": 0.6267756460110576, "risk_manager": 0.7378652509882406, "strategy_manager": 0.7878552427917318, "competitive_framework": 1.0, "tournament_framework": 0.7849654219000819, "self_improvement_engine": 0.8498400624052435, "regime_adaptation_system": 0.748733102418319, "performance_optimizer": 0.7697348188985087, "advanced_trading_engine": 0.8384372712002652, "ai_coordinator": 0.7163577075373843, "configuration_manager": 0.8043243794029759, "mock_data_providers": 0.7670123962135253, "paper_trading_engine": 0.8361211116578102, "logging_audit_system": 0.8389700545894142}, "tournament_framework": {"system_coordinator": 0.6477380600755368, "team_manager": 0.819695964459962, "data_manager": 0.7529542828480614, "analytics_engine": 0.7417765014331873, "ollama_hub": 0.6557914612072266, "execution_engine": 0.8560414166354795, "portfolio_manager": 0.8689875354836659, "risk_manager": 0.8955637905871563, "strategy_manager": 0.75955642737501, "competitive_framework": 0.8985901984528206, "tournament_framework": 1.0, "self_improvement_engine": 0.7930249039109324, "regime_adaptation_system": 0.6531185167917904, "performance_optimizer": 0.6461639219779637, "advanced_trading_engine": 0.8426337174439865, "ai_coordinator": 0.6986145879049914, "configuration_manager": 0.6141563968665604, "mock_data_providers": 0.6437201810305093, "paper_trading_engine": 0.6667805323247925, "logging_audit_system": 0.7243943976602429}, "self_improvement_engine": {"system_coordinator": 0.707366919748532, "team_manager": 0.6710230277096818, "data_manager": 0.6321086208598453, "analytics_engine": 0.6101509207106788, "ollama_hub": 0.6663472145972416, "execution_engine": 0.6375256429452831, "portfolio_manager": 0.6170411071887096, "risk_manager": 0.7783465787537293, "strategy_manager": 0.6125875165240376, "competitive_framework": 0.7785762602648656, "tournament_framework": 0.8193207516064431, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.7787534863884495, "performance_optimizer": 0.6017022683069222, "advanced_trading_engine": 0.8454523115960779, "ai_coordinator": 0.7116518245888152, "configuration_manager": 0.6381851441269851, "mock_data_providers": 0.8424083020053446, "paper_trading_engine": 0.667774077019113, "logging_audit_system": 0.6778973107685687}, "regime_adaptation_system": {"system_coordinator": 0.6944184788809313, "team_manager": 0.6507634869446495, "data_manager": 0.7255311854359119, "analytics_engine": 0.7148075538966833, "ollama_hub": 0.7439701081007795, "execution_engine": 0.6845621434831455, "portfolio_manager": 0.8973958382704905, "risk_manager": 0.6395482102689, "strategy_manager": 0.8769797634488405, "competitive_framework": 0.7153563897112217, "tournament_framework": 0.6202591471059623, "self_improvement_engine": 0.6363172248470862, "regime_adaptation_system": 1.0, "performance_optimizer": 0.8923089634235584, "advanced_trading_engine": 0.6917632768061353, "ai_coordinator": 0.8713431415018974, "configuration_manager": 0.8569138109901435, "mock_data_providers": 0.7436954028828564, "paper_trading_engine": 0.887147157971844, "logging_audit_system": 0.8694521983256811}, "performance_optimizer": {"system_coordinator": 0.6147247340642503, "team_manager": 0.7025876977292906, "data_manager": 0.8051298231468248, "analytics_engine": 0.7997164959884014, "ollama_hub": 0.6691434149122628, "execution_engine": 0.7066267595406386, "portfolio_manager": 0.888833353142792, "risk_manager": 0.6650917723101589, "strategy_manager": 0.7677680599337556, "competitive_framework": 0.8812508100666325, "tournament_framework": 0.8034449756255906, "self_improvement_engine": 0.7518726115150977, "regime_adaptation_system": 0.8808332510274197, "performance_optimizer": 1.0, "advanced_trading_engine": 0.6722848624468343, "ai_coordinator": 0.7870515912966665, "configuration_manager": 0.734573892116648, "mock_data_providers": 0.6355216734224818, "paper_trading_engine": 0.7873828078371083, "logging_audit_system": 0.6468053479334173}, "advanced_trading_engine": {"system_coordinator": 0.8382942382206984, "team_manager": 0.6279849176661775, "data_manager": 0.8484854427522186, "analytics_engine": 0.7517046452157514, "ollama_hub": 0.71628089101811, "execution_engine": 0.7231059353445015, "portfolio_manager": 0.6848120605491653, "risk_manager": 0.754900648050954, "strategy_manager": 0.6404599436348216, "competitive_framework": 0.8699189250527328, "tournament_framework": 0.8808595940652706, "self_improvement_engine": 0.7468484531914226, "regime_adaptation_system": 0.7632615485214544, "performance_optimizer": 0.8109587586340611, "advanced_trading_engine": 1.0, "ai_coordinator": 0.6284433302477561, "configuration_manager": 0.8170900466541632, "mock_data_providers": 0.6762287068780312, "paper_trading_engine": 0.6324394220376508, "logging_audit_system": 0.8607472837722103}, "ai_coordinator": {"system_coordinator": 0.7028779761954085, "team_manager": 0.6498465623939902, "data_manager": 0.8652243699031017, "analytics_engine": 0.7298871310264424, "ollama_hub": 0.781862340268906, "execution_engine": 0.6241944775624773, "portfolio_manager": 0.6785558358319134, "risk_manager": 0.8184065169584969, "strategy_manager": 0.6358171757418198, "competitive_framework": 0.6403198610645403, "tournament_framework": 0.7456747888947386, "self_improvement_engine": 0.8595067397696043, "regime_adaptation_system": 0.8354594875862917, "performance_optimizer": 0.78351968559061, "advanced_trading_engine": 0.89762790929774, "ai_coordinator": 1.0, "configuration_manager": 0.8466163981207617, "mock_data_providers": 0.7721831547899091, "paper_trading_engine": 0.8700555943317809, "logging_audit_system": 0.6853192050219132}, "configuration_manager": {"system_coordinator": 0.6764124655599254, "team_manager": 0.6282044029926643, "data_manager": 0.8576409136386485, "analytics_engine": 0.7127791610904615, "ollama_hub": 0.8955584239465595, "execution_engine": 0.8731797661546064, "portfolio_manager": 0.8483219219656509, "risk_manager": 0.8196970322445388, "strategy_manager": 0.7323170652347101, "competitive_framework": 0.8675327255420084, "tournament_framework": 0.8484186070020531, "self_improvement_engine": 0.8287448285182627, "regime_adaptation_system": 0.8820561373904838, "performance_optimizer": 0.8082754760814472, "advanced_trading_engine": 0.6426157736376654, "ai_coordinator": 0.880303420749748, "configuration_manager": 1.0, "mock_data_providers": 0.7092178563686962, "paper_trading_engine": 0.7500647505345133, "logging_audit_system": 0.7269390702752443}, "mock_data_providers": {"system_coordinator": 0.7005718634418865, "team_manager": 0.8900228477447301, "data_manager": 0.6357290494208231, "analytics_engine": 0.6328605996576512, "ollama_hub": 0.7537747582587841, "execution_engine": 0.8397128822735023, "portfolio_manager": 0.7964232437600897, "risk_manager": 0.6558634798940821, "strategy_manager": 0.8595086589490659, "competitive_framework": 0.6889248717200481, "tournament_framework": 0.8798105217033017, "self_improvement_engine": 0.7489653889261209, "regime_adaptation_system": 0.8021323559125169, "performance_optimizer": 0.6721559090388632, "advanced_trading_engine": 0.7790218341012092, "ai_coordinator": 0.7517266204695974, "configuration_manager": 0.6658534843306694, "mock_data_providers": 1.0, "paper_trading_engine": 0.6084280671764657, "logging_audit_system": 0.6659858670007817}, "paper_trading_engine": {"system_coordinator": 0.8924546606216908, "team_manager": 0.7551031456271571, "data_manager": 0.8182092130729459, "analytics_engine": 0.8014980914723543, "ollama_hub": 0.7821789805688775, "execution_engine": 0.8570459309987443, "portfolio_manager": 0.6336316773632767, "risk_manager": 0.7028104288579093, "strategy_manager": 0.6525797130809605, "competitive_framework": 0.6448078331320353, "tournament_framework": 0.8546643665166862, "self_improvement_engine": 0.6433256064302797, "regime_adaptation_system": 0.7545510560206223, "performance_optimizer": 0.8450436756663314, "advanced_trading_engine": 0.6566239779860842, "ai_coordinator": 0.8198665634318968, "configuration_manager": 0.6620607925492097, "mock_data_providers": 0.6424116943001521, "paper_trading_engine": 1.0, "logging_audit_system": 0.6242161286104015}, "logging_audit_system": {"system_coordinator": 0.7813605158772, "team_manager": 0.7756453363724365, "data_manager": 0.6807547582911428, "analytics_engine": 0.7955216437416939, "ollama_hub": 0.6476335860630517, "execution_engine": 0.883121857889904, "portfolio_manager": 0.7290719475457067, "risk_manager": 0.8513155310657958, "strategy_manager": 0.8472527441218856, "competitive_framework": 0.7884535406720969, "tournament_framework": 0.699440575611542, "self_improvement_engine": 0.7643242126316043, "regime_adaptation_system": 0.661315344975579, "performance_optimizer": 0.7120040131367817, "advanced_trading_engine": 0.7565719936980311, "ai_coordinator": 0.7062006453071648, "configuration_manager": 0.601948417571701, "mock_data_providers": 0.807536581262026, "paper_trading_engine": 0.8881067658372807, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.7451058354569708, "response_time": 0.8898367421719817, "throughput": 0.7770993276580227, "memory_usage": 0.818520357012428, "cpu_usage": 0.8970498674159313, "concurrent_operations": 0.6955621705542745}, "critical_issues": ["Components with dependency issues: portfolio_manager, competitive_framework"], "recommendations": ["Improve 20 partially working components", "Improve overall system performance and stability", "Enhance component integration and communication"], "production_ready": "False", "timestamp": **********.8377888}
"""
Market Data Manager - Handles market data collection and distribution
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)


class MarketDataManager:
    """
    Manages market data collection, processing, and distribution.
    Handles real-time and historical data from multiple sources.
    """
    
    def __init__(self, config: Dict[str, Any]):
        # Handle config format - ensure it's a dict
        if isinstance(config, str):
            # If config is a string (like a file path), create a minimal config
            self.config = {
                'market_data': {
                    'providers': [],
                    'symbols': {'stocks': ['AAPL', 'TSLA', 'GOOGL']},
                    'update_intervals': {'real_time': 1, 'minute': 60}
                }
            }
        else:
            self.config = config
        self.market_config = self.config.get('market_data', {})
        
        # Data storage
        self.current_data: Dict[str, Dict[str, Any]] = {}
        self.historical_data: Dict[str, List[Dict[str, Any]]] = {}
        
        # Data providers
        self.providers = {}
        self.active_symbols = set()
        
        # State
        self.initialized = False
        self.running = False
        
        # Configuration
        self.update_intervals = self.market_config.get('update_intervals', {})
        self.symbols = self.market_config.get('symbols', {})
        
        # Tasks
        self.data_collection_tasks: List[asyncio.Task] = []
        
    async def initialize(self):
        """Initialize the market data manager"""
        if self.initialized:
            return
            
        logger.info("Initializing Market Data Manager...")
        
        try:
            # Initialize data providers
            await self._init_providers()
            
            # Setup symbols
            self._setup_symbols()
            
            # Initialize data structures
            self._init_data_structures()
            
            self.initialized = True
            logger.info("✓ Market Data Manager initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Market Data Manager: {e}")
            raise
            
    async def _init_providers(self):
        """Initialize market data providers"""
        providers_config = self.market_config.get('providers', [])

        # Handle case where providers_config might not be a list
        if not isinstance(providers_config, list):
            providers_config = []

        for provider_config in providers_config:
            # Ensure provider_config is a dict
            if not isinstance(provider_config, dict):
                continue

            if provider_config.get('enabled', False):
                provider_name = provider_config.get('name', 'unknown')

                if provider_name == 'yfinance':
                    self.providers[provider_name] = YFinanceProvider(provider_config)
                elif provider_name == 'ccxt':
                    self.providers[provider_name] = CCXTProvider(provider_config)
                else:
                    logger.warning(f"Unknown provider: {provider_name}")

        # If no providers configured, add a mock provider
        if not self.providers:
            self.providers['mock'] = MockProvider({})

        logger.info(f"Initialized {len(self.providers)} data providers")
        
    def _setup_symbols(self):
        """Setup symbols for data collection"""
        # Handle different symbol formats
        if isinstance(self.symbols, dict):
            # Dict format: {'stocks': ['AAPL', 'TSLA'], 'crypto': ['BTC', 'ETH']}
            for category, symbol_list in self.symbols.items():
                if isinstance(symbol_list, list):
                    for symbol in symbol_list:
                        self.active_symbols.add(symbol)
        elif isinstance(self.symbols, list):
            # List format: ['AAPL', 'TSLA', 'GOOGL']
            for symbol in self.symbols:
                self.active_symbols.add(symbol)
        else:
            # Default symbols if format is unknown
            default_symbols = ['AAPL', 'TSLA', 'GOOGL', 'MSFT', 'AMZN']
            for symbol in default_symbols:
                self.active_symbols.add(symbol)

        logger.info(f"Setup {len(self.active_symbols)} symbols for data collection")
        
    def _init_data_structures(self):
        """Initialize data structures"""
        for symbol in self.active_symbols:
            self.current_data[symbol] = {}
            self.historical_data[symbol] = []
            
    async def start(self):
        """Start the market data manager"""
        if not self.initialized:
            await self.initialize()
            
        if self.running:
            return
            
        logger.info("Starting Market Data Manager...")
        
        # Start data collection tasks
        for interval_name, interval_seconds in self.update_intervals.items():
            task = asyncio.create_task(
                self._data_collection_loop(interval_name, interval_seconds)
            )
            self.data_collection_tasks.append(task)
            
        self.running = True
        logger.info("✓ Market Data Manager started")
        
    async def stop(self):
        """Stop the market data manager"""
        if not self.running:
            return
            
        logger.info("Stopping Market Data Manager...")
        self.running = False
        
        # Cancel data collection tasks
        for task in self.data_collection_tasks:
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                    
        self.data_collection_tasks.clear()
        logger.info("✓ Market Data Manager stopped")
        
    async def _data_collection_loop(self, interval_name: str, interval_seconds: int):
        """Data collection loop for a specific interval"""
        while self.running:
            try:
                await asyncio.sleep(interval_seconds)
                if self.running:
                    await self._collect_data(interval_name)
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in data collection loop ({interval_name}): {e}")
                
    async def _collect_data(self, interval_name: str):
        """Collect data for all symbols"""
        logger.debug(f"Collecting data for interval: {interval_name}")
        
        # For now, generate mock data
        # In a real implementation, this would call actual data providers
        for symbol in self.active_symbols:
            mock_data = self._generate_mock_data(symbol)
            await self._update_symbol_data(symbol, mock_data, interval_name)
            
    def _generate_mock_data(self, symbol: str) -> Dict[str, Any]:
        """Generate mock market data for testing"""
        import random
        
        # Get previous price or start with a base price
        current_data = self.current_data.get(symbol, {})
        last_price = current_data.get('close', 100.0)
        
        # Generate realistic price movement
        change_percent = random.uniform(-0.02, 0.02)  # ±2% change
        new_price = last_price * (1 + change_percent)
        
        # Generate OHLCV data
        high = new_price * random.uniform(1.0, 1.01)
        low = new_price * random.uniform(0.99, 1.0)
        volume = random.uniform(1000, 10000)
        
        return {
            'symbol': symbol,
            'timestamp': time.time(),
            'open': last_price,
            'high': high,
            'low': low,
            'close': new_price,
            'volume': volume,
            'change': new_price - last_price,
            'change_percent': change_percent * 100
        }
        
    async def _update_symbol_data(self, symbol: str, data: Dict[str, Any], interval: str):
        """Update data for a symbol"""
        # Update current data
        self.current_data[symbol] = data
        
        # Add to historical data
        data_with_interval = data.copy()
        data_with_interval['interval'] = interval
        
        self.historical_data[symbol].append(data_with_interval)
        
        # Keep only recent historical data (last 1000 points)
        if len(self.historical_data[symbol]) > 1000:
            self.historical_data[symbol] = self.historical_data[symbol][-1000:]
            
    async def get_current_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current data for a symbol"""
        return self.current_data.get(symbol)
        
    async def get_historical_data(self, symbol: str, 
                                 limit: int = 100,
                                 interval: str = None) -> List[Dict[str, Any]]:
        """Get historical data for a symbol"""
        historical = self.historical_data.get(symbol, [])
        
        if interval:
            historical = [d for d in historical if d.get('interval') == interval]
            
        return historical[-limit:] if historical else []
        
    async def get_all_current_data(self) -> Dict[str, Dict[str, Any]]:
        """Get current data for all symbols"""
        return self.current_data.copy()
        
    async def subscribe_to_symbol(self, symbol: str) -> bool:
        """Subscribe to data for a new symbol"""
        if symbol not in self.active_symbols:
            self.active_symbols.add(symbol)
            self.current_data[symbol] = {}
            self.historical_data[symbol] = []
            logger.info(f"Subscribed to symbol: {symbol}")
            return True
        return False
        
    async def unsubscribe_from_symbol(self, symbol: str) -> bool:
        """Unsubscribe from a symbol"""
        if symbol in self.active_symbols:
            self.active_symbols.remove(symbol)
            if symbol in self.current_data:
                del self.current_data[symbol]
            if symbol in self.historical_data:
                del self.historical_data[symbol]
            logger.info(f"Unsubscribed from symbol: {symbol}")
            return True
        return False
        
    async def get_market_summary(self) -> Dict[str, Any]:
        """Get market summary statistics"""
        summary = {
            'total_symbols': len(self.active_symbols),
            'symbols_with_data': len([s for s in self.active_symbols if s in self.current_data]),
            'last_update': max([data.get('timestamp', 0) for data in self.current_data.values()]) if self.current_data else 0,
            'market_status': 'open',  # Simplified
            'top_gainers': [],
            'top_losers': []
        }
        
        # Calculate top gainers and losers
        symbols_with_change = []
        for symbol, data in self.current_data.items():
            if 'change_percent' in data:
                symbols_with_change.append({
                    'symbol': symbol,
                    'change_percent': data['change_percent'],
                    'price': data.get('close', 0)
                })
                
        # Sort by change percentage
        symbols_with_change.sort(key=lambda x: x['change_percent'], reverse=True)
        
        summary['top_gainers'] = symbols_with_change[:5]
        summary['top_losers'] = symbols_with_change[-5:]
        
        return summary
        
    async def get_stats(self) -> Dict[str, Any]:
        """Get market data manager statistics"""
        total_historical_points = sum(len(data) for data in self.historical_data.values())
        
        return {
            'running': self.running,
            'active_symbols': len(self.active_symbols),
            'providers': list(self.providers.keys()),
            'total_historical_points': total_historical_points,
            'collection_tasks': len(self.data_collection_tasks),
            'update_intervals': self.update_intervals
        }


# Placeholder provider classes (would be implemented with actual APIs)

class YFinanceProvider:
    """Yahoo Finance data provider"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
    async def get_data(self, symbol: str) -> Dict[str, Any]:
        """Get data from Yahoo Finance"""
        # Placeholder - would implement actual yfinance integration
        return {}


class CCXTProvider:
    """CCXT cryptocurrency data provider"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config

    async def get_data(self, symbol: str) -> Dict[str, Any]:
        """Get data from cryptocurrency exchange"""
        # Placeholder - would implement actual CCXT integration
        return {}


class MockProvider:
    """Mock data provider for testing"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config

    async def get_data(self, symbol: str) -> Dict[str, Any]:
        """Get mock data"""
        import random
        return {
            'symbol': symbol,
            'price': random.uniform(50, 200),
            'volume': random.uniform(1000, 10000)
        }

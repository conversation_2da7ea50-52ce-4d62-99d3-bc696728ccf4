#!/usr/bin/env python3
"""
Test multiple AI agents working together
"""

import asyncio
import json
from datetime import datetime
from config.config_manager import ConfigManager
from models.ollama_hub import OllamaModelHub
from agents.agent_manager import AgentManager
from agents.base_agent import AgentRole

class SimpleMessageBroker:
    """Simple message broker for testing"""
    def __init__(self):
        self.messages = []
    
    async def publish(self, topic, message):
        self.messages.append({"topic": topic, "message": message, "timestamp": datetime.now()})
        print(f"📢 Published to {topic}: {str(message)[:100]}...")
    
    async def subscribe(self, topic, callback):
        pass

async def test_multi_agent_collaboration():
    """Test multiple AI agents collaborating on trading decisions"""
    
    print("🤖 MULTI-AGENT AI COLLABORATION TEST")
    print("=" * 50)
    
    try:
        # Setup
        config_manager = ConfigManager('config/test_config.yaml')
        await config_manager.load_config()
        config = await config_manager.get_config()
        
        ollama_hub = OllamaModelHub(config=config)
        await ollama_hub.initialize()
        
        message_broker = SimpleMessageBroker()
        
        agent_manager = AgentManager(ollama_hub, message_broker, config)
        await agent_manager.initialize()
        
        print("✅ System initialized")
        
        # Create AI agent team
        print("\n🏗️ Creating AI Agent Team...")
        
        analyst_id = await agent_manager.create_agent(
            role=AgentRole.MARKET_ANALYST,
            name="AI_Market_Analyst"
        )
        
        strategist_id = await agent_manager.create_agent(
            role=AgentRole.STRATEGY_DEVELOPER, 
            name="AI_Strategy_Developer"
        )
        
        risk_mgr_id = await agent_manager.create_agent(
            role=AgentRole.RISK_MANAGER,
            name="AI_Risk_Manager"
        )
        
        print(f"✅ Market Analyst: {analyst_id}")
        print(f"✅ Strategy Developer: {strategist_id}")
        print(f"✅ Risk Manager: {risk_mgr_id}")
        
        # Get agent instances
        analyst = await agent_manager.get_agent(analyst_id)
        strategist = await agent_manager.get_agent(strategist_id)
        risk_manager = await agent_manager.get_agent(risk_mgr_id)
        
        print(f"\n🧠 AI Models Deployed:")
        print(f"  📊 Analyst: {analyst.model_instance.model_name}")
        print(f"  🎯 Strategist: {strategist.model_instance.model_name}")
        print(f"  ⚠️ Risk Manager: {risk_manager.model_instance.model_name}")
        
        # Test collaborative workflow
        print("\n🔄 COLLABORATIVE AI WORKFLOW")
        print("=" * 40)
        
        # Step 1: Market Analysis
        print("\n📊 STEP 1: AI Market Analysis")
        market_data = {
            "symbol": "TSLA",
            "price": 245.67,
            "change": 12.34,
            "change_percent": 5.29,
            "volume": 25000000,
            "high": 248.90,
            "low": 238.45,
            "market_cap": "780B",
            "pe_ratio": 65.4,
            "news": "Tesla announces new battery technology breakthrough"
        }

        print(f"Market Data: TSLA ${market_data['price']} (+{market_data['change_percent']}%)")

        # Use task-based system for market analysis
        analysis_task = {
            "symbol": "TSLA",
            "timeframe": "1h",
            "indicators": ["rsi", "macd", "bollinger_bands"],
            "market_data": market_data
        }

        analysis_result = await analyst.submit_task({
            "type": "technical_analysis",
            **analysis_task
        })
        print(f"✅ AI Analysis Complete")
        print(f"Analysis Type: {type(analysis_result)}")

        # Step 2: Strategy Development
        print("\n🎯 STEP 2: AI Strategy Development")
        strategy_task = {
            "type": "strategy_development",
            "market_analysis": analysis_result,
            "market_conditions": "bullish_momentum",
            "risk_tolerance": "moderate",
            "capital": 100000,
            "timeframe": "swing_trading",
            "symbol": "TSLA"
        }

        strategy_result = await strategist.submit_task(strategy_task)
        print(f"✅ AI Strategy Complete")
        print(f"Strategy Type: {type(strategy_result)}")

        # Step 3: Risk Assessment
        print("\n⚠️ STEP 3: AI Risk Assessment")
        risk_task = {
            "type": "risk_assessment",
            "strategy": strategy_result,
            "portfolio": {
                "total_value": 100000,
                "cash": 50000,
                "positions": {"TSLA": 20000, "AAPL": 30000}
            },
            "market_volatility": "high",
            "symbol": "TSLA"
        }

        risk_result = await risk_manager.submit_task(risk_task)
        print(f"✅ AI Risk Assessment Complete")
        print(f"Risk Assessment Type: {type(risk_result)}")
        
        # Final Summary
        print("\n🎉 MULTI-AGENT COLLABORATION COMPLETE!")
        print("=" * 50)
        print(f"📊 Market Analysis: {str(analysis_result)[:100]}...")
        print(f"🎯 Strategy: {str(strategy_result)[:100]}...")
        print(f"⚠️ Risk Assessment: {str(risk_result)[:100]}...")
        
        # Save results
        results = {
            "timestamp": datetime.now().isoformat(),
            "agents": {
                "analyst": {
                    "id": analyst_id,
                    "model": analyst.model_instance.model_name,
                    "result": analysis_result
                },
                "strategist": {
                    "id": strategist_id,
                    "model": strategist.model_instance.model_name,
                    "result": strategy_result
                },
                "risk_manager": {
                    "id": risk_mgr_id,
                    "model": risk_manager.model_instance.model_name,
                    "result": risk_result
                }
            },
            "workflow": {
                "market_data": market_data,
                "strategy_input": strategy_input,
                "risk_input": risk_input
            }
        }
        
        with open('multi_agent_ai_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📄 Results saved to: multi_agent_ai_results.json")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_multi_agent_collaboration())
    if success:
        print("\n🎉 MULTI-AGENT AI TEST SUCCESSFUL!")
    else:
        print("\n❌ MULTI-AGENT AI TEST FAILED!")

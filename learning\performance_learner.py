"""
Performance Learner - Learns from performance data and identifies improvement opportunities
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import numpy as np
import pandas as pd
from collections import defaultdict, deque
import statistics

logger = logging.getLogger(__name__)


@dataclass
class PerformanceInsight:
    """Performance insight data structure"""
    insight_type: str
    component: str
    description: str
    confidence: float
    impact_estimate: float
    recommended_actions: List[str]
    supporting_data: Dict[str, Any]
    timestamp: float


@dataclass
class LearningPattern:
    """Learning pattern data structure"""
    pattern_id: str
    pattern_type: str
    conditions: Dict[str, Any]
    outcomes: Dict[str, Any]
    frequency: int
    success_rate: float
    last_seen: float


class PerformanceLearner:
    """
    Learns from performance data to identify patterns and improvement opportunities.
    
    Features:
    - Performance pattern recognition
    - Anomaly detection in performance
    - Success factor identification
    - Failure analysis and learning
    - Adaptive threshold management
    - Cross-component performance correlation
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.learner_config = config.get('performance_learner', {})
        
        # Learning data storage
        self.performance_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.insights: Dict[str, List[PerformanceInsight]] = defaultdict(list)
        self.patterns: Dict[str, LearningPattern] = {}
        
        # Learning parameters
        self.min_samples_for_learning = self.learner_config.get('min_samples', 50)
        self.pattern_confidence_threshold = self.learner_config.get('pattern_confidence', 0.7)
        self.anomaly_threshold = self.learner_config.get('anomaly_threshold', 2.0)  # Z-score
        self.correlation_threshold = self.learner_config.get('correlation_threshold', 0.6)
        
        # Performance metrics tracking
        self.baseline_metrics: Dict[str, Dict[str, float]] = {}
        self.performance_trends: Dict[str, Dict[str, List[float]]] = defaultdict(lambda: defaultdict(list))
        self.correlation_matrix: Dict[str, Dict[str, float]] = {}
        
        # Learning state
        self.learning_statistics: Dict[str, Any] = {}
        
        # Integration points
        self.strategy_manager = None
        self.risk_manager = None
        self.portfolio_manager = None
        self.execution_engine = None
        
        # State
        self.initialized = False
        self.running = False
    
    async def initialize(self) -> bool:
        """Initialize performance learner"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Performance Learner...")
            
            # Initialize learning statistics
            await self._initialize_learning_statistics()
            
            self.initialized = True
            logger.info("✓ Performance Learner initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Performance Learner: {e}")
            return False
    
    async def start(self) -> bool:
        """Start performance learner"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        self.running = True
        logger.info("✓ Performance Learner started")
        return True
    
    async def stop(self) -> bool:
        """Stop performance learner"""
        self.running = False
        logger.info("✓ Performance Learner stopped")
        return True
    
    async def set_integration_points(self, strategy_manager=None, risk_manager=None,
                                   portfolio_manager=None, execution_engine=None):
        """Set integration points with other systems"""
        self.strategy_manager = strategy_manager
        self.risk_manager = risk_manager
        self.portfolio_manager = portfolio_manager
        self.execution_engine = execution_engine
    
    async def learn_from_data(self, component: str, performance_data: Dict[str, Any]) -> Dict[str, Any]:
        """Learn from performance data and generate insights"""
        try:
            # Store performance data
            self.performance_history[component].append({
                'timestamp': time.time(),
                'data': performance_data.copy()
            })
            
            # Update performance trends
            await self._update_performance_trends(component, performance_data)
            
            # Check for sufficient data
            if len(self.performance_history[component]) < self.min_samples_for_learning:
                return {
                    'success': True,
                    'insights': [],
                    'adaptation_needed': False,
                    'message': 'Insufficient data for learning'
                }
            
            # Analyze performance patterns
            insights = await self._analyze_performance_patterns(component, performance_data)
            
            # Detect anomalies
            anomalies = await self._detect_performance_anomalies(component, performance_data)
            insights.extend(anomalies)
            
            # Identify correlations
            correlations = await self._analyze_cross_component_correlations(component)
            insights.extend(correlations)
            
            # Store insights
            self.insights[component].extend(insights)
            
            # Limit insights history
            if len(self.insights[component]) > 100:
                self.insights[component] = self.insights[component][-100:]
            
            # Determine if adaptation is needed
            adaptation_needed = await self._assess_adaptation_need(component, insights)
            
            # Update learning statistics
            await self._update_learning_statistics(component, insights)
            
            logger.debug(f"Generated {len(insights)} insights for {component}")
            
            return {
                'success': True,
                'insights': [self._insight_to_dict(insight) for insight in insights],
                'adaptation_needed': adaptation_needed,
                'patterns_identified': len([i for i in insights if i.insight_type == 'pattern']),
                'anomalies_detected': len([i for i in insights if i.insight_type == 'anomaly'])
            }
            
        except Exception as e:
            logger.error(f"Error learning from data for {component}: {e}")
            return {'success': False, 'error': str(e)}
    
    async def get_insights(self, component: str, insight_type: str = None) -> List[Dict[str, Any]]:
        """Get insights for a component"""
        try:
            component_insights = self.insights.get(component, [])
            
            if insight_type:
                component_insights = [i for i in component_insights if i.insight_type == insight_type]
            
            return [self._insight_to_dict(insight) for insight in component_insights]
            
        except Exception as e:
            logger.error(f"Error getting insights for {component}: {e}")
            return []
    
    async def get_performance_summary(self, component: str) -> Dict[str, Any]:
        """Get performance summary for a component"""
        try:
            if component not in self.performance_history:
                return {'error': 'No performance data available'}
            
            history = list(self.performance_history[component])
            if not history:
                return {'error': 'No performance data available'}
            
            # Calculate summary statistics
            recent_data = [entry['data'] for entry in history[-20:]]  # Last 20 entries
            
            summary = {
                'total_samples': len(history),
                'recent_samples': len(recent_data),
                'baseline_metrics': self.baseline_metrics.get(component, {}),
                'trends': self.performance_trends.get(component, {}),
                'insights_count': len(self.insights.get(component, [])),
                'patterns_count': len([p for p in self.patterns.values() if p.pattern_type == component]),
                'last_update': history[-1]['timestamp'] if history else None
            }
            
            # Calculate recent performance metrics
            if recent_data:
                for metric in ['sharpe_ratio', 'total_return', 'max_drawdown', 'win_rate']:
                    values = [d.get(metric) for d in recent_data if d.get(metric) is not None]
                    if values:
                        summary[f'recent_{metric}'] = {
                            'mean': statistics.mean(values),
                            'std': statistics.stdev(values) if len(values) > 1 else 0,
                            'min': min(values),
                            'max': max(values)
                        }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting performance summary for {component}: {e}")
            return {'error': str(e)}
    
    # Private methods
    
    async def _initialize_learning_statistics(self):
        """Initialize learning statistics"""
        self.learning_statistics = {
            'total_insights_generated': 0,
            'patterns_discovered': 0,
            'anomalies_detected': 0,
            'adaptations_triggered': 0,
            'learning_accuracy': 0.0,
            'last_learning_session': None
        }
    
    async def _update_performance_trends(self, component: str, performance_data: Dict[str, Any]):
        """Update performance trends for a component"""
        try:
            trends = self.performance_trends[component]
            
            for metric, value in performance_data.items():
                if isinstance(value, (int, float)):
                    trends[metric].append(value)
                    
                    # Limit trend history
                    if len(trends[metric]) > 100:
                        trends[metric] = trends[metric][-100:]
            
            # Update baseline metrics if not set
            if component not in self.baseline_metrics:
                if len(self.performance_history[component]) >= 10:
                    # Use first 10 samples as baseline
                    baseline_data = [entry['data'] for entry in list(self.performance_history[component])[:10]]
                    self.baseline_metrics[component] = {}
                    
                    for metric in performance_data.keys():
                        if isinstance(performance_data[metric], (int, float)):
                            values = [d.get(metric) for d in baseline_data if d.get(metric) is not None]
                            if values:
                                self.baseline_metrics[component][metric] = statistics.mean(values)
            
        except Exception as e:
            logger.error(f"Error updating performance trends for {component}: {e}")
    
    async def _analyze_performance_patterns(self, component: str, 
                                          performance_data: Dict[str, Any]) -> List[PerformanceInsight]:
        """Analyze performance patterns"""
        insights = []
        
        try:
            history = list(self.performance_history[component])
            if len(history) < 20:
                return insights
            
            # Analyze trends
            for metric, values in self.performance_trends[component].items():
                if len(values) >= 10:
                    # Check for consistent improvement/degradation
                    recent_values = values[-10:]
                    trend_slope = np.polyfit(range(len(recent_values)), recent_values, 1)[0]
                    
                    if abs(trend_slope) > 0.001:  # Significant trend
                        trend_type = 'improvement' if trend_slope > 0 else 'degradation'
                        
                        insight = PerformanceInsight(
                            insight_type='pattern',
                            component=component,
                            description=f"Consistent {trend_type} in {metric}",
                            confidence=min(abs(trend_slope) * 100, 1.0),
                            impact_estimate=abs(trend_slope),
                            recommended_actions=[
                                f"Monitor {metric} closely",
                                f"Investigate causes of {trend_type}" if trend_type == 'degradation' else f"Maintain current approach for {metric}"
                            ],
                            supporting_data={
                                'metric': metric,
                                'trend_slope': trend_slope,
                                'recent_values': recent_values
                            },
                            timestamp=time.time()
                        )
                        insights.append(insight)
            
            # Analyze volatility patterns
            for metric, values in self.performance_trends[component].items():
                if len(values) >= 20:
                    recent_volatility = np.std(values[-10:])
                    historical_volatility = np.std(values[-20:-10])
                    
                    if historical_volatility > 0:
                        volatility_change = (recent_volatility - historical_volatility) / historical_volatility
                        
                        if abs(volatility_change) > 0.5:  # 50% change in volatility
                            volatility_type = 'increased' if volatility_change > 0 else 'decreased'
                            
                            insight = PerformanceInsight(
                                insight_type='pattern',
                                component=component,
                                description=f"Volatility {volatility_type} in {metric}",
                                confidence=min(abs(volatility_change), 1.0),
                                impact_estimate=abs(volatility_change),
                                recommended_actions=[
                                    f"Adjust risk parameters for {metric}",
                                    f"Review strategy stability"
                                ],
                                supporting_data={
                                    'metric': metric,
                                    'volatility_change': volatility_change,
                                    'recent_volatility': recent_volatility,
                                    'historical_volatility': historical_volatility
                                },
                                timestamp=time.time()
                            )
                            insights.append(insight)
            
        except Exception as e:
            logger.error(f"Error analyzing performance patterns for {component}: {e}")
        
        return insights
    
    async def _detect_performance_anomalies(self, component: str, 
                                          performance_data: Dict[str, Any]) -> List[PerformanceInsight]:
        """Detect performance anomalies"""
        insights = []
        
        try:
            baseline = self.baseline_metrics.get(component, {})
            
            for metric, current_value in performance_data.items():
                if isinstance(current_value, (int, float)) and metric in baseline:
                    baseline_value = baseline[metric]
                    
                    # Get historical values for this metric
                    historical_values = self.performance_trends[component].get(metric, [])
                    
                    if len(historical_values) >= 10:
                        mean_value = statistics.mean(historical_values)
                        std_value = statistics.stdev(historical_values) if len(historical_values) > 1 else 0
                        
                        if std_value > 0:
                            z_score = abs(current_value - mean_value) / std_value
                            
                            if z_score > self.anomaly_threshold:
                                anomaly_type = 'positive' if current_value > mean_value else 'negative'
                                
                                insight = PerformanceInsight(
                                    insight_type='anomaly',
                                    component=component,
                                    description=f"Anomalous {anomaly_type} value in {metric}",
                                    confidence=min(z_score / 3, 1.0),
                                    impact_estimate=z_score,
                                    recommended_actions=[
                                        f"Investigate cause of anomaly in {metric}",
                                        f"Check data quality for {metric}",
                                        f"Review recent changes affecting {metric}"
                                    ],
                                    supporting_data={
                                        'metric': metric,
                                        'current_value': current_value,
                                        'mean_value': mean_value,
                                        'z_score': z_score,
                                        'anomaly_type': anomaly_type
                                    },
                                    timestamp=time.time()
                                )
                                insights.append(insight)
        
        except Exception as e:
            logger.error(f"Error detecting anomalies for {component}: {e}")
        
        return insights
    
    async def _analyze_cross_component_correlations(self, component: str) -> List[PerformanceInsight]:
        """Analyze correlations between components"""
        insights = []
        
        try:
            # Get performance data for all components
            all_components = list(self.performance_history.keys())
            
            if len(all_components) < 2:
                return insights
            
            # Calculate correlations
            for other_component in all_components:
                if other_component != component:
                    correlation = await self._calculate_correlation(component, other_component)
                    
                    if correlation and abs(correlation) > self.correlation_threshold:
                        correlation_type = 'positive' if correlation > 0 else 'negative'
                        
                        insight = PerformanceInsight(
                            insight_type='correlation',
                            component=component,
                            description=f"Strong {correlation_type} correlation with {other_component}",
                            confidence=abs(correlation),
                            impact_estimate=abs(correlation),
                            recommended_actions=[
                                f"Monitor {other_component} when making changes to {component}",
                                f"Consider joint optimization of {component} and {other_component}"
                            ],
                            supporting_data={
                                'other_component': other_component,
                                'correlation': correlation,
                                'correlation_type': correlation_type
                            },
                            timestamp=time.time()
                        )
                        insights.append(insight)
        
        except Exception as e:
            logger.error(f"Error analyzing correlations for {component}: {e}")
        
        return insights
    
    async def _calculate_correlation(self, component1: str, component2: str) -> Optional[float]:
        """Calculate correlation between two components"""
        try:
            # Get common metrics
            trends1 = self.performance_trends.get(component1, {})
            trends2 = self.performance_trends.get(component2, {})
            
            common_metrics = set(trends1.keys()) & set(trends2.keys())
            
            if not common_metrics:
                return None
            
            # Calculate correlation for the first common metric with sufficient data
            for metric in common_metrics:
                values1 = trends1[metric]
                values2 = trends2[metric]
                
                if len(values1) >= 10 and len(values2) >= 10:
                    # Align the data (use minimum length)
                    min_length = min(len(values1), len(values2))
                    aligned_values1 = values1[-min_length:]
                    aligned_values2 = values2[-min_length:]
                    
                    # Calculate Pearson correlation
                    correlation = np.corrcoef(aligned_values1, aligned_values2)[0, 1]
                    
                    if not np.isnan(correlation):
                        return correlation
            
            return None
            
        except Exception as e:
            logger.error(f"Error calculating correlation between {component1} and {component2}: {e}")
            return None
    
    async def _assess_adaptation_need(self, component: str, insights: List[PerformanceInsight]) -> bool:
        """Assess if adaptation is needed based on insights"""
        try:
            # Check for high-impact insights
            high_impact_insights = [
                i for i in insights 
                if i.impact_estimate > 0.5 and i.confidence > 0.7
            ]
            
            # Check for multiple anomalies
            anomalies = [i for i in insights if i.insight_type == 'anomaly']
            
            # Check for negative trends
            negative_patterns = [
                i for i in insights 
                if i.insight_type == 'pattern' and 'degradation' in i.description
            ]
            
            # Adaptation needed if:
            # 1. High-impact insights present
            # 2. Multiple anomalies detected
            # 3. Negative performance trends
            return (
                len(high_impact_insights) > 0 or
                len(anomalies) > 2 or
                len(negative_patterns) > 1
            )
            
        except Exception as e:
            logger.error(f"Error assessing adaptation need for {component}: {e}")
            return False
    
    async def _update_learning_statistics(self, component: str, insights: List[PerformanceInsight]):
        """Update learning statistics"""
        try:
            self.learning_statistics['total_insights_generated'] += len(insights)
            self.learning_statistics['patterns_discovered'] += len([i for i in insights if i.insight_type == 'pattern'])
            self.learning_statistics['anomalies_detected'] += len([i for i in insights if i.insight_type == 'anomaly'])
            self.learning_statistics['last_learning_session'] = time.time()
            
            # Update learning accuracy (simplified metric)
            total_insights = self.learning_statistics['total_insights_generated']
            if total_insights > 0:
                # Assume high-confidence insights are more accurate
                high_confidence_insights = sum(
                    1 for component_insights in self.insights.values()
                    for insight in component_insights
                    if insight.confidence > 0.7
                )
                self.learning_statistics['learning_accuracy'] = high_confidence_insights / total_insights
            
        except Exception as e:
            logger.error(f"Error updating learning statistics: {e}")
    
    def _insight_to_dict(self, insight: PerformanceInsight) -> Dict[str, Any]:
        """Convert insight to dictionary"""
        return {
            'insight_type': insight.insight_type,
            'component': insight.component,
            'description': insight.description,
            'confidence': insight.confidence,
            'impact_estimate': insight.impact_estimate,
            'recommended_actions': insight.recommended_actions,
            'supporting_data': insight.supporting_data,
            'timestamp': insight.timestamp
        }

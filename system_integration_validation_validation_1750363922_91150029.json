{"validation_id": "validation_**********_91150029", "validation_level": "basic", "overall_status": "partial", "overall_score": 0.7724831986119565, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "partial", "integration_score": 0.806487458009443, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "team_manager", "component_type": "core", "status": "partial", "integration_score": 0.7399879103044401, "error_count": 0, "warnings": ["Functionality concerns in team_manager", "Integration issues in team_manager"], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "partial", "integration_score": 0.7068505265866291, "error_count": 0, "warnings": ["Functionality concerns in data_manager", "Integration issues in data_manager"], "dependencies_met": "True"}, {"component_name": "analytics_engine", "component_type": "core", "status": "partial", "integration_score": 0.7706404813951591, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "ollama_hub", "component_type": "core", "status": "partial", "integration_score": 0.7646511294044566, "error_count": 0, "warnings": ["Integration issues in ollama_hub"], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "partial", "integration_score": 0.7668536588922255, "error_count": 0, "warnings": ["Functionality concerns in execution_engine"], "dependencies_met": "True"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "partial", "integration_score": 0.8246004370907004, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "risk_manager", "component_type": "core", "status": "partial", "integration_score": 0.798830726899577, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "partial", "integration_score": 0.7729814011614734, "error_count": 0, "warnings": ["Functionality concerns in strategy_manager", "Integration issues in strategy_manager"], "dependencies_met": "True"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "partial", "integration_score": 0.8228786683536516, "error_count": 0, "warnings": ["Integration issues in competitive_framework"], "dependencies_met": "True"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "partial", "integration_score": 0.8013000294594703, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.7295053212922519, "error_count": 0, "warnings": ["Functionality concerns in self_improvement_engine", "Integration issues in self_improvement_engine"], "dependencies_met": "False"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "partial", "integration_score": 0.8351315869684841, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "partial", "integration_score": 0.8075176284505217, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.7787706503572693, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "partial", "integration_score": 0.7377658641891969, "error_count": 0, "warnings": ["Functionality concerns in ai_coordinator"], "dependencies_met": "True"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "partial", "integration_score": 0.8614015874864615, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "partial", "integration_score": 0.8788110386971715, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "partial", "integration_score": 0.8129791149982957, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "partial", "integration_score": 0.7744946356135975, "error_count": 0, "warnings": [], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.9046888511759451, "data_manager": 0.7096555976321562, "analytics_engine": 0.6980093103419274, "ollama_hub": 0.6770257086434247, "execution_engine": 0.7738508897073509, "portfolio_manager": 0.647789325516076, "risk_manager": 0.6359633895084585, "strategy_manager": 0.7222498099724182, "competitive_framework": 0.6529541803719751, "tournament_framework": 0.7130065848740511, "self_improvement_engine": 0.8405970113115397, "regime_adaptation_system": 0.899112621276903, "performance_optimizer": 0.7435704605617254, "advanced_trading_engine": 0.6701490400725928, "ai_coordinator": 0.7189020838956774, "configuration_manager": 0.6169264172335645, "mock_data_providers": 0.7140623013312426, "paper_trading_engine": 0.6071189648489983, "logging_audit_system": 0.7822148920954803}, "team_manager": {"system_coordinator": 0.8149904576594857, "team_manager": 1.0, "data_manager": 0.945310309772429, "analytics_engine": 0.6107203551862608, "ollama_hub": 0.8974205787502473, "execution_engine": 0.822614772129762, "portfolio_manager": 0.7826410020477423, "risk_manager": 0.8206121902427586, "strategy_manager": 0.6638959271490092, "competitive_framework": 0.7612896929878985, "tournament_framework": 0.8641748972097911, "self_improvement_engine": 0.8201177621531307, "regime_adaptation_system": 0.6134671815792915, "performance_optimizer": 0.6355701874149632, "advanced_trading_engine": 0.7121724761576481, "ai_coordinator": 0.8617716420343655, "configuration_manager": 0.644958759545998, "mock_data_providers": 0.8865322713027308, "paper_trading_engine": 0.6591272678890127, "logging_audit_system": 0.6272522458444821}, "data_manager": {"system_coordinator": 0.6088391325951734, "team_manager": 0.6944075588577215, "data_manager": 1.0, "analytics_engine": 0.8582910694588199, "ollama_hub": 0.7425308076737895, "execution_engine": 0.6726818502018432, "portfolio_manager": 0.8287788378917004, "risk_manager": 0.7877409233283539, "strategy_manager": 0.7485302319305949, "competitive_framework": 0.899052263116692, "tournament_framework": 0.8486470231946511, "self_improvement_engine": 0.7210489344271935, "regime_adaptation_system": 0.7241233706094231, "performance_optimizer": 0.604556216353184, "advanced_trading_engine": 0.8509941924480628, "ai_coordinator": 0.8562636467067821, "configuration_manager": 0.8563248498885903, "mock_data_providers": 0.7930747274296224, "paper_trading_engine": 0.7617502334199098, "logging_audit_system": 0.7191365842696693}, "analytics_engine": {"system_coordinator": 0.6001347066840615, "team_manager": 0.6274660806029901, "data_manager": 0.6729131105025684, "analytics_engine": 1.0, "ollama_hub": 0.8829437468820427, "execution_engine": 0.7143542916561042, "portfolio_manager": 0.8752302266065057, "risk_manager": 0.7952644361573182, "strategy_manager": 0.8489848127805921, "competitive_framework": 0.8046474009976873, "tournament_framework": 0.6329491092551732, "self_improvement_engine": 0.7626951917311929, "regime_adaptation_system": 0.8858330460532698, "performance_optimizer": 0.8566132042869814, "advanced_trading_engine": 0.6817899900456004, "ai_coordinator": 0.7380760834848454, "configuration_manager": 0.7552572617943089, "mock_data_providers": 0.6534752583054743, "paper_trading_engine": 0.8666878899651114, "logging_audit_system": 0.7904896613305415}, "ollama_hub": {"system_coordinator": 0.7307715698498642, "team_manager": 0.8748516894645875, "data_manager": 0.7419398415538687, "analytics_engine": 0.8210503855198832, "ollama_hub": 1.0, "execution_engine": 0.7920113257750199, "portfolio_manager": 0.7516762908981491, "risk_manager": 0.786467166741658, "strategy_manager": 0.7933036767233933, "competitive_framework": 0.7969916849838008, "tournament_framework": 0.7986867857158264, "self_improvement_engine": 0.6051738217978782, "regime_adaptation_system": 0.6708194620682996, "performance_optimizer": 0.7869600214046504, "advanced_trading_engine": 0.8504287274208089, "ai_coordinator": 0.8309300947339457, "configuration_manager": 0.8998188129158753, "mock_data_providers": 0.7063620321498987, "paper_trading_engine": 0.7548592447595015, "logging_audit_system": 0.8810239283841844}, "execution_engine": {"system_coordinator": 0.819634169611112, "team_manager": 0.8233455356651779, "data_manager": 0.8148076392173843, "analytics_engine": 0.8706256965643331, "ollama_hub": 0.6376465838384725, "execution_engine": 1.0, "portfolio_manager": 0.8813545281929046, "risk_manager": 0.7609020705325464, "strategy_manager": 0.8236761744275909, "competitive_framework": 0.8064167739816588, "tournament_framework": 0.8497192558577519, "self_improvement_engine": 0.6856291915391044, "regime_adaptation_system": 0.8726779352400929, "performance_optimizer": 0.8355728523074972, "advanced_trading_engine": 0.7089251411052013, "ai_coordinator": 0.6180314657771007, "configuration_manager": 0.6293471449818807, "mock_data_providers": 0.7133500373401769, "paper_trading_engine": 0.8956482804559548, "logging_audit_system": 0.7480860785293714}, "portfolio_manager": {"system_coordinator": 0.6400489763068383, "team_manager": 0.636201770376839, "data_manager": 0.6331423964040125, "analytics_engine": 0.6321247595831635, "ollama_hub": 0.75593425115393, "execution_engine": 0.8878162516322237, "portfolio_manager": 1.0, "risk_manager": 0.6657301012834106, "strategy_manager": 0.8475529231638456, "competitive_framework": 0.6395685635885873, "tournament_framework": 0.6207935611870994, "self_improvement_engine": 0.7648491437718747, "regime_adaptation_system": 0.6839755617181513, "performance_optimizer": 0.8939115482139688, "advanced_trading_engine": 0.8193867974313844, "ai_coordinator": 0.7163742879177202, "configuration_manager": 0.8282011212879392, "mock_data_providers": 0.858796211648206, "paper_trading_engine": 0.761766206680868, "logging_audit_system": 0.7116158508421133}, "risk_manager": {"system_coordinator": 0.6128942091856088, "team_manager": 0.8495244347746895, "data_manager": 0.6815043546080134, "analytics_engine": 0.6268164143882375, "ollama_hub": 0.8873623844879357, "execution_engine": 0.7730696371474438, "portfolio_manager": 0.8348651761133016, "risk_manager": 1.0, "strategy_manager": 0.7851190063279827, "competitive_framework": 0.7875562029144244, "tournament_framework": 0.8116299789605204, "self_improvement_engine": 0.6877575136306104, "regime_adaptation_system": 0.6927568043609795, "performance_optimizer": 0.8500104048067698, "advanced_trading_engine": 0.6387544824852431, "ai_coordinator": 0.8708852843999524, "configuration_manager": 0.6540918103640161, "mock_data_providers": 0.6630963560776835, "paper_trading_engine": 0.6931410464894018, "logging_audit_system": 0.692570953197467}, "strategy_manager": {"system_coordinator": 0.6718955205051719, "team_manager": 0.7409954643602038, "data_manager": 0.8897622223892294, "analytics_engine": 0.6131259268965533, "ollama_hub": 0.7623442948928216, "execution_engine": 0.7311615829943245, "portfolio_manager": 0.6883686260410048, "risk_manager": 0.7174489370155631, "strategy_manager": 1.0, "competitive_framework": 0.8047636286108373, "tournament_framework": 0.745066245117477, "self_improvement_engine": 0.8395574506163178, "regime_adaptation_system": 0.6419586654529817, "performance_optimizer": 0.69869973458184, "advanced_trading_engine": 0.6944844025347331, "ai_coordinator": 0.6378430535878578, "configuration_manager": 0.8354383206501965, "mock_data_providers": 0.6792063883953642, "paper_trading_engine": 0.6593660855028476, "logging_audit_system": 0.7873031111802442}, "competitive_framework": {"system_coordinator": 0.8370128106875073, "team_manager": 0.8220922623354732, "data_manager": 0.7638696352503938, "analytics_engine": 0.6804046587773834, "ollama_hub": 0.8165990184125944, "execution_engine": 0.8793650812037257, "portfolio_manager": 0.634299198101055, "risk_manager": 0.7596180924890279, "strategy_manager": 0.7851101792542556, "competitive_framework": 1.0, "tournament_framework": 0.6653489685236006, "self_improvement_engine": 0.8834079919615616, "regime_adaptation_system": 0.6097072217112139, "performance_optimizer": 0.7080186353251882, "advanced_trading_engine": 0.7310848902767445, "ai_coordinator": 0.6006575804584987, "configuration_manager": 0.7015609363772015, "mock_data_providers": 0.632629598397691, "paper_trading_engine": 0.7223624474688373, "logging_audit_system": 0.7237404098103714}, "tournament_framework": {"system_coordinator": 0.8688077735318517, "team_manager": 0.8617685798436703, "data_manager": 0.8346454509641406, "analytics_engine": 0.744088471789605, "ollama_hub": 0.8462385147886936, "execution_engine": 0.8934953703538063, "portfolio_manager": 0.6276539551450736, "risk_manager": 0.8994903423436605, "strategy_manager": 0.6925259027481008, "competitive_framework": 0.8988918922603273, "tournament_framework": 1.0, "self_improvement_engine": 0.6547125901930047, "regime_adaptation_system": 0.7770612411836351, "performance_optimizer": 0.8925383265173897, "advanced_trading_engine": 0.6685653302136101, "ai_coordinator": 0.6441772312069936, "configuration_manager": 0.891973968164628, "mock_data_providers": 0.806138490125245, "paper_trading_engine": 0.7849045675300771, "logging_audit_system": 0.6475197348760104}, "self_improvement_engine": {"system_coordinator": 0.7366559722990064, "team_manager": 0.722207375927493, "data_manager": 0.7723956838207483, "analytics_engine": 0.6157701244033822, "ollama_hub": 0.7109680425288887, "execution_engine": 0.8148680205586614, "portfolio_manager": 0.7774340380297879, "risk_manager": 0.8063724820599396, "strategy_manager": 0.820463734705756, "competitive_framework": 0.7060683968291881, "tournament_framework": 0.7152584507921738, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.852655597079538, "performance_optimizer": 0.7401425605382463, "advanced_trading_engine": 0.7300306246261145, "ai_coordinator": 0.782519581017702, "configuration_manager": 0.6809543963135912, "mock_data_providers": 0.6336559763496173, "paper_trading_engine": 0.868205777750717, "logging_audit_system": 0.6485565256445913}, "regime_adaptation_system": {"system_coordinator": 0.6142290852413566, "team_manager": 0.6399522821913691, "data_manager": 0.8241579750510095, "analytics_engine": 0.6481146327238971, "ollama_hub": 0.609820427308482, "execution_engine": 0.8105875124874702, "portfolio_manager": 0.7824447151184487, "risk_manager": 0.7693003033719238, "strategy_manager": 0.795322061039685, "competitive_framework": 0.7231507300257194, "tournament_framework": 0.6494345380299128, "self_improvement_engine": 0.6549295976980697, "regime_adaptation_system": 1.0, "performance_optimizer": 0.6480807579480645, "advanced_trading_engine": 0.6404173180407876, "ai_coordinator": 0.6879323767007617, "configuration_manager": 0.6987858256121103, "mock_data_providers": 0.7183244178455399, "paper_trading_engine": 0.6770096106452481, "logging_audit_system": 0.7531849207016855}, "performance_optimizer": {"system_coordinator": 0.704126434769927, "team_manager": 0.6446929417636709, "data_manager": 0.8943784778176969, "analytics_engine": 0.879228776529209, "ollama_hub": 0.8190161375586518, "execution_engine": 0.7033213896559594, "portfolio_manager": 0.7491460042174682, "risk_manager": 0.6971573568028846, "strategy_manager": 0.7076451957347376, "competitive_framework": 0.7712443857330931, "tournament_framework": 0.8495363521528159, "self_improvement_engine": 0.8611107150702252, "regime_adaptation_system": 0.6105601208722321, "performance_optimizer": 1.0, "advanced_trading_engine": 0.8512216894321075, "ai_coordinator": 0.7224550784686119, "configuration_manager": 0.7716712799777858, "mock_data_providers": 0.8638007194184036, "paper_trading_engine": 0.6123914080566157, "logging_audit_system": 0.7744413500866477}, "advanced_trading_engine": {"system_coordinator": 0.8460954246247612, "team_manager": 0.6796149389813244, "data_manager": 0.6059293540148701, "analytics_engine": 0.6384024387640151, "ollama_hub": 0.7003738999047773, "execution_engine": 0.6800464287176553, "portfolio_manager": 0.6946229864755976, "risk_manager": 0.7505909897038301, "strategy_manager": 0.7948230037372839, "competitive_framework": 0.6521009404936468, "tournament_framework": 0.8136171187189206, "self_improvement_engine": 0.8254123631390944, "regime_adaptation_system": 0.868525283448671, "performance_optimizer": 0.6012783575476061, "advanced_trading_engine": 1.0, "ai_coordinator": 0.7321911342275398, "configuration_manager": 0.7765785409170985, "mock_data_providers": 0.759112946690668, "paper_trading_engine": 0.7373477959367888, "logging_audit_system": 0.793378464176425}, "ai_coordinator": {"system_coordinator": 0.7185151305235407, "team_manager": 0.7912052260486875, "data_manager": 0.708355725237087, "analytics_engine": 0.6645369986423616, "ollama_hub": 0.8242547531415527, "execution_engine": 0.8906235065194075, "portfolio_manager": 0.6858410240957789, "risk_manager": 0.868944742244836, "strategy_manager": 0.6612642009843709, "competitive_framework": 0.6288013308961978, "tournament_framework": 0.8419398328632707, "self_improvement_engine": 0.7315616776660709, "regime_adaptation_system": 0.6770674027137639, "performance_optimizer": 0.6825489190899687, "advanced_trading_engine": 0.8440790915596812, "ai_coordinator": 1.0, "configuration_manager": 0.6748392415965117, "mock_data_providers": 0.8456669844124228, "paper_trading_engine": 0.8733821855521928, "logging_audit_system": 0.6164710556479487}, "configuration_manager": {"system_coordinator": 0.7828095070520952, "team_manager": 0.7396666392554115, "data_manager": 0.8418692311923732, "analytics_engine": 0.8072219829250672, "ollama_hub": 0.6678023501597278, "execution_engine": 0.7874882499674488, "portfolio_manager": 0.6667859130644964, "risk_manager": 0.6830310523578779, "strategy_manager": 0.6285867704347081, "competitive_framework": 0.6082911048734748, "tournament_framework": 0.6628966570360167, "self_improvement_engine": 0.7282638964991313, "regime_adaptation_system": 0.7865692727684488, "performance_optimizer": 0.6681364670800172, "advanced_trading_engine": 0.7566890932283967, "ai_coordinator": 0.6152628441597078, "configuration_manager": 1.0, "mock_data_providers": 0.8431023644611364, "paper_trading_engine": 0.7603396912172622, "logging_audit_system": 0.7982450405873243}, "mock_data_providers": {"system_coordinator": 0.6112380850404713, "team_manager": 0.6567433963271838, "data_manager": 0.64331095321443, "analytics_engine": 0.8546609909283969, "ollama_hub": 0.752881920794933, "execution_engine": 0.6815841654717614, "portfolio_manager": 0.825975047312798, "risk_manager": 0.8119688252985457, "strategy_manager": 0.6513994064931315, "competitive_framework": 0.7029995927812274, "tournament_framework": 0.8159366244354735, "self_improvement_engine": 0.6268830479376787, "regime_adaptation_system": 0.7662738813687471, "performance_optimizer": 0.8236262343164173, "advanced_trading_engine": 0.7104841414298957, "ai_coordinator": 0.8036318424394231, "configuration_manager": 0.8377802012059872, "mock_data_providers": 1.0, "paper_trading_engine": 0.8917329635228388, "logging_audit_system": 0.7574267723273422}, "paper_trading_engine": {"system_coordinator": 0.7097171244333151, "team_manager": 0.8189366407086289, "data_manager": 0.6567544739837469, "analytics_engine": 0.822164305775704, "ollama_hub": 0.8194883644105824, "execution_engine": 0.8605038632714537, "portfolio_manager": 0.8114320387454024, "risk_manager": 0.65796089336915, "strategy_manager": 0.6551645611561879, "competitive_framework": 0.7462177039667051, "tournament_framework": 0.6828665228826832, "self_improvement_engine": 0.7710496845315867, "regime_adaptation_system": 0.8912069900769, "performance_optimizer": 0.8518151313743636, "advanced_trading_engine": 0.6621353447240236, "ai_coordinator": 0.7323624252060845, "configuration_manager": 0.7735511848733787, "mock_data_providers": 0.7134622408167833, "paper_trading_engine": 1.0, "logging_audit_system": 0.8679879638888708}, "logging_audit_system": {"system_coordinator": 0.7856669436569569, "team_manager": 0.6813654536757237, "data_manager": 0.7032564312012566, "analytics_engine": 0.8097206256109987, "ollama_hub": 0.7125104677655227, "execution_engine": 0.7992992551938454, "portfolio_manager": 0.7251007291553524, "risk_manager": 0.8125110680687637, "strategy_manager": 0.8046479369851539, "competitive_framework": 0.7376629467626028, "tournament_framework": 0.8577034183170724, "self_improvement_engine": 0.8175353486020283, "regime_adaptation_system": 0.8418496134920981, "performance_optimizer": 0.6071816914967613, "advanced_trading_engine": 0.6088422235303474, "ai_coordinator": 0.7552666069588918, "configuration_manager": 0.8319982600036337, "mock_data_providers": 0.8778535296419623, "paper_trading_engine": 0.8893553544880257, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.7240338953620579, "response_time": 0.9094220618442275, "throughput": 0.7193375153256214, "memory_usage": 0.7758348409258263, "cpu_usage": 0.8278689269444793, "concurrent_operations": 0.7600618395064539}, "critical_issues": ["Components with dependency issues: analytics_engine, self_improvement_engine"], "recommendations": ["Improve 20 partially working components", "Improve overall system performance and stability", "Enhance component integration and communication"], "production_ready": "True", "timestamp": **********.8093386}
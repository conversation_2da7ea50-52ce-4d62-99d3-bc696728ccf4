"""
End-to-End Integration Tests

These tests verify complete workflows from market data ingestion
through analysis, strategy execution, risk management, and order execution.
"""

import pytest
import pytest_asyncio
import asyncio
import numpy as np
import pandas as pd
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

from main import TradingSystem
from tests.fixtures import TestFixtures


class TestCompleteWorkflows:
    """Test complete trading workflows end-to-end"""
    
    @pytest.fixture
    async def trading_system(self):
        """Setup complete trading system for testing"""
        config = {
            'testing': True,
            'database': {
                'postgres': {
                    'host': 'localhost',
                    'port': 5432,
                    'database': 'test_trading_agents'
                },
                'redis': {
                    'host': 'localhost',
                    'port': 6379,
                    'database': 1
                }
            },
            'api': {
                'host': '0.0.0.0',
                'port': 8001
            },
            'agents': {
                'max_agents': 5,
                'heartbeat_interval': 5
            },
            'risk': {
                'max_position_size': 0.1,
                'max_daily_loss': 0.05
            },
            'execution': {
                'enabled': True,
                'paper_trading': True
            }
        }
        
        with patch('asyncpg.connect'), \
             patch('redis.Redis'), \
             patch('ollama.AsyncClient'):
            
            system = TradingSystem(config=config)
            await system.initialize()
            yield system
            await system.stop()
    
    @pytest.fixture
    def market_data_stream(self):
        """Generate realistic market data stream"""
        symbols = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA']
        base_prices = {'AAPL': 150, 'GOOGL': 2500, 'MSFT': 350, 'AMZN': 3000, 'TSLA': 200}
        
        data_stream = []
        current_time = datetime.now()
        
        for i in range(100):  # 100 data points
            timestamp = current_time + timedelta(minutes=i)
            
            for symbol in symbols:
                # Simulate price movement
                base_price = base_prices[symbol]
                price_change = np.random.normal(0, 0.01)  # 1% volatility
                new_price = base_price * (1 + price_change)
                base_prices[symbol] = new_price
                
                data_point = {
                    'symbol': symbol,
                    'timestamp': timestamp,
                    'price': new_price,
                    'volume': np.random.randint(100000, 1000000),
                    'bid': new_price * 0.999,
                    'ask': new_price * 1.001
                }
                data_stream.append(data_point)
        
        return data_stream
    
    @pytest_asyncio.mark.asyncio
    async def test_market_data_to_trade_workflow(self, trading_system, market_data_stream):
        """Test complete workflow from market data to trade execution"""
        # Start the trading system
        await trading_system.start()
        
        # Verify system is running
        assert trading_system.running is True
        
        # Process market data stream
        trades_executed = []
        
        for data_point in market_data_stream[:10]:  # Process first 10 data points
            # 1. Market data ingestion
            await trading_system.process_market_data(data_point)
            
            # 2. Agent analysis (mocked)
            analysis_result = {
                'symbol': data_point['symbol'],
                'trend': 'bullish' if np.random.random() > 0.5 else 'bearish',
                'confidence': np.random.uniform(0.6, 0.9),
                'signals': ['momentum', 'volume']
            }
            
            # 3. Strategy signal generation
            if analysis_result['confidence'] > 0.8:
                signal = {
                    'symbol': data_point['symbol'],
                    'action': 'buy' if analysis_result['trend'] == 'bullish' else 'sell',
                    'quantity': 100,
                    'confidence': analysis_result['confidence'],
                    'strategy_id': 'test_strategy'
                }
                
                # 4. Risk assessment
                risk_approved = await trading_system.assess_trade_risk(signal)
                
                if risk_approved:
                    # 5. Order execution
                    trade_result = await trading_system.execute_trade(signal)
                    if trade_result:
                        trades_executed.append(trade_result)
        
        # Verify trades were executed
        assert len(trades_executed) >= 0  # May or may not execute trades based on conditions
        
        # Verify system state
        system_status = await trading_system.get_system_status()
        assert system_status['status'] in ['healthy', 'warning']
    
    @pytest_asyncio.mark.asyncio
    async def test_multi_agent_collaboration(self, trading_system):
        """Test collaboration between multiple agents"""
        await trading_system.start()
        
        # Mock agent responses
        with patch.object(trading_system.agent_manager, 'send_message') as mock_send:
            mock_send.return_value = True
            
            # Simulate analyst agent sending market insight
            market_insight = {
                'type': 'market_analysis',
                'symbol': 'AAPL',
                'analysis': {
                    'trend': 'bullish',
                    'support_level': 145,
                    'resistance_level': 155,
                    'confidence': 0.85
                },
                'from_agent': 'analyst_001'
            }
            
            # Send to strategy agent
            await trading_system.agent_manager.send_message(
                'strategy_001', market_insight
            )
            
            # Strategy agent processes and sends signal to risk agent
            trading_signal = {
                'type': 'trading_signal',
                'symbol': 'AAPL',
                'action': 'buy',
                'quantity': 100,
                'confidence': 0.8,
                'from_agent': 'strategy_001'
            }
            
            await trading_system.agent_manager.send_message(
                'risk_001', trading_signal
            )
            
            # Risk agent approves and sends to execution agent
            risk_approval = {
                'type': 'risk_approval',
                'signal': trading_signal,
                'approved': True,
                'risk_score': 0.3,
                'from_agent': 'risk_001'
            }
            
            await trading_system.agent_manager.send_message(
                'execution_001', risk_approval
            )
            
            # Verify message flow
            assert mock_send.call_count >= 3
    
    @pytest_asyncio.mark.asyncio
    async def test_portfolio_rebalancing_workflow(self, trading_system):
        """Test portfolio rebalancing workflow"""
        await trading_system.start()
        
        # Setup initial portfolio
        initial_portfolio = {
            'total_value': 1000000,
            'cash': 100000,
            'positions': {
                'AAPL': {'quantity': 1000, 'market_value': 150000},
                'GOOGL': {'quantity': 200, 'market_value': 500000},
                'MSFT': {'quantity': 500, 'market_value': 175000}
            }
        }
        
        # Define target allocation
        target_allocation = {
            'AAPL': 0.20,  # Reduce from ~17% to 20%
            'GOOGL': 0.40,  # Reduce from ~56% to 40%
            'MSFT': 0.25,   # Increase from ~19% to 25%
            'AMZN': 0.15    # New position
        }
        
        # Generate rebalancing orders
        rebalancing_orders = await trading_system.generate_rebalancing_orders(
            initial_portfolio, target_allocation
        )
        
        assert isinstance(rebalancing_orders, list)
        assert len(rebalancing_orders) > 0
        
        # Execute rebalancing orders
        executed_orders = []
        for order in rebalancing_orders:
            # Risk check each order
            risk_approved = await trading_system.assess_trade_risk(order)
            
            if risk_approved:
                result = await trading_system.execute_trade(order)
                if result:
                    executed_orders.append(result)
        
        # Verify rebalancing execution
        assert len(executed_orders) >= 0
    
    @pytest_asyncio.mark.asyncio
    async def test_risk_limit_breach_workflow(self, trading_system):
        """Test workflow when risk limits are breached"""
        await trading_system.start()
        
        # Simulate portfolio with risk limit breach
        risky_portfolio = {
            'total_value': 1000000,
            'cash': 50000,
            'positions': {
                'AAPL': {'quantity': 2000, 'market_value': 300000, 'unrealized_pnl': -50000},
                'GOOGL': {'quantity': 300, 'market_value': 750000, 'unrealized_pnl': -100000},
                # High concentration and losses
            }
        }
        
        # Assess risk
        risk_assessment = await trading_system.assess_portfolio_risk(risky_portfolio)
        
        # Should detect violations
        assert 'violations' in risk_assessment
        violations = risk_assessment['violations']
        assert len(violations) > 0
        
        # Generate emergency actions
        emergency_actions = await trading_system.get_emergency_actions(risky_portfolio)
        
        assert isinstance(emergency_actions, list)
        assert len(emergency_actions) > 0
        
        # Should recommend position reduction
        action_types = [action['type'] for action in emergency_actions]
        assert any(action_type in ['reduce_position', 'liquidate'] for action_type in action_types)
    
    @pytest_asyncio.mark.asyncio
    async def test_strategy_performance_monitoring(self, trading_system):
        """Test strategy performance monitoring workflow"""
        await trading_system.start()
        
        # Mock strategy with performance history
        strategy_id = 'momentum_001'
        
        # Simulate strategy trades over time
        trades = []
        for i in range(20):
            trade = {
                'timestamp': datetime.now() - timedelta(days=i),
                'symbol': 'AAPL',
                'side': 'buy' if i % 2 == 0 else 'sell',
                'quantity': 100,
                'price': 150 + np.random.normal(0, 5),
                'pnl': np.random.normal(100, 200),  # Random P&L
                'strategy_id': strategy_id
            }
            trades.append(trade)
        
        # Record strategy performance
        for trade in trades:
            await trading_system.record_strategy_trade(trade)
        
        # Calculate performance metrics
        performance = await trading_system.calculate_strategy_performance(strategy_id)
        
        assert performance is not None
        assert 'total_return' in performance
        assert 'sharpe_ratio' in performance
        assert 'max_drawdown' in performance
        assert 'win_rate' in performance
        
        # Check if strategy should be paused due to poor performance
        if performance['total_return'] < -0.1:  # 10% loss
            await trading_system.pause_strategy(strategy_id)
            
            strategy_status = await trading_system.get_strategy_status(strategy_id)
            assert strategy_status['status'] == 'paused'
    
    @pytest_asyncio.mark.asyncio
    async def test_market_volatility_response(self, trading_system):
        """Test system response to high market volatility"""
        await trading_system.start()
        
        # Simulate high volatility market data
        volatile_data = []
        base_price = 150
        
        for i in range(50):
            # High volatility price movements
            price_change = np.random.normal(0, 0.05)  # 5% volatility
            new_price = base_price * (1 + price_change)
            base_price = new_price
            
            data_point = {
                'symbol': 'AAPL',
                'timestamp': datetime.now() + timedelta(minutes=i),
                'price': new_price,
                'volume': np.random.randint(500000, 2000000),  # High volume
                'volatility': abs(price_change)
            }
            volatile_data.append(data_point)
        
        # Process volatile market data
        volatility_alerts = []
        
        for data_point in volatile_data:
            await trading_system.process_market_data(data_point)
            
            # Check for volatility alerts
            if data_point['volatility'] > 0.03:  # 3% move
                alert = await trading_system.check_volatility_alert(data_point)
                if alert:
                    volatility_alerts.append(alert)
        
        # System should detect high volatility
        assert len(volatility_alerts) > 0
        
        # Risk limits should be tightened
        current_limits = await trading_system.get_current_risk_limits()
        assert current_limits['volatility_adjusted'] is True
    
    @pytest_asyncio.mark.asyncio
    async def test_system_recovery_workflow(self, trading_system):
        """Test system recovery from failures"""
        await trading_system.start()
        
        # Simulate component failure
        with patch.object(trading_system.database_coordinator, 'execute') as mock_db:
            mock_db.side_effect = Exception("Database connection lost")
            
            # System should detect failure
            health_check = await trading_system.run_health_check()
            assert health_check['status'] != 'healthy'
            assert 'database' in health_check['failed_components']
            
            # System should attempt recovery
            recovery_result = await trading_system.attempt_recovery()
            
            # Mock successful recovery
            mock_db.side_effect = None
            mock_db.return_value = {'rows_affected': 1}
            
            # Verify recovery
            post_recovery_health = await trading_system.run_health_check()
            # May still show issues due to mocking, but recovery should be attempted
    
    @pytest_asyncio.mark.asyncio
    async def test_real_time_monitoring_workflow(self, trading_system):
        """Test real-time monitoring and alerting"""
        await trading_system.start()
        
        # Setup monitoring
        await trading_system.start_monitoring()
        
        # Simulate various system events
        events = [
            {'type': 'trade_executed', 'symbol': 'AAPL', 'quantity': 100},
            {'type': 'risk_limit_approached', 'limit_type': 'position_size', 'current': 0.09},
            {'type': 'strategy_signal', 'strategy': 'momentum_001', 'action': 'buy'},
            {'type': 'market_volatility', 'symbol': 'GOOGL', 'volatility': 0.04}
        ]
        
        alerts_generated = []
        
        for event in events:
            alert = await trading_system.process_monitoring_event(event)
            if alert:
                alerts_generated.append(alert)
        
        # Should generate appropriate alerts
        assert len(alerts_generated) >= 0
        
        # Stop monitoring
        await trading_system.stop_monitoring()


class TestSystemIntegration:
    """Test integration between major system components"""
    
    @pytest.fixture
    async def system_components(self):
        """Setup individual system components for integration testing"""
        config = {
            'testing': True,
            'agents': {'max_agents': 5},
            'strategies': {'max_strategies': 10},
            'risk': {'max_position_size': 0.1},
            'execution': {'paper_trading': True}
        }
        
        with patch('asyncpg.connect'), \
             patch('redis.Redis'), \
             patch('ollama.AsyncClient'):
            
            from agents.agent_manager import AgentManager
            from strategies.strategy_manager import StrategyManager
            from risk.risk_manager import RiskManager
            from execution.execution_engine import ExecutionEngine
            from portfolio.portfolio_manager import PortfolioManager
            
            components = {
                'agent_manager': AgentManager(config),
                'strategy_manager': StrategyManager(config),
                'risk_manager': RiskManager(config),
                'execution_engine': ExecutionEngine(config),
                'portfolio_manager': PortfolioManager(config)
            }
            
            # Initialize all components
            for component in components.values():
                await component.initialize()
            
            yield components
            
            # Cleanup
            for component in components.values():
                if hasattr(component, 'stop'):
                    await component.stop()
    
    @pytest_asyncio.mark.asyncio
    async def test_agent_strategy_integration(self, system_components):
        """Test integration between agents and strategies"""
        agent_manager = system_components['agent_manager']
        strategy_manager = system_components['strategy_manager']
        
        # Create mock agent and strategy
        with patch('ollama.AsyncClient') as mock_client:
            mock_client.return_value.generate.return_value = {
                'response': '{"action": "buy", "confidence": 0.8}',
                'done': True
            }
            
            from agents.analyst_agent import AnalystAgent
            from strategies.momentum_strategy import MomentumStrategy
            
            agent = AnalystAgent('analyst_001', 'Test Analyst', {'agents': {}})
            strategy = MomentumStrategy('momentum_001', 'Test Momentum', {'strategies': {}})
            
            await agent_manager.register_agent(agent)
            await strategy_manager.register_strategy(strategy)
            
            await agent.start()
            await strategy.start()
            
            # Test agent-strategy communication
            market_data = {
                'AAPL': {'price': 150, 'volume': 1000000}
            }
            
            # Agent analyzes market data
            analysis = await agent.analyze_market_data(market_data)
            
            # Strategy uses analysis to generate signal
            signal = await strategy.generate_signal(market_data)
            
            # Verify integration
            assert analysis is not None
            assert signal is not None
    
    @pytest_asyncio.mark.asyncio
    async def test_strategy_risk_integration(self, system_components):
        """Test integration between strategies and risk management"""
        strategy_manager = system_components['strategy_manager']
        risk_manager = system_components['risk_manager']
        
        # Mock strategy signal
        signal = {
            'symbol': 'AAPL',
            'action': 'buy',
            'quantity': 1000,  # Large position
            'confidence': 0.9,
            'strategy_id': 'test_strategy'
        }
        
        # Mock portfolio
        portfolio = {
            'total_value': 1000000,
            'positions': {
                'AAPL': {'quantity': 500, 'market_value': 75000}
            }
        }
        
        # Risk assessment of strategy signal
        risk_assessment = await risk_manager.assess_trade_risk(signal, portfolio)
        
        assert risk_assessment is not None
        assert 'approved' in risk_assessment
        
        # Large position should be rejected or reduced
        if signal['quantity'] * 150 > portfolio['total_value'] * 0.1:  # Exceeds 10% limit
            assert risk_assessment['approved'] is False or risk_assessment['adjusted_quantity'] < signal['quantity']
    
    @pytest_asyncio.mark.asyncio
    async def test_risk_execution_integration(self, system_components):
        """Test integration between risk management and execution"""
        risk_manager = system_components['risk_manager']
        execution_engine = system_components['execution_engine']
        
        # Mock approved trade
        approved_trade = {
            'symbol': 'AAPL',
            'side': 'buy',
            'quantity': 100,
            'order_type': 'market',
            'risk_approved': True
        }
        
        # Execute trade
        execution_result = await execution_engine.execute_order(approved_trade)
        
        assert execution_result is not None
        assert 'order_id' in execution_result
        
        # Risk manager should monitor execution
        post_trade_assessment = await risk_manager.assess_post_trade_risk(
            approved_trade, execution_result
        )
        
        assert post_trade_assessment is not None
    
    @pytest_asyncio.mark.asyncio
    async def test_execution_portfolio_integration(self, system_components):
        """Test integration between execution and portfolio management"""
        execution_engine = system_components['execution_engine']
        portfolio_manager = system_components['portfolio_manager']
        
        # Mock trade execution
        trade = {
            'symbol': 'AAPL',
            'side': 'buy',
            'quantity': 100,
            'fill_price': 150.0,
            'timestamp': datetime.now()
        }
        
        # Update portfolio with trade
        await portfolio_manager.update_position(trade)
        
        # Verify portfolio update
        portfolio = await portfolio_manager.get_portfolio('default')
        
        assert portfolio is not None
        assert 'AAPL' in portfolio['positions']
        assert portfolio['positions']['AAPL']['quantity'] >= 100
    
    @pytest_asyncio.mark.asyncio
    async def test_full_component_integration(self, system_components):
        """Test integration across all major components"""
        # Get all components
        agent_manager = system_components['agent_manager']
        strategy_manager = system_components['strategy_manager']
        risk_manager = system_components['risk_manager']
        execution_engine = system_components['execution_engine']
        portfolio_manager = system_components['portfolio_manager']
        
        # Simulate complete workflow
        market_data = {
            'AAPL': {
                'price': 150.0,
                'volume': 1000000,
                'timestamp': datetime.now()
            }
        }
        
        # 1. Agent analysis (mocked)
        analysis = {
            'trend': 'bullish',
            'confidence': 0.85,
            'recommendation': 'buy'
        }
        
        # 2. Strategy signal generation (mocked)
        signal = {
            'symbol': 'AAPL',
            'action': 'buy',
            'quantity': 100,
            'confidence': 0.8,
            'strategy_id': 'test_strategy'
        }
        
        # 3. Risk assessment
        portfolio = await portfolio_manager.get_portfolio('default')
        risk_assessment = await risk_manager.assess_trade_risk(signal, portfolio)
        
        if risk_assessment.get('approved', False):
            # 4. Order execution
            execution_result = await execution_engine.execute_order(signal)
            
            if execution_result:
                # 5. Portfolio update
                await portfolio_manager.update_position(execution_result)
                
                # 6. Post-trade risk assessment
                await risk_manager.assess_post_trade_risk(signal, execution_result)
        
        # Verify workflow completion
        # (Results may vary based on mocking, but workflow should complete without errors)
        assert True  # Workflow completed successfully


class TestErrorHandlingAndRecovery:
    """Test error handling and recovery mechanisms"""
    
    @pytest.fixture
    async def trading_system_with_failures(self):
        """Setup trading system with simulated failures"""
        config = {
            'testing': True,
            'database': {'postgres': {'host': 'localhost'}},
            'agents': {'max_agents': 5}
        }
        
        with patch('asyncpg.connect'), \
             patch('redis.Redis'), \
             patch('ollama.AsyncClient'):
            
            system = TradingSystem(config=config)
            await system.initialize()
            yield system
            await system.stop()
    
    @pytest_asyncio.mark.asyncio
    async def test_database_failure_recovery(self, trading_system_with_failures):
        """Test recovery from database failures"""
        system = trading_system_with_failures
        
        # Simulate database failure
        with patch.object(system.database_coordinator, 'execute') as mock_execute:
            mock_execute.side_effect = Exception("Connection lost")
            
            # System should handle database errors gracefully
            try:
                await system.process_market_data({
                    'symbol': 'AAPL',
                    'price': 150.0,
                    'timestamp': datetime.now()
                })
            except Exception:
                pass  # Expected to handle gracefully
            
            # System should attempt reconnection
            recovery_attempted = await system.attempt_database_recovery()
            assert recovery_attempted is not None
    
    @pytest_asyncio.mark.asyncio
    async def test_agent_failure_recovery(self, trading_system_with_failures):
        """Test recovery from agent failures"""
        system = trading_system_with_failures
        await system.start()
        
        # Simulate agent failure
        with patch.object(system.agent_manager, 'send_message') as mock_send:
            mock_send.side_effect = Exception("Agent not responding")
            
            # System should detect agent failure
            health_check = await system.check_agent_health('analyst_001')
            
            # Should attempt agent recovery
            recovery_result = await system.recover_failed_agent('analyst_001')
            assert recovery_result is not None
    
    @pytest_asyncio.mark.asyncio
    async def test_execution_failure_handling(self, trading_system_with_failures):
        """Test handling of execution failures"""
        system = trading_system_with_failures
        await system.start()
        
        # Mock execution failure
        with patch.object(system.execution_engine, 'execute_order') as mock_execute:
            mock_execute.side_effect = Exception("Order rejected")
            
            signal = {
                'symbol': 'AAPL',
                'action': 'buy',
                'quantity': 100
            }
            
            # System should handle execution failure gracefully
            result = await system.execute_trade(signal)
            
            # Should log failure and not crash
            assert result is None or 'error' in result
    
    @pytest_asyncio.mark.asyncio
    async def test_system_wide_recovery(self, trading_system_with_failures):
        """Test system-wide recovery procedures"""
        system = trading_system_with_failures
        
        # Simulate multiple component failures
        failures = {
            'database': Exception("DB connection lost"),
            'agents': Exception("Agent communication failed"),
            'execution': Exception("Execution engine down")
        }
        
        # System should detect multiple failures
        health_status = await system.run_comprehensive_health_check()
        
        # Should initiate emergency procedures
        if health_status['critical_failures'] > 2:
            emergency_result = await system.initiate_emergency_procedures()
            assert emergency_result is not None
            assert 'actions_taken' in emergency_result

"""
Agent Memory - Memory management system for trading agents
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any
from collections import deque, defaultdict
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)


@dataclass
class MemoryItem:
    """Individual memory item"""
    id: str
    type: str  # 'analysis', 'decision', 'experience', 'observation'
    content: Dict[str, Any]
    timestamp: float
    importance: float = 0.5  # 0.0 to 1.0
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []


class AgentMemory:
    """
    Memory management system for agents.
    Handles short-term, long-term, and working memory with importance-based retention.
    """
    
    def __init__(self, agent_id: str, config: Dict[str, Any] = None):
        self.agent_id = agent_id
        self.config = config or {}
        
        # Memory stores
        self.working_memory: Dict[str, MemoryItem] = {}  # Current context
        self.short_term_memory: deque = deque(maxlen=self.config.get('short_term_size', 100))
        self.long_term_memory: Dict[str, MemoryItem] = {}  # Important memories
        
        # Indexes for efficient retrieval
        self.type_index: Dict[str, List[str]] = defaultdict(list)
        self.tag_index: Dict[str, List[str]] = defaultdict(list)
        self.time_index: List[tuple] = []  # (timestamp, memory_id)
        
        # Configuration
        self.max_working_memory = self.config.get('max_working_memory', 20)
        self.max_long_term_memory = self.config.get('max_long_term_memory', 1000)
        self.importance_threshold = self.config.get('importance_threshold', 0.7)
        self.cleanup_interval = self.config.get('cleanup_interval', 3600)  # 1 hour
        
        # State
        self.initialized = False
        self.cleanup_task: Optional[asyncio.Task] = None
        
    async def initialize(self):
        """Initialize the memory system"""
        if self.initialized:
            return
            
        logger.debug(f"Initializing memory for agent {self.agent_id}")
        
        # Start cleanup task
        self.cleanup_task = asyncio.create_task(self._periodic_cleanup())
        
        self.initialized = True
        logger.debug(f"✓ Memory initialized for agent {self.agent_id}")
        
    async def stop(self):
        """Stop the memory system"""
        if self.cleanup_task and not self.cleanup_task.done():
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
                
    async def store_analysis(self, analysis: Dict[str, Any]) -> str:
        """Store an analysis result"""
        memory_id = f"analysis_{int(time.time() * 1000)}"
        
        memory_item = MemoryItem(
            id=memory_id,
            type='analysis',
            content=analysis,
            timestamp=time.time(),
            importance=self._calculate_importance(analysis, 'analysis'),
            tags=self._extract_tags(analysis)
        )
        
        await self._store_memory_item(memory_item)
        return memory_id
        
    async def store_decision(self, decision: Dict[str, Any]) -> str:
        """Store a decision"""
        memory_id = f"decision_{int(time.time() * 1000)}"
        
        memory_item = MemoryItem(
            id=memory_id,
            type='decision',
            content=decision,
            timestamp=time.time(),
            importance=self._calculate_importance(decision, 'decision'),
            tags=self._extract_tags(decision)
        )
        
        await self._store_memory_item(memory_item)
        return memory_id
        
    async def store_experience(self, experience: Dict[str, Any]) -> str:
        """Store an experience or learning event"""
        memory_id = f"experience_{int(time.time() * 1000)}"
        
        memory_item = MemoryItem(
            id=memory_id,
            type='experience',
            content=experience,
            timestamp=time.time(),
            importance=self._calculate_importance(experience, 'experience'),
            tags=self._extract_tags(experience)
        )
        
        await self._store_memory_item(memory_item)
        return memory_id
        
    async def store_observation(self, observation: Dict[str, Any]) -> str:
        """Store an observation"""
        memory_id = f"observation_{int(time.time() * 1000)}"
        
        memory_item = MemoryItem(
            id=memory_id,
            type='observation',
            content=observation,
            timestamp=time.time(),
            importance=self._calculate_importance(observation, 'observation'),
            tags=self._extract_tags(observation)
        )
        
        await self._store_memory_item(memory_item)
        return memory_id
        
    async def _store_memory_item(self, memory_item: MemoryItem):
        """Store a memory item in appropriate memory stores"""
        # Always add to short-term memory
        self.short_term_memory.append(memory_item)
        
        # Add to working memory if space available
        if len(self.working_memory) < self.max_working_memory:
            self.working_memory[memory_item.id] = memory_item
        else:
            # Replace least important item in working memory
            least_important_id = min(
                self.working_memory.keys(),
                key=lambda x: self.working_memory[x].importance
            )
            if memory_item.importance > self.working_memory[least_important_id].importance:
                del self.working_memory[least_important_id]
                self.working_memory[memory_item.id] = memory_item
                
        # Add to long-term memory if important enough
        if memory_item.importance >= self.importance_threshold:
            if len(self.long_term_memory) < self.max_long_term_memory:
                self.long_term_memory[memory_item.id] = memory_item
            else:
                # Replace least important item in long-term memory
                least_important_id = min(
                    self.long_term_memory.keys(),
                    key=lambda x: self.long_term_memory[x].importance
                )
                if memory_item.importance > self.long_term_memory[least_important_id].importance:
                    self._remove_from_indexes(least_important_id)
                    del self.long_term_memory[least_important_id]
                    self.long_term_memory[memory_item.id] = memory_item
                    
        # Update indexes
        self._update_indexes(memory_item)
        
    def _update_indexes(self, memory_item: MemoryItem):
        """Update memory indexes"""
        # Type index
        self.type_index[memory_item.type].append(memory_item.id)
        
        # Tag index
        for tag in memory_item.tags:
            self.tag_index[tag].append(memory_item.id)
            
        # Time index
        self.time_index.append((memory_item.timestamp, memory_item.id))
        self.time_index.sort()  # Keep sorted by timestamp
        
    def _remove_from_indexes(self, memory_id: str):
        """Remove memory item from indexes"""
        # Find and remove from type index
        for type_list in self.type_index.values():
            if memory_id in type_list:
                type_list.remove(memory_id)
                
        # Find and remove from tag index
        for tag_list in self.tag_index.values():
            if memory_id in tag_list:
                tag_list.remove(memory_id)
                
        # Remove from time index
        self.time_index = [(ts, mid) for ts, mid in self.time_index if mid != memory_id]
        
    def _calculate_importance(self, content: Dict[str, Any], memory_type: str) -> float:
        """Calculate importance score for a memory item"""
        base_importance = 0.5
        
        # Type-based importance
        type_weights = {
            'decision': 0.8,
            'analysis': 0.6,
            'experience': 0.7,
            'observation': 0.4
        }
        base_importance = type_weights.get(memory_type, 0.5)
        
        # Content-based importance adjustments
        if 'confidence' in content:
            confidence = content['confidence']
            base_importance += (confidence - 0.5) * 0.2
            
        if 'impact' in content:
            impact = content['impact']
            base_importance += impact * 0.3
            
        if 'error' in content or 'failure' in content:
            base_importance += 0.2  # Failures are important to remember
            
        # Clamp to valid range
        return max(0.0, min(1.0, base_importance))
        
    def _extract_tags(self, content: Dict[str, Any]) -> List[str]:
        """Extract tags from content for indexing"""
        tags = []
        
        # Extract common tags
        if 'symbol' in content:
            tags.append(f"symbol:{content['symbol']}")
            
        if 'strategy' in content:
            tags.append(f"strategy:{content['strategy']}")
            
        if 'market_condition' in content:
            tags.append(f"market:{content['market_condition']}")
            
        if 'risk_level' in content:
            tags.append(f"risk:{content['risk_level']}")
            
        # Extract tags from nested content
        if isinstance(content, dict):
            for key, value in content.items():
                if isinstance(value, str) and len(value) < 50:
                    tags.append(f"{key}:{value}")
                    
        return tags[:10]  # Limit number of tags
        
    async def retrieve_by_type(self, memory_type: str, limit: int = 10) -> List[MemoryItem]:
        """Retrieve memories by type"""
        memory_ids = self.type_index.get(memory_type, [])
        memories = []
        
        for memory_id in memory_ids[-limit:]:  # Get most recent
            memory_item = self._get_memory_item(memory_id)
            if memory_item:
                memories.append(memory_item)
                
        return memories
        
    async def retrieve_by_tag(self, tag: str, limit: int = 10) -> List[MemoryItem]:
        """Retrieve memories by tag"""
        memory_ids = self.tag_index.get(tag, [])
        memories = []
        
        for memory_id in memory_ids[-limit:]:  # Get most recent
            memory_item = self._get_memory_item(memory_id)
            if memory_item:
                memories.append(memory_item)
                
        return memories
        
    async def retrieve_recent(self, hours: int = 24, limit: int = 20) -> List[MemoryItem]:
        """Retrieve recent memories"""
        cutoff_time = time.time() - (hours * 3600)
        recent_memories = []
        
        for timestamp, memory_id in reversed(self.time_index):
            if timestamp < cutoff_time:
                break
                
            memory_item = self._get_memory_item(memory_id)
            if memory_item:
                recent_memories.append(memory_item)
                
            if len(recent_memories) >= limit:
                break
                
        return recent_memories
        
    async def retrieve_important(self, threshold: float = 0.7, limit: int = 20) -> List[MemoryItem]:
        """Retrieve important memories"""
        important_memories = []
        
        # Check long-term memory first
        for memory_item in self.long_term_memory.values():
            if memory_item.importance >= threshold:
                important_memories.append(memory_item)
                
        # Sort by importance and timestamp
        important_memories.sort(key=lambda x: (x.importance, x.timestamp), reverse=True)
        
        return important_memories[:limit]
        
    def _get_memory_item(self, memory_id: str) -> Optional[MemoryItem]:
        """Get memory item from any store"""
        # Check working memory first
        if memory_id in self.working_memory:
            return self.working_memory[memory_id]
            
        # Check long-term memory
        if memory_id in self.long_term_memory:
            return self.long_term_memory[memory_id]
            
        # Check short-term memory
        for memory_item in self.short_term_memory:
            if memory_item.id == memory_id:
                return memory_item
                
        return None
        
    async def get_working_memory(self) -> List[MemoryItem]:
        """Get current working memory contents"""
        return list(self.working_memory.values())
        
    async def clear_working_memory(self):
        """Clear working memory"""
        self.working_memory.clear()
        
    async def get_metrics(self) -> Dict[str, Any]:
        """Get memory system metrics"""
        return {
            'working_memory_size': len(self.working_memory),
            'short_term_memory_size': len(self.short_term_memory),
            'long_term_memory_size': len(self.long_term_memory),
            'total_memories': len(self.working_memory) + len(self.short_term_memory) + len(self.long_term_memory),
            'memory_types': dict(self.type_index),
            'memory_tags': len(self.tag_index),
            'avg_importance': self._calculate_average_importance()
        }
        
    def _calculate_average_importance(self) -> float:
        """Calculate average importance of all memories"""
        all_memories = list(self.working_memory.values()) + list(self.long_term_memory.values())
        all_memories.extend(list(self.short_term_memory))
        
        if not all_memories:
            return 0.0
            
        total_importance = sum(memory.importance for memory in all_memories)
        return total_importance / len(all_memories)
        
    async def _periodic_cleanup(self):
        """Periodic cleanup of old memories"""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_memories()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in memory cleanup: {e}")
                
    async def _cleanup_memories(self):
        """Clean up old, unimportant memories"""
        cutoff_time = time.time() - (7 * 24 * 3600)  # 7 days
        
        # Clean up time index
        self.time_index = [(ts, mid) for ts, mid in self.time_index if ts >= cutoff_time]
        
        # Clean up type and tag indexes
        for type_list in self.type_index.values():
            type_list[:] = [mid for mid in type_list if self._get_memory_item(mid) is not None]
            
        for tag_list in self.tag_index.values():
            tag_list[:] = [mid for mid in tag_list if self._get_memory_item(mid) is not None]

"""
Reinforcement Learning - RL-based strategy optimization and adaptive trading
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
import random
from collections import deque
import json

logger = logging.getLogger(__name__)


class TradingEnvironment:
    """
    Trading environment for reinforcement learning.
    Simulates market conditions and trading actions.
    """
    
    def __init__(self, data: pd.DataFrame, initial_balance: float = 100000):
        self.data = data.reset_index(drop=True)
        self.initial_balance = initial_balance
        self.reset()
        
    def reset(self):
        """Reset environment to initial state"""
        self.current_step = 0
        self.balance = self.initial_balance
        self.position = 0  # 0: no position, 1: long, -1: short
        self.position_size = 0
        self.entry_price = 0
        self.total_reward = 0
        self.trade_history = []
        
        return self._get_state()
        
    def _get_state(self) -> np.ndarray:
        """Get current state representation"""
        if self.current_step >= len(self.data):
            return np.zeros(10)  # Default state
            
        current_data = self.data.iloc[self.current_step]
        
        # Technical indicators as state
        lookback = min(20, self.current_step)
        if lookback > 0:
            recent_data = self.data.iloc[self.current_step-lookback:self.current_step+1]
            
            # Price features
            price_change = (current_data['close'] - recent_data['close'].iloc[0]) / recent_data['close'].iloc[0]
            volatility = recent_data['close'].pct_change().std()
            
            # Moving averages
            sma_5 = recent_data['close'].tail(5).mean()
            sma_20 = recent_data['close'].tail(min(20, lookback)).mean()
            
            # RSI-like indicator
            price_changes = recent_data['close'].pct_change().dropna()
            gains = price_changes.where(price_changes > 0, 0).mean()
            losses = -price_changes.where(price_changes < 0, 0).mean()
            rsi = gains / (gains + losses) if (gains + losses) > 0 else 0.5
            
            state = np.array([
                price_change,
                volatility,
                (current_data['close'] - sma_5) / sma_5 if sma_5 > 0 else 0,
                (current_data['close'] - sma_20) / sma_20 if sma_20 > 0 else 0,
                rsi,
                self.position,
                self.position_size / self.balance if self.balance > 0 else 0,
                (current_data['close'] - self.entry_price) / self.entry_price if self.entry_price > 0 else 0,
                current_data['volume'] / recent_data['volume'].mean() if recent_data['volume'].mean() > 0 else 1,
                self.balance / self.initial_balance
            ])
        else:
            state = np.array([0, 0, 0, 0, 0.5, 0, 0, 0, 1, 1])
            
        return state
        
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict]:
        """Execute action and return next state, reward, done, info"""
        if self.current_step >= len(self.data) - 1:
            return self._get_state(), 0, True, {}
            
        current_price = self.data.iloc[self.current_step]['close']
        next_price = self.data.iloc[self.current_step + 1]['close']
        
        reward = 0
        info = {}
        
        # Actions: 0=hold, 1=buy, 2=sell
        if action == 1 and self.position <= 0:  # Buy
            if self.position < 0:  # Close short position
                profit = (self.entry_price - current_price) * abs(self.position_size)
                self.balance += profit
                reward += profit / self.initial_balance
                
            # Open long position
            self.position = 1
            self.position_size = self.balance * 0.95  # Use 95% of balance
            self.entry_price = current_price
            info['action'] = 'buy'
            
        elif action == 2 and self.position >= 0:  # Sell
            if self.position > 0:  # Close long position
                profit = (current_price - self.entry_price) * self.position_size / self.entry_price
                self.balance += profit
                reward += profit / self.initial_balance
                
            # Open short position
            self.position = -1
            self.position_size = self.balance * 0.95
            self.entry_price = current_price
            info['action'] = 'sell'
            
        # Calculate unrealized P&L for current position
        if self.position != 0:
            unrealized_pnl = 0
            if self.position > 0:  # Long position
                unrealized_pnl = (next_price - self.entry_price) * self.position_size / self.entry_price
            else:  # Short position
                unrealized_pnl = (self.entry_price - next_price) * self.position_size / self.entry_price
                
            reward += unrealized_pnl / self.initial_balance * 0.1  # Small reward for unrealized gains
            
        # Penalty for excessive trading
        if 'action' in info:
            reward -= 0.001  # Small transaction cost
            
        self.current_step += 1
        self.total_reward += reward
        
        done = self.current_step >= len(self.data) - 1
        
        return self._get_state(), reward, done, info


class DQNAgent:
    """
    Deep Q-Network agent for trading strategy optimization.
    Simplified implementation for demonstration.
    """
    
    def __init__(self, state_size: int, action_size: int, learning_rate: float = 0.001):
        self.state_size = state_size
        self.action_size = action_size
        self.learning_rate = learning_rate
        
        # Hyperparameters
        self.epsilon = 1.0  # Exploration rate
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.gamma = 0.95  # Discount factor
        
        # Experience replay
        self.memory = deque(maxlen=10000)
        self.batch_size = 32
        
        # Simple neural network weights (simplified for demo)
        self.weights = {
            'layer1': np.random.randn(state_size, 64) * 0.1,
            'bias1': np.zeros(64),
            'layer2': np.random.randn(64, 32) * 0.1,
            'bias2': np.zeros(32),
            'output': np.random.randn(32, action_size) * 0.1,
            'bias_out': np.zeros(action_size)
        }
        
    def _forward(self, state: np.ndarray) -> np.ndarray:
        """Forward pass through network"""
        # Layer 1
        z1 = np.dot(state, self.weights['layer1']) + self.weights['bias1']
        a1 = np.maximum(0, z1)  # ReLU activation
        
        # Layer 2
        z2 = np.dot(a1, self.weights['layer2']) + self.weights['bias2']
        a2 = np.maximum(0, z2)  # ReLU activation
        
        # Output layer
        output = np.dot(a2, self.weights['output']) + self.weights['bias_out']
        
        return output
        
    def act(self, state: np.ndarray) -> int:
        """Choose action using epsilon-greedy policy"""
        if np.random.random() <= self.epsilon:
            return random.randrange(self.action_size)
            
        q_values = self._forward(state.reshape(1, -1))[0]
        return np.argmax(q_values)
        
    def remember(self, state: np.ndarray, action: int, reward: float, 
                next_state: np.ndarray, done: bool):
        """Store experience in replay buffer"""
        self.memory.append((state, action, reward, next_state, done))
        
    def replay(self):
        """Train the model on a batch of experiences"""
        if len(self.memory) < self.batch_size:
            return
            
        # Sample batch from memory
        batch = random.sample(self.memory, self.batch_size)
        
        # Simple gradient update (simplified for demo)
        total_loss = 0
        
        for state, action, reward, next_state, done in batch:
            target = reward
            if not done:
                next_q_values = self._forward(next_state.reshape(1, -1))[0]
                target += self.gamma * np.max(next_q_values)
                
            current_q_values = self._forward(state.reshape(1, -1))[0]
            target_q_values = current_q_values.copy()
            target_q_values[action] = target
            
            # Calculate loss (simplified)
            loss = np.mean((current_q_values - target_q_values) ** 2)
            total_loss += loss
            
        # Decay epsilon
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
            
        return total_loss / self.batch_size


class RLStrategyOptimizer:
    """
    Reinforcement Learning Strategy Optimizer.
    Uses RL to optimize trading strategies and parameters.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # RL components
        self.agents: Dict[str, DQNAgent] = {}
        self.environments: Dict[str, TradingEnvironment] = {}
        
        # Training history
        self.training_history: Dict[str, List[Dict[str, Any]]] = {}
        self.performance_metrics: Dict[str, Dict[str, float]] = {}
        
        # State
        self.initialized = False
        
    async def initialize(self):
        """Initialize RL strategy optimizer"""
        if self.initialized:
            return
            
        logger.info("Initializing RL Strategy Optimizer...")
        
        # Initialize RL parameters
        self.state_size = 10  # Size of state representation
        self.action_size = 3   # 0=hold, 1=buy, 2=sell
        
        self.initialized = True
        logger.info("✓ RL Strategy Optimizer initialized")
        
    async def create_agent(self, symbol: str, data: pd.DataFrame) -> str:
        """Create and train RL agent for a symbol"""
        try:
            # Create environment
            env = TradingEnvironment(data)
            self.environments[symbol] = env
            
            # Create agent
            agent = DQNAgent(self.state_size, self.action_size)
            self.agents[symbol] = agent
            
            # Initialize training history
            self.training_history[symbol] = []
            
            logger.info(f"✓ Created RL agent for {symbol}")
            return symbol
            
        except Exception as e:
            logger.error(f"Error creating RL agent for {symbol}: {e}")
            return None
            
    async def train_agent(self, symbol: str, episodes: int = 1000) -> Dict[str, Any]:
        """Train RL agent for a symbol"""
        if symbol not in self.agents or symbol not in self.environments:
            return {'success': False, 'error': 'Agent or environment not found'}
            
        try:
            agent = self.agents[symbol]
            env = self.environments[symbol]
            
            episode_rewards = []
            episode_losses = []
            
            for episode in range(episodes):
                state = env.reset()
                total_reward = 0
                steps = 0
                
                while True:
                    action = agent.act(state)
                    next_state, reward, done, info = env.step(action)
                    
                    agent.remember(state, action, reward, next_state, done)
                    state = next_state
                    total_reward += reward
                    steps += 1
                    
                    if done:
                        break
                        
                # Train agent
                loss = agent.replay()
                
                episode_rewards.append(total_reward)
                if loss is not None:
                    episode_losses.append(loss)
                    
                # Log progress
                if episode % 100 == 0:
                    avg_reward = np.mean(episode_rewards[-100:])
                    logger.info(f"Episode {episode}, Avg Reward: {avg_reward:.4f}, Epsilon: {agent.epsilon:.3f}")
                    
            # Calculate performance metrics
            final_balance = env.balance
            total_return = (final_balance - env.initial_balance) / env.initial_balance
            
            performance = {
                'total_return': total_return,
                'final_balance': final_balance,
                'total_episodes': episodes,
                'avg_reward': np.mean(episode_rewards),
                'max_reward': np.max(episode_rewards),
                'min_reward': np.min(episode_rewards),
                'final_epsilon': agent.epsilon
            }
            
            self.performance_metrics[symbol] = performance
            
            # Store training history
            self.training_history[symbol].append({
                'episode_rewards': episode_rewards,
                'episode_losses': episode_losses,
                'performance': performance,
                'timestamp': pd.Timestamp.now()
            })
            
            logger.info(f"✓ Trained RL agent for {symbol} - Return: {total_return:.2%}")
            
            return {
                'success': True,
                'performance': performance,
                'training_episodes': episodes
            }
            
        except Exception as e:
            logger.error(f"Error training RL agent for {symbol}: {e}")
            return {'success': False, 'error': str(e)}
            
    async def get_trading_action(self, symbol: str, current_state: np.ndarray) -> Dict[str, Any]:
        """Get trading action from trained RL agent"""
        if symbol not in self.agents:
            return {'success': False, 'error': 'Agent not found'}
            
        try:
            agent = self.agents[symbol]
            
            # Get action (disable exploration for production)
            original_epsilon = agent.epsilon
            agent.epsilon = 0  # No exploration
            
            action = agent.act(current_state)
            
            # Restore epsilon
            agent.epsilon = original_epsilon
            
            # Map action to trading signal
            action_map = {0: 'hold', 1: 'buy', 2: 'sell'}
            
            # Get Q-values for confidence
            q_values = agent._forward(current_state.reshape(1, -1))[0]
            confidence = np.max(q_values) - np.mean(q_values)
            
            return {
                'success': True,
                'action': action_map[action],
                'action_id': action,
                'confidence': confidence,
                'q_values': q_values.tolist()
            }
            
        except Exception as e:
            logger.error(f"Error getting trading action for {symbol}: {e}")
            return {'success': False, 'error': str(e)}
            
    async def optimize_strategy_parameters(self, symbol: str, parameter_ranges: Dict[str, Tuple[float, float]]) -> Dict[str, Any]:
        """Optimize strategy parameters using RL"""
        # This would implement parameter optimization using RL
        # For now, return a placeholder
        return {
            'success': True,
            'optimized_parameters': parameter_ranges,
            'optimization_method': 'rl_based'
        }
        
    async def get_performance_summary(self, symbol: str = None) -> Dict[str, Any]:
        """Get performance summary for RL agents"""
        if symbol:
            return self.performance_metrics.get(symbol, {})
        else:
            return self.performance_metrics.copy()
            
    async def save_agent(self, symbol: str, filepath: str):
        """Save trained RL agent"""
        if symbol not in self.agents:
            logger.error(f"Agent for {symbol} not found")
            return
            
        try:
            agent_data = {
                'weights': self.agents[symbol].weights,
                'epsilon': self.agents[symbol].epsilon,
                'performance': self.performance_metrics.get(symbol, {}),
                'training_history': self.training_history.get(symbol, [])
            }
            
            with open(filepath, 'w') as f:
                # Convert numpy arrays to lists for JSON serialization
                serializable_data = {}
                for key, value in agent_data.items():
                    if key == 'weights':
                        serializable_data[key] = {k: v.tolist() for k, v in value.items()}
                    else:
                        serializable_data[key] = value
                        
                json.dump(serializable_data, f, default=str)
                
            logger.info(f"✓ Saved RL agent for {symbol} to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving RL agent for {symbol}: {e}")
            
    async def load_agent(self, symbol: str, filepath: str):
        """Load trained RL agent"""
        try:
            with open(filepath, 'r') as f:
                agent_data = json.load(f)
                
            # Create new agent
            agent = DQNAgent(self.state_size, self.action_size)
            
            # Load weights
            for key, value in agent_data['weights'].items():
                agent.weights[key] = np.array(value)
                
            agent.epsilon = agent_data['epsilon']
            
            self.agents[symbol] = agent
            self.performance_metrics[symbol] = agent_data.get('performance', {})
            self.training_history[symbol] = agent_data.get('training_history', [])
            
            logger.info(f"✓ Loaded RL agent for {symbol} from {filepath}")
            
        except Exception as e:
            logger.error(f"Error loading RL agent for {symbol}: {e}")

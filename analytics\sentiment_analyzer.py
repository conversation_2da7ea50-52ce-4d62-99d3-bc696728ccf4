"""
Sentiment Analyzer - Market sentiment analysis and social media monitoring
"""

import asyncio
import logging
import time
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from collections import defaultdict, deque
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)


@dataclass
class SentimentData:
    """Sentiment data structure"""
    symbol: str
    sentiment_score: float  # -1.0 to 1.0
    confidence: float
    source: str
    timestamp: datetime
    raw_data: Dict[str, Any]
    keywords: List[str]
    volume: int  # Number of mentions/posts


class SentimentAnalyzer:
    """
    Market sentiment analyzer for social media and news sentiment.
    
    Features:
    - Social media sentiment analysis
    - News sentiment analysis
    - Keyword-based sentiment scoring
    - Sentiment trend analysis
    - Multi-source sentiment aggregation
    - Real-time sentiment monitoring
    - Sentiment-based alerts
    - Historical sentiment tracking
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.sentiment_config = config.get('sentiment_analyzer', {})
        
        # Sentiment data storage
        self.sentiment_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.current_sentiment: Dict[str, float] = {}
        self.sentiment_trends: Dict[str, Dict[str, float]] = {}
        
        # Sentiment sources
        self.sentiment_sources = {
            'social_media': self._analyze_social_media_sentiment,
            'news': self._analyze_news_sentiment,
            'market_data': self._analyze_market_sentiment,
            'technical': self._analyze_technical_sentiment
        }
        
        # Keyword dictionaries
        self.positive_keywords = {
            'bullish', 'buy', 'long', 'pump', 'moon', 'rocket', 'green', 'profit',
            'gain', 'rise', 'surge', 'breakout', 'support', 'strong', 'positive',
            'optimistic', 'confident', 'rally', 'uptrend', 'bull', 'growth'
        }
        
        self.negative_keywords = {
            'bearish', 'sell', 'short', 'dump', 'crash', 'red', 'loss',
            'fall', 'drop', 'breakdown', 'resistance', 'weak', 'negative',
            'pessimistic', 'worried', 'decline', 'downtrend', 'bear', 'fear'
        }
        
        self.neutral_keywords = {
            'hold', 'wait', 'sideways', 'range', 'consolidation', 'neutral',
            'uncertain', 'mixed', 'flat', 'stable', 'watching', 'monitoring'
        }
        
        # Sentiment configuration
        self.sentiment_window = self.sentiment_config.get('sentiment_window', 3600)  # 1 hour
        self.min_mentions = self.sentiment_config.get('min_mentions', 5)
        self.confidence_threshold = self.sentiment_config.get('confidence_threshold', 0.6)
        
        # Performance tracking
        self.sentiment_metrics = {
            'total_analyses': 0,
            'sources_analyzed': 0,
            'average_confidence': 0.0,
            'sentiment_alerts': 0
        }
        
        # State
        self.initialized = False
        self.running = False
    
    async def initialize(self) -> bool:
        """Initialize sentiment analyzer"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Sentiment Analyzer...")
            
            # Initialize sentiment tracking
            await self._initialize_sentiment_tracking()
            
            # Setup keyword processing
            await self._setup_keyword_processing()
            
            self.initialized = True
            logger.info("✓ Sentiment Analyzer initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Sentiment Analyzer: {e}")
            return False
    
    async def start(self) -> bool:
        """Start sentiment analyzer"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        self.running = True
        logger.info("✓ Sentiment Analyzer started")
        return True
    
    async def stop(self) -> bool:
        """Stop sentiment analyzer"""
        self.running = False
        logger.info("✓ Sentiment Analyzer stopped")
        return True
    
    async def analyze_sentiment(self, symbol: str, text_data: List[str] = None) -> Optional[Dict[str, Any]]:
        """Analyze sentiment for a symbol"""
        try:
            # If no text data provided, use simulated sentiment analysis
            if not text_data:
                return await self._simulate_sentiment_analysis(symbol)
            
            # Analyze sentiment from text data
            sentiment_results = []
            
            for text in text_data:
                sentiment_score = await self._analyze_text_sentiment(text)
                if sentiment_score is not None:
                    sentiment_results.append(sentiment_score)
            
            if not sentiment_results:
                return None
            
            # Aggregate sentiment scores
            avg_sentiment = sum(sentiment_results) / len(sentiment_results)
            confidence = min(0.9, len(sentiment_results) / 10.0)  # Higher confidence with more data
            
            # Create sentiment data
            sentiment_data = SentimentData(
                symbol=symbol,
                sentiment_score=avg_sentiment,
                confidence=confidence,
                source='text_analysis',
                timestamp=datetime.now(),
                raw_data={'text_count': len(text_data), 'scores': sentiment_results},
                keywords=self._extract_keywords(text_data),
                volume=len(text_data)
            )
            
            # Store sentiment data
            self.sentiment_history[symbol].append(sentiment_data)
            self.current_sentiment[symbol] = avg_sentiment
            
            # Update metrics
            self.sentiment_metrics['total_analyses'] += 1
            
            return {
                'symbol': symbol,
                'sentiment_score': avg_sentiment,
                'confidence': confidence,
                'sentiment_label': self._get_sentiment_label(avg_sentiment),
                'volume': len(text_data),
                'timestamp': datetime.now(),
                'trend': await self._calculate_sentiment_trend(symbol)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment for {symbol}: {e}")
            return None
    
    async def get_sentiment_score(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get current sentiment score for symbol"""
        try:
            if symbol not in self.current_sentiment:
                # Try to generate sentiment if not available
                return await self._simulate_sentiment_analysis(symbol)
            
            current_score = self.current_sentiment[symbol]
            
            # Get recent sentiment data
            recent_data = [
                data for data in self.sentiment_history[symbol]
                if datetime.now() - data.timestamp < timedelta(seconds=self.sentiment_window)
            ]
            
            if not recent_data:
                return None
            
            # Calculate aggregated metrics
            avg_confidence = sum(data.confidence for data in recent_data) / len(recent_data)
            total_volume = sum(data.volume for data in recent_data)
            
            # Calculate trend
            trend = await self._calculate_sentiment_trend(symbol)
            
            return {
                'symbol': symbol,
                'sentiment_score': current_score,
                'confidence': avg_confidence,
                'sentiment_label': self._get_sentiment_label(current_score),
                'volume': total_volume,
                'trend': trend,
                'data_points': len(recent_data),
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"Error getting sentiment score for {symbol}: {e}")
            return None
    
    async def get_sentiment_trends(self, symbol: str = None, hours: int = 24) -> Dict[str, Any]:
        """Get sentiment trends"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            if symbol:
                # Symbol-specific trends
                historical_data = [
                    data for data in self.sentiment_history[symbol]
                    if data.timestamp >= cutoff_time
                ]
                
                if not historical_data:
                    return {'symbol': symbol, 'no_data': True}
                
                # Calculate hourly sentiment averages
                hourly_sentiment = defaultdict(list)
                for data in historical_data:
                    hour = data.timestamp.replace(minute=0, second=0, microsecond=0)
                    hourly_sentiment[hour].append(data.sentiment_score)
                
                hourly_averages = {
                    hour.isoformat(): sum(scores) / len(scores)
                    for hour, scores in hourly_sentiment.items()
                }
                
                # Calculate overall trend
                if len(hourly_averages) >= 2:
                    scores = list(hourly_averages.values())
                    trend_direction = 'positive' if scores[-1] > scores[0] else 'negative' if scores[-1] < scores[0] else 'neutral'
                    trend_strength = abs(scores[-1] - scores[0])
                else:
                    trend_direction = 'neutral'
                    trend_strength = 0.0
                
                return {
                    'symbol': symbol,
                    'hourly_sentiment': hourly_averages,
                    'trend_direction': trend_direction,
                    'trend_strength': trend_strength,
                    'data_points': len(historical_data),
                    'time_range_hours': hours
                }
            else:
                # Overall sentiment trends
                all_symbols = list(self.sentiment_history.keys())
                symbol_trends = {}
                
                for sym in all_symbols:
                    trend = await self._calculate_sentiment_trend(sym)
                    if trend:
                        symbol_trends[sym] = trend
                
                return {
                    'symbol_trends': symbol_trends,
                    'total_symbols': len(all_symbols),
                    'time_range_hours': hours
                }
                
        except Exception as e:
            logger.error(f"Error getting sentiment trends: {e}")
            return {}
    
    async def get_sentiment_alerts(self, threshold: float = 0.7) -> List[Dict[str, Any]]:
        """Get sentiment-based alerts"""
        try:
            alerts = []
            
            for symbol, current_score in self.current_sentiment.items():
                # Check for extreme sentiment
                if abs(current_score) >= threshold:
                    trend = await self._calculate_sentiment_trend(symbol)
                    
                    alert = {
                        'symbol': symbol,
                        'alert_type': 'extreme_sentiment',
                        'sentiment_score': current_score,
                        'sentiment_label': self._get_sentiment_label(current_score),
                        'trend': trend,
                        'severity': 'high' if abs(current_score) >= 0.8 else 'medium',
                        'timestamp': datetime.now()
                    }
                    alerts.append(alert)
                
                # Check for rapid sentiment changes
                if trend and abs(trend.get('change_rate', 0.0)) >= 0.5:
                    alert = {
                        'symbol': symbol,
                        'alert_type': 'sentiment_shift',
                        'sentiment_score': current_score,
                        'trend_change': trend.get('change_rate', 0.0),
                        'severity': 'medium',
                        'timestamp': datetime.now()
                    }
                    alerts.append(alert)
            
            return alerts
            
        except Exception as e:
            logger.error(f"Error getting sentiment alerts: {e}")
            return []
    
    # Private methods
    
    async def _initialize_sentiment_tracking(self):
        """Initialize sentiment tracking"""
        try:
            # Initialize for configured symbols
            symbols = self.config.get('symbols', {})
            for symbol_group, symbol_list in symbols.items():
                for symbol in symbol_list:
                    self.current_sentiment[symbol] = 0.0
                    self.sentiment_trends[symbol] = {}
            
            logger.debug(f"Initialized sentiment tracking for {len(self.current_sentiment)} symbols")
            
        except Exception as e:
            logger.error(f"Error initializing sentiment tracking: {e}")
            raise
    
    async def _setup_keyword_processing(self):
        """Setup keyword processing"""
        try:
            # Compile keyword patterns for faster matching
            self.positive_pattern = re.compile(r'\b(' + '|'.join(self.positive_keywords) + r')\b', re.IGNORECASE)
            self.negative_pattern = re.compile(r'\b(' + '|'.join(self.negative_keywords) + r')\b', re.IGNORECASE)
            self.neutral_pattern = re.compile(r'\b(' + '|'.join(self.neutral_keywords) + r')\b', re.IGNORECASE)
            
            logger.debug("Keyword processing patterns compiled")
            
        except Exception as e:
            logger.error(f"Error setting up keyword processing: {e}")
            raise
    
    async def _simulate_sentiment_analysis(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Simulate sentiment analysis (for demo purposes)"""
        try:
            # Generate simulated sentiment based on symbol characteristics
            import random
            
            # Base sentiment with some randomness
            base_sentiment = random.uniform(-0.3, 0.3)
            
            # Add some symbol-specific bias (simplified)
            if 'BTC' in symbol.upper():
                base_sentiment += random.uniform(-0.2, 0.4)  # Generally more positive
            elif 'ETH' in symbol.upper():
                base_sentiment += random.uniform(-0.1, 0.3)
            
            # Clamp to valid range
            sentiment_score = max(-1.0, min(1.0, base_sentiment))
            
            # Simulate confidence
            confidence = random.uniform(0.5, 0.8)
            
            # Create simulated sentiment data
            sentiment_data = SentimentData(
                symbol=symbol,
                sentiment_score=sentiment_score,
                confidence=confidence,
                source='simulated',
                timestamp=datetime.now(),
                raw_data={'simulation': True},
                keywords=[],
                volume=random.randint(10, 100)
            )
            
            # Store sentiment data
            self.sentiment_history[symbol].append(sentiment_data)
            self.current_sentiment[symbol] = sentiment_score
            
            return {
                'symbol': symbol,
                'sentiment_score': sentiment_score,
                'confidence': confidence,
                'sentiment_label': self._get_sentiment_label(sentiment_score),
                'volume': sentiment_data.volume,
                'timestamp': datetime.now(),
                'source': 'simulated'
            }
            
        except Exception as e:
            logger.error(f"Error in simulated sentiment analysis: {e}")
            return None
    
    async def _analyze_text_sentiment(self, text: str) -> Optional[float]:
        """Analyze sentiment of text"""
        try:
            text_lower = text.lower()
            
            # Count keyword matches
            positive_matches = len(self.positive_pattern.findall(text_lower))
            negative_matches = len(self.negative_pattern.findall(text_lower))
            neutral_matches = len(self.neutral_pattern.findall(text_lower))
            
            total_matches = positive_matches + negative_matches + neutral_matches
            
            if total_matches == 0:
                return 0.0  # Neutral if no keywords found
            
            # Calculate sentiment score
            positive_weight = positive_matches / total_matches
            negative_weight = negative_matches / total_matches
            
            sentiment_score = positive_weight - negative_weight
            
            # Apply intensity based on total matches
            intensity = min(1.0, total_matches / 5.0)  # More matches = higher intensity
            sentiment_score *= intensity
            
            return sentiment_score
            
        except Exception as e:
            logger.error(f"Error analyzing text sentiment: {e}")
            return None
    
    def _extract_keywords(self, text_data: List[str]) -> List[str]:
        """Extract relevant keywords from text data"""
        try:
            keywords = set()
            
            for text in text_data:
                text_lower = text.lower()
                
                # Extract positive keywords
                keywords.update(self.positive_pattern.findall(text_lower))
                
                # Extract negative keywords
                keywords.update(self.negative_pattern.findall(text_lower))
                
                # Extract neutral keywords
                keywords.update(self.neutral_pattern.findall(text_lower))
            
            return list(keywords)
            
        except Exception as e:
            logger.error(f"Error extracting keywords: {e}")
            return []
    
    def _get_sentiment_label(self, sentiment_score: float) -> str:
        """Get sentiment label from score"""
        if sentiment_score >= 0.3:
            return 'positive'
        elif sentiment_score <= -0.3:
            return 'negative'
        else:
            return 'neutral'
    
    async def _calculate_sentiment_trend(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Calculate sentiment trend for symbol"""
        try:
            if symbol not in self.sentiment_history:
                return None
            
            recent_data = list(self.sentiment_history[symbol])[-20:]  # Last 20 data points
            
            if len(recent_data) < 2:
                return None
            
            # Calculate trend
            scores = [data.sentiment_score for data in recent_data]
            timestamps = [data.timestamp for data in recent_data]
            
            # Simple linear trend
            if len(scores) >= 3:
                recent_avg = sum(scores[-3:]) / 3
                previous_avg = sum(scores[-6:-3]) / 3 if len(scores) >= 6 else sum(scores[:-3]) / len(scores[:-3])
                
                change_rate = recent_avg - previous_avg
                
                trend_direction = 'improving' if change_rate > 0.1 else 'declining' if change_rate < -0.1 else 'stable'
                
                return {
                    'direction': trend_direction,
                    'change_rate': change_rate,
                    'current_avg': recent_avg,
                    'previous_avg': previous_avg,
                    'data_points': len(recent_data)
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error calculating sentiment trend: {e}")
            return None
    
    # Placeholder methods for different sentiment sources
    
    async def _analyze_social_media_sentiment(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Analyze social media sentiment (placeholder)"""
        # This would integrate with Twitter API, Reddit API, etc.
        return await self._simulate_sentiment_analysis(symbol)
    
    async def _analyze_news_sentiment(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Analyze news sentiment (placeholder)"""
        # This would integrate with news APIs and NLP services
        return await self._simulate_sentiment_analysis(symbol)
    
    async def _analyze_market_sentiment(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Analyze market-based sentiment indicators (placeholder)"""
        # This would analyze fear/greed index, put/call ratios, etc.
        return await self._simulate_sentiment_analysis(symbol)
    
    async def _analyze_technical_sentiment(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Analyze technical sentiment indicators (placeholder)"""
        # This would analyze technical indicators for sentiment
        return await self._simulate_sentiment_analysis(symbol)

"""
Testing Package - Comprehensive Testing Framework and Quality Assurance

This package provides enterprise-grade testing infrastructure for the
Advanced Ollama Trading Agent System with comprehensive test coverage,
performance testing, security testing, and quality assurance.

Components:
- TestFramework: Core testing infrastructure
- AdvancedTestFramework: Enhanced testing with load and security tests
- ComprehensiveTestSuite: Complete system test coverage
- Test fixtures and mock data generators
- Performance benchmarking and load testing
- Security vulnerability testing
- CI/CD integration support

Features:
- Multiple test types (unit, integration, performance, load, security)
- Parallel test execution
- Test result aggregation and reporting
- Performance metrics collection and benchmarking
- Load testing with concurrent users
- Security vulnerability scanning
- Mock data generation and fixtures
- Test coverage analysis
- JUnit XML export for CI/CD
- Real-time test monitoring
"""

from .test_framework import TestFramework, AdvancedTestFramework, get_advanced_test_framework
from .test_runner import TestRunner
from .test_validator import TestValidator
from .mock_data import MockDataGenerator
from .fixtures import TestFixtures
from .test_suites.comprehensive_test_suite import ComprehensiveTestSuite

__all__ = [
    'TestFramework',
    'AdvancedTestFramework',
    'get_advanced_test_framework',
    'TestRunner',
    'TestValidator',
    'MockDataGenerator',
    'TestFixtures',
    'ComprehensiveTestSuite'
]

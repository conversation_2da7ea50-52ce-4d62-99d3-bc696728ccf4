#!/usr/bin/env python3
"""
Core System Assembly Test - Test core system components without import conflicts
"""

import asyncio
import json
import time
import sys
import os
from datetime import datetime

# Add current directory to path
sys.path.append(os.getcwd())

async def test_core_system_assembly():
    """Test core system assembly"""
    
    print("🚀 CORE SYSTEM ASSEMBLY TEST")
    print("=" * 80)
    print("Testing core system components assembly and integration")
    print("=" * 80)
    
    results = {}
    
    try:
        # Phase 1: Import Tests
        print("\n📦 PHASE 1: IMPORT VALIDATION")
        
        import_results = {}
        
        # Test core imports
        core_imports = [
            ("System Coordinator", "system.system_coordinator", "SystemCoordinator"),
            ("Team Manager", "teams.team_manager", "TeamManager"),
            ("Data Manager", "data.data_manager", "DataManager"),
            ("Analytics Engine", "analytics.analytics_engine", "AnalyticsEngine"),
            ("Ollama Hub", "ollama.ollama_hub", "OllamaHub"),
        ]
        
        for name, module_path, class_name in core_imports:
            try:
                module = __import__(module_path, fromlist=[class_name])
                cls = getattr(module, class_name)
                print(f"  ✅ {name}: IMPORTED")
                import_results[name.lower().replace(' ', '_')] = True
            except Exception as e:
                print(f"  ❌ {name}: FAILED - {e}")
                import_results[name.lower().replace(' ', '_')] = False
                
        # Test advanced feature imports
        advanced_imports = [
            ("Competitive Framework", "teams.competitive_cooperative_framework", "CompetitiveCooperativeFramework"),
            ("Tournament Framework", "innovation.tournament_framework", "InnovationTournamentFramework"),
            ("Self-Improvement Engine", "learning.self_improvement_engine", "SelfImprovementEngine"),
            ("Regime Adaptation", "market.regime_adaptation_system", "MarketRegimeAdaptationSystem"),
            ("Performance Optimizer", "optimization.advanced_performance_optimizer", "AdvancedPerformanceOptimizer"),
        ]
        
        for name, module_path, class_name in advanced_imports:
            try:
                module = __import__(module_path, fromlist=[class_name])
                cls = getattr(module, class_name)
                print(f"  ✅ {name}: IMPORTED")
                import_results[name.lower().replace(' ', '_')] = True
            except Exception as e:
                print(f"  ❌ {name}: FAILED - {e}")
                import_results[name.lower().replace(' ', '_')] = False
                
        # Test utility imports
        utility_imports = [
            ("Mock Data Providers", "simulation.mock_data_providers", "MockDataProviders"),
            ("Paper Trading Engine", "trading.paper_trading_engine", "PaperTradingEngine"),
            ("Configuration Manager", "config.configuration_manager", "ConfigurationManager"),
        ]
        
        for name, module_path, class_name in utility_imports:
            try:
                module = __import__(module_path, fromlist=[class_name])
                cls = getattr(module, class_name)
                print(f"  ✅ {name}: IMPORTED")
                import_results[name.lower().replace(' ', '_')] = True
            except Exception as e:
                print(f"  ❌ {name}: FAILED - {e}")
                import_results[name.lower().replace(' ', '_')] = False
                
        results['imports'] = import_results
        
        # Phase 2: Basic Instantiation Tests
        print("\n🏗️ PHASE 2: COMPONENT INSTANTIATION")
        
        instantiation_results = {}
        
        # Test core component instantiation
        try:
            from system.system_coordinator import SystemCoordinator
            coordinator = SystemCoordinator('config/test_config.yaml')
            print("  ✅ System Coordinator: INSTANTIATED")
            instantiation_results['system_coordinator'] = True
        except Exception as e:
            print(f"  ❌ System Coordinator: FAILED - {e}")
            instantiation_results['system_coordinator'] = False
            
        # Test advanced features instantiation
        try:
            from teams.competitive_cooperative_framework import CompetitiveCooperativeFramework
            
            class MockTeamManager:
                def __init__(self):
                    self.name = "mock_team_manager"
                    
            mock_team_manager = MockTeamManager()
            competitive_framework = CompetitiveCooperativeFramework(mock_team_manager, {})
            print("  ✅ Competitive Framework: INSTANTIATED")
            instantiation_results['competitive_framework'] = True
        except Exception as e:
            print(f"  ❌ Competitive Framework: FAILED - {e}")
            instantiation_results['competitive_framework'] = False
            
        try:
            from simulation.mock_data_providers import MockDataProviders
            mock_data = MockDataProviders({})
            print("  ✅ Mock Data Providers: INSTANTIATED")
            instantiation_results['mock_data_providers'] = True
        except Exception as e:
            print(f"  ❌ Mock Data Providers: FAILED - {e}")
            instantiation_results['mock_data_providers'] = False
            
        try:
            from trading.paper_trading_engine import PaperTradingEngine
            paper_trading = PaperTradingEngine({})
            print("  ✅ Paper Trading Engine: INSTANTIATED")
            instantiation_results['paper_trading_engine'] = True
        except Exception as e:
            print(f"  ❌ Paper Trading Engine: FAILED - {e}")
            instantiation_results['paper_trading_engine'] = False
            
        try:
            from config.configuration_manager import ConfigurationManager
            config_manager = ConfigurationManager()
            print("  ✅ Configuration Manager: INSTANTIATED")
            instantiation_results['configuration_manager'] = True
        except Exception as e:
            print(f"  ❌ Configuration Manager: FAILED - {e}")
            instantiation_results['configuration_manager'] = False
            
        results['instantiation'] = instantiation_results
        
        # Phase 3: Basic Functionality Tests
        print("\n⚙️ PHASE 3: BASIC FUNCTIONALITY")
        
        functionality_results = {}
        
        # Test Configuration Manager functionality
        try:
            config_manager = ConfigurationManager()
            init_success = await config_manager.initialize()
            
            if init_success:
                test_config = {"test": "value", "number": 42}
                set_success = await config_manager.set_config("test_config", test_config)
                get_result = await config_manager.get_config("test_config")
                
                if set_success and get_result:
                    print("  ✅ Configuration Manager: FUNCTIONAL")
                    functionality_results['configuration_manager'] = True
                else:
                    print("  ⚠️ Configuration Manager: PARTIAL")
                    functionality_results['configuration_manager'] = False
            else:
                print("  ❌ Configuration Manager: INIT FAILED")
                functionality_results['configuration_manager'] = False
                
        except Exception as e:
            print(f"  ❌ Configuration Manager: ERROR - {e}")
            functionality_results['configuration_manager'] = False
            
        # Test Mock Data Providers functionality
        try:
            mock_data = MockDataProviders({})
            init_success = await mock_data.initialize()
            start_success = await mock_data.start()
            
            if init_success and start_success:
                # Test data generation
                await asyncio.sleep(1)  # Let it generate some data
                real_time_data = await mock_data.get_real_time_data('AAPL')
                stats = await mock_data.get_simulation_stats()
                
                await mock_data.stop()
                
                if stats and stats.get('total_symbols', 0) > 0:
                    print("  ✅ Mock Data Providers: FUNCTIONAL")
                    functionality_results['mock_data_providers'] = True
                else:
                    print("  ⚠️ Mock Data Providers: PARTIAL")
                    functionality_results['mock_data_providers'] = False
            else:
                print("  ❌ Mock Data Providers: INIT/START FAILED")
                functionality_results['mock_data_providers'] = False
                
        except Exception as e:
            print(f"  ❌ Mock Data Providers: ERROR - {e}")
            functionality_results['mock_data_providers'] = False
            
        # Test Paper Trading Engine functionality
        try:
            paper_trading = PaperTradingEngine({})
            init_success = await paper_trading.initialize()
            start_success = await paper_trading.start()
            
            if init_success and start_success:
                # Test account creation
                account_created = await paper_trading.create_account("test_account", 100000.0)
                stats = await paper_trading.get_trading_statistics()
                
                await paper_trading.stop()
                
                if account_created and stats:
                    print("  ✅ Paper Trading Engine: FUNCTIONAL")
                    functionality_results['paper_trading_engine'] = True
                else:
                    print("  ⚠️ Paper Trading Engine: PARTIAL")
                    functionality_results['paper_trading_engine'] = False
            else:
                print("  ❌ Paper Trading Engine: INIT/START FAILED")
                functionality_results['paper_trading_engine'] = False
                
        except Exception as e:
            print(f"  ❌ Paper Trading Engine: ERROR - {e}")
            functionality_results['paper_trading_engine'] = False
            
        results['functionality'] = functionality_results
        
        # Phase 4: System Assembly Assessment
        print("\n🔗 PHASE 4: SYSTEM ASSEMBLY ASSESSMENT")
        
        # Calculate scores
        import_score = (sum(import_results.values()) / len(import_results)) * 100
        instantiation_score = (sum(instantiation_results.values()) / len(instantiation_results)) * 100
        functionality_score = (sum(functionality_results.values()) / len(functionality_results)) * 100
        
        overall_score = (import_score * 0.3 + instantiation_score * 0.3 + functionality_score * 0.4)
        
        print(f"  📊 Import Success Rate: {import_score:.1f}%")
        print(f"  📊 Instantiation Success Rate: {instantiation_score:.1f}%")
        print(f"  📊 Functionality Success Rate: {functionality_score:.1f}%")
        print(f"  📊 Overall Assembly Score: {overall_score:.1f}%")
        
        # Final Assessment
        print("\n🎉 FINAL ASSEMBLY ASSESSMENT")
        print("=" * 80)
        
        total_tests = len(import_results) + len(instantiation_results) + len(functionality_results)
        passed_tests = sum(import_results.values()) + sum(instantiation_results.values()) + sum(functionality_results.values())
        
        print(f"📊 Passed Tests: {passed_tests}/{total_tests}")
        print(f"🔧 Assembly Success Rate: {overall_score:.1f}%")
        
        # Detailed breakdown
        print("\n📋 DETAILED ASSEMBLY RESULTS:")
        
        print("  📦 Import Results:")
        for component, success in import_results.items():
            status = "✅ SUCCESS" if success else "❌ FAILED"
            print(f"    {component.replace('_', ' ').title()}: {status}")
            
        print("  🏗️ Instantiation Results:")
        for component, success in instantiation_results.items():
            status = "✅ SUCCESS" if success else "❌ FAILED"
            print(f"    {component.replace('_', ' ').title()}: {status}")
            
        print("  ⚙️ Functionality Results:")
        for component, success in functionality_results.items():
            status = "✅ SUCCESS" if success else "❌ FAILED"
            print(f"    {component.replace('_', ' ').title()}: {status}")
            
        # Save results
        assembly_summary = {
            "timestamp": datetime.now().isoformat(),
            "test_type": "core_system_assembly",
            "results": results,
            "scores": {
                "import_score": import_score,
                "instantiation_score": instantiation_score,
                "functionality_score": functionality_score,
                "overall_score": overall_score
            },
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "assembly_success_rate": overall_score,
                "system_assembled": overall_score >= 70.0,
                "ready_for_integration": overall_score >= 80.0
            }
        }
        
        with open('core_system_assembly_results.json', 'w') as f:
            json.dump(assembly_summary, f, indent=2, default=str)
        
        print(f"\n📄 Assembly results saved to: core_system_assembly_results.json")
        
        # Final verdict
        print("\n" + "=" * 80)
        if overall_score >= 95:
            print("🎉 OUTSTANDING! PERFECT SYSTEM ASSEMBLY!")
            print("🚀 All components assembled and functional!")
            print("🏆 Ready for advanced integration testing!")
        elif overall_score >= 85:
            print("🎉 EXCELLENT! COMPREHENSIVE SYSTEM ASSEMBLY!")
            print("🚀 Most components working excellently!")
            print("✅ Ready for integration testing!")
        elif overall_score >= 75:
            print("✅ VERY GOOD! SOLID SYSTEM ASSEMBLY!")
            print("🔧 Core components working well!")
            print("💪 Strong foundation established!")
        elif overall_score >= 65:
            print("✅ GOOD! BASIC SYSTEM ASSEMBLY!")
            print("🛠️ Some components working, needs improvement!")
            print("📈 Good progress on system assembly!")
        else:
            print("⚠️ NEEDS SIGNIFICANT IMPROVEMENT!")
            print("🔧 Major assembly issues detected!")
            print("📋 Review failed components and fix issues!")
        
        print("=" * 80)
        
        return overall_score >= 70.0
        
    except Exception as e:
        print(f"❌ Core System Assembly Test Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_core_system_assembly())
    if success:
        print("\n🎉 CORE SYSTEM ASSEMBLY TEST SUCCESSFUL!")
        print("🚀 Advanced Ollama Trading Agents System CORE is ASSEMBLED!")
    else:
        print("\n⚠️ SYSTEM ASSEMBLY NEEDS ATTENTION!")
        print("🔧 Review test results and address issues!")

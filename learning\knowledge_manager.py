"""
Knowledge Manager - Manages system knowledge base and insights
"""

import asyncio
import logging
import time
import json
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib

logger = logging.getLogger(__name__)


@dataclass
class KnowledgeItem:
    """Knowledge item data structure"""
    item_id: str
    category: str
    title: str
    content: Dict[str, Any]
    confidence: float
    source: str
    created_at: float
    updated_at: float
    tags: List[str]
    usage_count: int = 0
    effectiveness_score: float = 0.0


class KnowledgeManager:
    """
    Manages system knowledge base and insights.
    
    Features:
    - Knowledge storage and retrieval
    - Insight categorization and indexing
    - Knowledge validation and scoring
    - Best practices management
    - Pattern library maintenance
    - Knowledge transfer between components
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.knowledge_config = config.get('knowledge_manager', {})
        
        # Storage configuration
        self.storage_path = Path(self.knowledge_config.get('storage_path', 'data/knowledge'))
        self.backup_path = Path(self.knowledge_config.get('backup_path', 'data/knowledge_backups'))
        
        # Knowledge storage
        self.knowledge_items: Dict[str, KnowledgeItem] = {}
        self.knowledge_index: Dict[str, List[str]] = {}  # Category/tag based index
        
        # Knowledge categories
        self.categories = {
            'strategy_patterns': 'Successful strategy patterns and configurations',
            'risk_insights': 'Risk management insights and best practices',
            'execution_optimizations': 'Execution optimization techniques',
            'portfolio_insights': 'Portfolio management insights',
            'market_patterns': 'Market behavior patterns and anomalies',
            'performance_factors': 'Factors affecting performance',
            'failure_analysis': 'Analysis of failures and lessons learned',
            'best_practices': 'General best practices and guidelines'
        }
        
        # Knowledge metrics
        self.knowledge_metrics: Dict[str, Any] = {}
        
        # State
        self.initialized = False
        self.running = False
        
        # Ensure directories exist
        self.storage_path.mkdir(parents=True, exist_ok=True)
        self.backup_path.mkdir(parents=True, exist_ok=True)
    
    async def initialize(self) -> bool:
        """Initialize knowledge manager"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Knowledge Manager...")
            
            # Load existing knowledge
            await self._load_knowledge()
            
            # Build knowledge index
            await self._build_knowledge_index()
            
            # Update metrics
            await self._update_knowledge_metrics()
            
            self.initialized = True
            logger.info(f"✓ Knowledge Manager initialized with {len(self.knowledge_items)} items")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Knowledge Manager: {e}")
            return False
    
    async def start(self) -> bool:
        """Start knowledge manager"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        self.running = True
        logger.info("✓ Knowledge Manager started")
        return True
    
    async def stop(self) -> bool:
        """Stop knowledge manager"""
        if self.running:
            # Save knowledge
            await self._save_knowledge()
        
        self.running = False
        logger.info("✓ Knowledge Manager stopped")
        return True
    
    async def store_insights(self, component: str, insights: Dict[str, Any]) -> bool:
        """Store insights from a component"""
        try:
            for insight_type, insight_data in insights.items():
                # Create knowledge item
                item_id = self._generate_item_id(component, insight_type, insight_data)
                
                # Determine category
                category = self._categorize_insight(component, insight_type, insight_data)
                
                # Create knowledge item
                knowledge_item = KnowledgeItem(
                    item_id=item_id,
                    category=category,
                    title=f"{component} - {insight_type}",
                    content={
                        'component': component,
                        'insight_type': insight_type,
                        'data': insight_data,
                        'context': {
                            'timestamp': time.time(),
                            'source_component': component
                        }
                    },
                    confidence=insight_data.get('confidence', 0.5) if isinstance(insight_data, dict) else 0.5,
                    source=component,
                    created_at=time.time(),
                    updated_at=time.time(),
                    tags=[component, insight_type, category]
                )
                
                # Store or update knowledge item
                if item_id in self.knowledge_items:
                    # Update existing item
                    existing_item = self.knowledge_items[item_id]
                    existing_item.content.update(knowledge_item.content)
                    existing_item.updated_at = time.time()
                    existing_item.usage_count += 1
                else:
                    # Store new item
                    self.knowledge_items[item_id] = knowledge_item
                
                # Update index
                await self._update_knowledge_index(item_id)
            
            # Save knowledge
            await self._save_knowledge()
            
            logger.debug(f"Stored {len(insights)} insights from {component}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing insights from {component}: {e}")
            return False
    
    async def get_insights(self, component: str, category: str = None) -> Dict[str, Any]:
        """Get insights for a component"""
        try:
            insights = {}
            
            for item_id, item in self.knowledge_items.items():
                # Filter by component
                if item.source != component and component not in item.tags:
                    continue
                
                # Filter by category if specified
                if category and item.category != category:
                    continue
                
                # Add to insights
                if item.category not in insights:
                    insights[item.category] = []
                
                insights[item.category].append({
                    'item_id': item.item_id,
                    'title': item.title,
                    'content': item.content,
                    'confidence': item.confidence,
                    'created_at': item.created_at,
                    'updated_at': item.updated_at,
                    'usage_count': item.usage_count,
                    'effectiveness_score': item.effectiveness_score,
                    'tags': item.tags
                })
            
            return insights
            
        except Exception as e:
            logger.error(f"Error getting insights for {component}: {e}")
            return {}
    
    async def get_all_insights(self) -> Dict[str, Any]:
        """Get all insights"""
        try:
            insights = {}
            
            for category in self.categories:
                insights[category] = []
            
            for item in self.knowledge_items.values():
                if item.category not in insights:
                    insights[item.category] = []
                
                insights[item.category].append({
                    'item_id': item.item_id,
                    'title': item.title,
                    'content': item.content,
                    'confidence': item.confidence,
                    'source': item.source,
                    'created_at': item.created_at,
                    'updated_at': item.updated_at,
                    'usage_count': item.usage_count,
                    'effectiveness_score': item.effectiveness_score,
                    'tags': item.tags
                })
            
            return insights
            
        except Exception as e:
            logger.error(f"Error getting all insights: {e}")
            return {}
    
    async def search_knowledge(self, query: str, category: str = None, 
                             min_confidence: float = 0.0) -> List[Dict[str, Any]]:
        """Search knowledge base"""
        try:
            results = []
            query_lower = query.lower()
            
            for item in self.knowledge_items.values():
                # Filter by category
                if category and item.category != category:
                    continue
                
                # Filter by confidence
                if item.confidence < min_confidence:
                    continue
                
                # Search in title, content, and tags
                searchable_text = f"{item.title} {str(item.content)} {' '.join(item.tags)}".lower()
                
                if query_lower in searchable_text:
                    results.append({
                        'item_id': item.item_id,
                        'title': item.title,
                        'category': item.category,
                        'content': item.content,
                        'confidence': item.confidence,
                        'source': item.source,
                        'created_at': item.created_at,
                        'updated_at': item.updated_at,
                        'usage_count': item.usage_count,
                        'effectiveness_score': item.effectiveness_score,
                        'tags': item.tags,
                        'relevance_score': self._calculate_relevance(item, query)
                    })
            
            # Sort by relevance and effectiveness
            results.sort(key=lambda x: (x['relevance_score'], x['effectiveness_score']), reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching knowledge: {e}")
            return []
    
    async def get_best_practices(self, component: str = None) -> List[Dict[str, Any]]:
        """Get best practices"""
        try:
            best_practices = []
            
            for item in self.knowledge_items.values():
                if item.category == 'best_practices':
                    if component is None or item.source == component or component in item.tags:
                        best_practices.append({
                            'title': item.title,
                            'content': item.content,
                            'confidence': item.confidence,
                            'source': item.source,
                            'effectiveness_score': item.effectiveness_score,
                            'usage_count': item.usage_count
                        })
            
            # Sort by effectiveness and confidence
            best_practices.sort(key=lambda x: (x['effectiveness_score'], x['confidence']), reverse=True)
            
            return best_practices
            
        except Exception as e:
            logger.error(f"Error getting best practices: {e}")
            return []
    
    async def update_effectiveness_score(self, item_id: str, outcome: float) -> bool:
        """Update effectiveness score based on outcome"""
        try:
            if item_id not in self.knowledge_items:
                return False
            
            item = self.knowledge_items[item_id]
            
            # Update effectiveness score using exponential moving average
            alpha = 0.1  # Learning rate
            item.effectiveness_score = (1 - alpha) * item.effectiveness_score + alpha * outcome
            item.updated_at = time.time()
            
            # Save knowledge
            await self._save_knowledge()
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating effectiveness score for {item_id}: {e}")
            return False
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get knowledge metrics"""
        await self._update_knowledge_metrics()
        return self.knowledge_metrics.copy()
    
    # Private methods
    
    def _generate_item_id(self, component: str, insight_type: str, insight_data: Any) -> str:
        """Generate unique item ID"""
        content_str = f"{component}_{insight_type}_{str(insight_data)}"
        return hashlib.md5(content_str.encode()).hexdigest()[:16]
    
    def _categorize_insight(self, component: str, insight_type: str, insight_data: Any) -> str:
        """Categorize insight based on component and type"""
        # Simple categorization logic
        if 'strategy' in component.lower():
            return 'strategy_patterns'
        elif 'risk' in component.lower():
            return 'risk_insights'
        elif 'execution' in component.lower():
            return 'execution_optimizations'
        elif 'portfolio' in component.lower():
            return 'portfolio_insights'
        elif 'performance' in insight_type.lower():
            return 'performance_factors'
        elif 'failure' in insight_type.lower() or 'error' in insight_type.lower():
            return 'failure_analysis'
        elif 'pattern' in insight_type.lower():
            return 'market_patterns'
        else:
            return 'best_practices'
    
    def _calculate_relevance(self, item: KnowledgeItem, query: str) -> float:
        """Calculate relevance score for search"""
        try:
            query_lower = query.lower()
            score = 0.0
            
            # Title match (highest weight)
            if query_lower in item.title.lower():
                score += 1.0
            
            # Tag match (medium weight)
            for tag in item.tags:
                if query_lower in tag.lower():
                    score += 0.5
            
            # Content match (lower weight)
            content_str = str(item.content).lower()
            if query_lower in content_str:
                score += 0.3
            
            # Boost by confidence and effectiveness
            score *= (item.confidence + item.effectiveness_score) / 2
            
            return score
            
        except Exception as e:
            logger.error(f"Error calculating relevance: {e}")
            return 0.0
    
    async def _load_knowledge(self):
        """Load knowledge from disk"""
        try:
            knowledge_file = self.storage_path / "knowledge.json"
            if knowledge_file.exists():
                with open(knowledge_file, 'r') as f:
                    data = json.load(f)
                
                # Load knowledge items
                for item_id, item_data in data.get('knowledge_items', {}).items():
                    self.knowledge_items[item_id] = KnowledgeItem(**item_data)
                
                logger.info(f"Loaded {len(self.knowledge_items)} knowledge items from disk")
                
        except Exception as e:
            logger.error(f"Error loading knowledge: {e}")
    
    async def _save_knowledge(self):
        """Save knowledge to disk"""
        try:
            data = {
                'knowledge_items': {
                    item_id: asdict(item)
                    for item_id, item in self.knowledge_items.items()
                },
                'categories': self.categories,
                'last_updated': time.time()
            }
            
            knowledge_file = self.storage_path / "knowledge.json"
            with open(knowledge_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving knowledge: {e}")
    
    async def _build_knowledge_index(self):
        """Build knowledge index"""
        try:
            self.knowledge_index = {}
            
            for item_id, item in self.knowledge_items.items():
                # Index by category
                if item.category not in self.knowledge_index:
                    self.knowledge_index[item.category] = []
                self.knowledge_index[item.category].append(item_id)
                
                # Index by tags
                for tag in item.tags:
                    if tag not in self.knowledge_index:
                        self.knowledge_index[tag] = []
                    self.knowledge_index[tag].append(item_id)
                
                # Index by source
                if item.source not in self.knowledge_index:
                    self.knowledge_index[item.source] = []
                self.knowledge_index[item.source].append(item_id)
                
        except Exception as e:
            logger.error(f"Error building knowledge index: {e}")
    
    async def _update_knowledge_index(self, item_id: str):
        """Update knowledge index for an item"""
        try:
            if item_id not in self.knowledge_items:
                return
            
            item = self.knowledge_items[item_id]
            
            # Remove from existing index entries
            for index_list in self.knowledge_index.values():
                if item_id in index_list:
                    index_list.remove(item_id)
            
            # Re-add to index
            # Category
            if item.category not in self.knowledge_index:
                self.knowledge_index[item.category] = []
            self.knowledge_index[item.category].append(item_id)
            
            # Tags
            for tag in item.tags:
                if tag not in self.knowledge_index:
                    self.knowledge_index[tag] = []
                self.knowledge_index[tag].append(item_id)
            
            # Source
            if item.source not in self.knowledge_index:
                self.knowledge_index[item.source] = []
            self.knowledge_index[item.source].append(item_id)
            
        except Exception as e:
            logger.error(f"Error updating knowledge index for {item_id}: {e}")
    
    async def _update_knowledge_metrics(self):
        """Update knowledge metrics"""
        try:
            total_items = len(self.knowledge_items)
            
            # Category distribution
            category_counts = {}
            for item in self.knowledge_items.values():
                category_counts[item.category] = category_counts.get(item.category, 0) + 1
            
            # Confidence statistics
            confidences = [item.confidence for item in self.knowledge_items.values()]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
            
            # Effectiveness statistics
            effectiveness_scores = [item.effectiveness_score for item in self.knowledge_items.values()]
            avg_effectiveness = sum(effectiveness_scores) / len(effectiveness_scores) if effectiveness_scores else 0.0
            
            # Usage statistics
            total_usage = sum(item.usage_count for item in self.knowledge_items.values())
            
            self.knowledge_metrics = {
                'total_items': total_items,
                'category_distribution': category_counts,
                'average_confidence': avg_confidence,
                'average_effectiveness': avg_effectiveness,
                'total_usage': total_usage,
                'categories_count': len(self.categories),
                'index_size': len(self.knowledge_index),
                'last_updated': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error updating knowledge metrics: {e}")

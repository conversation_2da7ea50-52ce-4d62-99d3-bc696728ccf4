#!/usr/bin/env python3
"""
Complete System Integration Test - Test all working components
"""

import asyncio
import json
from datetime import datetime
from config.config_manager import ConfigManager
from models.ollama_hub import OllamaModelHub
from agents.agent_manager import AgentManager
from agents.base_agent import Agent<PERSON><PERSON>
from strategies.strategy_manager import StrategyManager
from risk.risk_manager import RiskManager

class SimpleMessageBroker:
    def __init__(self):
        self.messages = []
    
    async def publish(self, topic, message):
        self.messages.append({"topic": topic, "message": message, "timestamp": datetime.now()})
        print(f"📢 {topic}: {str(message)[:80]}...")
    
    async def subscribe(self, topic, callback):
        pass

async def test_complete_system():
    """Test complete system integration with all working components"""
    
    print("🎯 COMPLETE SYSTEM INTEGRATION TEST")
    print("=" * 60)
    
    results = {}
    
    try:
        # Phase 1: Core System Initialization
        print("\n🔧 PHASE 1: Core System Initialization")
        print("-" * 40)
        
        # Load configuration
        config_manager = ConfigManager('config/test_config.yaml')
        await config_manager.load_config()
        config = await config_manager.get_config()
        print("✅ Configuration loaded and validated")
        
        # Initialize Ollama Hub
        ollama_hub = OllamaModelHub(config=config)
        await ollama_hub.initialize()
        print("✅ Ollama Hub initialized with AI models")
        
        # Initialize Message Broker
        message_broker = SimpleMessageBroker()
        print("✅ Message Broker initialized")
        
        # Initialize Agent Manager
        agent_manager = AgentManager(ollama_hub, message_broker, config)
        await agent_manager.initialize()
        print("✅ Agent Manager initialized")
        
        results['core_initialization'] = {'success': True, 'components': 4}
        
        # Phase 2: AI Agent Team Deployment
        print("\n🤖 PHASE 2: AI Agent Team Deployment")
        print("-" * 40)
        
        # Create specialized AI agents
        agents = {}
        
        try:
            analyst_id = await agent_manager.create_agent(
                role=AgentRole.MARKET_ANALYST,
                name="System_Market_Analyst"
            )
            agents['analyst'] = await agent_manager.get_agent(analyst_id)
            print(f"✅ Market Analyst: {agents['analyst'].model_instance.model_name}")
        except Exception as e:
            print(f"⚠️ Market Analyst failed: {e}")
            agents['analyst'] = None
        
        try:
            strategist_id = await agent_manager.create_agent(
                role=AgentRole.STRATEGY_DEVELOPER, 
                name="System_Strategy_Developer"
            )
            agents['strategist'] = await agent_manager.get_agent(strategist_id)
            print(f"✅ Strategy Developer: {agents['strategist'].model_instance.model_name}")
        except Exception as e:
            print(f"⚠️ Strategy Developer failed: {e}")
            agents['strategist'] = None
        
        try:
            risk_mgr_id = await agent_manager.create_agent(
                role=AgentRole.RISK_MANAGER,
                name="System_Risk_Manager"
            )
            agents['risk_manager'] = await agent_manager.get_agent(risk_mgr_id)
            print(f"✅ Risk Manager: {agents['risk_manager'].model_instance.model_name}")
        except Exception as e:
            print(f"⚠️ Risk Manager failed: {e}")
            agents['risk_manager'] = None
        
        successful_agents = sum(1 for agent in agents.values() if agent is not None)
        results['agent_deployment'] = {'success': True, 'agents_created': successful_agents, 'total_attempted': 3}
        
        # Phase 3: Strategy Management System
        print("\n🎯 PHASE 3: Strategy Management System")
        print("-" * 40)
        
        try:
            strategy_manager = StrategyManager(config)
            await strategy_manager.initialize()
            
            # Create test strategies
            momentum_strategy = await strategy_manager.create_strategy(
                strategy_type='momentum',
                name='system_test_momentum',
                config={'lookback_period': 20, 'threshold': 0.02}
            )
            print(f"✅ Momentum Strategy created: {momentum_strategy}")
            
            mean_reversion_strategy = await strategy_manager.create_strategy(
                strategy_type='mean_reversion',
                name='system_test_mean_reversion',
                config={'lookback_period': 10, 'std_threshold': 2.0}
            )
            print(f"✅ Mean Reversion Strategy created: {mean_reversion_strategy}")
            
            # Test strategy status
            status = strategy_manager.get_strategy_status(momentum_strategy)
            print(f"✅ Strategy status retrieved: {status['state']}")
            
            results['strategy_management'] = {'success': True, 'strategies_created': 2}
            
        except Exception as e:
            print(f"❌ Strategy Management failed: {e}")
            results['strategy_management'] = {'success': False, 'error': str(e)}
        
        # Phase 4: Risk Management System
        print("\n⚠️ PHASE 4: Risk Management System")
        print("-" * 40)
        
        try:
            risk_manager = RiskManager(config)
            await risk_manager.initialize()
            
            # Test portfolio risk assessment
            test_portfolio = {
                'total_value': 250000,
                'positions': [
                    {'symbol': 'AAPL', 'value': 75000, 'shares': 500},
                    {'symbol': 'TSLA', 'value': 50000, 'shares': 200},
                    {'symbol': 'GOOGL', 'value': 60000, 'shares': 20},
                    {'symbol': 'MSFT', 'value': 40000, 'shares': 100}
                ],
                'cash': 25000
            }
            
            risk_assessment = await risk_manager.assess_portfolio_risk(test_portfolio)
            print(f"✅ Portfolio risk assessment completed: {type(risk_assessment)}")
            
            # Test risk limit monitoring
            risk_limits = {
                'max_portfolio_risk': 0.03,
                'max_position_size': 0.15,
                'max_sector_exposure': 0.4
            }
            
            limit_check = await risk_manager.check_risk_limits(test_portfolio, risk_limits)
            print(f"✅ Risk limits checked: {len(limit_check) if isinstance(limit_check, list) else 'Completed'}")
            
            results['risk_management'] = {'success': True, 'assessments_completed': 2}
            
        except Exception as e:
            print(f"❌ Risk Management failed: {e}")
            results['risk_management'] = {'success': False, 'error': str(e)}
        
        # Phase 5: AI Agent Task Execution
        print("\n🧠 PHASE 5: AI Agent Task Execution")
        print("-" * 40)
        
        ai_task_results = {}
        
        # Test Risk Manager AI
        if agents['risk_manager']:
            try:
                risk_task = {
                    "type": "assess_portfolio_risk",
                    "portfolio": test_portfolio,
                    "market_conditions": {"volatility": "moderate", "correlation": "normal"}
                }
                
                ai_risk_result = await agents['risk_manager'].execute_task(risk_task)
                if ai_risk_result and ai_risk_result.get('success'):
                    print("✅ AI Risk Assessment: SUCCESS")
                    ai_task_results['risk_assessment'] = ai_risk_result
                else:
                    print(f"⚠️ AI Risk Assessment: {ai_risk_result.get('error', 'Failed')}")
                    
            except Exception as e:
                print(f"❌ AI Risk Assessment error: {e}")
        
        # Test Risk Monitoring
        if agents['risk_manager']:
            try:
                monitor_task = {
                    "type": "monitor_risk_limits",
                    "portfolio": test_portfolio,
                    "limits": risk_limits
                }
                
                monitor_result = await agents['risk_manager'].execute_task(monitor_task)
                if monitor_result and monitor_result.get('success'):
                    print("✅ AI Risk Monitoring: SUCCESS")
                    ai_task_results['risk_monitoring'] = monitor_result
                else:
                    print(f"⚠️ AI Risk Monitoring: {monitor_result.get('error', 'Failed')}")
                    
            except Exception as e:
                print(f"❌ AI Risk Monitoring error: {e}")
        
        results['ai_task_execution'] = {
            'success': len(ai_task_results) > 0,
            'tasks_completed': len(ai_task_results),
            'results': ai_task_results
        }
        
        # Phase 6: System Integration Test
        print("\n🔗 PHASE 6: System Integration Test")
        print("-" * 40)
        
        integration_score = 0
        total_integrations = 5
        
        # Test 1: Config -> Agents
        if successful_agents > 0:
            integration_score += 1
            print("✅ Configuration → Agent Integration")
        
        # Test 2: Agents -> AI Models
        if any(agent for agent in agents.values() if agent):
            integration_score += 1
            print("✅ Agents → AI Models Integration")
        
        # Test 3: Strategy -> Risk Integration
        if results.get('strategy_management', {}).get('success') and results.get('risk_management', {}).get('success'):
            integration_score += 1
            print("✅ Strategy ↔ Risk Management Integration")
        
        # Test 4: AI Task Processing
        if len(ai_task_results) > 0:
            integration_score += 1
            print("✅ AI Task Processing Integration")
        
        # Test 5: Message Broker
        if len(message_broker.messages) > 0:
            integration_score += 1
            print("✅ Message Broker Integration")
        
        results['system_integration'] = {
            'success': integration_score >= 3,
            'integration_score': integration_score,
            'total_integrations': total_integrations,
            'integration_percentage': (integration_score / total_integrations) * 100
        }
        
        # Final Summary
        print("\n🎉 COMPLETE SYSTEM TEST SUMMARY")
        print("=" * 60)
        
        total_phases = len(results)
        successful_phases = sum(1 for result in results.values() if result.get('success'))
        
        print(f"📊 System Phases: {successful_phases}/{total_phases} successful")
        print(f"🤖 AI Agents: {successful_agents}/3 deployed")
        print(f"🔗 Integration: {integration_score}/{total_integrations} systems connected")
        print(f"📈 Overall Success Rate: {(successful_phases/total_phases)*100:.1f}%")
        
        # Detailed results
        for phase_name, result in results.items():
            status = "✅ PASS" if result.get('success') else "❌ FAIL"
            print(f"  {phase_name}: {status}")
        
        # Save comprehensive results
        test_summary = {
            "timestamp": datetime.now().isoformat(),
            "test_type": "complete_system_integration",
            "phases": results,
            "summary": {
                "total_phases": total_phases,
                "successful_phases": successful_phases,
                "success_rate": (successful_phases/total_phases)*100,
                "ai_agents_deployed": successful_agents,
                "integration_score": integration_score,
                "message_broker_messages": len(message_broker.messages)
            }
        }
        
        with open('complete_system_results.json', 'w') as f:
            json.dump(test_summary, f, indent=2, default=str)
        
        print(f"\n📄 Complete results saved to: complete_system_results.json")
        
        return successful_phases >= (total_phases * 0.7)  # 70% success threshold
        
    except Exception as e:
        print(f"❌ System Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_complete_system())
    if success:
        print("\n🎉 COMPLETE SYSTEM INTEGRATION SUCCESSFUL!")
        print("🚀 AI Trading System is ready for advanced features!")
    else:
        print("\n⚠️ SYSTEM INTEGRATION NEEDS IMPROVEMENT")
        print("🔧 Focus on fixing identified issues")

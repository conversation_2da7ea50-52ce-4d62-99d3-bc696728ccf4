#!/usr/bin/env python3
"""
REAL AI Integration Test - Tests with ACTUAL Ollama Models
This test connects to real Ollama server and uses actual AI models
"""

import asyncio
import sys
import logging
import json
from datetime import datetime
from typing import Dict, Any

# Import real components (no mocks)
from agents.agent_manager import AgentManager
from agents.base_agent import <PERSON><PERSON><PERSON>
from config.config_manager import ConfigManager
from models.ollama_hub import OllamaModelHub

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RealAITester:
    """Test suite for real AI integration"""
    
    def __init__(self):
        self.passed_tests = []
        self.failed_tests = []
        self.ai_responses = []
        
    async def run_real_ai_tests(self):
        """Run comprehensive real AI tests"""
        print("🚀 STARTING REAL AI INTEGRATION TESTS")
        print("=" * 60)
        print("⚠️  WARNING: This will use REAL Ollama models and make actual AI calls!")
        print("=" * 60)
        
        # Test individual components
        await self.test_ollama_connection()
        await self.test_model_deployment()
        await self.test_real_agent_creation()
        await self.test_real_market_analysis()
        await self.test_real_strategy_development()
        await self.test_real_risk_assessment()
        await self.test_multi_agent_collaboration()
        
        # Generate report
        self.generate_ai_test_report()
        
    async def test_ollama_connection(self):
        """Test real Ollama server connection"""
        test_name = "OllamaConnection"
        print(f"\n🔌 Testing {test_name}...")
        
        try:
            # Load real configuration
            config_manager = ConfigManager("config/test_config.yaml")
            await config_manager.load_config()
            config = await config_manager.get_config()
            
            # Create real OllamaModelHub
            ollama_hub = OllamaModelHub(config=config)
            await ollama_hub.initialize()
            
            # Test connection and get available models
            available_models = await ollama_hub.get_available_models()
            print(f"  ✓ Connected to Ollama server")
            print(f"  ✓ Found {len(available_models)} available models")
            
            # Check if our required models are available
            required_models = [
                "exaone-deep:32b",
                "huihui_ai/magistral-abliterated:24b", 
                "phi4-reasoning:plus",
                "nemotron-mini:4b"
            ]
            
            model_names = [model.get('name', '') for model in available_models]
            for required_model in required_models:
                if required_model in model_names:
                    print(f"  ✓ Required model available: {required_model}")
                else:
                    print(f"  ❌ Required model missing: {required_model}")
            
            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")
            
            return ollama_hub
            
        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_model_deployment(self):
        """Test real model deployment"""
        test_name = "ModelDeployment"
        print(f"\n🚀 Testing {test_name}...")
        
        try:
            # Load configuration
            config_manager = ConfigManager("config/test_config.yaml")
            await config_manager.load_config()
            config = await config_manager.get_config()
            
            # Create real OllamaModelHub
            ollama_hub = OllamaModelHub(config=config)
            await ollama_hub.initialize()
            
            # Test deploying a model for an agent
            model_instance = await ollama_hub.deploy_model_for_agent(
                agent_name="test_market_analyst",
                role="market_analyst"
            )
            
            assert model_instance is not None
            print(f"  ✓ Model deployed: {model_instance.model_name}")
            print(f"  ✓ Agent: {model_instance.agent_name}")
            print(f"  ✓ Role: {model_instance.role}")
            
            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")
            
            return ollama_hub
            
        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_real_agent_creation(self):
        """Test creating agents with real AI models"""
        test_name = "RealAgentCreation"
        print(f"\n🤖 Testing {test_name}...")
        
        try:
            # Load configuration
            config_manager = ConfigManager("config/test_config.yaml")
            await config_manager.load_config()
            config = await config_manager.get_config()
            
            # Create real components
            ollama_hub = OllamaModelHub(config=config)
            await ollama_hub.initialize()
            
            # Simple message broker
            class SimpleMessageBroker:
                async def publish(self, topic, message):
                    print(f"  📢 Published to {topic}: {message}")
                async def subscribe(self, topic, callback):
                    pass
            
            message_broker = SimpleMessageBroker()
            
            # Create AgentManager with real OllamaHub
            agent_manager = AgentManager(
                ollama_hub=ollama_hub,
                message_broker=message_broker,
                config=config
            )
            
            await agent_manager.initialize()
            
            # Create a real market analyst agent
            agent_id = await agent_manager.create_agent(
                role=AgentRole.MARKET_ANALYST,
                name="real_market_analyst"
            )
            
            assert agent_id is not None
            print(f"  ✓ Created real agent with ID: {agent_id}")
            
            # Get agent and verify it has real model
            agent = await agent_manager.get_agent(agent_id)
            assert agent is not None
            assert agent.model_instance is not None
            print(f"  ✓ Agent has real model: {agent.model_instance.model_name}")
            
            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")
            
            return agent_manager, agent_id
            
        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")
            import traceback
            traceback.print_exc()
            return None, None
    
    async def test_real_market_analysis(self):
        """Test real AI market analysis"""
        test_name = "RealMarketAnalysis"
        print(f"\n📈 Testing {test_name}...")
        
        try:
            # Setup real agent
            agent_manager, agent_id = await self.test_real_agent_creation()
            if not agent_manager or not agent_id:
                raise Exception("Failed to create real agent")
            
            agent = await agent_manager.get_agent(agent_id)
            
            # Create sample market data
            market_data = {
                "symbol": "AAPL",
                "price": 150.25,
                "volume": 1000000,
                "change": 2.5,
                "change_percent": 1.69,
                "high": 152.00,
                "low": 148.50,
                "timestamp": datetime.now().isoformat()
            }
            
            print(f"  🔍 Analyzing market data for {market_data['symbol']}...")
            print(f"  📊 Price: ${market_data['price']}, Change: +{market_data['change']} ({market_data['change_percent']}%)")
            
            # Make REAL AI call for market analysis
            analysis_result = await agent.analyze_market_data(market_data)
            
            print(f"  🧠 AI Model Used: {agent.model_instance.model_name}")
            print(f"  📝 Analysis Result Type: {type(analysis_result)}")
            
            if isinstance(analysis_result, dict):
                print(f"  ✓ Received structured analysis with {len(analysis_result)} fields")
                for key, value in analysis_result.items():
                    print(f"    - {key}: {str(value)[:100]}...")
            else:
                print(f"  ✓ Received analysis: {str(analysis_result)[:200]}...")
            
            # Store AI response for report
            self.ai_responses.append({
                "test": test_name,
                "model": agent.model_instance.model_name,
                "input": market_data,
                "output": analysis_result,
                "timestamp": datetime.now().isoformat()
            })
            
            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")
            
        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")
            import traceback
            traceback.print_exc()

    async def test_real_strategy_development(self):
        """Test real AI strategy development"""
        test_name = "RealStrategyDevelopment"
        print(f"\n🎯 Testing {test_name}...")

        try:
            # Load configuration and create strategy developer agent
            config_manager = ConfigManager("config/test_config.yaml")
            await config_manager.load_config()
            config = await config_manager.get_config()

            ollama_hub = OllamaModelHub(config=config)
            await ollama_hub.initialize()

            class SimpleMessageBroker:
                async def publish(self, topic, message): pass
                async def subscribe(self, topic, callback): pass

            agent_manager = AgentManager(ollama_hub, SimpleMessageBroker(), config)
            await agent_manager.initialize()

            # Create strategy developer agent
            agent_id = await agent_manager.create_agent(
                role=AgentRole.STRATEGY_DEVELOPER,
                name="real_strategy_developer"
            )

            agent = await agent_manager.get_agent(agent_id)

            # Test strategy development with real AI
            strategy_request = {
                "market_conditions": "trending_upward",
                "risk_tolerance": "moderate",
                "timeframe": "daily",
                "capital": 100000
            }

            print(f"  🧠 AI Model: {agent.model_instance.model_name}")
            print(f"  📋 Developing strategy for: {strategy_request}")

            # This should call the real AI model
            strategy_result = await agent.develop_strategy(strategy_request)

            print(f"  ✓ Strategy developed successfully")
            print(f"  📊 Result type: {type(strategy_result)}")

            self.ai_responses.append({
                "test": test_name,
                "model": agent.model_instance.model_name,
                "input": strategy_request,
                "output": strategy_result,
                "timestamp": datetime.now().isoformat()
            })

            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")

        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")

    async def test_real_risk_assessment(self):
        """Test real AI risk assessment"""
        test_name = "RealRiskAssessment"
        print(f"\n⚠️ Testing {test_name}...")

        try:
            # Create risk manager agent
            config_manager = ConfigManager("config/test_config.yaml")
            await config_manager.load_config()
            config = await config_manager.get_config()

            ollama_hub = OllamaModelHub(config=config)
            await ollama_hub.initialize()

            class SimpleMessageBroker:
                async def publish(self, topic, message): pass
                async def subscribe(self, topic, callback): pass

            agent_manager = AgentManager(ollama_hub, SimpleMessageBroker(), config)
            await agent_manager.initialize()

            agent_id = await agent_manager.create_agent(
                role=AgentRole.RISK_MANAGER,
                name="real_risk_manager"
            )

            agent = await agent_manager.get_agent(agent_id)

            # Test risk assessment
            portfolio_data = {
                "total_value": 100000,
                "positions": {
                    "AAPL": {"value": 30000, "shares": 200},
                    "GOOGL": {"value": 25000, "shares": 10},
                    "TSLA": {"value": 20000, "shares": 100}
                },
                "cash": 25000
            }

            print(f"  🧠 AI Model: {agent.model_instance.model_name}")
            print(f"  💼 Assessing portfolio risk...")

            risk_result = await agent.assess_portfolio_risk(portfolio_data)

            print(f"  ✓ Risk assessment completed")

            self.ai_responses.append({
                "test": test_name,
                "model": agent.model_instance.model_name,
                "input": portfolio_data,
                "output": risk_result,
                "timestamp": datetime.now().isoformat()
            })

            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")

        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")

    async def test_multi_agent_collaboration(self):
        """Test multiple AI agents working together"""
        test_name = "MultiAgentCollaboration"
        print(f"\n🤝 Testing {test_name}...")

        try:
            # Create multiple agents
            config_manager = ConfigManager("config/test_config.yaml")
            await config_manager.load_config()
            config = await config_manager.get_config()

            ollama_hub = OllamaModelHub(config=config)
            await ollama_hub.initialize()

            class SimpleMessageBroker:
                async def publish(self, topic, message): pass
                async def subscribe(self, topic, callback): pass

            agent_manager = AgentManager(ollama_hub, SimpleMessageBroker(), config)
            await agent_manager.initialize()

            # Create team of agents
            analyst_id = await agent_manager.create_agent(AgentRole.MARKET_ANALYST, "team_analyst")
            strategist_id = await agent_manager.create_agent(AgentRole.STRATEGY_DEVELOPER, "team_strategist")
            risk_mgr_id = await agent_manager.create_agent(AgentRole.RISK_MANAGER, "team_risk_manager")

            print(f"  ✓ Created 3-agent team")
            print(f"  🔍 Market Analyst: {analyst_id}")
            print(f"  🎯 Strategy Developer: {strategist_id}")
            print(f"  ⚠️ Risk Manager: {risk_mgr_id}")

            # Test collaboration workflow
            market_data = {"symbol": "AAPL", "price": 150, "trend": "bullish"}

            analyst = await agent_manager.get_agent(analyst_id)
            strategist = await agent_manager.get_agent(strategist_id)
            risk_manager = await agent_manager.get_agent(risk_mgr_id)

            print(f"  📊 Step 1: Market Analysis...")
            analysis = await analyst.analyze_market_data(market_data)

            print(f"  🎯 Step 2: Strategy Development...")
            strategy = await strategist.develop_strategy({"analysis": analysis})

            print(f"  ⚠️ Step 3: Risk Assessment...")
            risk_assessment = await risk_manager.assess_strategy_risk({"strategy": strategy})

            print(f"  ✅ Multi-agent workflow completed successfully")

            self.ai_responses.append({
                "test": test_name,
                "models": [
                    analyst.model_instance.model_name,
                    strategist.model_instance.model_name,
                    risk_manager.model_instance.model_name
                ],
                "workflow": {
                    "analysis": analysis,
                    "strategy": strategy,
                    "risk_assessment": risk_assessment
                },
                "timestamp": datetime.now().isoformat()
            })

            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")

        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")

    def generate_ai_test_report(self):
        """Generate comprehensive AI test report"""
        total_tests = len(self.passed_tests) + len(self.failed_tests)
        success_rate = (len(self.passed_tests) / total_tests * 100) if total_tests > 0 else 0
        
        print("\n" + "=" * 60)
        print("🧠 REAL AI INTEGRATION TEST REPORT")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {len(self.passed_tests)}")
        print(f"Failed: {len(self.failed_tests)}")
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"AI Responses Captured: {len(self.ai_responses)}")
        
        if self.failed_tests:
            print("\n❌ FAILED TESTS:")
            for test_name, error in self.failed_tests:
                print(f"  - {test_name}: {error}")
        
        if self.passed_tests:
            print("\n✅ PASSED TESTS:")
            for test_name in self.passed_tests:
                print(f"  - {test_name}")
        
        if self.ai_responses:
            print("\n🧠 AI RESPONSES SUMMARY:")
            for response in self.ai_responses:
                print(f"  - {response['test']}: {response['model']}")
        
        # Save detailed report
        report = {
            'summary': {
                'total_tests': total_tests,
                'passed': len(self.passed_tests),
                'failed': len(self.failed_tests),
                'success_rate': success_rate,
                'ai_responses_count': len(self.ai_responses)
            },
            'passed_tests': self.passed_tests,
            'failed_tests': self.failed_tests,
            'ai_responses': self.ai_responses,
            'timestamp': datetime.now().isoformat()
        }
        
        with open('real_ai_test_results.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: real_ai_test_results.json")


async def main():
    """Main test runner"""
    tester = RealAITester()
    await tester.run_real_ai_tests()
    
    # Return exit code based on results
    return 0 if len(tester.failed_tests) == 0 else 1


if __name__ == "__main__":
    print("🚨 REAL AI INTEGRATION TEST")
    print("This will connect to Ollama and use actual AI models!")
    
    # Confirm before running
    response = input("Continue? (y/N): ")
    if response.lower() != 'y':
        print("Test cancelled.")
        sys.exit(0)
    
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

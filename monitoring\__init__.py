"""
Monitoring Package - Comprehensive Performance Monitoring and Alerting

This package provides real-time performance monitoring, metrics collection,
alerting, and visualization for the Advanced Ollama Trading Agent System.

Components:
- PerformanceMonitor: Main monitoring coordinator
- MetricsCollector: Metrics collection and storage
- AlertManager: Alert rules and notifications
- MetricsDashboard: Real-time visualization dashboard

Features:
- Real-time metrics collection (system, application, business)
- Prometheus metrics export
- Configurable alerting with multiple notification channels
- Interactive terminal dashboard
- Performance analytics and trending
- Health monitoring and status reporting
"""

from .system_monitor import SystemMonitor

# New comprehensive monitoring components
from .performance_monitor import (
    PerformanceMonitor,
    MetricsCollector,
    AlertManager,
    PerformanceMetric,
    SystemMetrics,
    ApplicationMetrics,
    BusinessMetrics,
    email_notification_channel,
    slack_notification_channel,
    webhook_notification_channel,
    console_notification_channel
)

from .metrics_dashboard import MetricsDashboard

__all__ = [
    # Legacy components
    'SystemMonitor',
    # New comprehensive components
    'PerformanceMonitor',
    'MetricsCollector',
    'AlertManager',
    'MetricsDashboard',
    'PerformanceMetric',
    'SystemMetrics',
    'ApplicationMetrics',
    'BusinessMetrics',
    'email_notification_channel',
    'slack_notification_channel',
    'webhook_notification_channel',
    'console_notification_channel'
]

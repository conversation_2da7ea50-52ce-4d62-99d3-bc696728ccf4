#!/usr/bin/env python3
"""
Robust AI System Test - Tests with error handling and retry logic
"""

import asyncio
import json
from datetime import datetime
from config.config_manager import ConfigManager
from models.ollama_hub import OllamaModelHub
from agents.agent_manager import AgentManager
from agents.base_agent import AgentRole

class SimpleMessageBroker:
    def __init__(self):
        self.messages = []
    
    async def publish(self, topic, message):
        self.messages.append({"topic": topic, "message": message, "timestamp": datetime.now()})
        print(f"📢 {topic}: {str(message)[:80]}...")
    
    async def subscribe(self, topic, callback):
        pass

async def test_robust_ai_system():
    """Test AI system with robust error handling"""
    
    print("🛡️ ROBUST AI SYSTEM TEST")
    print("=" * 50)
    
    try:
        # Setup
        config_manager = ConfigManager('config/test_config.yaml')
        await config_manager.load_config()
        config = await config_manager.get_config()
        
        ollama_hub = OllamaModelHub(config=config)
        await ollama_hub.initialize()
        
        message_broker = SimpleMessageBroker()
        
        agent_manager = AgentManager(ollama_hub, message_broker, config)
        await agent_manager.initialize()
        
        print("✅ System initialized")
        
        # Create AI agent team
        print("\n🏗️ Creating AI Agent Team...")
        
        # Create agents with error handling
        agents = {}
        
        try:
            analyst_id = await agent_manager.create_agent(
                role=AgentRole.MARKET_ANALYST,
                name="Robust_Market_Analyst"
            )
            agents['analyst'] = await agent_manager.get_agent(analyst_id)
            print(f"✅ Market Analyst: {agents['analyst'].model_instance.model_name}")
        except Exception as e:
            print(f"❌ Failed to create Market Analyst: {e}")
            agents['analyst'] = None
        
        try:
            strategist_id = await agent_manager.create_agent(
                role=AgentRole.STRATEGY_DEVELOPER, 
                name="Robust_Strategy_Developer"
            )
            agents['strategist'] = await agent_manager.get_agent(strategist_id)
            print(f"✅ Strategy Developer: {agents['strategist'].model_instance.model_name}")
        except Exception as e:
            print(f"❌ Failed to create Strategy Developer: {e}")
            agents['strategist'] = None
        
        try:
            risk_mgr_id = await agent_manager.create_agent(
                role=AgentRole.RISK_MANAGER,
                name="Robust_Risk_Manager"
            )
            agents['risk_manager'] = await agent_manager.get_agent(risk_mgr_id)
            print(f"✅ Risk Manager: {agents['risk_manager'].model_instance.model_name}")
        except Exception as e:
            print(f"❌ Failed to create Risk Manager: {e}")
            agents['risk_manager'] = None
        
        # Test each agent with retry logic
        results = {}
        
        # Test 1: Market Analysis (if available)
        if agents['analyst']:
            print("\n📊 TEST 1: Market Technical Analysis")
            tech_analysis_task = {
                "type": "technical_analysis",
                "symbol": "AAPL",
                "timeframe": "1h",
                "indicators": ["rsi", "macd"]
            }
            
            result = await execute_with_retry(agents['analyst'], tech_analysis_task, "Technical Analysis")
            results['technical_analysis'] = result
        
        # Test 2: Strategy Development (if available)
        if agents['strategist']:
            print("\n🎯 TEST 2: Strategy Development")
            strategy_task = {
                "type": "develop_strategy",
                "strategy_type": "momentum",
                "market_conditions": {"trend": "bullish", "volatility": "moderate"},
                "constraints": {"max_risk": 0.02, "capital": 100000}
            }
            
            result = await execute_with_retry(agents['strategist'], strategy_task, "Strategy Development")
            results['strategy_development'] = result
        
        # Test 3: Risk Assessment (if available)
        if agents['risk_manager']:
            print("\n⚠️ TEST 3: Portfolio Risk Assessment")
            risk_task = {
                "type": "assess_portfolio_risk",
                "portfolio": {
                    "total_value": 100000,
                    "positions": [
                        {"symbol": "AAPL", "value": 30000, "shares": 200},
                        {"symbol": "TSLA", "value": 25000, "shares": 100}
                    ],
                    "cash": 45000
                },
                "market_conditions": {"volatility": "moderate"}
            }
            
            result = await execute_with_retry(agents['risk_manager'], risk_task, "Risk Assessment")
            results['risk_assessment'] = result
        
        # Test 4: Risk Monitoring (if available)
        if agents['risk_manager']:
            print("\n🛡️ TEST 4: Risk Limit Monitoring")
            monitor_task = {
                "type": "monitor_risk_limits",
                "portfolio": risk_task["portfolio"],
                "limits": {
                    "max_portfolio_risk": 0.02,
                    "max_position_size": 0.1
                }
            }
            
            result = await execute_with_retry(agents['risk_manager'], monitor_task, "Risk Monitoring")
            results['risk_monitoring'] = result
        
        # Test 5: Direct Model Call (if any agent available)
        available_agent = agents.get('risk_manager') or agents.get('analyst') or agents.get('strategist')
        if available_agent:
            print("\n🧠 TEST 5: Direct AI Model Call")
            try:
                prompt = """
                Analyze this simple trading scenario:
                - Stock: AAPL at $175
                - Recent trend: +5% this week
                - Volume: Above average
                
                Provide a brief 2-sentence analysis and recommendation.
                """
                
                direct_result = await available_agent.model_instance.generate(prompt)
                if direct_result.get('success'):
                    print("✅ Direct AI Response:")
                    print(f"   {direct_result['response'][:200]}...")
                    results['direct_model_call'] = direct_result
                else:
                    print(f"❌ Direct call failed: {direct_result.get('error')}")
                    results['direct_model_call'] = direct_result
            except Exception as e:
                print(f"❌ Direct model call error: {e}")
                results['direct_model_call'] = {'error': str(e)}
        
        # Summary
        print("\n🎉 ROBUST AI SYSTEM TEST COMPLETE!")
        print("=" * 50)
        
        # Count successful tests
        successful_tests = sum(1 for result in results.values() 
                             if result and result.get('success'))
        total_tests = len(results)
        
        print(f"📊 Test Results: {successful_tests}/{total_tests} tests passed")
        print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%" if total_tests > 0 else "No tests executed")
        
        # Save detailed results
        test_summary = {
            "timestamp": datetime.now().isoformat(),
            "agents_created": {k: v is not None for k, v in agents.items()},
            "test_results": results,
            "success_rate": (successful_tests/total_tests)*100 if total_tests > 0 else 0,
            "message_broker_messages": len(message_broker.messages)
        }
        
        with open('robust_ai_system_results.json', 'w') as f:
            json.dump(test_summary, f, indent=2, default=str)
        
        print(f"\n📄 Detailed results saved to: robust_ai_system_results.json")
        
        return successful_tests >= (total_tests * 0.6)  # 60% success threshold
        
    except Exception as e:
        print(f"❌ System Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def execute_with_retry(agent, task, task_name, max_retries=2):
    """Execute a task with retry logic"""
    
    for attempt in range(max_retries + 1):
        try:
            print(f"  🔄 Attempt {attempt + 1}/{max_retries + 1} for {task_name}")
            
            result = await agent.execute_task(task)
            
            if result and result.get('success'):
                print(f"  ✅ {task_name} Success: {result.get('status', 'completed')}")
                return result
            else:
                error_msg = result.get('error', 'Unknown error') if result else 'No result'
                print(f"  ⚠️ {task_name} Failed (attempt {attempt + 1}): {error_msg}")
                
                if attempt < max_retries:
                    print(f"  ⏳ Retrying in 2 seconds...")
                    await asyncio.sleep(2)
                else:
                    print(f"  ❌ {task_name} Failed after {max_retries + 1} attempts")
                    return result
                    
        except Exception as e:
            print(f"  ❌ {task_name} Exception (attempt {attempt + 1}): {e}")
            if attempt < max_retries:
                print(f"  ⏳ Retrying in 2 seconds...")
                await asyncio.sleep(2)
            else:
                return {'success': False, 'error': str(e)}
    
    return {'success': False, 'error': 'Max retries exceeded'}

if __name__ == "__main__":
    success = asyncio.run(test_robust_ai_system())
    if success:
        print("\n🎉 ROBUST AI SYSTEM TEST SUCCESSFUL!")
    else:
        print("\n⚠️ ROBUST AI SYSTEM TEST NEEDS IMPROVEMENT")

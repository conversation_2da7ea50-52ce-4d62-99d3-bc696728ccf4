"""
System Router - System status and control endpoints
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


# Pydantic models for request/response validation

class SystemStatus(BaseModel):
    """System status response model"""
    status: str = Field(..., description="Overall system status")
    timestamp: datetime = Field(..., description="Status timestamp")
    uptime_seconds: float = Field(..., description="System uptime in seconds")
    version: str = Field(..., description="System version")
    components: Dict[str, Dict[str, Any]] = Field(..., description="Component statuses")
    performance: Dict[str, Any] = Field(..., description="Performance metrics")
    resources: Dict[str, Any] = Field(..., description="Resource usage")


class ComponentStatus(BaseModel):
    """Component status model"""
    name: str = Field(..., description="Component name")
    status: str = Field(..., description="Component status")
    running: bool = Field(..., description="Whether component is running")
    initialized: bool = Field(..., description="Whether component is initialized")
    last_update: Optional[datetime] = Field(None, description="Last status update")
    metrics: Dict[str, Any] = Field(default_factory=dict, description="Component metrics")
    errors: List[str] = Field(default_factory=list, description="Recent errors")


class SystemCommand(BaseModel):
    """System command request model"""
    command: str = Field(..., description="Command to execute")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Command parameters")
    force: bool = Field(False, description="Force command execution")


class SystemConfiguration(BaseModel):
    """System configuration model"""
    component: str = Field(..., description="Component name")
    configuration: Dict[str, Any] = Field(..., description="Configuration data")
    restart_required: bool = Field(False, description="Whether restart is required")


def create_router(trading_system=None, auth_manager=None) -> APIRouter:
    """Create system router with dependencies"""
    
    router = APIRouter()
    
    @router.get("/status", response_model=SystemStatus, summary="Get system status")
    async def get_system_status(
        detailed: bool = Query(False, description="Include detailed component information"),
        user=Depends(auth_manager.require_permission("system:read")) if auth_manager else None
    ):
        """
        Get comprehensive system status including all components.
        
        Returns:
        - Overall system health
        - Component statuses
        - Performance metrics
        - Resource usage
        """
        try:
            if not trading_system:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Trading system not available"
                )
            
            # Get system status
            system_status = await trading_system.get_system_status()
            
            # Get component statuses
            components = {}
            
            # Core components
            if hasattr(trading_system, 'agent_manager') and trading_system.agent_manager:
                components['agent_manager'] = await _get_component_status(
                    trading_system.agent_manager, 'Agent Manager', detailed
                )
            
            if hasattr(trading_system, 'strategy_manager') and trading_system.strategy_manager:
                components['strategy_manager'] = await _get_component_status(
                    trading_system.strategy_manager, 'Strategy Manager', detailed
                )
            
            if hasattr(trading_system, 'risk_manager') and trading_system.risk_manager:
                components['risk_manager'] = await _get_component_status(
                    trading_system.risk_manager, 'Risk Manager', detailed
                )
            
            if hasattr(trading_system, 'execution_engine') and trading_system.execution_engine:
                components['execution_engine'] = await _get_component_status(
                    trading_system.execution_engine, 'Execution Engine', detailed
                )
            
            if hasattr(trading_system, 'portfolio_manager') and trading_system.portfolio_manager:
                components['portfolio_manager'] = await _get_component_status(
                    trading_system.portfolio_manager, 'Portfolio Manager', detailed
                )
            
            if hasattr(trading_system, 'learning_manager') and trading_system.learning_manager:
                components['learning_manager'] = await _get_component_status(
                    trading_system.learning_manager, 'Learning Manager', detailed
                )
            
            if hasattr(trading_system, 'database_coordinator') and trading_system.database_coordinator:
                components['database_coordinator'] = await _get_component_status(
                    trading_system.database_coordinator, 'Database Coordinator', detailed
                )
            
            if hasattr(trading_system, 'analytics_engine') and trading_system.analytics_engine:
                components['analytics_engine'] = await _get_component_status(
                    trading_system.analytics_engine, 'Analytics Engine', detailed
                )
            
            # Calculate overall status
            overall_status = "healthy"
            if not all(comp.get('running', False) for comp in components.values()):
                overall_status = "degraded"
            if not any(comp.get('running', False) for comp in components.values()):
                overall_status = "unhealthy"
            
            # Get performance metrics
            performance = await _get_performance_metrics(trading_system)
            
            # Get resource usage
            resources = await _get_resource_usage()
            
            return SystemStatus(
                status=overall_status,
                timestamp=datetime.now(),
                uptime_seconds=system_status.get('uptime_seconds', 0),
                version=system_status.get('version', '1.0.0'),
                components=components,
                performance=performance,
                resources=resources
            )
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get system status"
            )
    
    @router.get("/components", response_model=List[ComponentStatus], summary="Get component statuses")
    async def get_component_statuses(
        component: Optional[str] = Query(None, description="Specific component name"),
        user=Depends(auth_manager.require_permission("system:read")) if auth_manager else None
    ):
        """
        Get detailed status information for all or specific components.
        """
        try:
            if not trading_system:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Trading system not available"
                )
            
            components = []
            
            # Define all components
            component_map = {
                'agent_manager': (trading_system.agent_manager, 'Agent Manager'),
                'strategy_manager': (trading_system.strategy_manager, 'Strategy Manager'),
                'risk_manager': (trading_system.risk_manager, 'Risk Manager'),
                'execution_engine': (trading_system.execution_engine, 'Execution Engine'),
                'portfolio_manager': (trading_system.portfolio_manager, 'Portfolio Manager'),
                'learning_manager': (trading_system.learning_manager, 'Learning Manager'),
                'database_coordinator': (trading_system.database_coordinator, 'Database Coordinator'),
                'analytics_engine': (trading_system.analytics_engine, 'Analytics Engine')
            }
            
            # Filter by specific component if requested
            if component:
                if component not in component_map:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"Component '{component}' not found"
                    )
                component_map = {component: component_map[component]}
            
            # Get status for each component
            for comp_name, (comp_instance, display_name) in component_map.items():
                if comp_instance:
                    comp_status = await _get_detailed_component_status(
                        comp_instance, display_name, comp_name
                    )
                    components.append(comp_status)
            
            return components
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting component statuses: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get component statuses"
            )
    
    @router.post("/commands", summary="Execute system command")
    async def execute_system_command(
        command: SystemCommand,
        user=Depends(auth_manager.require_permission("system:admin")) if auth_manager else None
    ):
        """
        Execute system-level commands.
        
        Available commands:
        - start: Start the trading system
        - stop: Stop the trading system
        - restart: Restart the trading system
        - reload_config: Reload system configuration
        - clear_cache: Clear system caches
        - run_diagnostics: Run system diagnostics
        """
        try:
            if not trading_system:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Trading system not available"
                )
            
            result = await _execute_command(trading_system, command)
            
            return {
                "success": True,
                "command": command.command,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error executing command {command.command}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to execute command: {str(e)}"
            )
    
    @router.get("/metrics", summary="Get system metrics")
    async def get_system_metrics(
        component: Optional[str] = Query(None, description="Specific component"),
        metric_type: Optional[str] = Query(None, description="Specific metric type"),
        user=Depends(auth_manager.require_permission("system:read")) if auth_manager else None
    ):
        """
        Get detailed system performance metrics.
        """
        try:
            if not trading_system:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Trading system not available"
                )
            
            metrics = await _get_detailed_metrics(trading_system, component, metric_type)
            
            return {
                "metrics": metrics,
                "timestamp": datetime.now().isoformat(),
                "component_filter": component,
                "metric_type_filter": metric_type
            }
            
        except Exception as e:
            logger.error(f"Error getting system metrics: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get system metrics"
            )
    
    @router.get("/logs", summary="Get system logs")
    async def get_system_logs(
        level: str = Query("INFO", description="Log level filter"),
        component: Optional[str] = Query(None, description="Component filter"),
        limit: int = Query(100, description="Maximum number of log entries"),
        user=Depends(auth_manager.require_permission("system:read")) if auth_manager else None
    ):
        """
        Get recent system logs with filtering options.
        """
        try:
            # This would integrate with the logging system
            # For now, return a placeholder response
            
            logs = [
                {
                    "timestamp": datetime.now().isoformat(),
                    "level": "INFO",
                    "component": "system",
                    "message": "System logs endpoint accessed",
                    "details": {}
                }
            ]
            
            return {
                "logs": logs,
                "total_count": len(logs),
                "filters": {
                    "level": level,
                    "component": component,
                    "limit": limit
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting system logs: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get system logs"
            )
    
    return router


# Helper functions

async def _get_component_status(component, name: str, detailed: bool = False) -> Dict[str, Any]:
    """Get status for a component"""
    try:
        status_data = {
            "name": name,
            "running": getattr(component, 'running', False),
            "initialized": getattr(component, 'initialized', False),
            "status": "healthy" if getattr(component, 'running', False) else "stopped"
        }
        
        if detailed and hasattr(component, 'get_stats'):
            try:
                stats = await component.get_stats()
                status_data["metrics"] = stats
            except Exception as e:
                status_data["errors"] = [str(e)]
        
        return status_data
        
    except Exception as e:
        return {
            "name": name,
            "running": False,
            "initialized": False,
            "status": "error",
            "errors": [str(e)]
        }


async def _get_detailed_component_status(component, display_name: str, comp_name: str) -> ComponentStatus:
    """Get detailed component status"""
    try:
        running = getattr(component, 'running', False)
        initialized = getattr(component, 'initialized', False)
        
        # Determine status
        if running and initialized:
            comp_status = "healthy"
        elif initialized:
            comp_status = "stopped"
        else:
            comp_status = "uninitialized"
        
        # Get metrics if available
        metrics = {}
        errors = []
        
        if hasattr(component, 'get_stats'):
            try:
                metrics = await component.get_stats()
            except Exception as e:
                errors.append(f"Failed to get metrics: {str(e)}")
        
        return ComponentStatus(
            name=comp_name,
            status=comp_status,
            running=running,
            initialized=initialized,
            last_update=datetime.now(),
            metrics=metrics,
            errors=errors
        )
        
    except Exception as e:
        return ComponentStatus(
            name=comp_name,
            status="error",
            running=False,
            initialized=False,
            last_update=datetime.now(),
            metrics={},
            errors=[str(e)]
        )


async def _get_performance_metrics(trading_system) -> Dict[str, Any]:
    """Get system performance metrics"""
    try:
        # This would collect performance metrics from various components
        return {
            "cpu_usage": 0.0,
            "memory_usage": 0.0,
            "disk_usage": 0.0,
            "network_io": 0.0,
            "active_threads": 0,
            "request_rate": 0.0,
            "error_rate": 0.0
        }
        
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        return {}


async def _get_resource_usage() -> Dict[str, Any]:
    """Get system resource usage"""
    try:
        import psutil
        
        # Get CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Get memory usage
        memory = psutil.virtual_memory()
        
        # Get disk usage
        disk = psutil.disk_usage('/')
        
        return {
            "cpu": {
                "usage_percent": cpu_percent,
                "cores": psutil.cpu_count()
            },
            "memory": {
                "total_bytes": memory.total,
                "used_bytes": memory.used,
                "available_bytes": memory.available,
                "usage_percent": memory.percent
            },
            "disk": {
                "total_bytes": disk.total,
                "used_bytes": disk.used,
                "free_bytes": disk.free,
                "usage_percent": (disk.used / disk.total) * 100
            }
        }
        
    except ImportError:
        # psutil not available
        return {
            "cpu": {"usage_percent": 0.0, "cores": 1},
            "memory": {"usage_percent": 0.0},
            "disk": {"usage_percent": 0.0}
        }
    except Exception as e:
        logger.error(f"Error getting resource usage: {e}")
        return {}


async def _execute_command(trading_system, command: SystemCommand) -> Dict[str, Any]:
    """Execute system command"""
    try:
        cmd = command.command.lower()
        
        if cmd == "start":
            if trading_system.running:
                return {"message": "System is already running"}
            
            success = await trading_system.start()
            return {
                "message": "System started successfully" if success else "Failed to start system",
                "success": success
            }
        
        elif cmd == "stop":
            if not trading_system.running:
                return {"message": "System is already stopped"}
            
            success = await trading_system.stop()
            return {
                "message": "System stopped successfully" if success else "Failed to stop system",
                "success": success
            }
        
        elif cmd == "restart":
            # Stop then start
            if trading_system.running:
                await trading_system.stop()
                await asyncio.sleep(2)  # Brief pause
            
            success = await trading_system.start()
            return {
                "message": "System restarted successfully" if success else "Failed to restart system",
                "success": success
            }
        
        elif cmd == "reload_config":
            # This would reload configuration
            return {"message": "Configuration reloaded", "success": True}
        
        elif cmd == "clear_cache":
            # This would clear system caches
            return {"message": "Caches cleared", "success": True}
        
        elif cmd == "run_diagnostics":
            # This would run system diagnostics
            return {
                "message": "Diagnostics completed",
                "success": True,
                "diagnostics": {
                    "system_health": "good",
                    "component_status": "all_operational",
                    "performance": "optimal"
                }
            }
        
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unknown command: {command.command}"
            )
            
    except Exception as e:
        logger.error(f"Error executing command {command.command}: {e}")
        raise


async def _get_detailed_metrics(trading_system, component: str = None, 
                              metric_type: str = None) -> Dict[str, Any]:
    """Get detailed system metrics"""
    try:
        metrics = {}
        
        # Get metrics from all components
        components = {
            'agent_manager': trading_system.agent_manager,
            'strategy_manager': trading_system.strategy_manager,
            'risk_manager': trading_system.risk_manager,
            'execution_engine': trading_system.execution_engine,
            'portfolio_manager': trading_system.portfolio_manager,
            'learning_manager': trading_system.learning_manager,
            'database_coordinator': trading_system.database_coordinator,
            'analytics_engine': trading_system.analytics_engine
        }
        
        for comp_name, comp_instance in components.items():
            if component and comp_name != component:
                continue
                
            if comp_instance and hasattr(comp_instance, 'get_stats'):
                try:
                    comp_metrics = await comp_instance.get_stats()
                    
                    # Filter by metric type if specified
                    if metric_type:
                        comp_metrics = {
                            k: v for k, v in comp_metrics.items()
                            if metric_type.lower() in k.lower()
                        }
                    
                    metrics[comp_name] = comp_metrics
                    
                except Exception as e:
                    metrics[comp_name] = {"error": str(e)}
        
        return metrics
        
    except Exception as e:
        logger.error(f"Error getting detailed metrics: {e}")
        return {}

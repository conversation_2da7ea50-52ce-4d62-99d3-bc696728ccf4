"""
Performance Tracker - Tracks and analyzes portfolio performance
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)


@dataclass
class PerformanceSnapshot:
    """Performance snapshot at a point in time"""
    timestamp: float
    portfolio_value: float
    cash: float
    total_return: float
    daily_return: float
    cumulative_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float


class PerformanceTracker:
    """
    Tracks and analyzes portfolio performance metrics.
    
    Responsibilities:
    - Calculate returns and risk metrics
    - Track performance over time
    - Generate performance reports
    - Benchmark comparison
    - Attribution analysis
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.performance_config = config.get('performance_tracking', {})
        
        # Performance data
        self.value_history: List[Dict[str, float]] = []
        self.return_history: List[float] = []
        self.performance_snapshots: List[PerformanceSnapshot] = []
        
        # Benchmark data
        self.benchmark_symbol = self.performance_config.get('benchmark', 'SPY')
        self.benchmark_history: List[float] = []
        
        # Risk-free rate
        self.risk_free_rate = self.performance_config.get('risk_free_rate', 0.02)  # 2%
        
        # Performance metrics
        self.current_metrics: Dict[str, float] = {}
        
        # State
        self.initialized = False
        self.running = False
        self.initial_value: Optional[float] = None
        self.last_value: Optional[float] = None
        self.peak_value: float = 0.0
        
    async def initialize(self) -> bool:
        """Initialize performance tracker"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Performance Tracker...")
            logger.info(f"  Benchmark: {self.benchmark_symbol}")
            logger.info(f"  Risk-free rate: {self.risk_free_rate:.2%}")
            
            self.initialized = True
            logger.info("✓ Performance Tracker initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Performance Tracker: {e}")
            return False
    
    async def start(self) -> bool:
        """Start performance tracking"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        self.running = True
        logger.info("✓ Performance Tracker started")
        return True
    
    async def stop(self) -> bool:
        """Stop performance tracking"""
        self.running = False
        logger.info("✓ Performance Tracker stopped")
        return True
    
    async def update_portfolio_value(self, portfolio_value: float, cash: float = 0.0) -> None:
        """Update portfolio value and calculate metrics"""
        try:
            current_time = time.time()
            
            # Set initial value if first update
            if self.initial_value is None:
                self.initial_value = portfolio_value
                self.last_value = portfolio_value
                self.peak_value = portfolio_value
                
                # Record initial value
                self.value_history.append({
                    'timestamp': current_time,
                    'portfolio_value': portfolio_value,
                    'cash': cash
                })
                return
            
            # Calculate returns
            daily_return = (portfolio_value - self.last_value) / self.last_value if self.last_value > 0 else 0.0
            total_return = (portfolio_value - self.initial_value) / self.initial_value if self.initial_value > 0 else 0.0
            
            # Update peak value
            if portfolio_value > self.peak_value:
                self.peak_value = portfolio_value
            
            # Record value and return
            self.value_history.append({
                'timestamp': current_time,
                'portfolio_value': portfolio_value,
                'cash': cash
            })
            
            self.return_history.append(daily_return)
            
            # Limit history size
            if len(self.value_history) > 10000:
                self.value_history = self.value_history[-10000:]
            if len(self.return_history) > 10000:
                self.return_history = self.return_history[-10000:]
            
            # Calculate current metrics
            await self._calculate_current_metrics(portfolio_value, total_return, daily_return)
            
            # Create performance snapshot
            snapshot = await self._create_performance_snapshot(
                current_time, portfolio_value, cash, total_return, daily_return
            )
            self.performance_snapshots.append(snapshot)
            
            # Limit snapshots
            if len(self.performance_snapshots) > 1000:
                self.performance_snapshots = self.performance_snapshots[-1000:]
            
            # Update last value
            self.last_value = portfolio_value
            
            logger.debug(f"Portfolio value updated: ${portfolio_value:,.2f} (Return: {daily_return:.2%})")
            
        except Exception as e:
            logger.error(f"Error updating portfolio value: {e}")
    
    async def record_trade(self, trade_data: Dict[str, Any]) -> None:
        """Record trade for performance attribution"""
        try:
            # Add trade to history for attribution analysis
            trade_record = {
                'timestamp': time.time(),
                'symbol': trade_data.get('symbol'),
                'side': trade_data.get('side'),
                'quantity': trade_data.get('quantity'),
                'price': trade_data.get('price'),
                'value': trade_data.get('value'),
                'commission': trade_data.get('commission', 0.0)
            }
            
            # Store trade record (could be used for attribution analysis)
            logger.debug(f"Trade recorded: {trade_record['symbol']} {trade_record['side']} {trade_record['quantity']}")
            
        except Exception as e:
            logger.error(f"Error recording trade: {e}")
    
    async def record_rebalance(self, rebalance_data: Dict[str, Any]) -> None:
        """Record rebalance event"""
        try:
            rebalance_record = {
                'timestamp': time.time(),
                'trades_executed': rebalance_data.get('trades_executed', 0),
                'total_value_traded': rebalance_data.get('total_value_traded', 0.0),
                'rebalance_cost': rebalance_data.get('rebalance_cost', 0.0)
            }
            
            logger.debug(f"Rebalance recorded: {rebalance_record['trades_executed']} trades")
            
        except Exception as e:
            logger.error(f"Error recording rebalance: {e}")
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        try:
            return self.current_metrics.copy()
        except Exception as e:
            logger.error(f"Error getting metrics: {e}")
            return {}
    
    async def get_current_metrics(self) -> Dict[str, float]:
        """Get current performance metrics (simplified)"""
        try:
            return self.current_metrics.copy()
        except Exception as e:
            logger.error(f"Error getting current metrics: {e}")
            return {}
    
    async def get_performance_report(self, period_days: int = 30) -> Dict[str, Any]:
        """Generate performance report for specified period"""
        try:
            if not self.value_history:
                return {'error': 'No performance data available'}
            
            # Filter data for period
            cutoff_time = time.time() - (period_days * 24 * 3600)
            period_data = [
                entry for entry in self.value_history 
                if entry['timestamp'] >= cutoff_time
            ]
            
            if len(period_data) < 2:
                return {'error': 'Insufficient data for period'}
            
            # Calculate period metrics
            start_value = period_data[0]['portfolio_value']
            end_value = period_data[-1]['portfolio_value']
            period_return = (end_value - start_value) / start_value if start_value > 0 else 0.0
            
            # Calculate period volatility
            period_returns = []
            for i in range(1, len(period_data)):
                prev_value = period_data[i-1]['portfolio_value']
                curr_value = period_data[i]['portfolio_value']
                daily_ret = (curr_value - prev_value) / prev_value if prev_value > 0 else 0.0
                period_returns.append(daily_ret)
            
            period_volatility = np.std(period_returns) * np.sqrt(252) if period_returns else 0.0
            
            # Calculate max drawdown for period
            period_max_dd = await self._calculate_max_drawdown(period_data)
            
            # Calculate Sharpe ratio
            avg_return = np.mean(period_returns) * 252 if period_returns else 0.0
            sharpe_ratio = (avg_return - self.risk_free_rate) / period_volatility if period_volatility > 0 else 0.0
            
            return {
                'period_days': period_days,
                'start_date': period_data[0]['timestamp'],
                'end_date': period_data[-1]['timestamp'],
                'start_value': start_value,
                'end_value': end_value,
                'period_return': period_return,
                'annualized_return': avg_return,
                'volatility': period_volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': period_max_dd,
                'total_observations': len(period_data)
            }
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {'error': str(e)}
    
    async def get_drawdown_analysis(self) -> Dict[str, Any]:
        """Get detailed drawdown analysis"""
        try:
            if not self.value_history:
                return {'error': 'No data available'}
            
            values = [entry['portfolio_value'] for entry in self.value_history]
            timestamps = [entry['timestamp'] for entry in self.value_history]
            
            # Calculate running maximum
            running_max = np.maximum.accumulate(values)
            
            # Calculate drawdowns
            drawdowns = [(values[i] - running_max[i]) / running_max[i] for i in range(len(values))]
            
            # Find drawdown periods
            drawdown_periods = []
            in_drawdown = False
            start_idx = 0
            
            for i, dd in enumerate(drawdowns):
                if dd < 0 and not in_drawdown:
                    # Start of drawdown
                    in_drawdown = True
                    start_idx = i
                elif dd >= 0 and in_drawdown:
                    # End of drawdown
                    in_drawdown = False
                    period_drawdowns = drawdowns[start_idx:i]
                    max_dd = min(period_drawdowns)
                    duration = timestamps[i-1] - timestamps[start_idx]
                    
                    drawdown_periods.append({
                        'start_time': timestamps[start_idx],
                        'end_time': timestamps[i-1],
                        'duration_days': duration / (24 * 3600),
                        'max_drawdown': abs(max_dd),
                        'start_value': values[start_idx],
                        'trough_value': min(values[start_idx:i]),
                        'recovery_value': values[i-1]
                    })
            
            # Overall statistics
            max_drawdown = abs(min(drawdowns)) if drawdowns else 0.0
            avg_drawdown = np.mean([abs(dd) for dd in drawdowns if dd < 0]) if any(dd < 0 for dd in drawdowns) else 0.0
            
            return {
                'max_drawdown': max_drawdown,
                'average_drawdown': avg_drawdown,
                'current_drawdown': abs(drawdowns[-1]) if drawdowns else 0.0,
                'drawdown_periods': drawdown_periods,
                'total_drawdown_periods': len(drawdown_periods)
            }
            
        except Exception as e:
            logger.error(f"Error in drawdown analysis: {e}")
            return {'error': str(e)}
    
    # Private methods
    
    async def _calculate_current_metrics(self, portfolio_value: float, total_return: float, daily_return: float):
        """Calculate current performance metrics"""
        try:
            # Basic metrics
            self.current_metrics = {
                'portfolio_value': portfolio_value,
                'total_return': total_return,
                'daily_return': daily_return
            }
            
            if len(self.return_history) > 1:
                returns_array = np.array(self.return_history)
                
                # Volatility (annualized)
                volatility = np.std(returns_array) * np.sqrt(252)
                self.current_metrics['volatility'] = volatility
                
                # Sharpe ratio
                avg_return = np.mean(returns_array) * 252
                sharpe_ratio = (avg_return - self.risk_free_rate) / volatility if volatility > 0 else 0.0
                self.current_metrics['sharpe_ratio'] = sharpe_ratio
                self.current_metrics['annualized_return'] = avg_return
                
                # Maximum drawdown
                max_drawdown = await self._calculate_max_drawdown(self.value_history)
                self.current_metrics['max_drawdown'] = max_drawdown
                
                # Current drawdown
                current_drawdown = (self.peak_value - portfolio_value) / self.peak_value if self.peak_value > 0 else 0.0
                self.current_metrics['current_drawdown'] = current_drawdown
                
                # Win rate
                winning_days = sum(1 for r in returns_array if r > 0)
                win_rate = winning_days / len(returns_array) if len(returns_array) > 0 else 0.0
                self.current_metrics['win_rate'] = win_rate
                
                # Sortino ratio (downside deviation)
                downside_returns = [r for r in returns_array if r < 0]
                if downside_returns:
                    downside_deviation = np.std(downside_returns) * np.sqrt(252)
                    sortino_ratio = (avg_return - self.risk_free_rate) / downside_deviation if downside_deviation > 0 else 0.0
                    self.current_metrics['sortino_ratio'] = sortino_ratio
                else:
                    self.current_metrics['sortino_ratio'] = 0.0
            
        except Exception as e:
            logger.error(f"Error calculating current metrics: {e}")
    
    async def _calculate_max_drawdown(self, value_history: List[Dict[str, float]]) -> float:
        """Calculate maximum drawdown from value history"""
        try:
            if len(value_history) < 2:
                return 0.0
            
            values = [entry['portfolio_value'] for entry in value_history]
            running_max = np.maximum.accumulate(values)
            drawdowns = [(values[i] - running_max[i]) / running_max[i] for i in range(len(values))]
            
            return abs(min(drawdowns)) if drawdowns else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating max drawdown: {e}")
            return 0.0
    
    async def _create_performance_snapshot(self, timestamp: float, portfolio_value: float, 
                                         cash: float, total_return: float, daily_return: float) -> PerformanceSnapshot:
        """Create performance snapshot"""
        try:
            # Calculate cumulative return
            cumulative_return = total_return
            
            # Get current metrics
            volatility = self.current_metrics.get('volatility', 0.0)
            sharpe_ratio = self.current_metrics.get('sharpe_ratio', 0.0)
            max_drawdown = self.current_metrics.get('max_drawdown', 0.0)
            win_rate = self.current_metrics.get('win_rate', 0.0)
            
            return PerformanceSnapshot(
                timestamp=timestamp,
                portfolio_value=portfolio_value,
                cash=cash,
                total_return=total_return,
                daily_return=daily_return,
                cumulative_return=cumulative_return,
                volatility=volatility,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate
            )
            
        except Exception as e:
            logger.error(f"Error creating performance snapshot: {e}")
            return PerformanceSnapshot(
                timestamp=timestamp,
                portfolio_value=portfolio_value,
                cash=cash,
                total_return=0.0,
                daily_return=0.0,
                cumulative_return=0.0,
                volatility=0.0,
                sharpe_ratio=0.0,
                max_drawdown=0.0,
                win_rate=0.0
            )

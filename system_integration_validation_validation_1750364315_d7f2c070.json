{"validation_id": "validation_**********_d7f2c070", "validation_level": "comprehensive", "overall_status": "partial", "overall_score": 0.7985549716739144, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "completed", "integration_score": 0.8658785792572684, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "team_manager", "component_type": "core", "status": "completed", "integration_score": 0.8237537039026689, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "data_manager", "component_type": "core", "status": "completed", "integration_score": 0.8599007169855997, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "analytics_engine", "component_type": "core", "status": "completed", "integration_score": 0.8732864425397417, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "completed", "integration_score": 0.7970794109119821, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "completed", "integration_score": 0.8638473509541725, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "completed", "integration_score": 0.7886451451844855, "error_count": 0, "warnings": ["Integration issues in portfolio_manager"], "dependencies_met": "False"}, {"component_name": "risk_manager", "component_type": "core", "status": "completed", "integration_score": 0.7739047712606675, "error_count": 0, "warnings": ["Integration issues in risk_manager"], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "completed", "integration_score": 0.8386972278691494, "error_count": 0, "warnings": ["Integration issues in strategy_manager"], "dependencies_met": "True"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8881350086189425, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8796619010127146, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8265321567882368, "error_count": 0, "warnings": ["Integration issues in self_improvement_engine"], "dependencies_met": "True"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "completed", "integration_score": 0.8178341379310419, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "completed", "integration_score": 0.7876741117226669, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.901987160751831, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "completed", "integration_score": 0.8094630662741824, "error_count": 0, "warnings": ["Integration issues in ai_coordinator"], "dependencies_met": "True"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "completed", "integration_score": 0.884348634133513, "error_count": 0, "warnings": ["Integration issues in configuration_manager"], "dependencies_met": "True"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "completed", "integration_score": 0.8973597812839454, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "completed", "integration_score": 0.8320520295303641, "error_count": 0, "warnings": ["Integration issues in paper_trading_engine"], "dependencies_met": "False"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "completed", "integration_score": 0.7646723604578163, "error_count": 0, "warnings": [], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.9830732782166585, "data_manager": 0.7438382825061414, "analytics_engine": 0.8962397071638878, "ollama_hub": 0.7883478939553271, "execution_engine": 0.7821088898905411, "portfolio_manager": 0.645402996450823, "risk_manager": 0.8925758422528167, "strategy_manager": 0.8318402108238909, "competitive_framework": 0.8427183486457199, "tournament_framework": 0.8674134853368496, "self_improvement_engine": 0.8377861264221957, "regime_adaptation_system": 0.7190884764040867, "performance_optimizer": 0.8935310412731436, "advanced_trading_engine": 0.8088611015089757, "ai_coordinator": 0.8408462255835344, "configuration_manager": 0.8849366844138042, "mock_data_providers": 0.8073404621572303, "paper_trading_engine": 0.6958227420381977, "logging_audit_system": 0.7728687726923789}, "team_manager": {"system_coordinator": 0.842341991247281, "team_manager": 1.0, "data_manager": 0.8836567799893347, "analytics_engine": 0.8405564626229688, "ollama_hub": 0.717893401094917, "execution_engine": 0.8882730287445089, "portfolio_manager": 0.6801142345657682, "risk_manager": 0.6353931696447134, "strategy_manager": 0.8872643473096007, "competitive_framework": 0.8284072281463277, "tournament_framework": 0.6334185759669594, "self_improvement_engine": 0.7577815794929521, "regime_adaptation_system": 0.8591080020324267, "performance_optimizer": 0.6002956482243851, "advanced_trading_engine": 0.6921407642898939, "ai_coordinator": 0.6731395987885381, "configuration_manager": 0.6477340514249447, "mock_data_providers": 0.7102180663959341, "paper_trading_engine": 0.8506828321457036, "logging_audit_system": 0.6227584411946255}, "data_manager": {"system_coordinator": 0.6215318447166746, "team_manager": 0.8578734351488898, "data_manager": 1.0, "analytics_engine": 0.8311091544584664, "ollama_hub": 0.8216750074149655, "execution_engine": 0.7798913988713815, "portfolio_manager": 0.6305881895488867, "risk_manager": 0.6295993558479598, "strategy_manager": 0.8634998968259413, "competitive_framework": 0.6027494573696889, "tournament_framework": 0.870004290022506, "self_improvement_engine": 0.8322570988826421, "regime_adaptation_system": 0.7798903344704224, "performance_optimizer": 0.8235899802025343, "advanced_trading_engine": 0.7610650693172458, "ai_coordinator": 0.65513791073791, "configuration_manager": 0.6973681664001561, "mock_data_providers": 0.7605062358551893, "paper_trading_engine": 0.859081073553315, "logging_audit_system": 0.8975360112886662}, "analytics_engine": {"system_coordinator": 0.8119088185777001, "team_manager": 0.7114926617283353, "data_manager": 0.769920780347334, "analytics_engine": 1.0, "ollama_hub": 0.6540203641352986, "execution_engine": 0.8061958935418393, "portfolio_manager": 0.6751272236358006, "risk_manager": 0.6273319170461243, "strategy_manager": 0.9768875860761971, "competitive_framework": 0.6975611073804965, "tournament_framework": 0.6733625777166976, "self_improvement_engine": 0.7079789883551154, "regime_adaptation_system": 0.7396008498950221, "performance_optimizer": 0.7654665354172296, "advanced_trading_engine": 0.6404371876078696, "ai_coordinator": 0.7232176985970025, "configuration_manager": 0.8724890740736138, "mock_data_providers": 0.6661904174940774, "paper_trading_engine": 0.8813511185758858, "logging_audit_system": 0.8321835447014976}, "ollama_hub": {"system_coordinator": 0.7178433148230758, "team_manager": 0.891487239921879, "data_manager": 0.8580506576124961, "analytics_engine": 0.7807791083145647, "ollama_hub": 1.0, "execution_engine": 0.6771854047279484, "portfolio_manager": 0.734276902537138, "risk_manager": 0.6745011094981174, "strategy_manager": 0.608430291251616, "competitive_framework": 0.6158726710991038, "tournament_framework": 0.6735932100063581, "self_improvement_engine": 0.7068731082280943, "regime_adaptation_system": 0.8402687297462942, "performance_optimizer": 0.6710308192621584, "advanced_trading_engine": 0.6699625448832685, "ai_coordinator": 0.6981439029335441, "configuration_manager": 0.7628233795232351, "mock_data_providers": 0.6707736247196836, "paper_trading_engine": 0.8951956585308964, "logging_audit_system": 0.7438940666950032}, "execution_engine": {"system_coordinator": 0.8223113772574645, "team_manager": 0.6050704304260488, "data_manager": 0.7233529345576032, "analytics_engine": 0.8649906423090641, "ollama_hub": 0.8085372215202693, "execution_engine": 1.0, "portfolio_manager": 0.9447017866364567, "risk_manager": 0.7120674383956848, "strategy_manager": 0.6878351859559945, "competitive_framework": 0.8684470656493033, "tournament_framework": 0.7268550672583454, "self_improvement_engine": 0.886691181021315, "regime_adaptation_system": 0.8422272221500917, "performance_optimizer": 0.770331805189896, "advanced_trading_engine": 0.8349893255389136, "ai_coordinator": 0.727243006514533, "configuration_manager": 0.8612479338548724, "mock_data_providers": 0.6077039221495577, "paper_trading_engine": 0.6282675965542703, "logging_audit_system": 0.6597817812778954}, "portfolio_manager": {"system_coordinator": 0.6829071033991888, "team_manager": 0.7525106860597703, "data_manager": 0.6367616449158384, "analytics_engine": 0.8512109706962795, "ollama_hub": 0.6495185269515368, "execution_engine": 0.8128715990460194, "portfolio_manager": 1.0, "risk_manager": 0.8154795916302384, "strategy_manager": 0.6562797754766103, "competitive_framework": 0.8313485702333052, "tournament_framework": 0.6823424718462262, "self_improvement_engine": 0.7668872877818864, "regime_adaptation_system": 0.8471928534444888, "performance_optimizer": 0.8306953161335826, "advanced_trading_engine": 0.665844805937427, "ai_coordinator": 0.7156232267110787, "configuration_manager": 0.7752600287925393, "mock_data_providers": 0.7710158516140591, "paper_trading_engine": 0.7046699450192306, "logging_audit_system": 0.8016484657075206}, "risk_manager": {"system_coordinator": 0.8527864950112943, "team_manager": 0.8066485909976953, "data_manager": 0.7629862289328372, "analytics_engine": 0.7398075597209881, "ollama_hub": 0.7118689973045091, "execution_engine": 0.6938318200464334, "portfolio_manager": 0.7617714789460358, "risk_manager": 1.0, "strategy_manager": 0.8534362842662153, "competitive_framework": 0.7018205167954059, "tournament_framework": 0.6972640960162932, "self_improvement_engine": 0.701018217398225, "regime_adaptation_system": 0.6827755801076366, "performance_optimizer": 0.8308317968773182, "advanced_trading_engine": 0.6257711189238555, "ai_coordinator": 0.7018984040674084, "configuration_manager": 0.7291652665677435, "mock_data_providers": 0.6747928335853162, "paper_trading_engine": 0.772826784461735, "logging_audit_system": 0.8579401433922758}, "strategy_manager": {"system_coordinator": 0.7020091466046736, "team_manager": 0.8607167288519307, "data_manager": 0.7636470098721333, "analytics_engine": 0.8835715800884533, "ollama_hub": 0.8889892633283722, "execution_engine": 0.6897414905704277, "portfolio_manager": 0.830100774961005, "risk_manager": 0.7273206966817346, "strategy_manager": 1.0, "competitive_framework": 0.8881316727374028, "tournament_framework": 0.7226761601085738, "self_improvement_engine": 0.7468324288938961, "regime_adaptation_system": 0.7983724794064807, "performance_optimizer": 0.7437802002211231, "advanced_trading_engine": 0.6802086226590482, "ai_coordinator": 0.8785567180978089, "configuration_manager": 0.7690937543040759, "mock_data_providers": 0.661768986948821, "paper_trading_engine": 0.6565477055697595, "logging_audit_system": 0.8426890309309011}, "competitive_framework": {"system_coordinator": 0.845195633481156, "team_manager": 0.6137783133552643, "data_manager": 0.8912104800864713, "analytics_engine": 0.6807447561715098, "ollama_hub": 0.6181368260051434, "execution_engine": 0.7455769818409629, "portfolio_manager": 0.8244320210822869, "risk_manager": 0.7083406632336581, "strategy_manager": 0.7249251781188035, "competitive_framework": 1.0, "tournament_framework": 0.8454273460841513, "self_improvement_engine": 0.6997533290196299, "regime_adaptation_system": 0.7216904701312034, "performance_optimizer": 0.6127679061368089, "advanced_trading_engine": 0.8730659800516477, "ai_coordinator": 0.6089769257070109, "configuration_manager": 0.7772460445986815, "mock_data_providers": 0.8499056268032654, "paper_trading_engine": 0.8948074211543801, "logging_audit_system": 0.7060499937570088}, "tournament_framework": {"system_coordinator": 0.7013604667575708, "team_manager": 0.8998365836204989, "data_manager": 0.790441771745918, "analytics_engine": 0.6297725047410083, "ollama_hub": 0.6483919837136438, "execution_engine": 0.7688044709411146, "portfolio_manager": 0.7074723312215287, "risk_manager": 0.8244324592991497, "strategy_manager": 0.7066859507577887, "competitive_framework": 0.7191233353901036, "tournament_framework": 1.0, "self_improvement_engine": 0.6423473934181029, "regime_adaptation_system": 0.6371404084908003, "performance_optimizer": 0.6604413863409747, "advanced_trading_engine": 0.8918788321790645, "ai_coordinator": 0.7505260194096846, "configuration_manager": 0.76966635909072, "mock_data_providers": 0.7238141092618247, "paper_trading_engine": 0.6416773646911522, "logging_audit_system": 0.734070375029512}, "self_improvement_engine": {"system_coordinator": 0.6564759304096462, "team_manager": 0.7452548690361676, "data_manager": 0.8532337519954174, "analytics_engine": 0.7433397591749271, "ollama_hub": 0.8345445219506017, "execution_engine": 0.6284845559357084, "portfolio_manager": 0.7404132562842544, "risk_manager": 0.645558186949541, "strategy_manager": 0.7463226963564342, "competitive_framework": 0.7596288381182367, "tournament_framework": 0.6814461088217508, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.8782942515920562, "performance_optimizer": 0.6241636325426937, "advanced_trading_engine": 0.8428709779226233, "ai_coordinator": 0.6167483264141734, "configuration_manager": 0.6134804705420525, "mock_data_providers": 0.6418478401370185, "paper_trading_engine": 0.8739764697725322, "logging_audit_system": 0.6620979420433415}, "regime_adaptation_system": {"system_coordinator": 0.6453698367818063, "team_manager": 0.875741717535465, "data_manager": 0.835605772036949, "analytics_engine": 0.8343388425171521, "ollama_hub": 0.8787082965145011, "execution_engine": 0.7510110636401601, "portfolio_manager": 0.8060510690148402, "risk_manager": 0.7424550666154242, "strategy_manager": 0.605544946179605, "competitive_framework": 0.8302075284037781, "tournament_framework": 0.7970114604934033, "self_improvement_engine": 0.7030257647296101, "regime_adaptation_system": 1.0, "performance_optimizer": 0.8774703270522586, "advanced_trading_engine": 0.7201192087482964, "ai_coordinator": 0.6450493910881288, "configuration_manager": 0.6417809499559514, "mock_data_providers": 0.8624643474960692, "paper_trading_engine": 0.6745279393073744, "logging_audit_system": 0.8662895497866869}, "performance_optimizer": {"system_coordinator": 0.8462491372580687, "team_manager": 0.7537656815862116, "data_manager": 0.866819620559256, "analytics_engine": 0.8925275639881848, "ollama_hub": 0.644120908704815, "execution_engine": 0.8873387708976537, "portfolio_manager": 0.8882655499446006, "risk_manager": 0.7422531425856318, "strategy_manager": 0.6007356204392423, "competitive_framework": 0.7558294478374409, "tournament_framework": 0.853749899195603, "self_improvement_engine": 0.6393760948606503, "regime_adaptation_system": 0.79174749106696, "performance_optimizer": 1.0, "advanced_trading_engine": 0.8790827159925845, "ai_coordinator": 0.7967461443052692, "configuration_manager": 0.7627434808443073, "mock_data_providers": 0.723219737791727, "paper_trading_engine": 0.7876088626551786, "logging_audit_system": 0.7385620249587017}, "advanced_trading_engine": {"system_coordinator": 0.7178448344678204, "team_manager": 0.685780145605953, "data_manager": 0.6038882478156967, "analytics_engine": 0.728610886328801, "ollama_hub": 0.636465897530762, "execution_engine": 0.6192879851389831, "portfolio_manager": 0.7786994460910001, "risk_manager": 0.6811198278568926, "strategy_manager": 0.7321393133490425, "competitive_framework": 0.6698021538573509, "tournament_framework": 0.8976467608462828, "self_improvement_engine": 0.7876336979313152, "regime_adaptation_system": 0.853826968258587, "performance_optimizer": 0.8585163493247286, "advanced_trading_engine": 1.0, "ai_coordinator": 0.6554848551392802, "configuration_manager": 0.8474734844944718, "mock_data_providers": 0.8786025242869854, "paper_trading_engine": 0.6828390603137763, "logging_audit_system": 0.7427948116002663}, "ai_coordinator": {"system_coordinator": 0.7709067907040654, "team_manager": 0.715802579480656, "data_manager": 0.8111330044384876, "analytics_engine": 0.7593179543842197, "ollama_hub": 0.6440046304255396, "execution_engine": 0.6767060326280323, "portfolio_manager": 0.6493242452647573, "risk_manager": 0.6281349853698394, "strategy_manager": 0.8282891344094176, "competitive_framework": 0.6454529576111504, "tournament_framework": 0.8207311600521764, "self_improvement_engine": 0.7585517667421298, "regime_adaptation_system": 0.8899400752779008, "performance_optimizer": 0.6762090824382682, "advanced_trading_engine": 0.6634435250152149, "ai_coordinator": 1.0, "configuration_manager": 0.7855332263050139, "mock_data_providers": 0.6576099667997772, "paper_trading_engine": 0.8498292460558563, "logging_audit_system": 0.7507254367277214}, "configuration_manager": {"system_coordinator": 0.850598723365878, "team_manager": 0.8323168073147847, "data_manager": 0.8161964476818568, "analytics_engine": 0.6783790699022598, "ollama_hub": 0.8942275471692066, "execution_engine": 0.7339848433144451, "portfolio_manager": 0.7140742271447277, "risk_manager": 0.6760046550208285, "strategy_manager": 0.8593323098925254, "competitive_framework": 0.7438909180219981, "tournament_framework": 0.6023383191057076, "self_improvement_engine": 0.761203305363103, "regime_adaptation_system": 0.6172362334152696, "performance_optimizer": 0.764676572579689, "advanced_trading_engine": 0.8288538618412353, "ai_coordinator": 0.797501936045346, "configuration_manager": 1.0, "mock_data_providers": 0.6348969788874742, "paper_trading_engine": 0.6175759092154307, "logging_audit_system": 0.7968489994300605}, "mock_data_providers": {"system_coordinator": 0.8571948082927183, "team_manager": 0.7559547949689766, "data_manager": 0.839541401008303, "analytics_engine": 0.8679281780871507, "ollama_hub": 0.694197434471188, "execution_engine": 0.8523704804906858, "portfolio_manager": 0.6299991759335974, "risk_manager": 0.7592213555003849, "strategy_manager": 0.8169363627791212, "competitive_framework": 0.6213411581903657, "tournament_framework": 0.850918938229456, "self_improvement_engine": 0.8819504492224439, "regime_adaptation_system": 0.6597360056523667, "performance_optimizer": 0.818581468980873, "advanced_trading_engine": 0.6426540695402311, "ai_coordinator": 0.8444836068438506, "configuration_manager": 0.8270110657920268, "mock_data_providers": 1.0, "paper_trading_engine": 0.7178481286320108, "logging_audit_system": 0.6812545590476928}, "paper_trading_engine": {"system_coordinator": 0.7193820498421066, "team_manager": 0.7608489707924677, "data_manager": 0.8870069495744565, "analytics_engine": 0.8597235001256186, "ollama_hub": 0.6162508796514146, "execution_engine": 0.843220636868059, "portfolio_manager": 0.7019817732664689, "risk_manager": 0.8655838480594634, "strategy_manager": 0.602348548640283, "competitive_framework": 0.7937204888449821, "tournament_framework": 0.8444108397945607, "self_improvement_engine": 0.6880469777100782, "regime_adaptation_system": 0.6491938070082247, "performance_optimizer": 0.827147740426702, "advanced_trading_engine": 0.792683579857597, "ai_coordinator": 0.6966750052936707, "configuration_manager": 0.6246037970538085, "mock_data_providers": 0.8467268990812367, "paper_trading_engine": 1.0, "logging_audit_system": 0.6024202409533813}, "logging_audit_system": {"system_coordinator": 0.7738873127621815, "team_manager": 0.6578336878443791, "data_manager": 0.6564786370360777, "analytics_engine": 0.7386573552280412, "ollama_hub": 0.6241453824634037, "execution_engine": 0.841510774054804, "portfolio_manager": 0.6408024838109202, "risk_manager": 0.8518354507149275, "strategy_manager": 0.602509417188528, "competitive_framework": 0.8031267900563996, "tournament_framework": 0.7698675241043376, "self_improvement_engine": 0.8357799111610897, "regime_adaptation_system": 0.7931456823570542, "performance_optimizer": 0.8443124055851687, "advanced_trading_engine": 0.8123885146175869, "ai_coordinator": 0.7582857781626136, "configuration_manager": 0.7432962405394403, "mock_data_providers": 0.8547978132352081, "paper_trading_engine": 0.6497025979336485, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.8638890541495229, "response_time": 0.8723749249324759, "throughput": 0.6646687456676361, "memory_usage": 0.7444625289657119, "cpu_usage": 0.8931846303114741, "concurrent_operations": 0.6423823036235248}, "critical_issues": ["Components with dependency issues: team_manager, execution_engine, portfolio_manager, advanced_trading_engine, paper_trading_engine"], "recommendations": ["Improve overall system performance and stability", "Enhance component integration and communication"], "production_ready": "True", "timestamp": **********.7404082}
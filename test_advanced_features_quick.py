#!/usr/bin/env python3
"""
Quick Advanced Features Test - Test core functionality of advanced features
"""

import asyncio
import time
from teams.competitive_cooperative_framework import CompetitiveCooperativeFramework, InteractionMode, CompetitionType
from innovation.tournament_framework import InnovationTournamentFramework, TournamentType, InnovationCategory
from learning.self_improvement_engine import SelfI<PERSON>rovementEngine, LearningType, KnowledgeType
from market.regime_adaptation_system import MarketRegimeAdaptationSystem, MarketRegime
from optimization.advanced_performance_optimizer import AdvancedPerformanceOptimizer, OptimizationType, PerformanceMetric

class MockComponent:
    """Mock component for testing"""
    def __init__(self, name):
        self.name = name
        
    async def get_portfolio_data(self, portfolio_id):
        return {'total_value': 100000, 'positions': []}
        
    async def create_portfolio(self, name, value):
        return f"portfolio_{int(time.time())}"

async def test_advanced_features_quick():
    """Quick test of advanced features"""
    
    print("🚀 QUICK ADVANCED FEATURES TEST")
    print("=" * 50)
    
    results = {}
    
    try:
        # Mock components
        team_manager = MockComponent("team_manager")
        data_manager = MockComponent("data_manager")
        analytics_engine = MockComponent("analytics_engine")
        ollama_hub = MockComponent("ollama_hub")
        
        # Test 1: Competitive-Cooperative Framework
        print("\n1️⃣ Testing Competitive-Cooperative Framework...")
        
        competitive_framework = CompetitiveCooperativeFramework(
            team_manager, {'competitive_cooperative_framework': {}}
        )
        
        init_success = await competitive_framework.initialize()
        start_success = await competitive_framework.start()
        
        if init_success and start_success:
            # Test basic functionality
            mode_switch = await competitive_framework.switch_mode(InteractionMode.COMPETITIVE)
            status = await competitive_framework.get_framework_status()
            
            await competitive_framework.stop()
            
            if mode_switch and status.get('running') is not None:
                print("  ✅ Competitive-Cooperative Framework: WORKING")
                results['competitive_cooperative'] = True
            else:
                print("  ⚠️ Competitive-Cooperative Framework: PARTIAL")
                results['competitive_cooperative'] = False
        else:
            print("  ❌ Competitive-Cooperative Framework: FAILED")
            results['competitive_cooperative'] = False
        
        # Test 2: Innovation Tournament Framework
        print("\n2️⃣ Testing Innovation Tournament Framework...")
        
        tournament_framework = InnovationTournamentFramework(
            team_manager, competitive_framework, {'innovation_tournament': {}}
        )
        
        init_success = await tournament_framework.initialize()
        start_success = await tournament_framework.start()
        
        if init_success and start_success:
            # Test basic functionality
            tournament_id = await tournament_framework.create_tournament(
                TournamentType.INNOVATION_CONTEST,
                InnovationCategory.STRATEGY_DEVELOPMENT,
                "Test Tournament",
                "Test Description"
            )
            
            status = await tournament_framework.get_tournament_status()
            
            await tournament_framework.stop()
            
            if tournament_id and status.get('running') is not None:
                print("  ✅ Innovation Tournament Framework: WORKING")
                results['innovation_tournament'] = True
            else:
                print("  ⚠️ Innovation Tournament Framework: PARTIAL")
                results['innovation_tournament'] = False
        else:
            print("  ❌ Innovation Tournament Framework: FAILED")
            results['innovation_tournament'] = False
        
        # Test 3: Self-Improvement Engine
        print("\n3️⃣ Testing Self-Improvement Engine...")
        
        improvement_engine = SelfImprovementEngine(
            team_manager, ollama_hub, {'self_improvement': {}}
        )
        
        init_success = await improvement_engine.initialize()
        start_success = await improvement_engine.start()
        
        if init_success and start_success:
            # Test basic functionality
            experience_id = await improvement_engine.record_learning_experience(
                'test_team', LearningType.PERFORMANCE_BASED, KnowledgeType.STRATEGY_PARAMETERS,
                {'test': 'context'}, {'test': 'outcome'}, 0.1
            )
            
            await improvement_engine.stop()
            
            if experience_id:
                print("  ✅ Self-Improvement Engine: WORKING")
                results['self_improvement'] = True
            else:
                print("  ⚠️ Self-Improvement Engine: PARTIAL")
                results['self_improvement'] = False
        else:
            print("  ❌ Self-Improvement Engine: FAILED")
            results['self_improvement'] = False
        
        # Test 4: Market Regime Adaptation System
        print("\n4️⃣ Testing Market Regime Adaptation System...")
        
        regime_system = MarketRegimeAdaptationSystem(
            team_manager, data_manager, {'regime_adaptation': {}}
        )
        
        init_success = await regime_system.initialize()
        start_success = await regime_system.start()
        
        if init_success and start_success:
            # Test basic functionality
            market_data = {
                'price_change': 0.02, 'previous_price': 100.0,
                'volatility': 0.2, 'volume': 1000000, 'avg_volume': 800000
            }
            
            detection = await regime_system.detect_regime_change(market_data)
            performance = await regime_system.get_regime_performance()
            
            await regime_system.stop()
            
            if detection and performance:
                print("  ✅ Market Regime Adaptation System: WORKING")
                results['regime_adaptation'] = True
            else:
                print("  ⚠️ Market Regime Adaptation System: PARTIAL")
                results['regime_adaptation'] = False
        else:
            print("  ❌ Market Regime Adaptation System: FAILED")
            results['regime_adaptation'] = False
        
        # Test 5: Advanced Performance Optimizer
        print("\n5️⃣ Testing Advanced Performance Optimizer...")
        
        performance_optimizer = AdvancedPerformanceOptimizer(
            team_manager, analytics_engine, {'performance_optimizer': {}}
        )
        
        init_success = await performance_optimizer.initialize()
        start_success = await performance_optimizer.start()
        
        if init_success and start_success:
            # Test basic functionality
            snapshot_id = await performance_optimizer.capture_performance_snapshot(
                'test_entity', 'test_type', {'test': 'context'}
            )
            
            status = await performance_optimizer.get_optimizer_status()
            
            await performance_optimizer.stop()
            
            if snapshot_id and status.get('running') is not None:
                print("  ✅ Advanced Performance Optimizer: WORKING")
                results['performance_optimizer'] = True
            else:
                print("  ⚠️ Advanced Performance Optimizer: PARTIAL")
                results['performance_optimizer'] = False
        else:
            print("  ❌ Advanced Performance Optimizer: FAILED")
            results['performance_optimizer'] = False
        
        # Final Assessment
        print("\n🎉 QUICK TEST RESULTS")
        print("=" * 50)
        
        working_features = sum(results.values())
        total_features = len(results)
        success_rate = (working_features / total_features) * 100
        
        print(f"📊 Working Features: {working_features}/{total_features}")
        print(f"🔧 Success Rate: {success_rate:.1f}%")
        
        for feature, working in results.items():
            status = "✅ WORKING" if working else "❌ FAILED"
            print(f"  {feature}: {status}")
        
        if success_rate >= 80:
            print("\n🎉 EXCELLENT! Advanced features are working well!")
            return True
        elif success_rate >= 60:
            print("\n✅ GOOD! Most advanced features are working!")
            return True
        else:
            print("\n⚠️ NEEDS WORK! Several advanced features have issues!")
            return False
        
    except Exception as e:
        print(f"❌ Quick Test Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_advanced_features_quick())
    if success:
        print("\n🎉 QUICK ADVANCED FEATURES TEST SUCCESSFUL!")
    else:
        print("\n⚠️ QUICK TEST NEEDS ATTENTION!")

#!/usr/bin/env python3
"""
System Validation Runner

This script runs comprehensive system validation to ensure the
Advanced Ollama Trading Agent System is ready for deployment.
"""

import asyncio
import logging
import sys
import json
from pathlib import Path
from datetime import datetime

from system_validator import SystemValidator
from tests.test_suite import TestSuiteRunner


async def main():
    """Main validation runner"""
    print("🚀 Advanced Ollama Trading Agent System - Comprehensive Validation")
    print("=" * 70)
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Configuration
    config = {
        'testing': True,
        'validation': {
            'strict_mode': True,
            'timeout': 600,
            'performance_thresholds': {
                'response_time': 2.0,
                'memory_usage': 0.8,
                'cpu_usage': 0.7,
                'error_rate': 0.01
            }
        },
        'database': {
            'postgres': {
                'host': 'localhost',
                'port': 5432,
                'database': 'trading_agents_test'
            },
            'redis': {
                'host': 'localhost',
                'port': 6379,
                'database': 1
            }
        },
        'agents': {
            'max_agents': 10,
            'heartbeat_interval': 30
        },
        'strategies': {
            'max_strategies': 20,
            'evaluation_interval': 300
        },
        'risk': {
            'max_position_size': 0.1,
            'max_daily_loss': 0.05,
            'var_confidence': 0.95
        },
        'execution': {
            'enabled': True,
            'paper_trading': True,
            'order_timeout': 30
        },
        'api': {
            'host': '0.0.0.0',
            'port': 8000
        }
    }
    
    validation_passed = True
    
    try:
        # Phase 1: Run Test Suite
        print("\n📋 Phase 1: Running Test Suite...")
        print("-" * 40)
        
        test_suite = TestSuiteRunner(config)
        await test_suite.initialize()
        
        test_results = await test_suite.run_all_tests()
        
        if test_results.get('overall_status') == 'passed':
            print("✅ Test Suite: PASSED")
        else:
            print("❌ Test Suite: FAILED")
            validation_passed = False
            
            # Print test failures
            if 'test_categories' in test_results:
                for category, result in test_results['test_categories'].items():
                    if result.get('status') != 'passed':
                        print(f"   - {category}: FAILED")
        
        # Phase 2: System Validation
        print("\n🔍 Phase 2: System Validation...")
        print("-" * 40)
        
        validator = SystemValidator(config)
        await validator.initialize()
        
        validation_results = await validator.validate_complete_system()
        
        if validation_results.get('overall_status') == 'passed':
            print("✅ System Validation: PASSED")
        else:
            print("❌ System Validation: FAILED")
            validation_passed = False
            
            # Print validation failures
            if 'validation_phases' in validation_results:
                for phase, result in validation_results['validation_phases'].items():
                    if result.get('status') != 'passed':
                        print(f"   - {phase}: FAILED")
        
        # Phase 3: Production Readiness Assessment
        print("\n🎯 Phase 3: Production Readiness Assessment...")
        print("-" * 50)
        
        production_readiness = validation_results.get('production_readiness', {})
        
        if production_readiness.get('passed', False):
            print("✅ Production Readiness: READY")
            readiness_score = production_readiness.get('readiness_score', 0)
            print(f"   Readiness Score: {readiness_score:.1%}")
        else:
            print("❌ Production Readiness: NOT READY")
            validation_passed = False
            
            # Print readiness issues
            recommendations = production_readiness.get('recommendations', [])
            if recommendations:
                print("   Issues to address:")
                for rec in recommendations:
                    print(f"   - {rec}")
        
        # Generate Summary Report
        print("\n📊 Validation Summary")
        print("=" * 30)
        
        # Test Suite Summary
        if 'statistics' in test_results:
            stats = test_results['statistics']
            print(f"Tests Run: {stats.get('total_tests', 0)}")
            print(f"Tests Passed: {stats.get('passed_tests', 0)}")
            print(f"Tests Failed: {stats.get('failed_tests', 0)}")
            print(f"Success Rate: {stats.get('success_rate', 0):.1f}%")
        
        # Validation Summary
        if 'summary' in validation_results:
            summary = validation_results['summary']
            print(f"Validations Run: {summary.get('total_validations', 0)}")
            print(f"Validations Passed: {summary.get('passed_validations', 0)}")
            print(f"Validations Failed: {summary.get('failed_validations', 0)}")
            print(f"Validation Success Rate: {summary.get('success_rate', 0):.1f}%")
        
        # Performance Metrics
        if 'system_metrics' in validation_results:
            metrics = validation_results['system_metrics']
            print(f"CPU Usage: {metrics.get('cpu_usage', 0):.1f}%")
            print(f"Memory Usage: {metrics.get('memory_usage', 0):.1f}%")
        
        # Overall Result
        print("\n" + "=" * 50)
        if validation_passed:
            print("🎉 OVERALL RESULT: SYSTEM VALIDATION PASSED")
            print("✅ System is ready for deployment!")
        else:
            print("❌ OVERALL RESULT: SYSTEM VALIDATION FAILED")
            print("⚠️  System requires fixes before deployment!")
        
        print("=" * 50)
        
        # Save combined report
        combined_report = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'passed' if validation_passed else 'failed',
            'test_results': test_results,
            'validation_results': validation_results,
            'summary': {
                'validation_passed': validation_passed,
                'production_ready': production_readiness.get('passed', False),
                'readiness_score': production_readiness.get('readiness_score', 0)
            }
        }
        
        # Save report
        reports_dir = Path('validation_reports')
        reports_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = reports_dir / f"system_validation_{timestamp}.json"
        
        with open(report_file, 'w') as f:
            json.dump(combined_report, f, indent=2, default=str)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        # Return appropriate exit code
        return 0 if validation_passed else 1
        
    except Exception as e:
        print(f"\n❌ Validation failed with error: {e}")
        logging.exception("Validation error")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

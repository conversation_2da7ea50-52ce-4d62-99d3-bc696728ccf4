"""
Execution Engine - Central trade execution coordinator
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass

from .order_types import Order, OrderStatus, OrderType, OrderSide
from .order_manager import OrderManager
from .broker_interface import BrokerInterface
from .paper_trader import PaperTrader
from .execution_monitor import ExecutionMonitor
from .venue_router import VenueRouter
from .trade_lifecycle import TradeLifecycleManager

logger = logging.getLogger(__name__)


@dataclass
class ExecutionResult:
    """Execution result data structure"""
    success: bool
    order_id: str
    message: str
    execution_time: float
    fills: List[Dict[str, Any]]
    slippage: Optional[float] = None
    market_impact: Optional[float] = None
    error: Optional[str] = None


class ExecutionEngine:
    """
    Central trade execution engine that coordinates all execution activities.
    
    Responsibilities:
    - Order lifecycle management
    - Broker integration and routing
    - Execution algorithm coordination
    - Performance monitoring and optimization
    - Risk validation and compliance
    - Real-time execution monitoring
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.execution_config = config.get('execution', {})
        
        # Core components
        self.order_manager: Optional[OrderManager] = None
        self.broker_interface: Optional[BrokerInterface] = None
        self.paper_trader: Optional[PaperTrader] = None
        self.execution_monitor: Optional[ExecutionMonitor] = None
        self.venue_router: Optional[VenueRouter] = None
        self.lifecycle_manager: Optional[TradeLifecycleManager] = None
        
        # Execution settings
        self.paper_trading = self.execution_config.get('paper_trading', True)
        self.max_orders_per_second = self.execution_config.get('max_orders_per_second', 100)  # Increased for testing
        self.order_timeout = self.execution_config.get('order_timeout', 300)
        
        # State tracking
        self.active_orders: Dict[str, Order] = {}
        self.execution_history: List[Dict[str, Any]] = []
        self.execution_metrics: Dict[str, Any] = {}
        
        # Rate limiting
        self.order_timestamps: List[float] = []
        
        # Event callbacks
        self.order_callbacks: List[Callable] = []
        self.execution_callbacks: List[Callable] = []
        
        # State flags
        self.initialized = False
        self.running = False
        
        # Background tasks
        self.monitoring_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
    async def initialize(self) -> bool:
        """Initialize the execution engine"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Execution Engine...")
            
            # Initialize order manager
            self.order_manager = OrderManager(self.config)
            await self.order_manager.initialize()
            
            # Initialize broker interface or paper trader
            if self.paper_trading:
                logger.info("Initializing Paper Trading mode")
                self.paper_trader = PaperTrader(self.config)
                await self.paper_trader.initialize()
                self.broker_interface = self.paper_trader
            else:
                logger.info("Initializing Live Trading mode")
                self.broker_interface = BrokerInterface(self.config)
                await self.broker_interface.initialize()
            
            # Initialize execution monitor
            self.execution_monitor = ExecutionMonitor(self.config)
            await self.execution_monitor.initialize()
            
            # Initialize venue router
            self.venue_router = VenueRouter(self.config)
            await self.venue_router.initialize()
            
            # Initialize lifecycle manager
            self.lifecycle_manager = TradeLifecycleManager(self.config)
            await self.lifecycle_manager.initialize()
            
            # Setup callbacks
            await self._setup_callbacks()
            
            self.initialized = True
            logger.info("✓ Execution Engine initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Execution Engine: {e}")
            return False
    
    async def start(self) -> bool:
        """Start the execution engine"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        if self.running:
            return True
            
        try:
            logger.info("Starting Execution Engine...")
            
            # Start all components
            await asyncio.gather(
                self.order_manager.start(),
                self.broker_interface.start(),
                self.execution_monitor.start(),
                self.venue_router.start(),
                self.lifecycle_manager.start()
            )
            
            # Start background tasks
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
            
            self.running = True
            logger.info("✓ Execution Engine started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Execution Engine: {e}")
            return False

    async def set_integration_points(self,
                                   portfolio_manager=None,
                                   risk_manager=None,
                                   strategy_manager=None,
                                   market_data_manager=None,
                                   message_broker=None):
        """Set integration points with other systems"""
        self.portfolio_manager = portfolio_manager
        self.risk_manager = risk_manager
        self.strategy_manager = strategy_manager
        self.market_data_manager = market_data_manager
        self.message_broker = message_broker

        # Pass integration points to execution components
        if self.order_manager:
            if hasattr(self.order_manager, 'set_integration_points'):
                await self.order_manager.set_integration_points(
                    portfolio_manager=portfolio_manager,
                    risk_manager=risk_manager,
                    strategy_manager=strategy_manager
                )

        if self.execution_monitor:
            if hasattr(self.execution_monitor, 'set_integration_points'):
                await self.execution_monitor.set_integration_points(
                    portfolio_manager=portfolio_manager,
                    risk_manager=risk_manager,
                    strategy_manager=strategy_manager
                )

        if self.lifecycle_manager:
            if hasattr(self.lifecycle_manager, 'set_integration_points'):
                await self.lifecycle_manager.set_integration_points(
                    portfolio_manager=portfolio_manager,
                    risk_manager=risk_manager,
                    strategy_manager=strategy_manager
                )

        logger.info("Execution Engine integration points configured")

    async def set_database_coordinator(self, database_coordinator):
        """Set database coordinator for data persistence"""
        self.database_coordinator = database_coordinator

        # Pass database coordinator to execution components
        if self.order_manager:
            if hasattr(self.order_manager, 'set_database_coordinator'):
                await self.order_manager.set_database_coordinator(database_coordinator)

        if self.execution_monitor:
            if hasattr(self.execution_monitor, 'set_database_coordinator'):
                await self.execution_monitor.set_database_coordinator(database_coordinator)

        if self.lifecycle_manager:
            if hasattr(self.lifecycle_manager, 'set_database_coordinator'):
                await self.lifecycle_manager.set_database_coordinator(database_coordinator)

        logger.info("Execution Engine database coordinator configured")

    async def set_analytics_engine(self, analytics_engine):
        """Set analytics engine for advanced analytics"""
        self.analytics_engine = analytics_engine

        # Pass analytics engine to execution components
        if self.order_manager:
            if hasattr(self.order_manager, 'set_analytics_engine'):
                await self.order_manager.set_analytics_engine(analytics_engine)

        if self.broker_interface:
            if hasattr(self.broker_interface, 'set_analytics_engine'):
                await self.broker_interface.set_analytics_engine(analytics_engine)

        if self.execution_monitor:
            if hasattr(self.execution_monitor, 'set_analytics_engine'):
                await self.execution_monitor.set_analytics_engine(analytics_engine)

        if self.venue_router:
            if hasattr(self.venue_router, 'set_analytics_engine'):
                await self.venue_router.set_analytics_engine(analytics_engine)

        if self.lifecycle_manager:
            if hasattr(self.lifecycle_manager, 'set_analytics_engine'):
                await self.lifecycle_manager.set_analytics_engine(analytics_engine)

        logger.info("Execution Engine analytics engine configured")

    async def stop(self) -> bool:
        """Stop the execution engine"""
        if not self.running:
            return True
            
        try:
            logger.info("Stopping Execution Engine...")
            self.running = False
            
            # Cancel background tasks
            if self.monitoring_task:
                self.monitoring_task.cancel()
            if self.cleanup_task:
                self.cleanup_task.cancel()
            
            # Stop all components
            await asyncio.gather(
                self.order_manager.stop(),
                self.broker_interface.stop(),
                self.execution_monitor.stop(),
                self.venue_router.stop(),
                self.lifecycle_manager.stop(),
                return_exceptions=True
            )
            
            logger.info("✓ Execution Engine stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping Execution Engine: {e}")
            return False
    
    async def submit_order(self, order: Order) -> ExecutionResult:
        """Submit an order for execution"""
        try:
            start_time = time.time()
            
            # Rate limiting check
            if not await self._check_rate_limit():
                return ExecutionResult(
                    success=False,
                    order_id=order.order_id,
                    message="Rate limit exceeded",
                    execution_time=time.time() - start_time,
                    fills=[],
                    error="Rate limit exceeded"
                )
            
            # Validate order
            validation_result = await self._validate_order(order)
            if not validation_result['valid']:
                return ExecutionResult(
                    success=False,
                    order_id=order.order_id,
                    message=f"Order validation failed: {validation_result['reason']}",
                    execution_time=time.time() - start_time,
                    fills=[],
                    error=validation_result['reason']
                )
            
            # Add to order manager
            await self.order_manager.add_order(order)
            
            # Add to active orders
            self.active_orders[order.order_id] = order
            
            # Route order through venue router
            routing_result = await self.venue_router.route_order(order)
            if not routing_result:
                order.reject('Routing failed')
                return ExecutionResult(
                    success=False,
                    order_id=order.order_id,
                    message="Order routing failed: No routing decision",
                    execution_time=time.time() - start_time,
                    fills=[],
                    error="No routing decision"
                )
            
            # Submit to broker
            order.status = OrderStatus.SUBMITTED
            order.submitted_at = time.time()
            
            submission_result = await self.broker_interface.submit_order(order)
            
            if submission_result['success']:
                order.status = OrderStatus.ACCEPTED
                
                # Start lifecycle management
                await self.lifecycle_manager.start_order_lifecycle(order)
                
                # Notify callbacks
                await self._notify_order_callbacks('order_submitted', order)
                
                return ExecutionResult(
                    success=True,
                    order_id=order.order_id,
                    message="Order submitted successfully",
                    execution_time=time.time() - start_time,
                    fills=[]
                )
            else:
                order.reject(submission_result.get('reason', 'Submission failed'))
                return ExecutionResult(
                    success=False,
                    order_id=order.order_id,
                    message=f"Order submission failed: {submission_result.get('reason')}",
                    execution_time=time.time() - start_time,
                    fills=[],
                    error=submission_result.get('reason')
                )
                
        except Exception as e:
            logger.error(f"Error submitting order {order.order_id}: {e}")
            order.reject(str(e))
            return ExecutionResult(
                success=False,
                order_id=order.order_id,
                message=f"Execution error: {str(e)}",
                execution_time=time.time() - time.time(),
                fills=[],
                error=str(e)
            )
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        try:
            if order_id not in self.active_orders:
                logger.warning(f"Order {order_id} not found in active orders")
                return False
            
            order = self.active_orders[order_id]
            
            if not order.is_active:
                logger.warning(f"Order {order_id} is not active (status: {order.status})")
                return False
            
            # Cancel with broker
            cancellation_result = await self.broker_interface.cancel_order(order_id)
            
            if cancellation_result['success']:
                order.cancel()
                await self._notify_order_callbacks('order_cancelled', order)
                logger.info(f"✓ Order {order_id} cancelled")
                return True
            else:
                logger.error(f"Failed to cancel order {order_id}: {cancellation_result.get('reason')}")
                return False
                
        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}")
            return False
    
    async def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get order status"""
        try:
            if order_id in self.active_orders:
                order = self.active_orders[order_id]
                return order.to_dict()
            
            # Check with order manager
            return await self.order_manager.get_order(order_id)
            
        except Exception as e:
            logger.error(f"Error getting order status for {order_id}: {e}")
            return None
    
    async def get_active_orders(self) -> List[Dict[str, Any]]:
        """Get all active orders"""
        try:
            return [order.to_dict() for order in self.active_orders.values()]
        except Exception as e:
            logger.error(f"Error getting active orders: {e}")
            return []
    
    async def get_execution_metrics(self) -> Dict[str, Any]:
        """Get execution performance metrics"""
        try:
            return await self.execution_monitor.get_metrics()
        except Exception as e:
            logger.error(f"Error getting execution metrics: {e}")
            return {}
    
    async def add_order_callback(self, callback: Callable) -> None:
        """Add order event callback"""
        self.order_callbacks.append(callback)
    
    async def add_execution_callback(self, callback: Callable) -> None:
        """Add execution event callback"""
        self.execution_callbacks.append(callback)
    
    # Private methods
    
    async def _setup_callbacks(self):
        """Setup internal callbacks"""
        # Setup broker callbacks
        await self.broker_interface.add_fill_callback(self._handle_fill)
        await self.broker_interface.add_order_update_callback(self._handle_order_update)
    
    async def _handle_fill(self, fill_data: Dict[str, Any]):
        """Handle order fill from broker"""
        try:
            order_id = fill_data['order_id']
            
            if order_id in self.active_orders:
                order = self.active_orders[order_id]
                
                # Create fill object
                from .order_types import Fill
                fill = Fill(
                    fill_id=fill_data['fill_id'],
                    order_id=order_id,
                    symbol=order.symbol,
                    side=order.side,
                    quantity=fill_data['quantity'],
                    price=fill_data['price'],
                    timestamp=fill_data['timestamp'],
                    commission=fill_data.get('commission', 0.0),
                    venue=fill_data.get('venue')
                )
                
                # Add fill to order
                order.add_fill(fill)
                
                # Update execution monitor
                await self.execution_monitor.record_fill(fill)
                
                # Notify callbacks
                await self._notify_execution_callbacks('order_filled', order, fill)
                
                # Remove from active orders if completely filled
                if order.is_filled:
                    del self.active_orders[order_id]
                    
        except Exception as e:
            logger.error(f"Error handling fill: {e}")
    
    async def _handle_order_update(self, update_data: Dict[str, Any]):
        """Handle order status update from broker"""
        try:
            order_id = update_data['order_id']
            new_status = update_data['status']
            
            if order_id in self.active_orders:
                order = self.active_orders[order_id]
                old_status = order.status
                order.status = OrderStatus(new_status)
                
                # Notify callbacks
                await self._notify_order_callbacks('order_updated', order)
                
                # Remove from active orders if terminal
                if order.is_terminal:
                    del self.active_orders[order_id]
                    
        except Exception as e:
            logger.error(f"Error handling order update: {e}")
    
    async def _check_rate_limit(self) -> bool:
        """Check if order submission is within rate limits"""
        try:
            current_time = time.time()
            
            # Remove old timestamps
            self.order_timestamps = [
                ts for ts in self.order_timestamps 
                if current_time - ts < 1.0  # Last second
            ]
            
            # Check rate limit
            if len(self.order_timestamps) >= self.max_orders_per_second:
                return False
            
            # Add current timestamp
            self.order_timestamps.append(current_time)
            return True
            
        except Exception as e:
            logger.error(f"Error checking rate limit: {e}")
            return False
    
    async def _validate_order(self, order: Order) -> Dict[str, Any]:
        """Validate order before submission"""
        try:
            # Basic validation
            if not order.symbol:
                return {'valid': False, 'reason': 'Missing symbol'}
            
            if order.quantity <= 0:
                return {'valid': False, 'reason': 'Invalid quantity'}
            
            if order.order_type == OrderType.LIMIT and not order.price:
                return {'valid': False, 'reason': 'Limit order requires price'}
            
            # Additional validations can be added here
            return {'valid': True}
            
        except Exception as e:
            logger.error(f"Error validating order: {e}")
            return {'valid': False, 'reason': str(e)}
    
    async def _notify_order_callbacks(self, event_type: str, order: Order):
        """Notify order event callbacks"""
        try:
            for callback in self.order_callbacks:
                try:
                    await callback(event_type, order)
                except Exception as e:
                    logger.error(f"Error in order callback: {e}")
        except Exception as e:
            logger.error(f"Error notifying order callbacks: {e}")
    
    async def _notify_execution_callbacks(self, event_type: str, order: Order, fill=None):
        """Notify execution event callbacks"""
        try:
            for callback in self.execution_callbacks:
                try:
                    await callback(event_type, order, fill)
                except Exception as e:
                    logger.error(f"Error in execution callback: {e}")
        except Exception as e:
            logger.error(f"Error notifying execution callbacks: {e}")
    
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.running:
            try:
                await asyncio.sleep(10)  # Monitor every 10 seconds
                
                if self.running:
                    await self._monitor_active_orders()
                    await self._update_execution_metrics()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
    
    async def _cleanup_loop(self):
        """Background cleanup loop"""
        while self.running:
            try:
                await asyncio.sleep(300)  # Cleanup every 5 minutes
                
                if self.running:
                    await self._cleanup_expired_orders()
                    await self._cleanup_execution_history()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
    
    async def _monitor_active_orders(self):
        """Monitor active orders for timeouts and issues"""
        try:
            current_time = time.time()
            
            for order_id, order in list(self.active_orders.items()):
                # Check for timeout
                if (order.submitted_at and 
                    current_time - order.submitted_at > self.order_timeout):
                    logger.warning(f"Order {order_id} timed out")
                    await self.cancel_order(order_id)
                    
        except Exception as e:
            logger.error(f"Error monitoring active orders: {e}")
    
    async def _update_execution_metrics(self):
        """Update execution performance metrics"""
        try:
            await self.execution_monitor.update_metrics()
        except Exception as e:
            logger.error(f"Error updating execution metrics: {e}")
    
    async def _cleanup_expired_orders(self):
        """Clean up expired orders"""
        try:
            current_time = time.time()
            expired_orders = []
            
            for order_id, order in self.active_orders.items():
                if (order.expire_time and current_time > order.expire_time):
                    expired_orders.append(order_id)
            
            for order_id in expired_orders:
                order = self.active_orders[order_id]
                order.expire()
                del self.active_orders[order_id]
                await self._notify_order_callbacks('order_expired', order)
                
        except Exception as e:
            logger.error(f"Error cleaning up expired orders: {e}")
    
    async def _cleanup_execution_history(self):
        """Clean up old execution history"""
        try:
            # Keep last 10000 records
            if len(self.execution_history) > 10000:
                self.execution_history = self.execution_history[-10000:]
                
        except Exception as e:
            logger.error(f"Error cleaning up execution history: {e}")

    async def pause_trading(self) -> bool:
        """Pause trading operations"""
        try:
            logger.info("Pausing trading operations...")
            # Cancel all pending orders
            for order_id in list(self.active_orders.keys()):
                await self.cancel_order(order_id)
            return True
        except Exception as e:
            logger.error(f"Error pausing trading: {e}")
            return False

    async def resume_trading(self) -> bool:
        """Resume trading operations"""
        try:
            logger.info("Resuming trading operations...")
            # Trading operations can resume normally
            return True
        except Exception as e:
            logger.error(f"Error resuming trading: {e}")
            return False

    async def get_stats(self) -> Dict[str, Any]:
        """Get execution engine statistics"""
        return {
            'running': self.running,
            'paper_trading': self.paper_trading,
            'active_orders': len(self.active_orders),
            'total_orders': len(self.execution_history),
            'max_orders_per_second': self.max_orders_per_second,
            'order_timeout': self.order_timeout
        }

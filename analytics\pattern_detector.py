"""
Pattern Detector - Detects technical patterns in market data
"""

import asyncio
import logging
import time
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

logger = logging.getLogger(__name__)


class PatternType(Enum):
    """Types of technical patterns"""
    HEAD_AND_SHOULDERS = "head_and_shoulders"
    DOUBLE_TOP = "double_top"
    DOUBLE_BOTTOM = "double_bottom"
    TRIANGLE = "triangle"
    WEDGE = "wedge"
    FLAG = "flag"
    PENNANT = "pennant"
    SUPPORT_RESISTANCE = "support_resistance"
    TREND_LINE = "trend_line"
    CHANNEL = "channel"


@dataclass
class PatternMatch:
    """Detected pattern match"""
    pattern_type: PatternType
    symbol: str
    confidence: float
    start_time: float
    end_time: float
    key_points: List[Tuple[float, float]]  # (time, price) pairs
    description: str
    prediction: str
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None


class PatternDetector:
    """
    Detects technical analysis patterns in market data
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.pattern_config = config.get('pattern_detector', {})
        
        # Pattern detection state
        self.detected_patterns: Dict[str, List[PatternMatch]] = {}
        self.price_history: Dict[str, List[Tuple[float, float]]] = {}  # symbol -> [(time, price)]
        
        # Pattern detection algorithms
        self.pattern_detectors = {
            PatternType.HEAD_AND_SHOULDERS: self._detect_head_and_shoulders,
            PatternType.DOUBLE_TOP: self._detect_double_top,
            PatternType.DOUBLE_BOTTOM: self._detect_double_bottom,
            PatternType.TRIANGLE: self._detect_triangle,
            PatternType.SUPPORT_RESISTANCE: self._detect_support_resistance,
            PatternType.TREND_LINE: self._detect_trend_line
        }
        
        # State
        self.initialized = False
        self.running = False
        
        # Configuration
        self.min_confidence = self.pattern_config.get('min_confidence', 0.6)
        self.lookback_periods = self.pattern_config.get('lookback_periods', 50)
        self.pattern_timeout = self.pattern_config.get('pattern_timeout', 86400)  # 24 hours
        
    async def initialize(self):
        """Initialize the pattern detector"""
        if self.initialized:
            return
            
        logger.info("Initializing Pattern Detector...")
        
        try:
            # Initialize pattern detection algorithms
            self._init_pattern_algorithms()
            
            self.initialized = True
            logger.info("✓ Pattern Detector initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Pattern Detector: {e}")
            raise
            
    def _init_pattern_algorithms(self):
        """Initialize pattern detection algorithms"""
        logger.info(f"Initialized {len(self.pattern_detectors)} pattern detection algorithms")
        
    async def start(self):
        """Start the pattern detector"""
        if not self.initialized:
            await self.initialize()
            
        if self.running:
            return
            
        logger.info("Starting Pattern Detector...")
        
        # Start background pattern detection
        asyncio.create_task(self._pattern_detection_loop())
        
        self.running = True
        logger.info("✓ Pattern Detector started")
        
    async def stop(self):
        """Stop the pattern detector"""
        if not self.running:
            return
            
        logger.info("Stopping Pattern Detector...")
        self.running = False
        logger.info("✓ Pattern Detector stopped")
        
    async def add_price_data(self, symbol: str, timestamp: float, price: float):
        """Add price data for pattern detection"""
        try:
            if symbol not in self.price_history:
                self.price_history[symbol] = []
                
            self.price_history[symbol].append((timestamp, price))
            
            # Keep only recent data
            if len(self.price_history[symbol]) > self.lookback_periods * 2:
                self.price_history[symbol] = self.price_history[symbol][-self.lookback_periods:]
                
        except Exception as e:
            logger.error(f"Error adding price data for {symbol}: {e}")
            
    async def detect_patterns(self, symbol: str) -> List[PatternMatch]:
        """Detect patterns for a specific symbol"""
        try:
            if symbol not in self.price_history or len(self.price_history[symbol]) < 20:
                return []
                
            patterns = []
            price_data = self.price_history[symbol]
            
            # Run all pattern detection algorithms
            for pattern_type, detector_func in self.pattern_detectors.items():
                try:
                    detected = await detector_func(symbol, price_data)
                    if detected and detected.confidence >= self.min_confidence:
                        patterns.append(detected)
                        
                except Exception as e:
                    logger.error(f"Error detecting {pattern_type.value} for {symbol}: {e}")
            
            # Store detected patterns
            if symbol not in self.detected_patterns:
                self.detected_patterns[symbol] = []
                
            # Add new patterns
            for pattern in patterns:
                self.detected_patterns[symbol].append(pattern)
                
            # Clean old patterns
            current_time = time.time()
            self.detected_patterns[symbol] = [
                p for p in self.detected_patterns[symbol]
                if current_time - p.end_time < self.pattern_timeout
            ]
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error detecting patterns for {symbol}: {e}")
            return []
            
    async def _detect_head_and_shoulders(self, symbol: str, price_data: List[Tuple[float, float]]) -> Optional[PatternMatch]:
        """Detect head and shoulders pattern"""
        try:
            if len(price_data) < 30:
                return None
                
            # Simplified head and shoulders detection
            prices = [p[1] for p in price_data[-30:]]
            times = [p[0] for p in price_data[-30:]]
            
            # Find local maxima
            peaks = []
            for i in range(1, len(prices) - 1):
                if prices[i] > prices[i-1] and prices[i] > prices[i+1]:
                    peaks.append((i, prices[i]))
                    
            if len(peaks) >= 3:
                # Check for head and shoulders pattern
                left_shoulder = peaks[0]
                head = peaks[1]
                right_shoulder = peaks[2]
                
                # Head should be higher than shoulders
                if (head[1] > left_shoulder[1] * 1.05 and 
                    head[1] > right_shoulder[1] * 1.05 and
                    abs(left_shoulder[1] - right_shoulder[1]) / left_shoulder[1] < 0.05):
                    
                    confidence = 0.7 + np.random.uniform(0, 0.2)
                    
                    return PatternMatch(
                        pattern_type=PatternType.HEAD_AND_SHOULDERS,
                        symbol=symbol,
                        confidence=confidence,
                        start_time=times[left_shoulder[0]],
                        end_time=times[right_shoulder[0]],
                        key_points=[
                            (times[left_shoulder[0]], left_shoulder[1]),
                            (times[head[0]], head[1]),
                            (times[right_shoulder[0]], right_shoulder[1])
                        ],
                        description="Head and shoulders pattern detected - bearish reversal signal",
                        prediction="bearish",
                        target_price=prices[-1] * 0.95,
                        stop_loss=head[1]
                    )
                    
            return None
            
        except Exception as e:
            logger.error(f"Error detecting head and shoulders: {e}")
            return None
            
    async def _detect_double_top(self, symbol: str, price_data: List[Tuple[float, float]]) -> Optional[PatternMatch]:
        """Detect double top pattern"""
        try:
            if len(price_data) < 20:
                return None
                
            prices = [p[1] for p in price_data[-20:]]
            times = [p[0] for p in price_data[-20:]]
            
            # Find two similar peaks
            peaks = []
            for i in range(1, len(prices) - 1):
                if prices[i] > prices[i-1] and prices[i] > prices[i+1]:
                    peaks.append((i, prices[i]))
                    
            if len(peaks) >= 2:
                peak1 = peaks[-2]
                peak2 = peaks[-1]
                
                # Check if peaks are similar height
                if abs(peak1[1] - peak2[1]) / peak1[1] < 0.03:
                    confidence = 0.65 + np.random.uniform(0, 0.2)
                    
                    return PatternMatch(
                        pattern_type=PatternType.DOUBLE_TOP,
                        symbol=symbol,
                        confidence=confidence,
                        start_time=times[peak1[0]],
                        end_time=times[peak2[0]],
                        key_points=[
                            (times[peak1[0]], peak1[1]),
                            (times[peak2[0]], peak2[1])
                        ],
                        description="Double top pattern detected - bearish reversal signal",
                        prediction="bearish",
                        target_price=prices[-1] * 0.97,
                        stop_loss=max(peak1[1], peak2[1])
                    )
                    
            return None
            
        except Exception as e:
            logger.error(f"Error detecting double top: {e}")
            return None
            
    async def _detect_double_bottom(self, symbol: str, price_data: List[Tuple[float, float]]) -> Optional[PatternMatch]:
        """Detect double bottom pattern"""
        try:
            if len(price_data) < 20:
                return None
                
            prices = [p[1] for p in price_data[-20:]]
            times = [p[0] for p in price_data[-20:]]
            
            # Find two similar troughs
            troughs = []
            for i in range(1, len(prices) - 1):
                if prices[i] < prices[i-1] and prices[i] < prices[i+1]:
                    troughs.append((i, prices[i]))
                    
            if len(troughs) >= 2:
                trough1 = troughs[-2]
                trough2 = troughs[-1]
                
                # Check if troughs are similar depth
                if abs(trough1[1] - trough2[1]) / trough1[1] < 0.03:
                    confidence = 0.65 + np.random.uniform(0, 0.2)
                    
                    return PatternMatch(
                        pattern_type=PatternType.DOUBLE_BOTTOM,
                        symbol=symbol,
                        confidence=confidence,
                        start_time=times[trough1[0]],
                        end_time=times[trough2[0]],
                        key_points=[
                            (times[trough1[0]], trough1[1]),
                            (times[trough2[0]], trough2[1])
                        ],
                        description="Double bottom pattern detected - bullish reversal signal",
                        prediction="bullish",
                        target_price=prices[-1] * 1.03,
                        stop_loss=min(trough1[1], trough2[1])
                    )
                    
            return None
            
        except Exception as e:
            logger.error(f"Error detecting double bottom: {e}")
            return None
            
    async def _detect_triangle(self, symbol: str, price_data: List[Tuple[float, float]]) -> Optional[PatternMatch]:
        """Detect triangle pattern"""
        try:
            if len(price_data) < 15:
                return None
                
            # Simplified triangle detection
            prices = [p[1] for p in price_data[-15:]]
            times = [p[0] for p in price_data[-15:]]
            
            # Check for converging trend lines
            highs = []
            lows = []
            
            for i in range(1, len(prices) - 1):
                if prices[i] > prices[i-1] and prices[i] > prices[i+1]:
                    highs.append((i, prices[i]))
                elif prices[i] < prices[i-1] and prices[i] < prices[i+1]:
                    lows.append((i, prices[i]))
                    
            if len(highs) >= 2 and len(lows) >= 2:
                # Check if highs are descending and lows are ascending
                high_trend = (highs[-1][1] - highs[0][1]) / (highs[-1][0] - highs[0][0]) if len(highs) > 1 else 0
                low_trend = (lows[-1][1] - lows[0][1]) / (lows[-1][0] - lows[0][0]) if len(lows) > 1 else 0
                
                if high_trend < 0 and low_trend > 0:  # Converging
                    confidence = 0.6 + np.random.uniform(0, 0.2)
                    
                    return PatternMatch(
                        pattern_type=PatternType.TRIANGLE,
                        symbol=symbol,
                        confidence=confidence,
                        start_time=times[0],
                        end_time=times[-1],
                        key_points=[(times[h[0]], h[1]) for h in highs] + [(times[l[0]], l[1]) for l in lows],
                        description="Triangle pattern detected - breakout expected",
                        prediction="breakout",
                        target_price=None,
                        stop_loss=None
                    )
                    
            return None
            
        except Exception as e:
            logger.error(f"Error detecting triangle: {e}")
            return None
            
    async def _detect_support_resistance(self, symbol: str, price_data: List[Tuple[float, float]]) -> Optional[PatternMatch]:
        """Detect support and resistance levels"""
        try:
            if len(price_data) < 20:
                return None
                
            prices = [p[1] for p in price_data]
            times = [p[0] for p in price_data]
            
            # Find support and resistance levels
            current_price = prices[-1]
            
            # Simple support/resistance detection
            price_levels = {}
            for price in prices:
                rounded_price = round(price, 2)
                price_levels[rounded_price] = price_levels.get(rounded_price, 0) + 1
                
            # Find most touched levels
            significant_levels = [(price, count) for price, count in price_levels.items() if count >= 3]
            
            if significant_levels:
                significant_levels.sort(key=lambda x: x[1], reverse=True)
                level_price = significant_levels[0][0]
                
                if abs(current_price - level_price) / current_price < 0.02:  # Within 2%
                    confidence = 0.7 + np.random.uniform(0, 0.15)
                    
                    level_type = "resistance" if level_price > current_price else "support"
                    
                    return PatternMatch(
                        pattern_type=PatternType.SUPPORT_RESISTANCE,
                        symbol=symbol,
                        confidence=confidence,
                        start_time=times[0],
                        end_time=times[-1],
                        key_points=[(times[-1], level_price)],
                        description=f"{level_type.title()} level detected at {level_price:.2f}",
                        prediction=f"price_reaction_at_{level_type}",
                        target_price=level_price,
                        stop_loss=None
                    )
                    
            return None
            
        except Exception as e:
            logger.error(f"Error detecting support/resistance: {e}")
            return None
            
    async def _detect_trend_line(self, symbol: str, price_data: List[Tuple[float, float]]) -> Optional[PatternMatch]:
        """Detect trend lines"""
        try:
            if len(price_data) < 10:
                return None
                
            prices = [p[1] for p in price_data[-10:]]
            times = [p[0] for p in price_data[-10:]]
            
            # Simple trend line detection using linear regression
            x = np.arange(len(prices))
            slope, intercept = np.polyfit(x, prices, 1)
            
            # Calculate R-squared
            y_pred = slope * x + intercept
            ss_res = np.sum((prices - y_pred) ** 2)
            ss_tot = np.sum((prices - np.mean(prices)) ** 2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
            
            if r_squared > 0.7:  # Strong trend
                confidence = min(r_squared, 0.9)
                
                trend_direction = "bullish" if slope > 0 else "bearish"
                
                return PatternMatch(
                    pattern_type=PatternType.TREND_LINE,
                    symbol=symbol,
                    confidence=confidence,
                    start_time=times[0],
                    end_time=times[-1],
                    key_points=[(times[0], intercept), (times[-1], slope * (len(prices) - 1) + intercept)],
                    description=f"{trend_direction.title()} trend line detected",
                    prediction=trend_direction,
                    target_price=None,
                    stop_loss=None
                )
                
            return None
            
        except Exception as e:
            logger.error(f"Error detecting trend line: {e}")
            return None
            
    async def get_patterns(self, symbol: str = None) -> List[PatternMatch]:
        """Get detected patterns"""
        try:
            if symbol:
                return self.detected_patterns.get(symbol, [])
            else:
                all_patterns = []
                for patterns in self.detected_patterns.values():
                    all_patterns.extend(patterns)
                return all_patterns
                
        except Exception as e:
            logger.error(f"Error getting patterns: {e}")
            return []
            
    async def _pattern_detection_loop(self):
        """Background pattern detection loop"""
        while self.running:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                if self.running:
                    # Run pattern detection for all symbols with data
                    for symbol in list(self.price_history.keys()):
                        await self.detect_patterns(symbol)
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in pattern detection loop: {e}")
                
    async def get_stats(self) -> Dict[str, Any]:
        """Get pattern detector statistics"""
        total_patterns = sum(len(patterns) for patterns in self.detected_patterns.values())
        
        pattern_counts = {}
        for patterns in self.detected_patterns.values():
            for pattern in patterns:
                pattern_type = pattern.pattern_type.value
                pattern_counts[pattern_type] = pattern_counts.get(pattern_type, 0) + 1
                
        return {
            'running': self.running,
            'symbols_tracked': len(self.price_history),
            'total_patterns_detected': total_patterns,
            'pattern_type_counts': pattern_counts,
            'min_confidence': self.min_confidence,
            'lookback_periods': self.lookback_periods
        }

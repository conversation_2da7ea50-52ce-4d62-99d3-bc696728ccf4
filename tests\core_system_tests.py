"""
Core System Tests - Comprehensive Testing of Backend Components

This module tests all core backend components without external dependencies.
Focus on unit tests, integration tests, and system validation.
"""

import asyncio
import pytest
import logging
import sys
import traceback
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import core components
try:
    from agents.agent_manager import AgentManager
    from agents.base_agent import AgentRole
    from config.config_manager import ConfigManager
    from models.ollama_hub import OllamaModelHub

    # Try to import other components (may not exist yet)
    try:
        from strategies.strategy_manager import StrategyManager
    except ImportError:
        StrategyManager = None

    try:
        from risk.risk_manager import RiskManager
    except ImportError:
        RiskManager = None

    try:
        from execution.execution_engine import ExecutionEngine
    except ImportError:
        ExecutionEngine = None

    try:
        from portfolio.portfolio_manager import PortfolioManager
    except ImportError:
        PortfolioManager = None

    try:
        from data.data_manager import DataManager
    except ImportError:
        DataManager = None

    try:
        from analytics.analytics_engine import AnalyticsEngine
    except ImportError:
        AnalyticsEngine = None

    try:
        from monitoring.performance_monitor import PerformanceMonitor
    except ImportError:
        PerformanceMonitor = None

except ImportError as e:
    print(f"Import error: {e}")
    print("Some components may not be available for testing")


class CoreSystemTester:
    """
    Comprehensive tester for all core system components.
    Tests each component in isolation and then integration.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.test_results = {}
        self.failed_tests = []
        self.passed_tests = []
        
        # Test configuration - complete structure
        self.test_config = {
            'system': {
                'name': 'Advanced Ollama Trading Agents Test',
                'version': '1.0.0',
                'environment': 'testing'
            },
            'database': {
                'postgres': {
                    'host': 'localhost',
                    'port': 5432,
                    'database': 'trading_test',
                    'username': 'test_user',
                    'password': 'test_pass'
                }
            },
            'api': {
                'host': '127.0.0.1',
                'port': 8001
            },
            'ollama': {
                'base_url': 'http://localhost:11434',
                'timeout': 30
            },
            'agents': {
                'max_agents': 5,
                'defaults': {
                    'model': 'llama2:7b',
                    'temperature': 0.7,
                    'max_tokens': 1024
                }
            },
            'risk': {
                'max_portfolio_risk': 0.02,
                'max_position_size': 0.1,
                'max_drawdown': 0.15
            },
            'portfolio': {
                'initial_capital': 100000.0
            },
            'execution': {
                'paper_trading': True
            }
        }
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all core system tests"""
        print("🧪 Starting Core System Tests...")
        print("=" * 60)
        
        # Test individual components
        await self.test_config_manager()
        await self.test_agent_manager()

        # Test other components if available
        if StrategyManager:
            await self.test_strategy_manager()
        else:
            print("\n⏭️ Skipping StrategyManager - not implemented yet")

        if RiskManager:
            await self.test_risk_manager()
        else:
            print("\n⏭️ Skipping RiskManager - not implemented yet")

        if ExecutionEngine:
            await self.test_execution_engine()
        else:
            print("\n⏭️ Skipping ExecutionEngine - not implemented yet")

        if PortfolioManager:
            await self.test_portfolio_manager()
        else:
            print("\n⏭️ Skipping PortfolioManager - not implemented yet")

        if DataManager:
            await self.test_data_manager()
        else:
            print("\n⏭️ Skipping DataManager - not implemented yet")

        if AnalyticsEngine:
            await self.test_analytics_engine()
        else:
            print("\n⏭️ Skipping AnalyticsEngine - not implemented yet")

        if PerformanceMonitor:
            await self.test_performance_monitor()
        else:
            print("\n⏭️ Skipping PerformanceMonitor - not implemented yet")

        # Test integration
        await self.test_system_integration()
        
        # Generate report
        return self.generate_test_report()
    
    async def test_config_manager(self):
        """Test configuration manager"""
        test_name = "ConfigManager"
        print(f"\n📋 Testing {test_name}...")

        try:
            # Test basic configuration loading with test config
            config_manager = ConfigManager("config/test_config.yaml")

            # Test loading configuration
            await config_manager.load_config()
            print(f"  ✓ Configuration loaded")

            # Test getting configuration
            config = await config_manager.get_config()
            assert isinstance(config, dict)
            assert 'system' in config
            assert 'database' in config
            print(f"  ✓ Configuration retrieved with {len(config)} sections")

            # Test validation
            validation_result = await config_manager.validate_config()
            assert hasattr(validation_result, 'valid')
            assert validation_result.valid == True
            print(f"  ✓ Configuration validation passed")

            # Test backup functionality (private method)
            backup_path = await config_manager._backup_config()
            assert backup_path is not None
            print(f"  ✓ Configuration backup created: {backup_path}")

            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")

        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")
            self.logger.error(f"ConfigManager test failed: {e}")
            import traceback
            traceback.print_exc()
    
    async def test_agent_manager(self):
        """Test agent manager"""
        test_name = "AgentManager"
        print(f"\n🤖 Testing {test_name}...")

        try:
            # Create required dependencies
            ollama_hub = OllamaModelHub(config=self.test_config)

            # Create a mock message broker
            class MockMessageBroker:
                async def publish(self, topic, message):
                    pass
                async def subscribe(self, topic, callback):
                    pass

            message_broker = MockMessageBroker()

            # Mock the OllamaModelHub to avoid external dependencies
            class MockOllamaHub:
                def __init__(self, config):
                    self.config = config
                    self.initialized = False

                async def initialize(self):
                    self.initialized = True

                async def deploy_model_for_agent(self, agent_name, role):
                    # Return a mock model instance
                    return {
                        'model_name': 'llama2:7b',
                        'agent_name': agent_name,
                        'role': role,
                        'status': 'deployed'
                    }

            # Use mock instead of real OllamaHub for testing
            mock_ollama_hub = MockOllamaHub(self.test_config)
            await mock_ollama_hub.initialize()

            # Create agent manager with proper dependencies
            agent_manager = AgentManager(
                ollama_hub=mock_ollama_hub,
                message_broker=message_broker,
                config=self.test_config
            )

            # Test initialization
            await agent_manager.initialize()
            assert agent_manager.initialized

            # Test agent creation using AgentRole
            agent_id = await agent_manager.create_agent(
                role=AgentRole.MARKET_ANALYST,
                name="test_market_analyst"
            )
            assert agent_id is not None

            # Test agent retrieval
            agent = await agent_manager.get_agent(agent_id)
            assert agent is not None

            # Test agent status
            statuses = await agent_manager.get_all_agent_statuses()
            assert isinstance(statuses, dict)
            assert agent_id in statuses

            # Test agents by role
            analysts = await agent_manager.get_agents_by_role(AgentRole.MARKET_ANALYST)
            assert len(analysts) >= 1

            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")

        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")
            self.logger.error(f"AgentManager test failed: {e}")
    
    async def test_strategy_manager(self):
        """Test strategy manager"""
        test_name = "StrategyManager"
        print(f"\n📈 Testing {test_name}...")

        try:
            strategy_manager = StrategyManager(self.test_config)
            await strategy_manager.initialize()

            # Test strategy creation
            strategy_id = await strategy_manager.create_strategy(
                strategy_type='momentum',
                name='test_momentum',
                config={
                    'lookback_period': 20,
                    'threshold': 0.02
                }
            )
            assert strategy_id is not None

            # Test strategy status
            status = strategy_manager.get_strategy_status(strategy_id)
            assert status is not None

            # Test strategy removal
            removed = await strategy_manager.remove_strategy(strategy_id)
            assert removed is True

            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")

        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")
            self.logger.error(f"StrategyManager test failed: {e}")
    
    async def test_risk_manager(self):
        """Test risk manager"""
        test_name = "RiskManager"
        print(f"\n⚠️ Testing {test_name}...")

        try:
            risk_manager = RiskManager(self.test_config)
            await risk_manager.initialize()

            # Test portfolio risk assessment
            portfolio_data = {
                'total_value': 100000.0,
                'positions': [
                    {'symbol': 'AAPL', 'quantity': 100, 'price': 150.0},
                    {'symbol': 'GOOGL', 'quantity': 10, 'price': 2500.0}
                ]
            }

            risk_assessment = await risk_manager.assess_portfolio_risk(portfolio_data)
            assert risk_assessment is not None
            assert hasattr(risk_assessment, 'portfolio_var')
            assert hasattr(risk_assessment, 'max_drawdown')

            # Test risk status
            risk_status = await risk_manager.get_risk_status()
            assert isinstance(risk_status, dict)
            assert 'current_assessment' in risk_status

            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")

        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")
            self.logger.error(f"RiskManager test failed: {e}")
    
    async def test_execution_engine(self):
        """Test execution engine"""
        test_name = "ExecutionEngine"
        print(f"\n⚡ Testing {test_name}...")

        try:
            execution_engine = ExecutionEngine(self.test_config)
            await execution_engine.initialize()

            # Import Order class
            from execution.order_types import Order, OrderType, OrderSide

            # Test order creation
            order = Order(
                order_id='test_order_001',
                symbol='AAPL',
                side=OrderSide.BUY,
                quantity=100,
                order_type=OrderType.MARKET,
                strategy_id='test_strategy'
            )

            execution_result = await execution_engine.submit_order(order)
            assert execution_result is not None
            assert hasattr(execution_result, 'success')

            # Test active orders
            active_orders = await execution_engine.get_active_orders()
            assert isinstance(active_orders, list)

            # Test execution metrics
            metrics = await execution_engine.get_execution_metrics()
            assert isinstance(metrics, dict)

            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")

        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")
            self.logger.error(f"ExecutionEngine test failed: {e}")
    
    async def test_portfolio_manager(self):
        """Test portfolio manager"""
        test_name = "PortfolioManager"
        print(f"\n💼 Testing {test_name}...")
        
        try:
            portfolio_manager = PortfolioManager(self.test_config)
            await portfolio_manager.initialize()
            
            # Test portfolio creation
            portfolio_config = {
                'initial_capital': 100000.0,
                'name': 'test_portfolio'
            }
            
            portfolio_id = await portfolio_manager.create_portfolio(portfolio_config)
            assert portfolio_id is not None
            
            # Test portfolio value calculation
            portfolio_value = await portfolio_manager.calculate_portfolio_value(portfolio_id)
            assert portfolio_value >= 0
            
            # Test position management
            position = {
                'symbol': 'AAPL',
                'quantity': 100,
                'price': 150.0
            }
            
            await portfolio_manager.add_position(portfolio_id, position)
            positions = await portfolio_manager.get_positions(portfolio_id)
            assert len(positions) >= 1
            
            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")
            
        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")
            self.logger.error(f"PortfolioManager test failed: {e}")
    
    async def test_data_manager(self):
        """Test data manager"""
        test_name = "DataManager"
        print(f"\n📊 Testing {test_name}...")
        
        try:
            data_manager = DataManager(self.test_config)
            await data_manager.initialize()
            
            # Test data storage
            test_data = {
                'symbol': 'AAPL',
                'price': 150.0,
                'timestamp': datetime.now(),
                'volume': 1000000
            }
            
            await data_manager.store_market_data(test_data)
            
            # Test data retrieval
            retrieved_data = await data_manager.get_market_data('AAPL')
            assert retrieved_data is not None
            
            # Test historical data
            start_date = datetime.now() - timedelta(days=30)
            end_date = datetime.now()
            
            historical_data = await data_manager.get_historical_data('AAPL', start_date, end_date)
            assert isinstance(historical_data, list)
            
            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")
            
        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")
            self.logger.error(f"DataManager test failed: {e}")
    
    async def test_analytics_engine(self):
        """Test analytics engine"""
        test_name = "AnalyticsEngine"
        print(f"\n🔍 Testing {test_name}...")
        
        try:
            analytics_engine = AnalyticsEngine(self.test_config)
            await analytics_engine.initialize()
            
            # Test technical analysis
            price_data = [100, 102, 101, 105, 103, 107, 106, 110, 108, 112]
            
            sma = await analytics_engine.calculate_sma(price_data, period=5)
            assert len(sma) > 0
            
            # Test pattern recognition
            patterns = await analytics_engine.detect_patterns(price_data)
            assert isinstance(patterns, list)
            
            # Test performance metrics
            returns = [0.02, -0.01, 0.03, -0.02, 0.04]
            sharpe_ratio = await analytics_engine.calculate_sharpe_ratio(returns)
            assert isinstance(sharpe_ratio, (int, float))
            
            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")
            
        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")
            self.logger.error(f"AnalyticsEngine test failed: {e}")
    
    async def test_performance_monitor(self):
        """Test performance monitor"""
        test_name = "PerformanceMonitor"
        print(f"\n📈 Testing {test_name}...")
        
        try:
            # Import the correct classes
            from monitoring.performance_monitor import MetricsCollector, AlertManager

            # Test MetricsCollector
            metrics_collector = MetricsCollector(self.test_config)

            # Test metric collection
            await metrics_collector.collect_system_metrics()

            # Test metric recording
            metrics_collector.record_metric('test.metric', 42.0, unit='count')

            # Test metric retrieval
            metric = metrics_collector.get_metric('test.metric')
            assert metric is not None
            assert metric.value == 42.0

            # Test AlertManager
            alert_manager = AlertManager(self.test_config)

            # Test alert rule addition
            alert_manager.add_alert_rule(
                name='test_alert',
                metric='system.cpu_usage',
                threshold=80.0,
                operator='gt'
            )

            # Test alert checking
            await alert_manager.check_alerts(metrics_collector)

            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")

        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")
            self.logger.error(f"PerformanceMonitor test failed: {e}")
    
    async def test_system_integration(self):
        """Test system integration"""
        test_name = "SystemIntegration"
        print(f"\n🔗 Testing {test_name}...")

        try:
            # Test basic component interaction
            config_manager = ConfigManager("config/test_config.yaml")

            # Create mock dependencies for AgentManager
            class MockOllamaHub:
                def __init__(self, config):
                    self.config = config
                    self.initialized = False

                async def initialize(self):
                    self.initialized = True

                async def deploy_model_for_agent(self, agent_name, role):
                    return {
                        'model_name': 'llama2:7b',
                        'agent_name': agent_name,
                        'role': role,
                        'status': 'deployed'
                    }

            class MockMessageBroker:
                async def publish(self, topic, message):
                    pass
                async def subscribe(self, topic, callback):
                    pass

            mock_ollama_hub = MockOllamaHub(self.test_config)
            await mock_ollama_hub.initialize()
            message_broker = MockMessageBroker()

            agent_manager = AgentManager(
                ollama_hub=mock_ollama_hub,
                message_broker=message_broker,
                config=self.test_config
            )

            # Initialize components
            await config_manager.load_config()
            await agent_manager.initialize()

            # Test basic workflow: Create multiple agents
            agent1_id = await agent_manager.create_agent(
                role=AgentRole.MARKET_ANALYST,
                name="integration_test_analyst"
            )

            agent2_id = await agent_manager.create_agent(
                role=AgentRole.RISK_MANAGER,
                name="integration_test_risk_manager"
            )

            # Verify both agents exist
            assert agent1_id is not None
            assert agent2_id is not None

            # Test agent communication (basic)
            statuses = await agent_manager.get_all_agent_statuses()
            assert agent1_id in statuses
            assert agent2_id in statuses

            # Test role-based retrieval
            analysts = await agent_manager.get_agents_by_role(AgentRole.MARKET_ANALYST)
            risk_managers = await agent_manager.get_agents_by_role(AgentRole.RISK_MANAGER)

            assert len(analysts) >= 1
            assert len(risk_managers) >= 1

            self.passed_tests.append(test_name)
            print(f"✅ {test_name} - PASSED")

        except Exception as e:
            self.failed_tests.append((test_name, str(e)))
            print(f"❌ {test_name} - FAILED: {e}")
            self.logger.error(f"SystemIntegration test failed: {e}")
    
    def generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        total_tests = len(self.passed_tests) + len(self.failed_tests)
        success_rate = (len(self.passed_tests) / total_tests * 100) if total_tests > 0 else 0
        
        report = {
            'summary': {
                'total_tests': total_tests,
                'passed': len(self.passed_tests),
                'failed': len(self.failed_tests),
                'success_rate': success_rate
            },
            'passed_tests': self.passed_tests,
            'failed_tests': self.failed_tests,
            'timestamp': datetime.now().isoformat()
        }
        
        print("\n" + "=" * 60)
        print("📊 TEST REPORT")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {len(self.passed_tests)}")
        print(f"Failed: {len(self.failed_tests)}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        if self.failed_tests:
            print("\n❌ FAILED TESTS:")
            for test_name, error in self.failed_tests:
                print(f"  - {test_name}: {error}")
        
        if self.passed_tests:
            print("\n✅ PASSED TESTS:")
            for test_name in self.passed_tests:
                print(f"  - {test_name}")
        
        return report


async def main():
    """Main test runner"""
    tester = CoreSystemTester()
    report = await tester.run_all_tests()
    
    # Save report
    import json
    with open('test_results.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Test report saved to: test_results.json")
    
    # Return exit code based on results
    return 0 if len(tester.failed_tests) == 0 else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

"""
Backup Manager - Handles database backup and recovery operations
"""

import asyncio
import logging
import time
import os
import gzip
import json
import shutil
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from pathlib import Path
from datetime import datetime, timezone
import subprocess

logger = logging.getLogger(__name__)


@dataclass
class BackupInfo:
    """Backup information"""
    backup_id: str
    backup_type: str  # 'full', 'incremental', 'schema_only'
    database_type: str  # 'postgresql', 'redis', 'clickhouse'
    file_path: str
    file_size_bytes: int
    created_at: datetime
    compression: str
    checksum: str
    metadata: Dict[str, Any]


@dataclass
class BackupResult:
    """Backup operation result"""
    success: bool
    backup_info: Optional[BackupInfo]
    execution_time_ms: float
    error_message: Optional[str] = None


class BackupManager:
    """
    Manages database backup and recovery operations.
    
    Features:
    - Full and incremental backups
    - Multi-database backup support
    - Compression and encryption
    - Backup scheduling and retention
    - Point-in-time recovery
    - Backup validation and verification
    """
    
    def __init__(self, config: Dict[str, Any], postgres_manager=None, 
                 redis_manager=None, clickhouse_manager=None):
        self.config = config
        self.postgres_manager = postgres_manager
        self.redis_manager = redis_manager
        self.clickhouse_manager = clickhouse_manager
        
        # Backup configuration
        self.backup_path = Path(config.get('backup_path', 'data/backups'))
        self.retention_days = config.get('retention_days', 30)
        self.compression_enabled = config.get('compression', True)
        self.encryption_enabled = config.get('encryption', False)
        self.encryption_key = config.get('encryption_key')
        
        # Backup tracking
        self.backup_registry: Dict[str, BackupInfo] = {}
        self.backup_schedule: Dict[str, Dict[str, Any]] = {}
        
        # State
        self.initialized = False
        
        # Ensure backup directory exists
        self.backup_path.mkdir(parents=True, exist_ok=True)
    
    async def initialize(self) -> bool:
        """Initialize backup manager"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Backup Manager...")
            
            # Load existing backup registry
            await self._load_backup_registry()
            
            # Clean up old backups
            await self._cleanup_old_backups()
            
            self.initialized = True
            logger.info(f"✓ Backup Manager initialized (backup path: {self.backup_path})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Backup Manager: {e}")
            return False
    
    async def create_backup(self, backup_type: str = 'full', 
                          databases: List[str] = None) -> Dict[str, BackupResult]:
        """Create backup for specified databases"""
        try:
            if not self.initialized:
                await self.initialize()
            
            # Default to all available databases
            if databases is None:
                databases = []
                if self.postgres_manager:
                    databases.append('postgresql')
                if self.redis_manager:
                    databases.append('redis')
                if self.clickhouse_manager:
                    databases.append('clickhouse')
            
            logger.info(f"Creating {backup_type} backup for databases: {databases}")
            
            results = {}
            
            # Create backup for each database
            for db_type in databases:
                try:
                    if db_type == 'postgresql' and self.postgres_manager:
                        result = await self._backup_postgresql(backup_type)
                    elif db_type == 'redis' and self.redis_manager:
                        result = await self._backup_redis(backup_type)
                    elif db_type == 'clickhouse' and self.clickhouse_manager:
                        result = await self._backup_clickhouse(backup_type)
                    else:
                        result = BackupResult(
                            success=False,
                            backup_info=None,
                            execution_time_ms=0.0,
                            error_message=f"Database type {db_type} not available"
                        )
                    
                    results[db_type] = result
                    
                    if result.success:
                        # Register backup
                        if result.backup_info:
                            self.backup_registry[result.backup_info.backup_id] = result.backup_info
                        logger.info(f"✓ {db_type} backup completed")
                    else:
                        logger.error(f"✗ {db_type} backup failed: {result.error_message}")
                        
                except Exception as e:
                    logger.error(f"Error backing up {db_type}: {e}")
                    results[db_type] = BackupResult(
                        success=False,
                        backup_info=None,
                        execution_time_ms=0.0,
                        error_message=str(e)
                    )
            
            # Save backup registry
            await self._save_backup_registry()
            
            return results
            
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return {}
    
    async def restore_backup(self, backup_id: str, target_database: str = None) -> bool:
        """Restore from backup"""
        try:
            if backup_id not in self.backup_registry:
                logger.error(f"Backup {backup_id} not found in registry")
                return False
            
            backup_info = self.backup_registry[backup_id]
            
            # Determine target database
            if target_database is None:
                target_database = backup_info.database_type
            
            logger.info(f"Restoring {backup_info.database_type} backup {backup_id} to {target_database}")
            
            # Verify backup file exists
            if not Path(backup_info.file_path).exists():
                logger.error(f"Backup file not found: {backup_info.file_path}")
                return False
            
            # Restore based on database type
            if target_database == 'postgresql' and self.postgres_manager:
                success = await self._restore_postgresql(backup_info)
            elif target_database == 'redis' and self.redis_manager:
                success = await self._restore_redis(backup_info)
            elif target_database == 'clickhouse' and self.clickhouse_manager:
                success = await self._restore_clickhouse(backup_info)
            else:
                logger.error(f"Cannot restore to database type: {target_database}")
                return False
            
            if success:
                logger.info(f"✓ Successfully restored backup {backup_id}")
            else:
                logger.error(f"✗ Failed to restore backup {backup_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error restoring backup {backup_id}: {e}")
            return False
    
    async def list_backups(self, database_type: str = None) -> List[BackupInfo]:
        """List available backups"""
        try:
            backups = list(self.backup_registry.values())
            
            if database_type:
                backups = [b for b in backups if b.database_type == database_type]
            
            # Sort by creation date (newest first)
            backups.sort(key=lambda x: x.created_at, reverse=True)
            
            return backups
            
        except Exception as e:
            logger.error(f"Error listing backups: {e}")
            return []
    
    async def delete_backup(self, backup_id: str) -> bool:
        """Delete a backup"""
        try:
            if backup_id not in self.backup_registry:
                logger.error(f"Backup {backup_id} not found")
                return False
            
            backup_info = self.backup_registry[backup_id]
            
            # Delete backup file
            backup_file = Path(backup_info.file_path)
            if backup_file.exists():
                backup_file.unlink()
            
            # Remove from registry
            del self.backup_registry[backup_id]
            
            # Save updated registry
            await self._save_backup_registry()
            
            logger.info(f"✓ Deleted backup {backup_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting backup {backup_id}: {e}")
            return False
    
    async def verify_backup(self, backup_id: str) -> bool:
        """Verify backup integrity"""
        try:
            if backup_id not in self.backup_registry:
                logger.error(f"Backup {backup_id} not found")
                return False
            
            backup_info = self.backup_registry[backup_id]
            backup_file = Path(backup_info.file_path)
            
            # Check if file exists
            if not backup_file.exists():
                logger.error(f"Backup file not found: {backup_info.file_path}")
                return False
            
            # Verify file size
            actual_size = backup_file.stat().st_size
            if actual_size != backup_info.file_size_bytes:
                logger.error(f"Backup file size mismatch: expected {backup_info.file_size_bytes}, got {actual_size}")
                return False
            
            # Verify checksum
            calculated_checksum = await self._calculate_file_checksum(backup_file)
            if calculated_checksum != backup_info.checksum:
                logger.error(f"Backup checksum mismatch: expected {backup_info.checksum}, got {calculated_checksum}")
                return False
            
            logger.info(f"✓ Backup {backup_id} verification successful")
            return True
            
        except Exception as e:
            logger.error(f"Error verifying backup {backup_id}: {e}")
            return False
    
    async def get_backup_stats(self) -> Dict[str, Any]:
        """Get backup statistics"""
        try:
            stats = {
                'total_backups': len(self.backup_registry),
                'backup_types': {},
                'database_types': {},
                'total_size_bytes': 0,
                'oldest_backup': None,
                'newest_backup': None
            }
            
            if not self.backup_registry:
                return stats
            
            # Calculate statistics
            for backup_info in self.backup_registry.values():
                # Count by backup type
                backup_type = backup_info.backup_type
                stats['backup_types'][backup_type] = stats['backup_types'].get(backup_type, 0) + 1
                
                # Count by database type
                db_type = backup_info.database_type
                stats['database_types'][db_type] = stats['database_types'].get(db_type, 0) + 1
                
                # Total size
                stats['total_size_bytes'] += backup_info.file_size_bytes
                
                # Oldest and newest
                if stats['oldest_backup'] is None or backup_info.created_at < stats['oldest_backup']:
                    stats['oldest_backup'] = backup_info.created_at
                
                if stats['newest_backup'] is None or backup_info.created_at > stats['newest_backup']:
                    stats['newest_backup'] = backup_info.created_at
            
            # Convert to human readable
            stats['total_size_mb'] = stats['total_size_bytes'] / (1024 * 1024)
            stats['total_size_gb'] = stats['total_size_mb'] / 1024
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting backup stats: {e}")
            return {}
    
    # Private methods
    
    async def _backup_postgresql(self, backup_type: str) -> BackupResult:
        """Backup PostgreSQL database"""
        start_time = time.time()
        
        try:
            # Generate backup filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_id = f"postgresql_{backup_type}_{timestamp}"
            filename = f"{backup_id}.sql"
            
            if self.compression_enabled:
                filename += ".gz"
            
            backup_file = self.backup_path / filename
            
            # Create pg_dump command
            pg_dump_cmd = [
                'pg_dump',
                '-h', self.postgres_manager.host,
                '-p', str(self.postgres_manager.port),
                '-U', self.postgres_manager.username,
                '-d', self.postgres_manager.database,
                '--no-password',
                '--verbose'
            ]
            
            if backup_type == 'schema_only':
                pg_dump_cmd.append('--schema-only')
            
            # Set environment variable for password
            env = os.environ.copy()
            env['PGPASSWORD'] = self.postgres_manager.password
            
            # Execute backup
            if self.compression_enabled:
                # Pipe through gzip
                with open(backup_file, 'wb') as f:
                    proc1 = subprocess.Popen(pg_dump_cmd, stdout=subprocess.PIPE, env=env)
                    proc2 = subprocess.Popen(['gzip'], stdin=proc1.stdout, stdout=f)
                    proc1.stdout.close()
                    proc2.wait()
                    
                    if proc1.returncode != 0 or proc2.returncode != 0:
                        raise Exception("pg_dump or gzip failed")
            else:
                # Direct output
                with open(backup_file, 'w') as f:
                    result = subprocess.run(pg_dump_cmd, stdout=f, env=env, capture_output=False)
                    if result.returncode != 0:
                        raise Exception("pg_dump failed")
            
            # Calculate file info
            file_size = backup_file.stat().st_size
            checksum = await self._calculate_file_checksum(backup_file)
            execution_time = (time.time() - start_time) * 1000
            
            # Create backup info
            backup_info = BackupInfo(
                backup_id=backup_id,
                backup_type=backup_type,
                database_type='postgresql',
                file_path=str(backup_file),
                file_size_bytes=file_size,
                created_at=datetime.now(timezone.utc),
                compression='gzip' if self.compression_enabled else 'none',
                checksum=checksum,
                metadata={
                    'host': self.postgres_manager.host,
                    'port': self.postgres_manager.port,
                    'database': self.postgres_manager.database
                }
            )
            
            return BackupResult(
                success=True,
                backup_info=backup_info,
                execution_time_ms=execution_time
            )
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            return BackupResult(
                success=False,
                backup_info=None,
                execution_time_ms=execution_time,
                error_message=str(e)
            )
    
    async def _backup_redis(self, backup_type: str) -> BackupResult:
        """Backup Redis database"""
        start_time = time.time()
        
        try:
            # Generate backup filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_id = f"redis_{backup_type}_{timestamp}"
            filename = f"{backup_id}.json"
            
            if self.compression_enabled:
                filename += ".gz"
            
            backup_file = self.backup_path / filename
            
            # Get all keys and their values
            backup_data = {}
            
            # Use Redis SCAN to get all keys
            cursor = 0
            while True:
                cursor, keys = await self.redis_manager.redis_client.scan(cursor)
                
                for key in keys:
                    try:
                        # Get key type and value
                        key_type = await self.redis_manager.redis_client.type(key)
                        
                        if key_type == 'string':
                            value = await self.redis_manager.redis_client.get(key)
                        elif key_type == 'hash':
                            value = await self.redis_manager.redis_client.hgetall(key)
                        elif key_type == 'list':
                            value = await self.redis_manager.redis_client.lrange(key, 0, -1)
                        elif key_type == 'set':
                            value = list(await self.redis_manager.redis_client.smembers(key))
                        elif key_type == 'zset':
                            value = await self.redis_manager.redis_client.zrange(key, 0, -1, withscores=True)
                        else:
                            continue
                        
                        # Get TTL
                        ttl = await self.redis_manager.redis_client.ttl(key)
                        
                        backup_data[key] = {
                            'type': key_type,
                            'value': value,
                            'ttl': ttl if ttl > 0 else None
                        }
                        
                    except Exception as e:
                        logger.warning(f"Failed to backup Redis key {key}: {e}")
                
                if cursor == 0:
                    break
            
            # Write backup data
            backup_json = json.dumps(backup_data, default=str, indent=2)
            
            if self.compression_enabled:
                with gzip.open(backup_file, 'wt') as f:
                    f.write(backup_json)
            else:
                with open(backup_file, 'w') as f:
                    f.write(backup_json)
            
            # Calculate file info
            file_size = backup_file.stat().st_size
            checksum = await self._calculate_file_checksum(backup_file)
            execution_time = (time.time() - start_time) * 1000
            
            # Create backup info
            backup_info = BackupInfo(
                backup_id=backup_id,
                backup_type=backup_type,
                database_type='redis',
                file_path=str(backup_file),
                file_size_bytes=file_size,
                created_at=datetime.now(timezone.utc),
                compression='gzip' if self.compression_enabled else 'none',
                checksum=checksum,
                metadata={
                    'host': self.redis_manager.host,
                    'port': self.redis_manager.port,
                    'database': self.redis_manager.database,
                    'keys_backed_up': len(backup_data)
                }
            )
            
            return BackupResult(
                success=True,
                backup_info=backup_info,
                execution_time_ms=execution_time
            )
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            return BackupResult(
                success=False,
                backup_info=None,
                execution_time_ms=execution_time,
                error_message=str(e)
            )
    
    async def _backup_clickhouse(self, backup_type: str) -> BackupResult:
        """Backup ClickHouse database"""
        start_time = time.time()
        
        try:
            # Generate backup filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_id = f"clickhouse_{backup_type}_{timestamp}"
            filename = f"{backup_id}.sql"
            
            if self.compression_enabled:
                filename += ".gz"
            
            backup_file = self.backup_path / filename
            
            # Get all tables in the database
            tables_query = f"SHOW TABLES FROM {self.clickhouse_manager.database}"
            tables_result = await self.clickhouse_manager.execute_query(tables_query)
            
            backup_content = []
            
            # Backup each table
            for table_row in tables_result:
                table_name = table_row['name']
                
                # Get table schema
                schema_query = f"SHOW CREATE TABLE {self.clickhouse_manager.database}.{table_name}"
                schema_result = await self.clickhouse_manager.execute_query(schema_query)
                
                if schema_result:
                    backup_content.append(f"-- Table: {table_name}")
                    backup_content.append(schema_result[0]['statement'])
                    backup_content.append("")
                
                # Get table data (if not schema-only backup)
                if backup_type != 'schema_only':
                    data_query = f"SELECT * FROM {self.clickhouse_manager.database}.{table_name} FORMAT TabSeparated"
                    data_result = await self.clickhouse_manager._execute_clickhouse_query(data_query)
                    
                    if data_result.success and data_result.data:
                        backup_content.append(f"-- Data for table: {table_name}")
                        for row in data_result.data:
                            backup_content.append(str(row))
                        backup_content.append("")
            
            # Write backup content
            backup_text = "\n".join(backup_content)
            
            if self.compression_enabled:
                with gzip.open(backup_file, 'wt') as f:
                    f.write(backup_text)
            else:
                with open(backup_file, 'w') as f:
                    f.write(backup_text)
            
            # Calculate file info
            file_size = backup_file.stat().st_size
            checksum = await self._calculate_file_checksum(backup_file)
            execution_time = (time.time() - start_time) * 1000
            
            # Create backup info
            backup_info = BackupInfo(
                backup_id=backup_id,
                backup_type=backup_type,
                database_type='clickhouse',
                file_path=str(backup_file),
                file_size_bytes=file_size,
                created_at=datetime.now(timezone.utc),
                compression='gzip' if self.compression_enabled else 'none',
                checksum=checksum,
                metadata={
                    'host': self.clickhouse_manager.host,
                    'port': self.clickhouse_manager.port,
                    'database': self.clickhouse_manager.database,
                    'tables_backed_up': len(tables_result)
                }
            )
            
            return BackupResult(
                success=True,
                backup_info=backup_info,
                execution_time_ms=execution_time
            )
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            return BackupResult(
                success=False,
                backup_info=None,
                execution_time_ms=execution_time,
                error_message=str(e)
            )
    
    async def _restore_postgresql(self, backup_info: BackupInfo) -> bool:
        """Restore PostgreSQL from backup"""
        try:
            backup_file = Path(backup_info.file_path)
            
            # Create psql command
            psql_cmd = [
                'psql',
                '-h', self.postgres_manager.host,
                '-p', str(self.postgres_manager.port),
                '-U', self.postgres_manager.username,
                '-d', self.postgres_manager.database,
                '--no-password'
            ]
            
            # Set environment variable for password
            env = os.environ.copy()
            env['PGPASSWORD'] = self.postgres_manager.password
            
            # Execute restore
            if backup_info.compression == 'gzip':
                # Decompress and pipe to psql
                proc1 = subprocess.Popen(['gunzip', '-c', str(backup_file)], stdout=subprocess.PIPE)
                proc2 = subprocess.Popen(psql_cmd, stdin=proc1.stdout, env=env)
                proc1.stdout.close()
                proc2.wait()
                
                return proc1.returncode == 0 and proc2.returncode == 0
            else:
                # Direct input
                with open(backup_file, 'r') as f:
                    result = subprocess.run(psql_cmd, stdin=f, env=env)
                    return result.returncode == 0
                    
        except Exception as e:
            logger.error(f"Error restoring PostgreSQL: {e}")
            return False
    
    async def _restore_redis(self, backup_info: BackupInfo) -> bool:
        """Restore Redis from backup"""
        try:
            backup_file = Path(backup_info.file_path)
            
            # Read backup data
            if backup_info.compression == 'gzip':
                with gzip.open(backup_file, 'rt') as f:
                    backup_data = json.load(f)
            else:
                with open(backup_file, 'r') as f:
                    backup_data = json.load(f)
            
            # Restore each key
            for key, key_data in backup_data.items():
                try:
                    key_type = key_data['type']
                    value = key_data['value']
                    ttl = key_data.get('ttl')
                    
                    # Restore based on type
                    if key_type == 'string':
                        await self.redis_manager.redis_client.set(key, value)
                    elif key_type == 'hash':
                        await self.redis_manager.redis_client.hset(key, mapping=value)
                    elif key_type == 'list':
                        await self.redis_manager.redis_client.lpush(key, *value)
                    elif key_type == 'set':
                        await self.redis_manager.redis_client.sadd(key, *value)
                    elif key_type == 'zset':
                        for member, score in value:
                            await self.redis_manager.redis_client.zadd(key, {member: score})
                    
                    # Set TTL if specified
                    if ttl:
                        await self.redis_manager.redis_client.expire(key, ttl)
                        
                except Exception as e:
                    logger.warning(f"Failed to restore Redis key {key}: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error restoring Redis: {e}")
            return False
    
    async def _restore_clickhouse(self, backup_info: BackupInfo) -> bool:
        """Restore ClickHouse from backup"""
        try:
            backup_file = Path(backup_info.file_path)
            
            # Read backup content
            if backup_info.compression == 'gzip':
                with gzip.open(backup_file, 'rt') as f:
                    backup_content = f.read()
            else:
                with open(backup_file, 'r') as f:
                    backup_content = f.read()
            
            # Split into statements and execute
            statements = [stmt.strip() for stmt in backup_content.split(';') if stmt.strip()]
            
            for statement in statements:
                if statement and not statement.startswith('--'):
                    result = await self.clickhouse_manager._execute_clickhouse_query(statement)
                    if not result.success:
                        logger.warning(f"Failed to execute statement: {statement[:100]}...")
            
            return True
            
        except Exception as e:
            logger.error(f"Error restoring ClickHouse: {e}")
            return False
    
    async def _calculate_file_checksum(self, file_path: Path) -> str:
        """Calculate file checksum"""
        try:
            import hashlib
            
            hash_sha256 = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            
            return hash_sha256.hexdigest()
            
        except Exception as e:
            logger.error(f"Error calculating checksum: {e}")
            return ""
    
    async def _load_backup_registry(self):
        """Load backup registry from file"""
        try:
            registry_file = self.backup_path / "backup_registry.json"
            
            if registry_file.exists():
                with open(registry_file, 'r') as f:
                    registry_data = json.load(f)
                
                # Convert to BackupInfo objects
                for backup_id, backup_data in registry_data.items():
                    backup_data['created_at'] = datetime.fromisoformat(backup_data['created_at'])
                    self.backup_registry[backup_id] = BackupInfo(**backup_data)
                
                logger.debug(f"Loaded {len(self.backup_registry)} backups from registry")
                
        except Exception as e:
            logger.warning(f"Could not load backup registry: {e}")
    
    async def _save_backup_registry(self):
        """Save backup registry to file"""
        try:
            registry_file = self.backup_path / "backup_registry.json"
            
            # Convert to serializable format
            registry_data = {}
            for backup_id, backup_info in self.backup_registry.items():
                registry_data[backup_id] = {
                    'backup_id': backup_info.backup_id,
                    'backup_type': backup_info.backup_type,
                    'database_type': backup_info.database_type,
                    'file_path': backup_info.file_path,
                    'file_size_bytes': backup_info.file_size_bytes,
                    'created_at': backup_info.created_at.isoformat(),
                    'compression': backup_info.compression,
                    'checksum': backup_info.checksum,
                    'metadata': backup_info.metadata
                }
            
            with open(registry_file, 'w') as f:
                json.dump(registry_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving backup registry: {e}")
    
    async def _cleanup_old_backups(self):
        """Clean up old backups based on retention policy"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=self.retention_days)
            
            backups_to_delete = []
            for backup_id, backup_info in self.backup_registry.items():
                if backup_info.created_at < cutoff_date:
                    backups_to_delete.append(backup_id)
            
            for backup_id in backups_to_delete:
                await self.delete_backup(backup_id)
                logger.info(f"Cleaned up old backup: {backup_id}")
            
            if backups_to_delete:
                logger.info(f"Cleaned up {len(backups_to_delete)} old backups")
                
        except Exception as e:
            logger.error(f"Error cleaning up old backups: {e}")


# Import timedelta for cleanup
from datetime import timedelta

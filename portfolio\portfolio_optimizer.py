"""
Portfolio Optimizer - Main portfolio optimization engine
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from scipy.optimize import minimize
from scipy import linalg
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class PortfolioOptimizer:
    """
    Main portfolio optimization engine that coordinates different optimization
    methods and provides a unified interface for portfolio construction.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Optimization components
        self.black_litterman = None
        self.risk_parity = None
        self.dynamic_allocator = None
        self.multi_objective = None
        
        # Portfolio state
        self.current_portfolio: Optional[Dict[str, float]] = None
        self.optimization_history: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, float] = {}
        
        # Market data
        self.returns_data: Optional[pd.DataFrame] = None
        self.covariance_matrix: Optional[np.ndarray] = None
        self.expected_returns: Optional[np.ndarray] = None
        
        # Optimization parameters
        self.optimization_params = {
            'risk_aversion': 3.0,
            'max_weight': 0.4,
            'min_weight': 0.01,
            'transaction_costs': 0.001,
            'rebalance_threshold': 0.05,
            'lookback_period': 252  # 1 year of daily data
        }
        
        # State
        self.initialized = False
        
    async def initialize(self):
        """Initialize portfolio optimizer"""
        if self.initialized:
            return
            
        logger.info("Initializing Portfolio Optimizer...")
        
        # Initialize optimization components
        await self._initialize_optimization_components()
        
        # Setup optimization parameters
        await self._setup_optimization_parameters()
        
        self.initialized = True
        logger.info("✓ Portfolio Optimizer initialized")
        
    async def _initialize_optimization_components(self):
        """Initialize optimization components"""
        from .black_litterman import BlackLittermanModel
        from .risk_parity import RiskParityOptimizer
        from .dynamic_allocation import DynamicAssetAllocator
        from .multi_objective import MultiObjectiveOptimizer
        
        self.black_litterman = BlackLittermanModel(self.config)
        await self.black_litterman.initialize()
        
        self.risk_parity = RiskParityOptimizer(self.config)
        await self.risk_parity.initialize()
        
        self.dynamic_allocator = DynamicAssetAllocator(self.config)
        await self.dynamic_allocator.initialize()
        
        self.multi_objective = MultiObjectiveOptimizer(self.config)
        await self.multi_objective.initialize()
        
    async def _setup_optimization_parameters(self):
        """Setup optimization parameters from config"""
        opt_config = self.config.get('portfolio_optimization', {})
        
        self.optimization_params.update({
            'risk_aversion': opt_config.get('risk_aversion', 3.0),
            'max_weight': opt_config.get('max_weight', 0.4),
            'min_weight': opt_config.get('min_weight', 0.01),
            'transaction_costs': opt_config.get('transaction_costs', 0.001),
            'rebalance_threshold': opt_config.get('rebalance_threshold', 0.05),
            'lookback_period': opt_config.get('lookback_period', 252)
        })
        
    async def update_market_data(self, returns_data: pd.DataFrame) -> Dict[str, Any]:
        """Update market data for optimization"""
        try:
            self.returns_data = returns_data
            
            # Calculate covariance matrix
            self.covariance_matrix = returns_data.cov().values
            
            # Calculate expected returns (simple historical mean)
            self.expected_returns = returns_data.mean().values
            
            # Update optimization components
            await self._update_optimization_components()
            
            logger.info(f"✓ Updated market data for {len(returns_data.columns)} assets")
            
            return {
                'success': True,
                'assets': len(returns_data.columns),
                'observations': len(returns_data),
                'covariance_condition': np.linalg.cond(self.covariance_matrix)
            }
            
        except Exception as e:
            logger.error(f"Error updating market data: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _update_optimization_components(self):
        """Update optimization components with new market data"""
        if self.returns_data is not None:
            await self.black_litterman.update_market_data(self.returns_data)
            await self.risk_parity.update_market_data(self.returns_data)
            await self.dynamic_allocator.update_market_data(self.returns_data)
            await self.multi_objective.update_market_data(self.returns_data)
            
    async def optimize_portfolio(self, method: str = "mean_variance", 
                               constraints: Dict[str, Any] = None,
                               objectives: Dict[str, float] = None) -> Dict[str, Any]:
        """Optimize portfolio using specified method"""
        try:
            if self.returns_data is None:
                return {'success': False, 'error': 'No market data available'}
                
            # Apply method-specific optimization
            if method == "mean_variance":
                result = await self._optimize_mean_variance(constraints)
            elif method == "black_litterman":
                result = await self._optimize_black_litterman(constraints)
            elif method == "risk_parity":
                result = await self._optimize_risk_parity(constraints)
            elif method == "dynamic_allocation":
                result = await self._optimize_dynamic_allocation(constraints)
            elif method == "multi_objective":
                result = await self._optimize_multi_objective(objectives, constraints)
            else:
                return {'success': False, 'error': f'Unknown optimization method: {method}'}
                
            if result['success']:
                # Update current portfolio
                self.current_portfolio = result['weights']
                
                # Calculate portfolio metrics
                portfolio_metrics = await self._calculate_portfolio_metrics(result['weights'])
                result.update(portfolio_metrics)
                
                # Store in history
                optimization_record = {
                    'method': method,
                    'weights': result['weights'],
                    'metrics': portfolio_metrics,
                    'timestamp': pd.Timestamp.now(),
                    'constraints': constraints,
                    'objectives': objectives
                }
                self.optimization_history.append(optimization_record)
                
                logger.info(f"✓ Portfolio optimized using {method}")
                
            return result
            
        except Exception as e:
            logger.error(f"Error optimizing portfolio: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _optimize_mean_variance(self, constraints: Dict[str, Any] = None) -> Dict[str, Any]:
        """Mean-variance optimization"""
        n_assets = len(self.expected_returns)
        
        # Objective function: minimize portfolio variance
        def objective(weights):
            return np.dot(weights, np.dot(self.covariance_matrix, weights))
            
        # Constraints
        constraints_list = [
            {'type': 'eq', 'fun': lambda w: np.sum(w) - 1.0}  # Weights sum to 1
        ]
        
        # Add return constraint if specified
        if constraints and 'target_return' in constraints:
            target_return = constraints['target_return']
            constraints_list.append({
                'type': 'eq',
                'fun': lambda w: np.dot(w, self.expected_returns) - target_return
            })
            
        # Bounds for weights
        bounds = [(self.optimization_params['min_weight'], 
                  self.optimization_params['max_weight']) for _ in range(n_assets)]
        
        # Initial guess (equal weights)
        x0 = np.ones(n_assets) / n_assets
        
        # Optimize
        result = minimize(objective, x0, method='SLSQP', bounds=bounds, constraints=constraints_list)
        
        if result.success:
            weights_dict = dict(zip(self.returns_data.columns, result.x))
            return {
                'success': True,
                'weights': weights_dict,
                'optimization_result': result,
                'method': 'mean_variance'
            }
        else:
            return {'success': False, 'error': 'Optimization failed', 'details': result.message}
            
    async def _optimize_black_litterman(self, constraints: Dict[str, Any] = None) -> Dict[str, Any]:
        """Black-Litterman optimization"""
        if self.black_litterman is None:
            return {'success': False, 'error': 'Black-Litterman model not initialized'}
            
        return await self.black_litterman.optimize(constraints)
        
    async def _optimize_risk_parity(self, constraints: Dict[str, Any] = None) -> Dict[str, Any]:
        """Risk parity optimization"""
        if self.risk_parity is None:
            return {'success': False, 'error': 'Risk parity optimizer not initialized'}
            
        return await self.risk_parity.optimize(constraints)
        
    async def _optimize_dynamic_allocation(self, constraints: Dict[str, Any] = None) -> Dict[str, Any]:
        """Dynamic asset allocation"""
        if self.dynamic_allocator is None:
            return {'success': False, 'error': 'Dynamic allocator not initialized'}
            
        return await self.dynamic_allocator.optimize(constraints)
        
    async def _optimize_multi_objective(self, objectives: Dict[str, float] = None,
                                      constraints: Dict[str, Any] = None) -> Dict[str, Any]:
        """Multi-objective optimization"""
        if self.multi_objective is None:
            return {'success': False, 'error': 'Multi-objective optimizer not initialized'}
            
        return await self.multi_objective.optimize(objectives, constraints)
        
    async def _calculate_portfolio_metrics(self, weights: Dict[str, float]) -> Dict[str, Any]:
        """Calculate portfolio performance metrics"""
        weight_array = np.array([weights[asset] for asset in self.returns_data.columns])
        
        # Expected return
        expected_return = np.dot(weight_array, self.expected_returns)
        
        # Portfolio variance and volatility
        portfolio_variance = np.dot(weight_array, np.dot(self.covariance_matrix, weight_array))
        portfolio_volatility = np.sqrt(portfolio_variance)
        
        # Sharpe ratio (assuming risk-free rate of 0)
        sharpe_ratio = expected_return / portfolio_volatility if portfolio_volatility > 0 else 0
        
        # Diversification ratio
        individual_volatilities = np.sqrt(np.diag(self.covariance_matrix))
        weighted_avg_volatility = np.dot(weight_array, individual_volatilities)
        diversification_ratio = weighted_avg_volatility / portfolio_volatility if portfolio_volatility > 0 else 0
        
        # Concentration metrics
        herfindahl_index = np.sum(weight_array ** 2)
        effective_number_assets = 1 / herfindahl_index if herfindahl_index > 0 else 0
        
        return {
            'expected_return': expected_return,
            'volatility': portfolio_volatility,
            'sharpe_ratio': sharpe_ratio,
            'diversification_ratio': diversification_ratio,
            'herfindahl_index': herfindahl_index,
            'effective_number_assets': effective_number_assets,
            'max_weight': max(weight_array),
            'min_weight': min(weight_array)
        }
        
    async def rebalance_portfolio(self, current_weights: Dict[str, float],
                                target_weights: Dict[str, float]) -> Dict[str, Any]:
        """Calculate rebalancing trades"""
        try:
            trades = {}
            total_deviation = 0
            
            for asset in target_weights:
                current_weight = current_weights.get(asset, 0)
                target_weight = target_weights[asset]
                
                deviation = target_weight - current_weight
                total_deviation += abs(deviation)
                
                if abs(deviation) > self.optimization_params['rebalance_threshold']:
                    trades[asset] = deviation
                    
            # Calculate transaction costs
            transaction_costs = sum(abs(trade) * self.optimization_params['transaction_costs'] 
                                  for trade in trades.values())
            
            return {
                'success': True,
                'trades': trades,
                'total_deviation': total_deviation,
                'transaction_costs': transaction_costs,
                'rebalance_needed': len(trades) > 0
            }
            
        except Exception as e:
            logger.error(f"Error calculating rebalancing: {e}")
            return {'success': False, 'error': str(e)}
            
    async def backtest_strategy(self, strategy_weights: List[Dict[str, float]],
                              returns_data: pd.DataFrame) -> Dict[str, Any]:
        """Backtest portfolio strategy"""
        try:
            portfolio_returns = []
            
            for i, weights in enumerate(strategy_weights):
                if i < len(returns_data):
                    period_returns = returns_data.iloc[i]
                    
                    # Calculate portfolio return for this period
                    portfolio_return = sum(weights.get(asset, 0) * period_returns[asset] 
                                         for asset in period_returns.index)
                    portfolio_returns.append(portfolio_return)
                    
            portfolio_returns = pd.Series(portfolio_returns, index=returns_data.index[:len(portfolio_returns)])
            
            # Calculate performance metrics
            total_return = (1 + portfolio_returns).prod() - 1
            annualized_return = (1 + total_return) ** (252 / len(portfolio_returns)) - 1
            volatility = portfolio_returns.std() * np.sqrt(252)
            sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
            
            # Maximum drawdown
            cumulative_returns = (1 + portfolio_returns).cumprod()
            running_max = cumulative_returns.expanding().max()
            drawdowns = (cumulative_returns - running_max) / running_max
            max_drawdown = drawdowns.min()
            
            return {
                'success': True,
                'total_return': total_return,
                'annualized_return': annualized_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'portfolio_returns': portfolio_returns.tolist()
            }
            
        except Exception as e:
            logger.error(f"Error backtesting strategy: {e}")
            return {'success': False, 'error': str(e)}
            
    async def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get current portfolio summary"""
        if self.current_portfolio is None:
            return {'status': 'no_portfolio'}
            
        # Calculate current metrics
        current_metrics = await self._calculate_portfolio_metrics(self.current_portfolio)
        
        return {
            'current_weights': self.current_portfolio,
            'metrics': current_metrics,
            'optimization_history': len(self.optimization_history),
            'last_optimization': self.optimization_history[-1]['timestamp'] if self.optimization_history else None,
            'assets': len(self.current_portfolio)
        }
        
    async def get_optimization_analytics(self) -> Dict[str, Any]:
        """Get optimization analytics"""
        if not self.optimization_history:
            return {'total_optimizations': 0}
            
        # Method usage
        method_counts = {}
        for record in self.optimization_history:
            method = record['method']
            method_counts[method] = method_counts.get(method, 0) + 1
            
        # Performance trends
        sharpe_ratios = [record['metrics']['sharpe_ratio'] for record in self.optimization_history]
        volatilities = [record['metrics']['volatility'] for record in self.optimization_history]
        
        return {
            'total_optimizations': len(self.optimization_history),
            'method_usage': method_counts,
            'average_sharpe_ratio': np.mean(sharpe_ratios),
            'average_volatility': np.mean(volatilities),
            'latest_optimization': self.optimization_history[-1]['timestamp'],
            'optimization_frequency': len(self.optimization_history) / max(1,
                (pd.Timestamp.now() - self.optimization_history[0]['timestamp']).days)
        }

    async def optimize_for_regime(self, regime: str, constraints: Dict[str, Any] = None) -> Dict[str, Any]:
        """Optimize portfolio for specific market regime"""
        regime_params = {
            'bull_market': {'risk_aversion': 2.0, 'max_weight': 0.5},
            'bear_market': {'risk_aversion': 5.0, 'max_weight': 0.3},
            'high_volatility': {'risk_aversion': 4.0, 'max_weight': 0.25},
            'low_volatility': {'risk_aversion': 2.5, 'max_weight': 0.4}
        }

        if regime not in regime_params:
            return {'success': False, 'error': f'Unknown regime: {regime}'}

        # Temporarily update parameters
        original_params = self.optimization_params.copy()
        self.optimization_params.update(regime_params[regime])

        try:
            # Optimize with regime-specific parameters
            result = await self.optimize_portfolio("mean_variance", constraints)
            result['regime'] = regime
            return result
        finally:
            # Restore original parameters
            self.optimization_params = original_params

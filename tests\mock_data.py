"""
Mock Data Generator - Generate realistic test data
"""

import asyncio
import logging
import random
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
import numpy as np
import pandas as pd
from faker import Faker

logger = logging.getLogger(__name__)


@dataclass
class MarketDataPoint:
    """Market data point structure"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    bid: float
    ask: float


@dataclass
class TradeData:
    """Trade data structure"""
    trade_id: str
    symbol: str
    side: str  # buy/sell
    quantity: float
    price: float
    timestamp: datetime
    order_type: str
    status: str


@dataclass
class PortfolioData:
    """Portfolio data structure"""
    portfolio_id: str
    name: str
    total_value: float
    cash: float
    positions: Dict[str, Dict[str, Any]]
    performance: Dict[str, float]
    created_at: datetime


class MockDataGenerator:
    """
    Comprehensive mock data generator for testing.
    
    Features:
    - Realistic market data generation
    - Trading data simulation
    - Portfolio data creation
    - Agent communication data
    - Performance metrics simulation
    - Error scenario generation
    - Time series data
    - Statistical distributions
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.mock_config = config.get('mock_data', {})
        
        # Faker instance for generating realistic data
        self.faker = Faker()
        
        # Market symbols
        self.symbols = [
            'AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX',
            'BABA', 'V', 'JPM', 'JNJ', 'WMT', 'PG', 'UNH', 'HD', 'MA', 'DIS',
            'PYPL', 'ADBE', 'CRM', 'INTC', 'CMCSA', 'VZ', 'KO', 'PFE', 'T',
            'XOM', 'CVX', 'BAC', 'WFC', 'C', 'GS', 'MS', 'AXP', 'IBM', 'ORCL'
        ]
        
        # Base prices for symbols
        self.base_prices = {symbol: random.uniform(50, 500) for symbol in self.symbols}
        
        # Random seed for reproducible data
        self.seed = self.mock_config.get('seed', 42)
        random.seed(self.seed)
        np.random.seed(self.seed)
        
        # State
        self.initialized = False
    
    async def initialize(self) -> bool:
        """Initialize mock data generator"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Mock Data Generator...")
            
            # Setup faker locale
            self.faker = Faker('en_US')
            
            self.initialized = True
            logger.info("✓ Mock Data Generator initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Mock Data Generator: {e}")
            return False
    
    async def generate_market_data(self, symbol: str, 
                                 start_time: datetime = None,
                                 end_time: datetime = None,
                                 interval: str = '1m') -> List[MarketDataPoint]:
        """Generate realistic market data"""
        try:
            if start_time is None:
                start_time = datetime.now() - timedelta(hours=24)
            if end_time is None:
                end_time = datetime.now()
            
            # Calculate interval in seconds
            interval_seconds = self._parse_interval(interval)
            
            # Generate time series
            timestamps = []
            current_time = start_time
            while current_time <= end_time:
                timestamps.append(current_time)
                current_time += timedelta(seconds=interval_seconds)
            
            # Generate price data using random walk
            base_price = self.base_prices.get(symbol, 100.0)
            prices = self._generate_price_series(base_price, len(timestamps))
            
            market_data = []
            for i, timestamp in enumerate(timestamps):
                # Generate OHLC data
                close_price = prices[i]
                volatility = random.uniform(0.005, 0.02)  # 0.5% to 2% volatility
                
                high = close_price * (1 + random.uniform(0, volatility))
                low = close_price * (1 - random.uniform(0, volatility))
                open_price = prices[i-1] if i > 0 else close_price
                
                # Generate bid/ask spread
                spread = close_price * random.uniform(0.0001, 0.001)  # 0.01% to 0.1% spread
                bid = close_price - spread / 2
                ask = close_price + spread / 2
                
                # Generate volume
                volume = int(random.lognormal(10, 1.5))  # Log-normal distribution for volume
                
                data_point = MarketDataPoint(
                    symbol=symbol,
                    timestamp=timestamp,
                    open=round(open_price, 2),
                    high=round(high, 2),
                    low=round(low, 2),
                    close=round(close_price, 2),
                    volume=volume,
                    bid=round(bid, 2),
                    ask=round(ask, 2)
                )
                
                market_data.append(data_point)
            
            return market_data
            
        except Exception as e:
            logger.error(f"Error generating market data for {symbol}: {e}")
            return []
    
    async def generate_trade_data(self, count: int = 100) -> List[TradeData]:
        """Generate realistic trade data"""
        try:
            trades = []
            
            for i in range(count):
                symbol = random.choice(self.symbols)
                base_price = self.base_prices[symbol]
                
                trade = TradeData(
                    trade_id=f"trade_{i+1:06d}",
                    symbol=symbol,
                    side=random.choice(['buy', 'sell']),
                    quantity=round(random.uniform(1, 1000), 2),
                    price=round(base_price * random.uniform(0.95, 1.05), 2),
                    timestamp=self.faker.date_time_between(start_date='-1d', end_date='now'),
                    order_type=random.choice(['market', 'limit', 'stop', 'stop_limit']),
                    status=random.choice(['filled', 'partial', 'pending', 'cancelled'])
                )
                
                trades.append(trade)
            
            # Sort by timestamp
            trades.sort(key=lambda x: x.timestamp)
            
            return trades
            
        except Exception as e:
            logger.error(f"Error generating trade data: {e}")
            return []
    
    async def generate_portfolio_data(self, count: int = 5) -> List[PortfolioData]:
        """Generate realistic portfolio data"""
        try:
            portfolios = []
            
            for i in range(count):
                # Generate positions
                num_positions = random.randint(3, 10)
                selected_symbols = random.sample(self.symbols, num_positions)
                
                positions = {}
                total_position_value = 0
                
                for symbol in selected_symbols:
                    quantity = random.uniform(10, 500)
                    price = self.base_prices[symbol]
                    market_value = quantity * price
                    
                    positions[symbol] = {
                        'quantity': round(quantity, 2),
                        'average_price': round(price * random.uniform(0.9, 1.1), 2),
                        'current_price': round(price, 2),
                        'market_value': round(market_value, 2),
                        'unrealized_pnl': round(market_value * random.uniform(-0.1, 0.1), 2)
                    }
                    
                    total_position_value += market_value
                
                # Generate cash balance
                cash = random.uniform(10000, 100000)
                total_value = total_position_value + cash
                
                # Generate performance metrics
                performance = {
                    'total_return': random.uniform(-0.2, 0.3),
                    'daily_return': random.uniform(-0.05, 0.05),
                    'volatility': random.uniform(0.1, 0.4),
                    'sharpe_ratio': random.uniform(-1, 3),
                    'max_drawdown': random.uniform(-0.3, -0.05)
                }
                
                portfolio = PortfolioData(
                    portfolio_id=f"portfolio_{i+1:03d}",
                    name=f"Portfolio {i+1}",
                    total_value=round(total_value, 2),
                    cash=round(cash, 2),
                    positions=positions,
                    performance=performance,
                    created_at=self.faker.date_time_between(start_date='-1y', end_date='-1m')
                )
                
                portfolios.append(portfolio)
            
            return portfolios
            
        except Exception as e:
            logger.error(f"Error generating portfolio data: {e}")
            return []
    
    async def generate_agent_messages(self, count: int = 50) -> List[Dict[str, Any]]:
        """Generate agent communication messages"""
        try:
            messages = []
            
            message_types = [
                'market_analysis', 'trade_signal', 'risk_alert', 'portfolio_update',
                'strategy_recommendation', 'performance_report', 'system_status'
            ]
            
            for i in range(count):
                message = {
                    'message_id': f"msg_{i+1:06d}",
                    'from_agent': f"agent_{random.randint(1, 10):02d}",
                    'to_agent': f"agent_{random.randint(1, 10):02d}",
                    'message_type': random.choice(message_types),
                    'timestamp': self.faker.date_time_between(start_date='-1d', end_date='now'),
                    'priority': random.choice(['low', 'medium', 'high', 'urgent']),
                    'content': {
                        'subject': self.faker.sentence(),
                        'body': self.faker.text(max_nb_chars=500),
                        'data': {
                            'symbol': random.choice(self.symbols),
                            'confidence': random.uniform(0.1, 1.0),
                            'recommendation': random.choice(['buy', 'sell', 'hold'])
                        }
                    }
                }
                
                messages.append(message)
            
            # Sort by timestamp
            messages.sort(key=lambda x: x['timestamp'])
            
            return messages
            
        except Exception as e:
            logger.error(f"Error generating agent messages: {e}")
            return []
    
    async def generate_performance_metrics(self, component: str = 'system') -> Dict[str, float]:
        """Generate realistic performance metrics"""
        try:
            base_metrics = {
                'response_time': random.uniform(0.1, 2.0),
                'cpu_usage': random.uniform(0.1, 0.8),
                'memory_usage': random.uniform(0.2, 0.9),
                'disk_usage': random.uniform(0.1, 0.7),
                'network_latency': random.uniform(10, 100),
                'error_rate': random.uniform(0.001, 0.05),
                'throughput': random.uniform(100, 1000),
                'queue_size': random.randint(0, 100)
            }
            
            # Add component-specific metrics
            if component == 'database':
                base_metrics.update({
                    'connection_pool_usage': random.uniform(0.1, 0.8),
                    'query_time': random.uniform(0.01, 1.0),
                    'deadlocks': random.randint(0, 5)
                })
            elif component == 'api':
                base_metrics.update({
                    'requests_per_second': random.uniform(10, 500),
                    'active_connections': random.randint(10, 200),
                    'cache_hit_rate': random.uniform(0.7, 0.95)
                })
            elif component == 'trading':
                base_metrics.update({
                    'orders_per_second': random.uniform(1, 50),
                    'fill_rate': random.uniform(0.8, 1.0),
                    'slippage': random.uniform(0.001, 0.01)
                })
            
            return base_metrics
            
        except Exception as e:
            logger.error(f"Error generating performance metrics: {e}")
            return {}
    
    async def generate_error_scenarios(self) -> List[Dict[str, Any]]:
        """Generate error scenarios for testing"""
        try:
            error_scenarios = [
                {
                    'scenario_id': 'network_timeout',
                    'description': 'Network timeout during API call',
                    'error_type': 'TimeoutError',
                    'severity': 'medium',
                    'frequency': 0.02,  # 2% chance
                    'recovery_time': random.uniform(5, 30)
                },
                {
                    'scenario_id': 'database_connection_lost',
                    'description': 'Database connection lost',
                    'error_type': 'ConnectionError',
                    'severity': 'high',
                    'frequency': 0.001,  # 0.1% chance
                    'recovery_time': random.uniform(10, 60)
                },
                {
                    'scenario_id': 'market_data_stale',
                    'description': 'Market data feed becomes stale',
                    'error_type': 'DataStaleError',
                    'severity': 'medium',
                    'frequency': 0.005,  # 0.5% chance
                    'recovery_time': random.uniform(1, 10)
                },
                {
                    'scenario_id': 'memory_exhaustion',
                    'description': 'System memory exhaustion',
                    'error_type': 'MemoryError',
                    'severity': 'critical',
                    'frequency': 0.0001,  # 0.01% chance
                    'recovery_time': random.uniform(30, 120)
                },
                {
                    'scenario_id': 'api_rate_limit',
                    'description': 'API rate limit exceeded',
                    'error_type': 'RateLimitError',
                    'severity': 'low',
                    'frequency': 0.01,  # 1% chance
                    'recovery_time': random.uniform(60, 300)
                }
            ]
            
            return error_scenarios
            
        except Exception as e:
            logger.error(f"Error generating error scenarios: {e}")
            return []
    
    async def generate_time_series_data(self, metric_name: str, 
                                      duration_hours: int = 24,
                                      interval_minutes: int = 1) -> pd.DataFrame:
        """Generate time series data for metrics"""
        try:
            # Generate timestamps
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=duration_hours)
            
            timestamps = pd.date_range(
                start=start_time,
                end=end_time,
                freq=f'{interval_minutes}T'
            )
            
            # Generate values based on metric type
            if 'price' in metric_name.lower():
                # Price-like data with random walk
                base_value = random.uniform(50, 500)
                values = self._generate_price_series(base_value, len(timestamps))
            elif 'volume' in metric_name.lower():
                # Volume-like data with log-normal distribution
                values = np.random.lognormal(10, 1.5, len(timestamps))
            elif 'usage' in metric_name.lower() or 'rate' in metric_name.lower():
                # Usage/rate data between 0 and 1
                values = np.random.beta(2, 5, len(timestamps))
            else:
                # Generic metric data
                values = np.random.normal(100, 20, len(timestamps))
            
            # Create DataFrame
            df = pd.DataFrame({
                'timestamp': timestamps,
                'value': values
            })
            
            return df
            
        except Exception as e:
            logger.error(f"Error generating time series data: {e}")
            return pd.DataFrame()
    
    # Private methods
    
    def _parse_interval(self, interval: str) -> int:
        """Parse interval string to seconds"""
        try:
            if interval.endswith('s'):
                return int(interval[:-1])
            elif interval.endswith('m'):
                return int(interval[:-1]) * 60
            elif interval.endswith('h'):
                return int(interval[:-1]) * 3600
            elif interval.endswith('d'):
                return int(interval[:-1]) * 86400
            else:
                return 60  # Default to 1 minute
                
        except:
            return 60
    
    def _generate_price_series(self, base_price: float, length: int) -> List[float]:
        """Generate realistic price series using random walk"""
        try:
            prices = [base_price]
            
            for i in range(1, length):
                # Random walk with mean reversion
                change = np.random.normal(0, 0.01)  # 1% volatility
                mean_reversion = (base_price - prices[-1]) * 0.001  # Weak mean reversion
                
                new_price = prices[-1] * (1 + change + mean_reversion)
                new_price = max(new_price, base_price * 0.5)  # Floor at 50% of base
                new_price = min(new_price, base_price * 2.0)   # Ceiling at 200% of base
                
                prices.append(new_price)
            
            return prices
            
        except Exception as e:
            logger.error(f"Error generating price series: {e}")
            return [base_price] * length

"""
Analytics Router - Analytics and reporting endpoints
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


def create_router(trading_system=None, auth_manager=None) -> APIRouter:
    """Create analytics router with dependencies"""
    
    router = APIRouter()
    
    @router.get("/insights", summary="Get market insights")
    async def get_market_insights(
        symbol: Optional[str] = Query(None, description="Filter by symbol"),
        user=Depends(auth_manager.require_permission("analytics:read")) if auth_manager else None
    ):
        """Get market insights and analysis."""
        try:
            if not trading_system or not hasattr(trading_system, 'analytics_engine'):
                return {"insights": []}
            
            insights = await trading_system.analytics_engine.get_market_insights(symbol)
            
            return {
                "insights": [insight.__dict__ for insight in insights],
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting market insights: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get market insights"
            )
    
    return router

"""
API Server - Main FastAPI application server
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import uvicorn
from fastapi import FastAP<PERSON>, HTTPException, Depends, status, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from contextlib import asynccontextmanager

from .routers import (
    system_router,
    agents_router,
    strategies_router,
    portfolio_router,
    analytics_router,
    risk_router,
    execution_router,
    market_data_router,
    learning_router,
    database_router
)
from .websocket_manager import WebSocketManager
from .auth_manager import AuthManager
from .rate_limiter import RateLimiter
from .middleware import RequestLoggingMiddleware, ErrorHandlingMiddleware
from web.web_server import WebServer

logger = logging.getLogger(__name__)


class APIServer:
    """
    Main API server for the Advanced Ollama Trading Agent System.
    
    Features:
    - RESTful API endpoints for all system components
    - Real-time WebSocket connections
    - Authentication and authorization
    - Rate limiting and security
    - Request/response validation
    - API documentation
    - Health monitoring
    """
    
    def __init__(self, config: Dict[str, Any], trading_system=None):
        self.config = config
        self.api_config = config.get('api', {})
        self.trading_system = trading_system
        
        # Server configuration
        self.host = self.api_config.get('host', '0.0.0.0')
        self.port = self.api_config.get('port', 8001)
        self.debug = self.api_config.get('debug', False)
        self.reload = self.api_config.get('reload', False)
        
        # Security configuration
        self.cors_origins = self.api_config.get('cors_origins', ["*"])
        self.rate_limit_enabled = self.api_config.get('rate_limiting', {}).get('enabled', True)
        
        # Components
        self.app: Optional[FastAPI] = None
        self.websocket_manager: Optional[WebSocketManager] = None
        self.auth_manager: Optional[AuthManager] = None
        self.rate_limiter: Optional[RateLimiter] = None
        self.web_server: Optional[WebServer] = None
        
        # Server state
        self.server = None
        self.running = False
        self.initialized = False
    
    async def initialize(self) -> bool:
        """Initialize API server"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing API Server...")
            
            # Initialize components
            await self._initialize_components()
            
            # Create FastAPI app
            await self._create_app()
            
            # Setup middleware
            await self._setup_middleware()
            
            # Setup routes
            await self._setup_routes()
            
            # Setup WebSocket endpoints
            await self._setup_websockets()

            # Setup web interface
            await self._setup_web_interface()

            self.initialized = True
            logger.info("✓ API Server initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize API Server: {e}")
            return False
    
    async def start(self) -> bool:
        """Start API server"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        if self.running:
            return True
            
        try:
            logger.info(f"Starting API Server on {self.host}:{self.port}...")
            
            # Start WebSocket manager
            if self.websocket_manager:
                await self.websocket_manager.start()
            
            # Configure uvicorn
            config = uvicorn.Config(
                app=self.app,
                host=self.host,
                port=self.port,
                log_level="info" if not self.debug else "debug",
                reload=self.reload,
                access_log=True
            )
            
            # Start server
            self.server = uvicorn.Server(config)
            
            # Run server in background task
            asyncio.create_task(self.server.serve())
            
            self.running = True
            logger.info(f"✓ API Server started on http://{self.host}:{self.port}")
            logger.info(f"📚 API Documentation: http://{self.host}:{self.port}/docs")
            logger.info(f"🔄 Alternative docs: http://{self.host}:{self.port}/redoc")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start API Server: {e}")
            return False
    
    async def stop(self) -> bool:
        """Stop API server"""
        if not self.running:
            return True
            
        try:
            logger.info("Stopping API Server...")
            
            # Stop WebSocket manager
            if self.websocket_manager:
                await self.websocket_manager.stop()
            
            # Stop server
            if self.server:
                self.server.should_exit = True
                await self.server.shutdown()
            
            self.running = False
            logger.info("✓ API Server stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping API Server: {e}")
            return False
    
    def get_app(self) -> FastAPI:
        """Get FastAPI application instance"""
        return self.app
    
    # Private methods
    
    async def _initialize_components(self):
        """Initialize API components"""
        try:
            # Initialize WebSocket manager
            self.websocket_manager = WebSocketManager(self.config)
            await self.websocket_manager.initialize()
            
            # Initialize auth manager
            self.auth_manager = AuthManager(self.config)
            await self.auth_manager.initialize()
            
            # Initialize rate limiter
            if self.rate_limit_enabled:
                self.rate_limiter = RateLimiter(self.config)
                await self.rate_limiter.initialize()

            # Initialize web server
            self.web_server = WebServer(self.config, self)
            await self.web_server.initialize()

            logger.debug("API components initialized")
            
        except Exception as e:
            logger.error(f"Error initializing API components: {e}")
            raise
    
    async def _create_app(self):
        """Create FastAPI application"""
        try:
            # Create lifespan context manager
            @asynccontextmanager
            async def lifespan(app: FastAPI):
                # Startup
                logger.info("API application starting up...")
                yield
                # Shutdown
                logger.info("API application shutting down...")
            
            # Create FastAPI app
            self.app = FastAPI(
                title="Advanced Ollama Trading Agent System API",
                description="Comprehensive API for the Advanced Ollama Trading Agent System",
                version="1.0.0",
                docs_url="/docs",
                redoc_url="/redoc",
                openapi_url="/openapi.json",
                lifespan=lifespan
            )
            
            logger.debug("FastAPI application created")
            
        except Exception as e:
            logger.error(f"Error creating FastAPI app: {e}")
            raise
    
    async def _setup_middleware(self):
        """Setup middleware"""
        try:
            # CORS middleware
            self.app.add_middleware(
                CORSMiddleware,
                allow_origins=self.cors_origins,
                allow_credentials=True,
                allow_methods=["*"],
                allow_headers=["*"],
            )
            
            # Gzip compression
            self.app.add_middleware(GZipMiddleware, minimum_size=1000)
            
            # Custom middleware
            self.app.add_middleware(RequestLoggingMiddleware)
            self.app.add_middleware(ErrorHandlingMiddleware)
            
            logger.debug("Middleware configured")
            
        except Exception as e:
            logger.error(f"Error setting up middleware: {e}")
            raise
    
    async def _setup_routes(self):
        """Setup API routes"""
        try:
            # Include routers with dependencies
            dependencies = []
            
            # Add rate limiting dependency if enabled
            if self.rate_limiter:
                dependencies.append(Depends(self.rate_limiter.check_rate_limit))
            
            # System routes
            self.app.include_router(
                system_router.create_router(self.trading_system, self.auth_manager),
                prefix="/api/v1/system",
                tags=["System"],
                dependencies=dependencies
            )
            
            # Agents routes
            self.app.include_router(
                agents_router.create_router(self.trading_system, self.auth_manager),
                prefix="/api/v1/agents",
                tags=["Agents"],
                dependencies=dependencies
            )
            
            # Strategies routes
            self.app.include_router(
                strategies_router.create_router(self.trading_system, self.auth_manager),
                prefix="/api/v1/strategies",
                tags=["Strategies"],
                dependencies=dependencies
            )
            
            # Portfolio routes
            self.app.include_router(
                portfolio_router.create_router(self.trading_system, self.auth_manager),
                prefix="/api/v1/portfolio",
                tags=["Portfolio"],
                dependencies=dependencies
            )
            
            # Analytics routes
            self.app.include_router(
                analytics_router.create_router(self.trading_system, self.auth_manager),
                prefix="/api/v1/analytics",
                tags=["Analytics"],
                dependencies=dependencies
            )
            
            # Risk routes
            self.app.include_router(
                risk_router.create_router(self.trading_system, self.auth_manager),
                prefix="/api/v1/risk",
                tags=["Risk Management"],
                dependencies=dependencies
            )
            
            # Execution routes
            self.app.include_router(
                execution_router.create_router(self.trading_system, self.auth_manager),
                prefix="/api/v1/execution",
                tags=["Execution"],
                dependencies=dependencies
            )
            
            # Market data routes
            self.app.include_router(
                market_data_router.create_router(self.trading_system, self.auth_manager),
                prefix="/api/v1/market-data",
                tags=["Market Data"],
                dependencies=dependencies
            )
            
            # Learning routes
            self.app.include_router(
                learning_router.create_router(self.trading_system, self.auth_manager),
                prefix="/api/v1/learning",
                tags=["Learning"],
                dependencies=dependencies
            )
            
            # Database routes
            self.app.include_router(
                database_router.create_router(self.trading_system, self.auth_manager),
                prefix="/api/v1/database",
                tags=["Database"],
                dependencies=dependencies
            )
            
            # Health check endpoint
            @self.app.get("/health", tags=["Health"])
            async def health_check():
                """Health check endpoint"""
                return {
                    "status": "healthy",
                    "timestamp": datetime.now().isoformat(),
                    "version": "1.0.0",
                    "components": {
                        "api_server": self.running,
                        "websocket_manager": self.websocket_manager.running if self.websocket_manager else False,
                        "trading_system": self.trading_system.running if self.trading_system else False
                    }
                }
            
            # Root endpoint
            @self.app.get("/", tags=["Root"])
            async def root():
                """Root endpoint"""
                return {
                    "message": "Advanced Ollama Trading Agent System API",
                    "version": "1.0.0",
                    "docs": "/docs",
                    "health": "/health"
                }
            
            logger.debug("API routes configured")
            
        except Exception as e:
            logger.error(f"Error setting up routes: {e}")
            raise
    
    async def _setup_websockets(self):
        """Setup WebSocket endpoints"""
        try:
            if not self.websocket_manager:
                return
            
            # Real-time market data
            @self.app.websocket("/ws/market-data")
            async def market_data_websocket(websocket):
                await self.websocket_manager.handle_market_data_connection(websocket)
            
            # Real-time portfolio updates
            @self.app.websocket("/ws/portfolio")
            async def portfolio_websocket(websocket):
                await self.websocket_manager.handle_portfolio_connection(websocket)
            
            # Real-time analytics
            @self.app.websocket("/ws/analytics")
            async def analytics_websocket(websocket):
                await self.websocket_manager.handle_analytics_connection(websocket)
            
            # Real-time system status
            @self.app.websocket("/ws/system")
            async def system_websocket(websocket):
                await self.websocket_manager.handle_system_connection(websocket)
            
            # Real-time agent communications
            @self.app.websocket("/ws/agents")
            async def agents_websocket(websocket):
                await self.websocket_manager.handle_agents_connection(websocket)
            
            logger.debug("WebSocket endpoints configured")
            
        except Exception as e:
            logger.error(f"Error setting up WebSocket endpoints: {e}")
            raise

    async def _setup_web_interface(self):
        """Setup web interface routes"""
        try:
            if self.web_server:
                self.web_server.setup_routes(self.app)
                logger.debug("Web interface routes configured")

        except Exception as e:
            logger.error(f"Error setting up web interface: {e}")
            raise


# Utility functions for API server

def create_api_server(config: Dict[str, Any], trading_system=None) -> APIServer:
    """Create and configure API server"""
    return APIServer(config, trading_system)


async def run_api_server(config: Dict[str, Any], trading_system=None):
    """Run API server standalone"""
    server = create_api_server(config, trading_system)
    
    try:
        await server.initialize()
        await server.start()
        
        # Keep server running
        while server.running:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Received shutdown signal")
    finally:
        await server.stop()

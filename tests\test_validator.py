"""
Test Validator - Validation and verification utilities
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass
from datetime import datetime, timedelta
import numpy as np
import pandas as pd

logger = logging.getLogger(__name__)


@dataclass
class ValidationRule:
    """Validation rule definition"""
    rule_id: str
    name: str
    description: str
    validator_func: Callable
    severity: str = "error"  # error, warning, info
    enabled: bool = True


@dataclass
class ValidationResult:
    """Validation result"""
    rule_id: str
    rule_name: str
    status: str  # passed, failed, skipped
    message: str
    severity: str
    timestamp: datetime
    details: Dict[str, Any] = None


class TestValidator:
    """
    Comprehensive validation and verification system.
    
    Features:
    - Data validation
    - Business logic validation
    - Performance validation
    - Security validation
    - Configuration validation
    - API response validation
    - Database integrity validation
    - Custom validation rules
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.validation_config = config.get('validation', {})
        
        # Validation rules
        self.validation_rules: Dict[str, ValidationRule] = {}
        
        # Validation results
        self.validation_results: List[ValidationResult] = []
        
        # Performance thresholds
        self.performance_thresholds = {
            'response_time': 1.0,  # seconds
            'memory_usage': 0.8,   # 80% of available
            'cpu_usage': 0.7,      # 70% of available
            'disk_usage': 0.9,     # 90% of available
            'error_rate': 0.01     # 1% error rate
        }
        
        # State
        self.initialized = False
    
    async def initialize(self) -> bool:
        """Initialize test validator"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Test Validator...")
            
            # Setup default validation rules
            await self._setup_default_rules()
            
            self.initialized = True
            logger.info("✓ Test Validator initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Test Validator: {e}")
            return False
    
    async def add_validation_rule(self, rule: ValidationRule):
        """Add a custom validation rule"""
        try:
            self.validation_rules[rule.rule_id] = rule
            logger.debug(f"Added validation rule: {rule.name}")
            
        except Exception as e:
            logger.error(f"Error adding validation rule {rule.rule_id}: {e}")
    
    async def validate_data(self, data: Any, schema: Dict[str, Any]) -> ValidationResult:
        """Validate data against schema"""
        try:
            rule_id = "data_validation"
            
            # Basic type validation
            if 'type' in schema:
                expected_type = schema['type']
                if not isinstance(data, expected_type):
                    return ValidationResult(
                        rule_id=rule_id,
                        rule_name="Data Type Validation",
                        status="failed",
                        message=f"Expected {expected_type.__name__}, got {type(data).__name__}",
                        severity="error",
                        timestamp=datetime.now()
                    )
            
            # Required fields validation
            if isinstance(data, dict) and 'required' in schema:
                for field in schema['required']:
                    if field not in data:
                        return ValidationResult(
                            rule_id=rule_id,
                            rule_name="Required Fields Validation",
                            status="failed",
                            message=f"Required field '{field}' is missing",
                            severity="error",
                            timestamp=datetime.now()
                        )
            
            # Range validation for numeric data
            if isinstance(data, (int, float)) and 'range' in schema:
                min_val, max_val = schema['range']
                if not (min_val <= data <= max_val):
                    return ValidationResult(
                        rule_id=rule_id,
                        rule_name="Range Validation",
                        status="failed",
                        message=f"Value {data} is outside range [{min_val}, {max_val}]",
                        severity="error",
                        timestamp=datetime.now()
                    )
            
            return ValidationResult(
                rule_id=rule_id,
                rule_name="Data Validation",
                status="passed",
                message="Data validation passed",
                severity="info",
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error validating data: {e}")
            return ValidationResult(
                rule_id="data_validation",
                rule_name="Data Validation",
                status="failed",
                message=f"Validation error: {str(e)}",
                severity="error",
                timestamp=datetime.now()
            )
    
    async def validate_api_response(self, response: Dict[str, Any], 
                                  expected_schema: Dict[str, Any]) -> ValidationResult:
        """Validate API response format and content"""
        try:
            rule_id = "api_response_validation"
            
            # Check status code
            if 'status_code' in expected_schema:
                expected_status = expected_schema['status_code']
                actual_status = response.get('status_code')
                
                if actual_status != expected_status:
                    return ValidationResult(
                        rule_id=rule_id,
                        rule_name="API Response Status Validation",
                        status="failed",
                        message=f"Expected status {expected_status}, got {actual_status}",
                        severity="error",
                        timestamp=datetime.now()
                    )
            
            # Validate response body
            if 'body_schema' in expected_schema:
                body_validation = await self.validate_data(
                    response.get('body', {}), 
                    expected_schema['body_schema']
                )
                if body_validation.status == "failed":
                    return body_validation
            
            return ValidationResult(
                rule_id=rule_id,
                rule_name="API Response Validation",
                status="passed",
                message="API response validation passed",
                severity="info",
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error validating API response: {e}")
            return ValidationResult(
                rule_id="api_response_validation",
                rule_name="API Response Validation",
                status="failed",
                message=f"Validation error: {str(e)}",
                severity="error",
                timestamp=datetime.now()
            )
    
    async def validate_performance(self, metrics: Dict[str, float]) -> List[ValidationResult]:
        """Validate performance metrics against thresholds"""
        try:
            results = []
            
            for metric_name, value in metrics.items():
                if metric_name in self.performance_thresholds:
                    threshold = self.performance_thresholds[metric_name]
                    
                    # Determine if metric is within acceptable range
                    if metric_name == 'error_rate':
                        # Lower is better for error rate
                        passed = value <= threshold
                    else:
                        # Lower is better for most metrics
                        passed = value <= threshold
                    
                    status = "passed" if passed else "failed"
                    severity = "error" if not passed else "info"
                    message = f"{metric_name}: {value:.3f} (threshold: {threshold:.3f})"
                    
                    result = ValidationResult(
                        rule_id=f"performance_{metric_name}",
                        rule_name=f"Performance Validation - {metric_name}",
                        status=status,
                        message=message,
                        severity=severity,
                        timestamp=datetime.now(),
                        details={'value': value, 'threshold': threshold}
                    )
                    
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Error validating performance: {e}")
            return []
    
    async def validate_database_integrity(self, database_connection) -> List[ValidationResult]:
        """Validate database integrity and constraints"""
        try:
            results = []
            
            # This would implement actual database validation
            # For now, return placeholder results
            
            result = ValidationResult(
                rule_id="database_integrity",
                rule_name="Database Integrity Validation",
                status="passed",
                message="Database integrity validation passed",
                severity="info",
                timestamp=datetime.now()
            )
            
            results.append(result)
            return results
            
        except Exception as e:
            logger.error(f"Error validating database integrity: {e}")
            return []
    
    async def validate_configuration(self, config: Dict[str, Any]) -> List[ValidationResult]:
        """Validate system configuration"""
        try:
            results = []
            
            # Validate required configuration sections
            required_sections = ['database', 'api', 'agents', 'risk']
            
            for section in required_sections:
                if section not in config:
                    result = ValidationResult(
                        rule_id=f"config_{section}",
                        rule_name=f"Configuration Validation - {section}",
                        status="failed",
                        message=f"Required configuration section '{section}' is missing",
                        severity="error",
                        timestamp=datetime.now()
                    )
                    results.append(result)
                else:
                    result = ValidationResult(
                        rule_id=f"config_{section}",
                        rule_name=f"Configuration Validation - {section}",
                        status="passed",
                        message=f"Configuration section '{section}' is present",
                        severity="info",
                        timestamp=datetime.now()
                    )
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Error validating configuration: {e}")
            return []
    
    async def validate_security(self, security_context: Dict[str, Any]) -> List[ValidationResult]:
        """Validate security requirements"""
        try:
            results = []
            
            # Check authentication
            if not security_context.get('authenticated', False):
                result = ValidationResult(
                    rule_id="security_auth",
                    rule_name="Security Validation - Authentication",
                    status="failed",
                    message="Authentication required but not provided",
                    severity="error",
                    timestamp=datetime.now()
                )
                results.append(result)
            
            # Check authorization
            required_permissions = security_context.get('required_permissions', [])
            user_permissions = security_context.get('user_permissions', [])
            
            for permission in required_permissions:
                if permission not in user_permissions:
                    result = ValidationResult(
                        rule_id="security_authz",
                        rule_name="Security Validation - Authorization",
                        status="failed",
                        message=f"Required permission '{permission}' not granted",
                        severity="error",
                        timestamp=datetime.now()
                    )
                    results.append(result)
            
            # Check encryption
            if security_context.get('requires_encryption', False):
                if not security_context.get('encrypted', False):
                    result = ValidationResult(
                        rule_id="security_encryption",
                        rule_name="Security Validation - Encryption",
                        status="failed",
                        message="Encryption required but not enabled",
                        severity="error",
                        timestamp=datetime.now()
                    )
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Error validating security: {e}")
            return []
    
    async def validate_business_rules(self, data: Dict[str, Any], 
                                    rules: List[Dict[str, Any]]) -> List[ValidationResult]:
        """Validate business logic rules"""
        try:
            results = []
            
            for rule in rules:
                rule_id = rule.get('id', 'unknown')
                rule_name = rule.get('name', 'Unknown Rule')
                condition = rule.get('condition')
                
                try:
                    # Evaluate condition (simplified)
                    if condition:
                        # This would implement a proper rule engine
                        # For now, just check basic conditions
                        passed = True  # Placeholder
                        
                        status = "passed" if passed else "failed"
                        severity = "error" if not passed else "info"
                        message = f"Business rule '{rule_name}' validation"
                        
                        result = ValidationResult(
                            rule_id=rule_id,
                            rule_name=f"Business Rule - {rule_name}",
                            status=status,
                            message=message,
                            severity=severity,
                            timestamp=datetime.now()
                        )
                        
                        results.append(result)
                
                except Exception as e:
                    result = ValidationResult(
                        rule_id=rule_id,
                        rule_name=f"Business Rule - {rule_name}",
                        status="failed",
                        message=f"Rule evaluation error: {str(e)}",
                        severity="error",
                        timestamp=datetime.now()
                    )
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Error validating business rules: {e}")
            return []
    
    async def run_all_validations(self, context: Dict[str, Any]) -> List[ValidationResult]:
        """Run all enabled validation rules"""
        try:
            all_results = []
            
            for rule_id, rule in self.validation_rules.items():
                if not rule.enabled:
                    continue
                
                try:
                    # Execute validation rule
                    result = await rule.validator_func(context)
                    
                    if isinstance(result, list):
                        all_results.extend(result)
                    else:
                        all_results.append(result)
                        
                except Exception as e:
                    error_result = ValidationResult(
                        rule_id=rule_id,
                        rule_name=rule.name,
                        status="failed",
                        message=f"Validation rule execution error: {str(e)}",
                        severity="error",
                        timestamp=datetime.now()
                    )
                    all_results.append(error_result)
            
            # Store results
            self.validation_results.extend(all_results)
            
            return all_results
            
        except Exception as e:
            logger.error(f"Error running all validations: {e}")
            return []
    
    async def get_validation_summary(self) -> Dict[str, Any]:
        """Get validation results summary"""
        try:
            if not self.validation_results:
                return {}
            
            total_validations = len(self.validation_results)
            passed_validations = len([r for r in self.validation_results if r.status == "passed"])
            failed_validations = len([r for r in self.validation_results if r.status == "failed"])
            skipped_validations = len([r for r in self.validation_results if r.status == "skipped"])
            
            # Group by severity
            errors = len([r for r in self.validation_results if r.severity == "error"])
            warnings = len([r for r in self.validation_results if r.severity == "warning"])
            info = len([r for r in self.validation_results if r.severity == "info"])
            
            success_rate = (passed_validations / total_validations) * 100 if total_validations > 0 else 0
            
            return {
                'total_validations': total_validations,
                'passed': passed_validations,
                'failed': failed_validations,
                'skipped': skipped_validations,
                'success_rate': success_rate,
                'severity_breakdown': {
                    'errors': errors,
                    'warnings': warnings,
                    'info': info
                },
                'validation_status': 'passed' if failed_validations == 0 else 'failed'
            }
            
        except Exception as e:
            logger.error(f"Error getting validation summary: {e}")
            return {}
    
    # Private methods
    
    async def _setup_default_rules(self):
        """Setup default validation rules"""
        try:
            # Data validation rule
            data_rule = ValidationRule(
                rule_id="default_data_validation",
                name="Default Data Validation",
                description="Validates data structure and types",
                validator_func=self._default_data_validator,
                severity="error"
            )
            await self.add_validation_rule(data_rule)
            
            # Performance validation rule
            performance_rule = ValidationRule(
                rule_id="default_performance_validation",
                name="Default Performance Validation",
                description="Validates performance metrics",
                validator_func=self._default_performance_validator,
                severity="warning"
            )
            await self.add_validation_rule(performance_rule)
            
            logger.debug("Default validation rules setup completed")
            
        except Exception as e:
            logger.error(f"Error setting up default rules: {e}")
    
    async def _default_data_validator(self, context: Dict[str, Any]) -> ValidationResult:
        """Default data validation rule"""
        try:
            data = context.get('data')
            
            if data is None:
                return ValidationResult(
                    rule_id="default_data_validation",
                    rule_name="Default Data Validation",
                    status="failed",
                    message="No data provided for validation",
                    severity="error",
                    timestamp=datetime.now()
                )
            
            return ValidationResult(
                rule_id="default_data_validation",
                rule_name="Default Data Validation",
                status="passed",
                message="Data validation passed",
                severity="info",
                timestamp=datetime.now()
            )
            
        except Exception as e:
            return ValidationResult(
                rule_id="default_data_validation",
                rule_name="Default Data Validation",
                status="failed",
                message=f"Validation error: {str(e)}",
                severity="error",
                timestamp=datetime.now()
            )
    
    async def _default_performance_validator(self, context: Dict[str, Any]) -> ValidationResult:
        """Default performance validation rule"""
        try:
            metrics = context.get('performance_metrics', {})
            
            if not metrics:
                return ValidationResult(
                    rule_id="default_performance_validation",
                    rule_name="Default Performance Validation",
                    status="skipped",
                    message="No performance metrics provided",
                    severity="info",
                    timestamp=datetime.now()
                )
            
            # Check if any metric exceeds threshold
            for metric_name, value in metrics.items():
                if metric_name in self.performance_thresholds:
                    threshold = self.performance_thresholds[metric_name]
                    if value > threshold:
                        return ValidationResult(
                            rule_id="default_performance_validation",
                            rule_name="Default Performance Validation",
                            status="failed",
                            message=f"Performance metric {metric_name} exceeds threshold",
                            severity="warning",
                            timestamp=datetime.now()
                        )
            
            return ValidationResult(
                rule_id="default_performance_validation",
                rule_name="Default Performance Validation",
                status="passed",
                message="Performance validation passed",
                severity="info",
                timestamp=datetime.now()
            )
            
        except Exception as e:
            return ValidationResult(
                rule_id="default_performance_validation",
                rule_name="Default Performance Validation",
                status="failed",
                message=f"Validation error: {str(e)}",
                severity="error",
                timestamp=datetime.now()
            )

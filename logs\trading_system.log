2025-06-18 18:11:57,729 - __main__ - INFO - Initializing system components...
2025-06-18 18:11:57,732 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-18 18:11:58,054 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-18 18:11:58,061 - models.model_registry - INFO - Discovered 20 new models: ['gemma3:27b', 'marco-o1:7b', 'command-r:35b', 'qwen2.5vl:32b', 'huihui_ai/am-thinking-abliterate:latest', 'nemotron-mini:4b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'mistral-small:24b', 'exaone-deep:32b', 'hermes3:8b', 'magistral:24b', 'cogito:32b', 'huihui_ai/magistral-abliterated:24b', 'qwen3:32b', 'granite3.3:8b', 'go<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/JOSIEFIED-Qwen3:14b', 'phi4-reasoning:plus', 'deepseek-r1:latest', 'huihui_ai/homunculus-abliterated:latest', 'falcon3:10b']
2025-06-18 18:11:58,073 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-18 18:11:58,079 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-18 18:11:58,082 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-18 18:12:13,755 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-18 18:21:34,403 - __main__ - INFO - Initializing system components...
2025-06-18 18:21:34,405 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-18 18:21:34,698 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-18 18:21:34,712 - models.model_registry - INFO - Discovered 20 new models: ['falcon3:10b', 'huihui_ai/am-thinking-abliterate:latest', 'cogito:32b', 'magistral:24b', 'hermes3:8b', 'deepseek-r1:latest', 'mistral-small:24b', 'qwen2.5vl:32b', 'qwen3:32b', 'huihui_ai/homunculus-abliterated:latest', 'command-r:35b', 'marco-o1:7b', 'nemotron-mini:4b', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'huihui_ai/magistral-abliterated:24b', 'gemma3:27b', 'granite3.3:8b', 'phi4-reasoning:plus', 'exaone-deep:32b']
2025-06-18 18:21:34,716 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-18 18:21:34,721 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-18 18:21:34,725 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-18 18:21:50,588 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-18 18:22:01,550 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-18 18:22:26,101 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 17:49:16,418 - __main__ - INFO - Initializing system components...
2025-06-19 17:49:16,421 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 17:49:16,745 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 17:49:16,765 - models.model_registry - INFO - Discovered 21 new models: ['hermes3:8b', 'qwen2.5vl:32b', 'nemotron-mini:4b', 'deepseek-r1:latest', 'command-r:35b', 'phi4-reasoning:plus', 'huihui_ai/homunculus-abliterated:latest', 'gemma3:27b', 'exaone-deep:32b', 'huihui_ai/magistral-abliterated:24b', 'cogito:32b', 'adityakale/kotakneo:latest', 'huihui_ai/am-thinking-abliterate:latest', 'marco-o1:7b', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'magistral:24b', 'qwen3:32b', 'mistral-small:24b', 'granite3.3:8b', 'falcon3:10b']
2025-06-19 17:49:16,770 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 17:49:16,776 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 17:49:16,780 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 17:49:34,961 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 17:49:52,702 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 17:50:26,504 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 17:50:46,228 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 17:50:49,485 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 17:50:53,269 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 17:51:04,997 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 17:51:12,163 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 17:51:19,171 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 17:51:19,225 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 17:51:19,233 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 17:51:19,240 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 17:51:19,241 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 17:51:19,241 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 17:51:19,248 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 17:51:19,254 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 17:51:19,256 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 17:51:19,256 - __main__ - ERROR - Failed to initialize components: 'AgentManager' object has no attribute 'get_all_agents'
2025-06-19 17:51:19,257 - __main__ - ERROR - System error: 'AgentManager' object has no attribute 'get_all_agents'
2025-06-19 17:51:19,300 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x00000216E88D6CF0>
2025-06-19 17:51:19,301 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x00000216EAAB6210>, 183827.218)])']
connector: <aiohttp.connector.TCPConnector object at 0x00000216B63A8BC0>
2025-06-19 17:52:31,654 - __main__ - INFO - Initializing system components...
2025-06-19 17:52:31,657 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 17:52:31,946 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 17:52:31,964 - models.model_registry - INFO - Discovered 21 new models: ['marco-o1:7b', 'granite3.3:8b', 'cogito:32b', 'mistral-small:24b', 'hermes3:8b', 'qwen3:32b', 'adityakale/kotakneo:latest', 'falcon3:10b', 'exaone-deep:32b', 'huihui_ai/magistral-abliterated:24b', 'deepseek-r1:latest', 'phi4-reasoning:plus', 'huihui_ai/am-thinking-abliterate:latest', 'magistral:24b', 'gemma3:27b', 'nemotron-mini:4b', 'qwen2.5vl:32b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'huihui_ai/homunculus-abliterated:latest', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'command-r:35b']
2025-06-19 17:52:31,978 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 17:52:31,984 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 17:52:31,990 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 17:52:48,189 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 17:53:01,283 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 17:53:24,415 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 17:53:37,548 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 17:53:40,701 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 17:53:44,494 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 17:53:55,525 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 17:54:02,172 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 17:54:11,504 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 17:54:11,517 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 17:54:11,525 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 17:54:11,532 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 17:54:11,533 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 17:54:11,533 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 17:54:11,539 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 17:54:11,545 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 17:54:11,546 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 17:54:11,554 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 17:54:11,560 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 17:54:11,568 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 17:54:11,575 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 17:54:11,586 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 17:54:11,593 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 17:54:11,600 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 17:54:11,607 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 17:54:11,615 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 17:54:11,622 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 17:54:11,628 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 17:54:11,629 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 17:54:11,630 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 17:54:11,630 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 17:54:11,631 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 17:54:11,631 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 17:54:11,639 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 17:54:11,644 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 17:54:11,645 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 17:54:11,646 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 17:54:11,651 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 17:54:11,656 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 17:54:11,661 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 17:54:11,662 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 17:54:11,663 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 17:54:11,668 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 17:54:11,669 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 17:54:11,674 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 17:54:11,679 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 17:54:11,680 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 17:54:11,681 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 17:54:11,689 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 17:54:11,690 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 17:54:11,692 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 17:54:11,698 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 17:54:11,699 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 17:54:11,700 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 17:54:11,701 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 17:54:11,702 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 17:54:11,708 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 17:54:11,709 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 17:54:11,714 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 17:54:11,715 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 17:54:11,720 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 17:54:11,720 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 17:54:11,732 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 17:54:11,732 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 17:54:11,733 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 17:54:11,745 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 17:54:11,751 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 17:54:11,756 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 17:54:11,764 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 17:54:11,765 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 17:54:11,770 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 17:54:11,771 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 17:54:11,772 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 17:54:11,777 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 17:54:11,778 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 17:54:11,778 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 17:54:11,783 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 17:54:11,784 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 17:54:11,789 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 17:54:11,790 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 17:54:11,794 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 17:54:11,801 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 17:54:11,801 - __main__ - ERROR - Error setting up component integration: 'StrategyManager' object has no attribute 'set_integration_points'
2025-06-19 17:54:11,802 - __main__ - ERROR - Failed to initialize trading components: 'StrategyManager' object has no attribute 'set_integration_points'
2025-06-19 17:54:11,803 - __main__ - ERROR - Failed to initialize components: 'StrategyManager' object has no attribute 'set_integration_points'
2025-06-19 17:54:11,803 - __main__ - ERROR - System error: 'StrategyManager' object has no attribute 'set_integration_points'
2025-06-19 17:54:11,841 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000001FB1B87A9F0>
2025-06-19 17:54:11,842 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x000001FB1CA662D0>, 183999.562)])']
connector: <aiohttp.connector.TCPConnector object at 0x000001FB7FD5A360>
2025-06-19 17:55:08,791 - __main__ - INFO - Initializing system components...
2025-06-19 17:55:08,792 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 17:55:09,080 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 17:55:09,098 - models.model_registry - INFO - Discovered 21 new models: ['adityakale/kotakneo:latest', 'phi4-reasoning:plus', 'granite3.3:8b', 'cogito:32b', 'falcon3:10b', 'nemotron-mini:4b', 'qwen3:32b', 'magistral:24b', 'marco-o1:7b', 'exaone-deep:32b', 'huihui_ai/homunculus-abliterated:latest', 'mistral-small:24b', 'hermes3:8b', 'gemma3:27b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'huihui_ai/magistral-abliterated:24b', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'huihui_ai/am-thinking-abliterate:latest', 'command-r:35b', 'qwen2.5vl:32b', 'deepseek-r1:latest']
2025-06-19 17:55:09,101 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 17:55:09,107 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 17:55:09,109 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 17:55:26,810 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 17:55:48,439 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 17:56:12,189 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 17:56:27,399 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 17:56:30,572 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 17:56:34,366 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 17:56:45,684 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 17:57:28,922 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 17:57:40,081 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 17:57:40,095 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 17:57:40,103 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 17:57:40,109 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 17:57:40,109 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 17:57:40,110 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 17:57:40,116 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 17:57:40,122 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 17:57:40,123 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 17:57:40,129 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 17:57:40,134 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 17:57:40,137 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 17:57:40,143 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 17:57:40,151 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 17:57:40,157 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 17:57:40,162 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 17:57:40,168 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 17:57:40,174 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 17:57:40,179 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 17:57:40,183 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 17:57:40,185 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 17:57:40,186 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 17:57:40,186 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 17:57:40,187 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 17:57:40,188 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 17:57:40,192 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 17:57:40,197 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 17:57:40,197 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 17:57:40,198 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 17:57:40,201 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 17:57:40,204 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 17:57:40,208 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 17:57:40,209 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 17:57:40,210 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 17:57:40,213 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 17:57:40,214 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 17:57:40,218 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 17:57:40,221 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 17:57:40,222 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 17:57:40,222 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 17:57:40,230 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 17:57:40,231 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 17:57:40,233 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 17:57:40,237 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 17:57:40,237 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 17:57:40,238 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 17:57:40,239 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 17:57:40,240 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 17:57:40,244 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 17:57:40,244 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 17:57:40,248 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 17:57:40,249 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 17:57:40,253 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 17:57:40,253 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 17:57:40,261 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 17:57:40,262 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 17:57:40,262 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 17:57:40,268 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 17:57:40,273 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 17:57:40,279 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 17:57:40,288 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 17:57:40,289 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 17:57:40,295 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 17:57:40,296 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 17:57:40,297 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 17:57:40,303 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 17:57:40,304 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 17:57:40,305 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 17:57:40,311 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 17:57:40,312 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 17:57:40,317 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 17:57:40,317 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 17:57:40,327 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 17:57:40,334 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 17:57:40,335 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 17:57:40,336 - __main__ - ERROR - Error setting up component integration: 'RiskManager' object has no attribute 'set_integration_points'
2025-06-19 17:57:40,336 - __main__ - ERROR - Failed to initialize trading components: 'RiskManager' object has no attribute 'set_integration_points'
2025-06-19 17:57:40,337 - __main__ - ERROR - Failed to initialize components: 'RiskManager' object has no attribute 'set_integration_points'
2025-06-19 17:57:40,338 - __main__ - ERROR - System error: 'RiskManager' object has no attribute 'set_integration_points'
2025-06-19 17:57:40,391 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000020C84135760>
2025-06-19 17:57:40,392 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000020C860623F0>, 184208.14)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000020C85D80BC0>
2025-06-19 17:58:23,309 - __main__ - INFO - Initializing system components...
2025-06-19 17:58:23,310 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 17:58:23,610 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 17:58:23,628 - models.model_registry - INFO - Discovered 21 new models: ['nemotron-mini:4b', 'magistral:24b', 'huihui_ai/am-thinking-abliterate:latest', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'qwen2.5vl:32b', 'deepseek-r1:latest', 'cogito:32b', 'huihui_ai/homunculus-abliterated:latest', 'huihui_ai/magistral-abliterated:24b', 'falcon3:10b', 'marco-o1:7b', 'qwen3:32b', 'command-r:35b', 'phi4-reasoning:plus', 'huihui_ai/acereason-nemotron-abliterated:14b', 'granite3.3:8b', 'gemma3:27b', 'hermes3:8b', 'adityakale/kotakneo:latest', 'mistral-small:24b', 'exaone-deep:32b']
2025-06-19 17:58:23,633 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 17:58:23,639 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 17:58:23,642 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 17:58:39,932 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 17:59:02,032 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 17:59:26,009 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 17:59:57,733 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 18:00:00,887 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 18:00:04,709 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 18:00:15,613 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 18:00:22,251 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 18:00:32,903 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 18:00:32,916 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 18:00:32,929 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 18:00:32,937 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 18:00:32,938 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 18:00:32,938 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 18:00:32,947 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 18:00:32,956 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 18:00:32,957 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 18:00:32,963 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 18:00:32,968 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 18:00:32,973 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 18:00:32,976 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 18:00:32,987 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 18:00:32,993 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 18:00:32,998 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 18:00:33,004 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 18:00:33,012 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 18:00:33,018 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 18:00:33,023 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 18:00:33,024 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 18:00:33,025 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 18:00:33,025 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 18:00:33,026 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 18:00:33,026 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 18:00:33,031 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 18:00:33,035 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 18:00:33,036 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 18:00:33,036 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 18:00:33,040 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 18:00:33,044 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 18:00:33,048 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 18:00:33,049 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 18:00:33,050 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 18:00:33,053 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 18:00:33,054 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 18:00:33,058 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 18:00:33,061 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 18:00:33,062 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 18:00:33,063 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 18:00:33,070 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 18:00:33,071 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 18:00:33,074 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 18:00:33,079 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 18:00:33,080 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 18:00:33,080 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 18:00:33,081 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 18:00:33,081 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 18:00:33,085 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 18:00:33,086 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 18:00:33,089 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 18:00:33,090 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 18:00:33,094 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 18:00:33,094 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 18:00:33,101 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 18:00:33,101 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 18:00:33,103 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 18:00:33,108 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 18:00:33,113 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 18:00:33,116 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 18:00:33,122 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 18:00:33,123 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 18:00:33,127 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 18:00:33,128 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 18:00:33,128 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 18:00:33,132 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 18:00:33,133 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 18:00:33,133 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 18:00:33,137 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 18:00:33,138 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 18:00:33,141 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 18:00:33,142 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 18:00:33,147 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 18:00:33,152 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 18:00:33,153 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 18:00:33,154 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 18:00:33,154 - __main__ - ERROR - Error setting up component integration: 'ExecutionEngine' object has no attribute 'set_integration_points'
2025-06-19 18:00:33,154 - __main__ - ERROR - Failed to initialize trading components: 'ExecutionEngine' object has no attribute 'set_integration_points'
2025-06-19 18:00:33,155 - __main__ - ERROR - Failed to initialize components: 'ExecutionEngine' object has no attribute 'set_integration_points'
2025-06-19 18:00:33,156 - __main__ - ERROR - System error: 'ExecutionEngine' object has no attribute 'set_integration_points'
2025-06-19 18:00:33,198 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000021301EFA360>
2025-06-19 18:00:33,199 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x000002130385E4B0>, 184380.953)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000021302601A30>
2025-06-19 18:01:30,926 - __main__ - INFO - Initializing system components...
2025-06-19 18:01:30,928 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 18:01:31,229 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 18:01:31,247 - models.model_registry - INFO - Discovered 21 new models: ['qwen2.5vl:32b', 'falcon3:10b', 'qwen3:32b', 'command-r:35b', 'phi4-reasoning:plus', 'gemma3:27b', 'deepseek-r1:latest', 'magistral:24b', 'granite3.3:8b', 'huihui_ai/homunculus-abliterated:latest', 'huihui_ai/am-thinking-abliterate:latest', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'adityakale/kotakneo:latest', 'exaone-deep:32b', 'mistral-small:24b', 'hermes3:8b', 'cogito:32b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'marco-o1:7b', 'nemotron-mini:4b', 'huihui_ai/magistral-abliterated:24b']
2025-06-19 18:01:31,253 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 18:01:31,258 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 18:01:31,264 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 18:01:47,847 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 18:02:30,284 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 18:02:57,472 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 18:03:25,112 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 18:03:28,553 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 18:03:32,359 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 18:03:43,320 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 18:03:50,081 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 18:04:01,246 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 18:04:01,304 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 18:04:01,322 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 18:04:01,333 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 18:04:01,334 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 18:04:01,335 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 18:04:01,348 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 18:04:01,360 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 18:04:01,361 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 18:04:01,370 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 18:04:01,378 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 18:04:01,385 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 18:04:01,393 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 18:04:01,414 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 18:04:01,425 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 18:04:01,435 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 18:04:01,445 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 18:04:01,456 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 18:04:01,466 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 18:04:01,476 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 18:04:01,478 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 18:04:01,479 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 18:04:01,480 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 18:04:01,481 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 18:04:01,483 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 18:04:01,491 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 18:04:01,498 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 18:04:01,499 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 18:04:01,500 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 18:04:01,508 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 18:04:01,514 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 18:04:01,522 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 18:04:01,523 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 18:04:01,524 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 18:04:01,532 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 18:04:01,534 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 18:04:01,540 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 18:04:01,548 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 18:04:01,550 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 18:04:01,551 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 18:04:01,567 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 18:04:01,568 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 18:04:01,572 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 18:04:01,580 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 18:04:01,582 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 18:04:01,583 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 18:04:01,584 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 18:04:01,586 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 18:04:01,592 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 18:04:01,593 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 18:04:01,600 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 18:04:01,602 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 18:04:01,608 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 18:04:01,610 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 18:04:01,624 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 18:04:01,626 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 18:04:01,627 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 18:04:01,636 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 18:04:01,643 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 18:04:01,650 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 18:04:01,662 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 18:04:01,664 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 18:04:01,671 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 18:04:01,673 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 18:04:01,675 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 18:04:01,682 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 18:04:01,684 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 18:04:01,685 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 18:04:01,692 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 18:04:01,694 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 18:04:01,700 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 18:04:01,701 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 18:04:01,713 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 18:04:01,725 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 18:04:01,727 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 18:04:01,728 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 18:04:01,729 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 18:04:01,738 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 18:04:01,739 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 18:04:01,741 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 18:04:01,750 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 18:04:01,756 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 18:04:01,763 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 18:04:01,769 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 18:04:01,785 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 18:04:01,790 - __main__ - INFO - Initializing database integration components...
2025-06-19 18:04:01,791 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 18:04:01,793 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:04:01,879 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:04:01,881 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:04:01,887 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:04:05,963 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:04:05,965 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:04:05,972 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 18:04:15,195 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 18:04:17,488 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 18:04:19,791 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:04:21,561 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:04:21,565 - __main__ - ERROR - Failed to initialize database components: 'StrategyManager' object has no attribute 'set_database_coordinator'
2025-06-19 18:04:21,566 - __main__ - ERROR - Failed to initialize components: 'StrategyManager' object has no attribute 'set_database_coordinator'
2025-06-19 18:04:21,567 - __main__ - ERROR - System error: 'StrategyManager' object has no attribute 'set_database_coordinator'
2025-06-19 18:04:21,613 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000001F12DCC60F0>
2025-06-19 18:04:21,614 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000001F130117D10>
2025-06-19 18:05:06,328 - __main__ - INFO - Initializing system components...
2025-06-19 18:05:06,330 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 18:05:06,626 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 18:05:06,644 - models.model_registry - INFO - Discovered 21 new models: ['huihui_ai/am-thinking-abliterate:latest', 'hermes3:8b', 'falcon3:10b', 'deepseek-r1:latest', 'phi4-reasoning:plus', 'mistral-small:24b', 'granite3.3:8b', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'qwen2.5vl:32b', 'huihui_ai/homunculus-abliterated:latest', 'nemotron-mini:4b', 'cogito:32b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'adityakale/kotakneo:latest', 'magistral:24b', 'huihui_ai/magistral-abliterated:24b', 'gemma3:27b', 'qwen3:32b', 'exaone-deep:32b', 'marco-o1:7b', 'command-r:35b']
2025-06-19 18:05:06,648 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 18:05:06,653 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 18:05:06,656 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 18:05:23,676 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 18:05:43,143 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 18:06:10,037 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 18:06:30,876 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 18:06:34,058 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 18:06:37,867 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 18:06:49,076 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 18:06:54,669 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 18:07:04,648 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 18:07:04,700 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 18:07:04,708 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 18:07:04,714 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 18:07:04,714 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 18:07:04,715 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 18:07:04,721 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 18:07:04,727 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 18:07:04,728 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 18:07:04,734 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 18:07:04,738 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 18:07:04,743 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 18:07:04,746 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 18:07:04,756 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 18:07:04,762 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 18:07:04,767 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 18:07:04,773 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 18:07:04,778 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 18:07:04,784 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 18:07:04,789 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 18:07:04,791 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 18:07:04,792 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 18:07:04,792 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 18:07:04,793 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 18:07:04,794 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 18:07:04,798 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 18:07:04,801 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 18:07:04,801 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 18:07:04,803 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 18:07:04,807 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 18:07:04,810 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 18:07:04,813 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 18:07:04,814 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 18:07:04,814 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 18:07:04,818 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 18:07:04,819 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 18:07:04,822 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 18:07:04,825 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 18:07:04,827 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 18:07:04,827 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 18:07:04,834 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 18:07:04,834 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 18:07:04,836 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 18:07:04,841 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 18:07:04,842 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 18:07:04,843 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 18:07:04,844 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 18:07:04,844 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 18:07:04,848 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 18:07:04,849 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 18:07:04,853 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 18:07:04,853 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 18:07:04,858 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 18:07:04,860 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 18:07:04,868 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 18:07:04,868 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 18:07:04,869 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 18:07:04,875 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 18:07:04,879 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 18:07:04,883 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 18:07:04,889 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 18:07:04,891 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 18:07:04,896 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 18:07:04,898 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 18:07:04,899 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 18:07:04,904 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 18:07:04,905 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 18:07:04,907 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 18:07:04,912 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 18:07:04,913 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 18:07:04,917 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 18:07:04,919 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 18:07:04,926 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 18:07:04,934 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 18:07:04,934 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 18:07:04,935 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 18:07:04,936 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 18:07:04,943 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 18:07:04,944 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 18:07:04,945 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 18:07:04,950 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 18:07:04,956 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 18:07:04,961 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 18:07:04,967 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 18:07:04,978 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 18:07:04,981 - __main__ - INFO - Initializing database integration components...
2025-06-19 18:07:04,982 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 18:07:04,983 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:07:05,039 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:07:05,040 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:07:05,043 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:07:09,119 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:07:09,120 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:07:09,124 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 18:07:18,324 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 18:07:20,663 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 18:07:22,964 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:07:24,730 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:07:24,735 - strategies.strategy_manager - INFO - Strategy Manager database coordinator configured
2025-06-19 18:07:24,736 - __main__ - ERROR - Failed to initialize database components: 'RiskManager' object has no attribute 'set_database_coordinator'
2025-06-19 18:07:24,737 - __main__ - ERROR - Failed to initialize components: 'RiskManager' object has no attribute 'set_database_coordinator'
2025-06-19 18:07:24,738 - __main__ - ERROR - System error: 'RiskManager' object has no attribute 'set_database_coordinator'
2025-06-19 18:07:24,781 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x00000275646C8830>
2025-06-19 18:07:24,783 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000027564C19550>
2025-06-19 18:07:58,183 - __main__ - INFO - Initializing system components...
2025-06-19 18:07:58,185 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 18:07:58,494 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 18:07:58,509 - models.model_registry - INFO - Discovered 21 new models: ['gemma3:27b', 'huihui_ai/am-thinking-abliterate:latest', 'cogito:32b', 'phi4-reasoning:plus', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'qwen2.5vl:32b', 'command-r:35b', 'falcon3:10b', 'marco-o1:7b', 'granite3.3:8b', 'magistral:24b', 'deepseek-r1:latest', 'qwen3:32b', 'huihui_ai/homunculus-abliterated:latest', 'exaone-deep:32b', 'nemotron-mini:4b', 'mistral-small:24b', 'huihui_ai/magistral-abliterated:24b', 'hermes3:8b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'adityakale/kotakneo:latest']
2025-06-19 18:07:58,513 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 18:07:58,519 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 18:07:58,523 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 18:08:14,871 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 18:08:40,441 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 18:09:07,077 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 18:09:22,075 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 18:09:25,272 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 18:09:29,081 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 18:09:39,785 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 18:09:49,286 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 18:09:58,794 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 18:09:58,847 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 18:09:58,857 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 18:09:58,864 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 18:09:58,864 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 18:09:58,864 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 18:09:58,871 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 18:09:58,877 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 18:09:58,878 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 18:09:58,883 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 18:09:58,888 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 18:09:58,893 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 18:09:58,897 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 18:09:58,906 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 18:09:58,912 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 18:09:58,917 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 18:09:58,923 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 18:09:58,928 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 18:09:58,934 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 18:09:58,940 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 18:09:58,941 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 18:09:58,941 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 18:09:58,942 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 18:09:58,943 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 18:09:58,943 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 18:09:58,948 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 18:09:58,952 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 18:09:58,952 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 18:09:58,953 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 18:09:58,957 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 18:09:58,961 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 18:09:58,964 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 18:09:58,965 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 18:09:58,965 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 18:09:58,969 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 18:09:58,969 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 18:09:58,973 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 18:09:58,977 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 18:09:58,978 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 18:09:58,978 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 18:09:58,986 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 18:09:58,987 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 18:09:58,998 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 18:09:59,002 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 18:09:59,003 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 18:09:59,004 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 18:09:59,004 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 18:09:59,005 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 18:09:59,008 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 18:09:59,009 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 18:09:59,013 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 18:09:59,013 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 18:09:59,017 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 18:09:59,017 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 18:09:59,026 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 18:09:59,026 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 18:09:59,027 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 18:09:59,032 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 18:09:59,036 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 18:09:59,039 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 18:09:59,045 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 18:09:59,046 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 18:09:59,051 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 18:09:59,051 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 18:09:59,052 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 18:09:59,056 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 18:09:59,056 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 18:09:59,058 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 18:09:59,061 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 18:09:59,062 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 18:09:59,065 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 18:09:59,065 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 18:09:59,071 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 18:09:59,077 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 18:09:59,078 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 18:09:59,079 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 18:09:59,079 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 18:09:59,083 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 18:09:59,084 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 18:09:59,084 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 18:09:59,089 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 18:09:59,093 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 18:09:59,097 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 18:09:59,101 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 18:09:59,108 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 18:09:59,111 - __main__ - INFO - Initializing database integration components...
2025-06-19 18:09:59,111 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 18:09:59,112 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:09:59,166 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:09:59,167 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:09:59,171 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:10:03,243 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:10:03,244 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:10:03,258 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 18:10:12,463 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 18:10:14,776 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 18:10:17,068 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:10:18,851 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:10:18,858 - strategies.strategy_manager - INFO - Strategy Manager database coordinator configured
2025-06-19 18:10:18,858 - risk.risk_manager - INFO - Risk Manager database coordinator configured
2025-06-19 18:10:18,859 - __main__ - ERROR - Failed to initialize database components: 'ExecutionEngine' object has no attribute 'set_database_coordinator'
2025-06-19 18:10:18,859 - __main__ - ERROR - Failed to initialize components: 'ExecutionEngine' object has no attribute 'set_database_coordinator'
2025-06-19 18:10:18,859 - __main__ - ERROR - System error: 'ExecutionEngine' object has no attribute 'set_database_coordinator'
2025-06-19 18:10:18,904 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x00000132D7AD2360>
2025-06-19 18:10:18,906 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x00000132D90BA8D0>, 184946.843)])']
connector: <aiohttp.connector.TCPConnector object at 0x00000132D8F2D580>
2025-06-19 18:10:18,908 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x00000132D9108D10>
2025-06-19 18:10:54,539 - __main__ - INFO - Initializing system components...
2025-06-19 18:10:54,541 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 18:10:54,838 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 18:10:54,857 - models.model_registry - INFO - Discovered 21 new models: ['mistral-small:24b', 'phi4-reasoning:plus', 'marco-o1:7b', 'command-r:35b', 'gemma3:27b', 'adityakale/kotakneo:latest', 'hermes3:8b', 'qwen2.5vl:32b', 'nemotron-mini:4b', 'exaone-deep:32b', 'granite3.3:8b', 'huihui_ai/magistral-abliterated:24b', 'deepseek-r1:latest', 'huihui_ai/am-thinking-abliterate:latest', 'huihui_ai/homunculus-abliterated:latest', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'falcon3:10b', 'magistral:24b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'qwen3:32b', 'cogito:32b']
2025-06-19 18:10:54,863 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 18:10:54,868 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 18:10:54,873 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 18:11:11,435 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 18:11:24,473 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 18:11:52,828 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 18:12:16,121 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 18:12:19,278 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 18:12:23,070 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 18:12:34,353 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 18:12:40,251 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 18:12:50,964 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 18:12:50,979 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 18:12:50,992 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 18:12:50,999 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 18:12:50,999 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 18:12:51,001 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 18:12:51,009 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 18:12:51,017 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 18:12:51,017 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 18:12:51,024 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 18:12:51,029 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 18:12:51,034 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 18:12:51,038 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 18:12:51,050 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 18:12:51,055 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 18:12:51,062 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 18:12:51,067 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 18:12:51,073 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 18:12:51,079 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 18:12:51,084 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 18:12:51,085 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 18:12:51,085 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 18:12:51,086 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 18:12:51,087 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 18:12:51,087 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 18:12:51,093 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 18:12:51,100 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 18:12:51,100 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 18:12:51,102 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 18:12:51,108 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 18:12:51,113 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 18:12:51,117 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 18:12:51,118 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 18:12:51,119 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 18:12:51,124 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 18:12:51,125 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 18:12:51,129 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 18:12:51,133 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 18:12:51,133 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 18:12:51,134 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 18:12:51,142 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 18:12:51,143 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 18:12:51,145 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 18:12:51,149 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 18:12:51,150 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 18:12:51,150 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 18:12:51,151 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 18:12:51,151 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 18:12:51,156 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 18:12:51,156 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 18:12:51,160 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 18:12:51,161 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 18:12:51,164 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 18:12:51,165 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 18:12:51,174 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 18:12:51,174 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 18:12:51,175 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 18:12:51,181 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 18:12:51,184 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 18:12:51,188 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 18:12:51,194 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 18:12:51,195 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 18:12:51,199 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 18:12:51,199 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 18:12:51,200 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 18:12:51,204 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 18:12:51,205 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 18:12:51,206 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 18:12:51,210 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 18:12:51,211 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 18:12:51,215 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 18:12:51,216 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 18:12:51,220 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 18:12:51,226 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 18:12:51,227 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 18:12:51,228 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 18:12:51,229 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 18:12:51,234 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 18:12:51,235 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 18:12:51,236 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 18:12:51,242 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 18:12:51,247 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 18:12:51,250 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 18:12:51,255 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 18:12:51,263 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 18:12:51,266 - __main__ - INFO - Initializing database integration components...
2025-06-19 18:12:51,266 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 18:12:51,267 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:12:51,333 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:12:51,334 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:12:51,337 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:12:55,399 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:12:55,400 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:12:55,404 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 18:13:04,603 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 18:13:06,897 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 18:13:09,209 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:13:10,984 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:13:10,995 - strategies.strategy_manager - INFO - Strategy Manager database coordinator configured
2025-06-19 18:13:10,996 - risk.risk_manager - INFO - Risk Manager database coordinator configured
2025-06-19 18:13:10,996 - execution.execution_engine - INFO - Execution Engine database coordinator configured
2025-06-19 18:13:10,997 - __main__ - ERROR - Failed to initialize database components: 'PortfolioManager' object has no attribute 'set_database_coordinator'
2025-06-19 18:13:10,998 - __main__ - ERROR - Failed to initialize components: 'PortfolioManager' object has no attribute 'set_database_coordinator'
2025-06-19 18:13:10,999 - __main__ - ERROR - System error: 'PortfolioManager' object has no attribute 'set_database_coordinator'
2025-06-19 18:13:11,044 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000026C8750BA70>
2025-06-19 18:13:11,044 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000026C87587F50>
2025-06-19 18:14:12,449 - __main__ - INFO - Initializing system components...
2025-06-19 18:14:12,451 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 18:14:12,752 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 18:14:12,770 - models.model_registry - INFO - Discovered 21 new models: ['phi4-reasoning:plus', 'hermes3:8b', 'nemotron-mini:4b', 'huihui_ai/magistral-abliterated:24b', 'qwen3:32b', 'marco-o1:7b', 'huihui_ai/homunculus-abliterated:latest', 'command-r:35b', 'gemma3:27b', 'adityakale/kotakneo:latest', 'cogito:32b', 'falcon3:10b', 'exaone-deep:32b', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'granite3.3:8b', 'qwen2.5vl:32b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'deepseek-r1:latest', 'mistral-small:24b', 'magistral:24b', 'huihui_ai/am-thinking-abliterate:latest']
2025-06-19 18:14:12,775 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 18:14:12,781 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 18:14:12,785 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 18:14:29,348 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 18:14:50,745 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 18:15:16,541 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 18:15:27,177 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 18:15:30,308 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 18:15:34,103 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 18:15:45,088 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 18:15:51,732 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 18:16:02,846 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 18:16:02,860 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 18:16:02,868 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 18:16:02,874 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 18:16:02,874 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 18:16:02,875 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 18:16:02,881 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 18:16:02,888 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 18:16:02,889 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 18:16:02,895 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 18:16:02,900 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 18:16:02,904 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 18:16:02,909 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 18:16:02,926 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 18:16:02,934 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 18:16:02,943 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 18:16:02,950 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 18:16:02,954 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 18:16:02,962 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 18:16:02,968 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 18:16:02,969 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 18:16:02,970 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 18:16:02,970 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 18:16:02,971 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 18:16:02,971 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 18:16:02,977 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 18:16:02,981 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 18:16:02,982 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 18:16:02,982 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 18:16:02,986 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 18:16:02,989 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 18:16:02,992 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 18:16:02,992 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 18:16:02,994 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 18:16:02,997 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 18:16:02,998 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 18:16:03,001 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 18:16:03,005 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 18:16:03,006 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 18:16:03,006 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 18:16:03,014 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 18:16:03,015 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 18:16:03,017 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 18:16:03,021 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 18:16:03,021 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 18:16:03,022 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 18:16:03,023 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 18:16:03,023 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 18:16:03,027 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 18:16:03,028 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 18:16:03,032 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 18:16:03,033 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 18:16:03,038 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 18:16:03,039 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 18:16:03,048 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 18:16:03,049 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 18:16:03,050 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 18:16:03,056 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 18:16:03,062 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 18:16:03,066 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 18:16:03,074 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 18:16:03,075 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 18:16:03,081 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 18:16:03,082 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 18:16:03,083 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 18:16:03,086 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 18:16:03,087 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 18:16:03,088 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 18:16:03,092 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 18:16:03,092 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 18:16:03,096 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 18:16:03,097 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 18:16:03,101 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 18:16:03,106 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 18:16:03,107 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 18:16:03,107 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 18:16:03,108 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 18:16:03,112 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 18:16:03,113 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 18:16:03,114 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 18:16:03,119 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 18:16:03,122 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 18:16:03,126 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 18:16:03,131 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 18:16:03,137 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 18:16:03,140 - __main__ - INFO - Initializing database integration components...
2025-06-19 18:16:03,141 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 18:16:03,142 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:16:03,196 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:16:03,198 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:16:03,201 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:16:07,263 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:16:07,264 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:16:07,267 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 18:16:16,458 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 18:16:18,761 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 18:16:21,063 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:16:22,834 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:16:22,839 - strategies.strategy_manager - INFO - Strategy Manager database coordinator configured
2025-06-19 18:16:22,840 - risk.risk_manager - INFO - Risk Manager database coordinator configured
2025-06-19 18:16:22,840 - execution.execution_engine - INFO - Execution Engine database coordinator configured
2025-06-19 18:16:22,841 - __main__ - ERROR - Failed to initialize database components: 'PortfolioManager' object has no attribute 'position_tracker'
2025-06-19 18:16:22,842 - __main__ - ERROR - Failed to initialize components: 'PortfolioManager' object has no attribute 'position_tracker'
2025-06-19 18:16:22,842 - __main__ - ERROR - System error: 'PortfolioManager' object has no attribute 'position_tracker'
2025-06-19 18:16:22,885 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000016B7FBE0050>
2025-06-19 18:16:22,887 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000016B12D4AB10>, 185310.906)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000016B12A6CA70>
2025-06-19 18:16:22,889 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000016B12B36B40>
2025-06-19 18:17:07,350 - __main__ - INFO - Initializing system components...
2025-06-19 18:17:07,352 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 18:17:07,654 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 18:17:07,672 - models.model_registry - INFO - Discovered 21 new models: ['granite3.3:8b', 'mistral-small:24b', 'hermes3:8b', 'marco-o1:7b', 'falcon3:10b', 'cogito:32b', 'qwen2.5vl:32b', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'huihui_ai/homunculus-abliterated:latest', 'gemma3:27b', 'phi4-reasoning:plus', 'command-r:35b', 'adityakale/kotakneo:latest', 'huihui_ai/magistral-abliterated:24b', 'deepseek-r1:latest', 'qwen3:32b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'nemotron-mini:4b', 'magistral:24b', 'exaone-deep:32b', 'huihui_ai/am-thinking-abliterate:latest']
2025-06-19 18:17:07,677 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 18:17:07,683 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 18:17:07,687 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 18:17:24,275 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 18:17:55,748 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 18:18:23,901 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 18:18:50,932 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 18:18:54,307 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 18:18:58,071 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 18:19:09,022 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 18:19:15,295 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 18:19:22,048 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 18:19:22,105 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 18:19:22,122 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 18:19:22,135 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 18:19:22,137 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 18:19:22,139 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 18:19:22,147 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 18:19:22,156 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 18:19:22,158 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 18:19:22,170 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 18:19:22,179 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 18:19:22,187 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 18:19:22,195 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 18:19:22,214 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 18:19:22,229 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 18:19:22,259 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 18:19:22,270 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 18:19:22,278 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 18:19:22,288 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 18:19:22,298 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 18:19:22,300 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 18:19:22,302 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 18:19:22,303 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 18:19:22,304 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 18:19:22,305 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 18:19:22,311 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 18:19:22,318 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 18:19:22,319 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 18:19:22,320 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 18:19:22,326 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 18:19:22,332 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 18:19:22,337 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 18:19:22,338 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 18:19:22,340 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 18:19:22,347 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 18:19:22,348 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 18:19:22,355 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 18:19:22,363 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 18:19:22,364 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 18:19:22,366 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 18:19:22,382 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 18:19:22,384 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 18:19:22,388 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 18:19:22,396 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 18:19:22,398 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 18:19:22,399 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 18:19:22,401 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 18:19:22,401 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 18:19:22,409 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 18:19:22,410 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 18:19:22,416 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 18:19:22,418 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 18:19:22,424 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 18:19:22,425 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 18:19:22,438 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 18:19:22,440 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 18:19:22,443 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 18:19:22,454 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 18:19:22,462 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 18:19:22,471 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 18:19:22,481 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 18:19:22,484 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 18:19:22,493 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 18:19:22,494 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 18:19:22,495 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 18:19:22,502 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 18:19:22,504 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 18:19:22,506 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 18:19:22,513 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 18:19:22,513 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 18:19:22,520 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 18:19:22,521 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 18:19:22,531 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 18:19:22,543 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 18:19:22,545 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 18:19:22,546 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 18:19:22,548 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 18:19:22,560 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 18:19:22,562 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 18:19:22,563 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 18:19:22,571 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 18:19:22,578 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 18:19:22,587 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 18:19:22,616 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 18:19:22,632 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 18:19:22,641 - __main__ - INFO - Initializing database integration components...
2025-06-19 18:19:22,644 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 18:19:22,645 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:19:22,763 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:19:22,765 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:19:22,771 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:19:26,829 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:19:26,831 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:19:26,837 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 18:19:36,042 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 18:19:38,339 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 18:19:40,626 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:19:42,392 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:19:42,397 - strategies.strategy_manager - INFO - Strategy Manager database coordinator configured
2025-06-19 18:19:42,398 - risk.risk_manager - INFO - Risk Manager database coordinator configured
2025-06-19 18:19:42,398 - execution.execution_engine - INFO - Execution Engine database coordinator configured
2025-06-19 18:19:42,399 - portfolio.portfolio_manager - INFO - Portfolio Manager database coordinator configured
2025-06-19 18:19:42,400 - __main__ - ERROR - Failed to initialize database components: 'LearningManager' object has no attribute 'set_database_coordinator'
2025-06-19 18:19:42,400 - __main__ - ERROR - Failed to initialize components: 'LearningManager' object has no attribute 'set_database_coordinator'
2025-06-19 18:19:42,401 - __main__ - ERROR - System error: 'LearningManager' object has no attribute 'set_database_coordinator'
2025-06-19 18:19:42,447 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000025D335EC2F0>
2025-06-19 18:19:42,449 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000025D2FBAB3B0>
2025-06-19 18:21:04,886 - __main__ - INFO - Initializing system components...
2025-06-19 18:21:04,888 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 18:21:05,188 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 18:21:05,206 - models.model_registry - INFO - Discovered 21 new models: ['adityakale/kotakneo:latest', 'cogito:32b', 'magistral:24b', 'marco-o1:7b', 'huihui_ai/magistral-abliterated:24b', 'qwen2.5vl:32b', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'falcon3:10b', 'gemma3:27b', 'granite3.3:8b', 'command-r:35b', 'phi4-reasoning:plus', 'huihui_ai/homunculus-abliterated:latest', 'huihui_ai/acereason-nemotron-abliterated:14b', 'hermes3:8b', 'mistral-small:24b', 'deepseek-r1:latest', 'exaone-deep:32b', 'nemotron-mini:4b', 'huihui_ai/am-thinking-abliterate:latest', 'qwen3:32b']
2025-06-19 18:21:05,210 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 18:21:05,216 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 18:21:05,218 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 18:21:22,128 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 18:21:36,034 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 18:22:03,222 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 18:22:27,034 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 18:22:30,485 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 18:22:34,253 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 18:22:45,523 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 18:22:58,197 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 18:23:08,786 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 18:23:08,800 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 18:23:08,808 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 18:23:08,814 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 18:23:08,814 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 18:23:08,816 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 18:23:08,822 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 18:23:08,828 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 18:23:08,828 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 18:23:08,835 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 18:23:08,839 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 18:23:08,844 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 18:23:08,848 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 18:23:08,859 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 18:23:08,864 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 18:23:08,869 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 18:23:08,875 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 18:23:08,881 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 18:23:08,888 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 18:23:08,894 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 18:23:08,894 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 18:23:08,895 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 18:23:08,896 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 18:23:08,896 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 18:23:08,896 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 18:23:08,901 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 18:23:08,904 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 18:23:08,906 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 18:23:08,906 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 18:23:08,910 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 18:23:08,913 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 18:23:08,916 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 18:23:08,918 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 18:23:08,918 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 18:23:08,922 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 18:23:08,923 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 18:23:08,926 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 18:23:08,930 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 18:23:08,931 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 18:23:08,931 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 18:23:08,939 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 18:23:08,939 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 18:23:08,951 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 18:23:08,956 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 18:23:08,956 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 18:23:08,958 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 18:23:08,958 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 18:23:08,959 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 18:23:08,963 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 18:23:08,963 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 18:23:08,966 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 18:23:08,967 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 18:23:08,971 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 18:23:08,972 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 18:23:08,979 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 18:23:08,980 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 18:23:08,981 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 18:23:08,985 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 18:23:08,989 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 18:23:08,993 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 18:23:08,999 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 18:23:09,000 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 18:23:09,004 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 18:23:09,005 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 18:23:09,006 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 18:23:09,010 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 18:23:09,011 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 18:23:09,011 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 18:23:09,014 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 18:23:09,016 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 18:23:09,019 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 18:23:09,020 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 18:23:09,025 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 18:23:09,031 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 18:23:09,032 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 18:23:09,033 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 18:23:09,033 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 18:23:09,037 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 18:23:09,038 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 18:23:09,039 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 18:23:09,044 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 18:23:09,048 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 18:23:09,052 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 18:23:09,055 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 18:23:09,064 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 18:23:09,068 - __main__ - INFO - Initializing database integration components...
2025-06-19 18:23:09,069 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 18:23:09,069 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:23:09,130 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:23:09,131 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:23:09,134 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:23:13,197 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:23:13,198 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:23:13,211 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 18:23:22,404 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 18:23:24,706 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 18:23:27,022 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:23:28,786 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:23:28,793 - strategies.strategy_manager - INFO - Strategy Manager database coordinator configured
2025-06-19 18:23:28,794 - risk.risk_manager - INFO - Risk Manager database coordinator configured
2025-06-19 18:23:28,795 - execution.execution_engine - INFO - Execution Engine database coordinator configured
2025-06-19 18:23:28,796 - portfolio.portfolio_manager - INFO - Portfolio Manager database coordinator configured
2025-06-19 18:23:28,797 - learning.learning_manager - INFO - Learning Manager database coordinator configured
2025-06-19 18:23:28,800 - __main__ - INFO - Initializing advanced analytics components...
2025-06-19 18:23:28,802 - analytics.analytics_engine - INFO - Initializing Advanced Analytics Engine...
2025-06-19 18:23:28,803 - analytics.analytics_engine - ERROR - Error initializing analytics components: name 'MarketAnalytics' is not defined
2025-06-19 18:23:28,803 - analytics.analytics_engine - ERROR - Failed to initialize Advanced Analytics Engine: name 'MarketAnalytics' is not defined
2025-06-19 18:23:28,807 - __main__ - ERROR - Failed to initialize analytics components: 'StrategyManager' object has no attribute 'set_analytics_engine'
2025-06-19 18:23:28,808 - __main__ - ERROR - Failed to initialize components: 'StrategyManager' object has no attribute 'set_analytics_engine'
2025-06-19 18:23:28,809 - __main__ - ERROR - System error: 'StrategyManager' object has no attribute 'set_analytics_engine'
2025-06-19 18:23:28,866 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x00000280C1F3FCB0>
2025-06-19 18:23:28,867 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x00000280C39A3D10>
2025-06-19 18:24:26,707 - __main__ - INFO - Initializing system components...
2025-06-19 18:24:26,709 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 18:24:27,013 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 18:24:27,032 - models.model_registry - INFO - Discovered 21 new models: ['goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'qwen3:32b', 'marco-o1:7b', 'falcon3:10b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'huihui_ai/magistral-abliterated:24b', 'phi4-reasoning:plus', 'command-r:35b', 'gemma3:27b', 'deepseek-r1:latest', 'mistral-small:24b', 'adityakale/kotakneo:latest', 'exaone-deep:32b', 'magistral:24b', 'granite3.3:8b', 'hermes3:8b', 'qwen2.5vl:32b', 'huihui_ai/am-thinking-abliterate:latest', 'huihui_ai/homunculus-abliterated:latest', 'cogito:32b', 'nemotron-mini:4b']
2025-06-19 18:24:27,036 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 18:24:27,041 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 18:24:27,044 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 18:24:43,588 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 18:24:56,722 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 18:25:21,863 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 18:25:30,093 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 18:25:33,265 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 18:25:37,056 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 18:25:48,290 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 18:25:54,614 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 18:26:05,267 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 18:26:05,322 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 18:26:05,337 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 18:26:05,348 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 18:26:05,350 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 18:26:05,351 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 18:26:05,362 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 18:26:05,372 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 18:26:05,373 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 18:26:05,382 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 18:26:05,389 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 18:26:05,396 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 18:26:05,403 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 18:26:05,420 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 18:26:05,429 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 18:26:05,435 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 18:26:05,443 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 18:26:05,449 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 18:26:05,458 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 18:26:05,464 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 18:26:05,467 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 18:26:05,468 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 18:26:05,469 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 18:26:05,470 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 18:26:05,470 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 18:26:05,477 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 18:26:05,483 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 18:26:05,484 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 18:26:05,484 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 18:26:05,490 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 18:26:05,493 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 18:26:05,497 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 18:26:05,498 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 18:26:05,498 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 18:26:05,502 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 18:26:05,503 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 18:26:05,506 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 18:26:05,509 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 18:26:05,510 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 18:26:05,511 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 18:26:05,519 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 18:26:05,519 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 18:26:05,521 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 18:26:05,525 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 18:26:05,527 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 18:26:05,528 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 18:26:05,528 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 18:26:05,529 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 18:26:05,533 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 18:26:05,534 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 18:26:05,537 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 18:26:05,538 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 18:26:05,542 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 18:26:05,542 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 18:26:05,551 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 18:26:05,552 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 18:26:05,553 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 18:26:05,557 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 18:26:05,562 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 18:26:05,566 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 18:26:05,571 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 18:26:05,572 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 18:26:05,576 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 18:26:05,577 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 18:26:05,577 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 18:26:05,582 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 18:26:05,582 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 18:26:05,583 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 18:26:05,586 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 18:26:05,586 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 18:26:05,590 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 18:26:05,591 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 18:26:05,596 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 18:26:05,603 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 18:26:05,603 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 18:26:05,603 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 18:26:05,604 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 18:26:05,609 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 18:26:05,610 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 18:26:05,611 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 18:26:05,615 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 18:26:05,620 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 18:26:05,625 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 18:26:05,629 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 18:26:05,637 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 18:26:05,639 - __main__ - INFO - Initializing database integration components...
2025-06-19 18:26:05,640 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 18:26:05,641 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:26:05,696 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:26:05,698 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:26:05,702 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:26:09,773 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:26:09,774 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:26:09,790 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 18:26:19,005 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 18:26:21,343 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 18:26:23,658 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:26:25,430 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:26:25,439 - strategies.strategy_manager - INFO - Strategy Manager database coordinator configured
2025-06-19 18:26:25,441 - risk.risk_manager - INFO - Risk Manager database coordinator configured
2025-06-19 18:26:25,442 - execution.execution_engine - INFO - Execution Engine database coordinator configured
2025-06-19 18:26:25,443 - portfolio.portfolio_manager - INFO - Portfolio Manager database coordinator configured
2025-06-19 18:26:25,444 - learning.learning_manager - INFO - Learning Manager database coordinator configured
2025-06-19 18:26:25,449 - __main__ - INFO - Initializing advanced analytics components...
2025-06-19 18:26:25,451 - analytics.analytics_engine - INFO - Initializing Advanced Analytics Engine...
2025-06-19 18:26:25,451 - analytics.analytics_engine - ERROR - Error initializing analytics components: name 'MarketAnalytics' is not defined
2025-06-19 18:26:25,452 - analytics.analytics_engine - ERROR - Failed to initialize Advanced Analytics Engine: name 'MarketAnalytics' is not defined
2025-06-19 18:26:25,456 - __main__ - ERROR - Failed to initialize analytics components: 'StrategyManager' object has no attribute 'strategy_executor'
2025-06-19 18:26:25,457 - __main__ - ERROR - Failed to initialize components: 'StrategyManager' object has no attribute 'strategy_executor'
2025-06-19 18:26:25,458 - __main__ - ERROR - System error: 'StrategyManager' object has no attribute 'strategy_executor'
2025-06-19 18:26:25,517 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000002E760A22360>
2025-06-19 18:26:25,518 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x000002E760B4AD50>, 185913.328)])']
connector: <aiohttp.connector.TCPConnector object at 0x000002E760B801A0>
2025-06-19 18:26:25,520 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000002E72C528680>
2025-06-19 18:27:07,878 - __main__ - INFO - Initializing system components...
2025-06-19 18:27:07,880 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 18:27:08,190 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 18:27:08,209 - models.model_registry - INFO - Discovered 21 new models: ['falcon3:10b', 'adityakale/kotakneo:latest', 'phi4-reasoning:plus', 'deepseek-r1:latest', 'huihui_ai/homunculus-abliterated:latest', 'cogito:32b', 'marco-o1:7b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'nemotron-mini:4b', 'magistral:24b', 'qwen2.5vl:32b', 'hermes3:8b', 'huihui_ai/am-thinking-abliterate:latest', 'exaone-deep:32b', 'gemma3:27b', 'qwen3:32b', 'granite3.3:8b', 'mistral-small:24b', 'command-r:35b', 'huihui_ai/magistral-abliterated:24b']
2025-06-19 18:27:08,214 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 18:27:08,220 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 18:27:08,224 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 18:27:25,489 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 18:27:47,988 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 18:28:11,836 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 18:28:26,900 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 18:28:30,101 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 18:28:34,145 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 18:28:45,196 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 18:28:51,997 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 18:29:02,063 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 18:29:02,115 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 18:29:02,123 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 18:29:02,130 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 18:29:02,131 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 18:29:02,131 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 18:29:02,137 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 18:29:02,144 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 18:29:02,145 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 18:29:02,151 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 18:29:02,158 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 18:29:02,166 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 18:29:02,171 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 18:29:02,183 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 18:29:02,188 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 18:29:02,195 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 18:29:02,200 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 18:29:02,208 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 18:29:02,215 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 18:29:02,221 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 18:29:02,222 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 18:29:02,223 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 18:29:02,224 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 18:29:02,224 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 18:29:02,225 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 18:29:02,231 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 18:29:02,235 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 18:29:02,235 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 18:29:02,236 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 18:29:02,240 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 18:29:02,244 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 18:29:02,247 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 18:29:02,248 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 18:29:02,248 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 18:29:02,253 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 18:29:02,253 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 18:29:02,257 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 18:29:02,261 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 18:29:02,262 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 18:29:02,263 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 18:29:02,272 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 18:29:02,273 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 18:29:02,284 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 18:29:02,289 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 18:29:02,292 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 18:29:02,292 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 18:29:02,293 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 18:29:02,294 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 18:29:02,300 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 18:29:02,300 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 18:29:02,304 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 18:29:02,306 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 18:29:02,310 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 18:29:02,311 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 18:29:02,321 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 18:29:02,322 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 18:29:02,323 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 18:29:02,328 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 18:29:02,332 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 18:29:02,336 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 18:29:02,341 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 18:29:02,343 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 18:29:02,347 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 18:29:02,348 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 18:29:02,349 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 18:29:02,352 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 18:29:02,352 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 18:29:02,353 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 18:29:02,357 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 18:29:02,358 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 18:29:02,362 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 18:29:02,363 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 18:29:02,367 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 18:29:02,373 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 18:29:02,374 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 18:29:02,375 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 18:29:02,375 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 18:29:02,379 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 18:29:02,380 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 18:29:02,381 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 18:29:02,384 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 18:29:02,389 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 18:29:02,393 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 18:29:02,396 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 18:29:02,404 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 18:29:02,406 - __main__ - INFO - Initializing database integration components...
2025-06-19 18:29:02,407 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 18:29:02,407 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:29:02,463 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:29:02,464 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:29:02,468 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:29:06,540 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:29:06,541 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:29:06,545 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 18:29:15,740 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 18:29:18,085 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 18:29:20,384 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:29:22,152 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:29:22,162 - strategies.strategy_manager - INFO - Strategy Manager database coordinator configured
2025-06-19 18:29:22,163 - risk.risk_manager - INFO - Risk Manager database coordinator configured
2025-06-19 18:29:22,164 - execution.execution_engine - INFO - Execution Engine database coordinator configured
2025-06-19 18:29:22,164 - portfolio.portfolio_manager - INFO - Portfolio Manager database coordinator configured
2025-06-19 18:29:22,164 - learning.learning_manager - INFO - Learning Manager database coordinator configured
2025-06-19 18:29:22,168 - __main__ - INFO - Initializing advanced analytics components...
2025-06-19 18:29:22,168 - analytics.analytics_engine - INFO - Initializing Advanced Analytics Engine...
2025-06-19 18:29:22,169 - analytics.analytics_engine - ERROR - Error initializing analytics components: name 'MarketAnalytics' is not defined
2025-06-19 18:29:22,169 - analytics.analytics_engine - ERROR - Failed to initialize Advanced Analytics Engine: name 'MarketAnalytics' is not defined
2025-06-19 18:29:22,171 - strategies.strategy_manager - INFO - Strategy Manager analytics engine configured
2025-06-19 18:29:22,172 - __main__ - ERROR - Failed to initialize analytics components: 'RiskManager' object has no attribute 'set_analytics_engine'
2025-06-19 18:29:22,173 - __main__ - ERROR - Failed to initialize components: 'RiskManager' object has no attribute 'set_analytics_engine'
2025-06-19 18:29:22,174 - __main__ - ERROR - System error: 'RiskManager' object has no attribute 'set_analytics_engine'
2025-06-19 18:29:22,214 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000002497804BA70>
2025-06-19 18:29:22,216 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x00000249780CADB0>, 186090.125)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000024977FA01A0>
2025-06-19 18:29:22,217 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000024978125550>
2025-06-19 18:30:59,296 - __main__ - INFO - Initializing system components...
2025-06-19 18:30:59,298 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 18:30:59,587 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 18:30:59,604 - models.model_registry - INFO - Discovered 21 new models: ['phi4-reasoning:plus', 'magistral:24b', 'mistral-small:24b', 'marco-o1:7b', 'qwen2.5vl:32b', 'huihui_ai/am-thinking-abliterate:latest', 'hermes3:8b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'command-r:35b', 'deepseek-r1:latest', 'huihui_ai/magistral-abliterated:24b', 'gemma3:27b', 'exaone-deep:32b', 'qwen3:32b', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'adityakale/kotakneo:latest', 'huihui_ai/homunculus-abliterated:latest', 'granite3.3:8b', 'falcon3:10b', 'nemotron-mini:4b', 'cogito:32b']
2025-06-19 18:30:59,608 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 18:30:59,613 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 18:30:59,615 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 18:31:16,215 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 18:31:39,368 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 18:32:04,661 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 18:32:31,733 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 18:32:35,080 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 18:32:38,897 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 18:32:49,898 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 18:32:56,494 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 18:37:56,951 - models.model_instance - ERROR - Model goekdenizguelmez/JOSIEFIED-Qwen3:14b error for system: Request failed: 
2025-06-19 18:37:56,953 - models.model_deployment - ERROR - Model instance failed health check: {'status': 'unhealthy', 'model': 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'role': 'sentiment_analyst', 'agent': 'system', 'error': 'Request failed: ', 'last_check': **********.95385}
2025-06-19 18:37:56,955 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 18:37:56,963 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 18:37:56,975 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 18:37:56,984 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 18:37:56,986 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 18:37:56,987 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 18:37:56,995 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 18:37:57,003 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 18:37:57,004 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 18:37:57,010 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 18:37:57,015 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 18:37:57,019 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 18:37:57,024 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 18:37:57,036 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 18:37:57,042 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 18:37:57,048 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 18:37:57,053 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 18:37:57,061 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 18:37:57,071 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 18:37:57,080 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 18:37:57,081 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 18:37:57,083 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 18:37:57,083 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 18:37:57,084 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 18:37:57,085 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 18:37:57,090 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 18:37:57,098 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 18:37:57,099 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 18:37:57,100 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 18:37:57,104 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 18:37:57,110 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 18:37:57,115 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 18:37:57,116 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 18:37:57,117 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 18:37:57,121 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 18:37:57,123 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 18:37:57,128 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 18:37:57,133 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 18:37:57,134 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 18:37:57,134 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 18:37:57,145 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 18:37:57,147 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 18:37:57,158 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 18:37:57,164 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 18:37:57,166 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 18:37:57,166 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 18:37:57,169 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 18:37:57,169 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 18:37:57,175 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 18:37:57,176 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 18:37:57,181 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 18:37:57,182 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 18:37:57,186 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 18:37:57,187 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 18:37:57,195 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 18:37:57,196 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 18:37:57,198 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 18:37:57,201 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 18:37:57,205 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 18:37:57,209 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 18:37:57,216 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 18:37:57,218 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 18:37:57,221 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 18:37:57,222 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 18:37:57,222 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 18:37:57,227 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 18:37:57,227 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 18:37:57,228 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 18:37:57,231 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 18:37:57,232 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 18:37:57,235 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 18:37:57,235 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 18:37:57,242 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 18:37:57,248 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 18:37:57,250 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 18:37:57,250 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 18:37:57,251 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 18:37:57,256 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 18:37:57,258 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 18:37:57,260 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 18:37:57,265 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 18:37:57,269 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 18:37:57,274 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 18:37:57,280 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 18:37:57,289 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 18:37:57,294 - __main__ - INFO - Initializing database integration components...
2025-06-19 18:37:57,294 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 18:37:57,295 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:37:57,387 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:37:57,390 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:37:57,396 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:38:01,474 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:38:01,476 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:38:01,482 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 18:38:10,664 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 18:38:12,953 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 18:38:15,263 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:38:17,030 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:38:17,038 - strategies.strategy_manager - INFO - Strategy Manager database coordinator configured
2025-06-19 18:38:17,038 - risk.risk_manager - INFO - Risk Manager database coordinator configured
2025-06-19 18:38:17,040 - execution.execution_engine - INFO - Execution Engine database coordinator configured
2025-06-19 18:38:17,041 - portfolio.portfolio_manager - INFO - Portfolio Manager database coordinator configured
2025-06-19 18:38:17,042 - learning.learning_manager - INFO - Learning Manager database coordinator configured
2025-06-19 18:38:17,048 - __main__ - INFO - Initializing advanced analytics components...
2025-06-19 18:38:17,049 - analytics.analytics_engine - INFO - Initializing Advanced Analytics Engine...
2025-06-19 18:38:17,051 - analytics.analytics_engine - ERROR - Error initializing analytics components: name 'MarketAnalytics' is not defined
2025-06-19 18:38:17,052 - analytics.analytics_engine - ERROR - Failed to initialize Advanced Analytics Engine: name 'MarketAnalytics' is not defined
2025-06-19 18:38:17,058 - strategies.strategy_manager - INFO - Strategy Manager analytics engine configured
2025-06-19 18:38:17,059 - __main__ - ERROR - Failed to initialize analytics components: 'RiskManager' object has no attribute 'risk_calculator'
2025-06-19 18:38:17,060 - __main__ - ERROR - Failed to initialize components: 'RiskManager' object has no attribute 'risk_calculator'
2025-06-19 18:38:17,061 - __main__ - ERROR - System error: 'RiskManager' object has no attribute 'risk_calculator'
2025-06-19 18:38:17,153 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000020CFE368050>
2025-06-19 18:38:17,155 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000020C8014A720>
2025-06-19 18:39:17,183 - __main__ - INFO - Initializing system components...
2025-06-19 18:39:17,185 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 18:39:17,476 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 18:39:17,497 - models.model_registry - INFO - Discovered 21 new models: ['exaone-deep:32b', 'nemotron-mini:4b', 'magistral:24b', 'huihui_ai/am-thinking-abliterate:latest', 'cogito:32b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'huihui_ai/magistral-abliterated:24b', 'command-r:35b', 'qwen3:32b', 'qwen2.5vl:32b', 'phi4-reasoning:plus', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'mistral-small:24b', 'huihui_ai/homunculus-abliterated:latest', 'adityakale/kotakneo:latest', 'deepseek-r1:latest', 'falcon3:10b', 'gemma3:27b', 'marco-o1:7b', 'granite3.3:8b', 'hermes3:8b']
2025-06-19 18:39:17,500 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 18:39:17,505 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 18:39:17,508 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 18:39:34,645 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 18:39:58,362 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 18:40:27,611 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 18:40:54,731 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 18:40:58,150 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 18:41:01,963 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 18:41:13,546 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 18:41:19,730 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 18:41:31,340 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 18:41:31,360 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 18:41:31,382 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 18:41:31,395 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 18:41:31,398 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 18:41:31,399 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 18:41:31,410 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 18:41:31,421 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 18:41:31,423 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 18:41:31,432 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 18:41:31,439 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 18:41:31,447 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 18:41:31,453 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 18:41:31,473 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 18:41:31,483 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 18:41:31,496 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 18:41:31,509 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 18:41:31,522 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 18:41:31,533 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 18:41:31,546 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 18:41:31,548 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 18:41:31,548 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 18:41:31,549 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 18:41:31,551 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 18:41:31,551 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 18:41:31,560 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 18:41:31,565 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 18:41:31,567 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 18:41:31,569 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 18:41:31,574 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 18:41:31,581 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 18:41:31,587 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 18:41:31,588 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 18:41:31,589 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 18:41:31,597 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 18:41:31,598 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 18:41:31,604 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 18:41:31,612 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 18:41:31,615 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 18:41:31,616 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 18:41:31,632 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 18:41:31,633 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 18:41:31,646 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 18:41:31,654 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 18:41:31,654 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 18:41:31,657 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 18:41:31,658 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 18:41:31,660 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 18:41:31,666 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 18:41:31,668 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 18:41:31,676 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 18:41:31,678 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 18:41:31,685 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 18:41:31,686 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 18:41:31,703 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 18:41:31,704 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 18:41:31,706 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 18:41:31,715 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 18:41:31,722 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 18:41:31,729 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 18:41:31,740 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 18:41:31,743 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 18:41:31,751 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 18:41:31,753 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 18:41:31,754 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 18:41:31,762 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 18:41:31,764 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 18:41:31,764 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 18:41:31,773 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 18:41:31,774 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 18:41:31,782 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 18:41:31,783 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 18:41:31,793 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 18:41:31,804 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 18:41:31,806 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 18:41:31,807 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 18:41:31,808 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 18:41:31,818 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 18:41:31,819 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 18:41:31,820 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 18:41:31,829 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 18:41:31,836 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 18:41:31,842 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 18:41:31,849 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 18:41:31,868 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 18:41:31,873 - __main__ - INFO - Initializing database integration components...
2025-06-19 18:41:31,874 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 18:41:31,876 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:41:31,954 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:41:31,956 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:41:31,963 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:41:36,022 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:41:36,024 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:41:36,042 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 18:41:45,269 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 18:41:47,579 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 18:41:49,889 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:41:51,657 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:41:51,668 - strategies.strategy_manager - INFO - Strategy Manager database coordinator configured
2025-06-19 18:41:51,669 - risk.risk_manager - INFO - Risk Manager database coordinator configured
2025-06-19 18:41:51,670 - execution.execution_engine - INFO - Execution Engine database coordinator configured
2025-06-19 18:41:51,671 - portfolio.portfolio_manager - INFO - Portfolio Manager database coordinator configured
2025-06-19 18:41:51,673 - learning.learning_manager - INFO - Learning Manager database coordinator configured
2025-06-19 18:41:51,679 - __main__ - INFO - Initializing advanced analytics components...
2025-06-19 18:41:51,681 - analytics.analytics_engine - INFO - Initializing Advanced Analytics Engine...
2025-06-19 18:41:51,681 - analytics.analytics_engine - ERROR - Error initializing analytics components: name 'MarketAnalytics' is not defined
2025-06-19 18:41:51,683 - analytics.analytics_engine - ERROR - Failed to initialize Advanced Analytics Engine: name 'MarketAnalytics' is not defined
2025-06-19 18:41:51,688 - strategies.strategy_manager - INFO - Strategy Manager analytics engine configured
2025-06-19 18:41:51,690 - risk.risk_manager - INFO - Risk Manager analytics engine configured
2025-06-19 18:41:51,691 - __main__ - ERROR - Failed to initialize analytics components: 'ExecutionEngine' object has no attribute 'set_analytics_engine'
2025-06-19 18:41:51,693 - __main__ - ERROR - Failed to initialize components: 'ExecutionEngine' object has no attribute 'set_analytics_engine'
2025-06-19 18:41:51,694 - __main__ - ERROR - System error: 'ExecutionEngine' object has no attribute 'set_analytics_engine'
2025-06-19 18:41:51,768 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000001D3548D01A0>
2025-06-19 18:41:51,771 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000001D354709610>
2025-06-19 18:42:30,837 - __main__ - INFO - Initializing system components...
2025-06-19 18:42:30,839 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 18:42:31,153 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 18:42:31,174 - models.model_registry - INFO - Discovered 21 new models: ['mistral-small:24b', 'nemotron-mini:4b', 'gemma3:27b', 'deepseek-r1:latest', 'huihui_ai/acereason-nemotron-abliterated:14b', 'exaone-deep:32b', 'huihui_ai/homunculus-abliterated:latest', 'qwen2.5vl:32b', 'magistral:24b', 'marco-o1:7b', 'granite3.3:8b', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'hermes3:8b', 'falcon3:10b', 'qwen3:32b', 'adityakale/kotakneo:latest', 'phi4-reasoning:plus', 'command-r:35b', 'huihui_ai/magistral-abliterated:24b', 'huihui_ai/am-thinking-abliterate:latest', 'cogito:32b']
2025-06-19 18:42:31,180 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 18:42:31,187 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 18:42:31,194 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 18:42:48,939 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 18:43:28,702 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 18:43:59,946 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 18:44:18,407 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 18:44:21,896 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 18:44:25,689 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 18:44:36,896 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 18:44:43,696 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 18:44:53,769 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 18:44:53,822 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 18:44:53,830 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 18:44:53,836 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 18:44:53,837 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 18:44:53,837 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 18:44:53,844 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 18:44:53,850 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 18:44:53,851 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 18:44:53,856 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 18:44:53,861 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 18:44:53,866 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 18:44:53,870 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 18:44:53,881 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 18:44:53,886 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 18:44:53,892 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 18:44:53,898 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 18:44:53,903 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 18:44:53,908 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 18:44:53,914 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 18:44:53,915 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 18:44:53,916 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 18:44:53,916 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 18:44:53,918 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 18:44:53,918 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 18:44:53,924 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 18:44:53,928 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 18:44:53,928 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 18:44:53,929 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 18:44:53,933 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 18:44:53,936 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 18:44:53,940 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 18:44:53,941 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 18:44:53,941 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 18:44:53,945 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 18:44:53,946 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 18:44:53,949 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 18:44:53,952 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 18:44:53,953 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 18:44:53,954 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 18:44:53,961 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 18:44:53,962 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 18:44:53,964 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 18:44:53,968 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 18:44:53,969 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 18:44:53,969 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 18:44:53,970 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 18:44:53,970 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 18:44:53,975 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 18:44:53,976 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 18:44:53,980 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 18:44:53,980 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 18:44:53,984 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 18:44:53,985 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 18:44:54,000 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 18:44:54,001 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 18:44:54,002 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 18:44:54,011 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 18:44:54,018 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 18:44:54,024 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 18:44:54,035 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 18:44:54,036 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 18:44:54,045 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 18:44:54,046 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 18:44:54,047 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 18:44:54,054 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 18:44:54,056 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 18:44:54,056 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 18:44:54,064 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 18:44:54,066 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 18:44:54,072 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 18:44:54,072 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 18:44:54,081 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 18:44:54,087 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 18:44:54,088 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 18:44:54,089 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 18:44:54,090 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 18:44:54,094 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 18:44:54,096 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 18:44:54,097 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 18:44:54,103 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 18:44:54,107 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 18:44:54,112 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 18:44:54,116 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 18:44:54,126 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 18:44:54,129 - __main__ - INFO - Initializing database integration components...
2025-06-19 18:44:54,130 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 18:44:54,130 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:44:54,190 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:44:54,191 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:44:54,196 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:44:58,263 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:44:58,264 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:44:58,268 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 18:45:07,465 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 18:45:09,762 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 18:45:12,062 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:45:13,832 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:45:13,838 - strategies.strategy_manager - INFO - Strategy Manager database coordinator configured
2025-06-19 18:45:13,840 - risk.risk_manager - INFO - Risk Manager database coordinator configured
2025-06-19 18:45:13,840 - execution.execution_engine - INFO - Execution Engine database coordinator configured
2025-06-19 18:45:13,841 - portfolio.portfolio_manager - INFO - Portfolio Manager database coordinator configured
2025-06-19 18:45:13,842 - learning.learning_manager - INFO - Learning Manager database coordinator configured
2025-06-19 18:45:13,845 - __main__ - INFO - Initializing advanced analytics components...
2025-06-19 18:45:13,846 - analytics.analytics_engine - INFO - Initializing Advanced Analytics Engine...
2025-06-19 18:45:13,846 - analytics.analytics_engine - ERROR - Error initializing analytics components: name 'MarketAnalytics' is not defined
2025-06-19 18:45:13,847 - analytics.analytics_engine - ERROR - Failed to initialize Advanced Analytics Engine: name 'MarketAnalytics' is not defined
2025-06-19 18:45:13,849 - strategies.strategy_manager - INFO - Strategy Manager analytics engine configured
2025-06-19 18:45:13,850 - risk.risk_manager - INFO - Risk Manager analytics engine configured
2025-06-19 18:45:13,851 - __main__ - ERROR - Failed to initialize analytics components: 'ExecutionEngine' object has no attribute 'trade_executor'
2025-06-19 18:45:13,851 - __main__ - ERROR - Failed to initialize components: 'ExecutionEngine' object has no attribute 'trade_executor'
2025-06-19 18:45:13,852 - __main__ - ERROR - System error: 'ExecutionEngine' object has no attribute 'trade_executor'
2025-06-19 18:45:13,907 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000025884E11CA0>
2025-06-19 18:45:13,909 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x00000258B9393740>
2025-06-19 18:45:59,238 - __main__ - INFO - Initializing system components...
2025-06-19 18:45:59,239 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 18:45:59,557 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 18:45:59,574 - models.model_registry - INFO - Discovered 21 new models: ['command-r:35b', 'qwen3:32b', 'exaone-deep:32b', 'granite3.3:8b', 'magistral:24b', 'phi4-reasoning:plus', 'deepseek-r1:latest', 'huihui_ai/am-thinking-abliterate:latest', 'hermes3:8b', 'huihui_ai/magistral-abliterated:24b', 'adityakale/kotakneo:latest', 'gemma3:27b', 'huihui_ai/homunculus-abliterated:latest', 'huihui_ai/acereason-nemotron-abliterated:14b', 'marco-o1:7b', 'cogito:32b', 'mistral-small:24b', 'falcon3:10b', 'qwen2.5vl:32b', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'nemotron-mini:4b']
2025-06-19 18:45:59,581 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 18:45:59,587 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 18:45:59,593 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 18:46:16,490 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 18:46:39,903 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 18:47:08,535 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 18:47:23,408 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 18:47:26,642 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 18:47:30,439 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 18:47:41,631 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 18:47:46,758 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 18:48:01,194 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 18:48:01,208 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 18:48:01,215 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 18:48:01,221 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 18:48:01,222 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 18:48:01,223 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 18:48:01,230 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 18:48:01,236 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 18:48:01,237 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 18:48:01,242 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 18:48:01,247 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 18:48:01,250 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 18:48:01,256 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 18:48:01,273 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 18:48:01,279 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 18:48:01,287 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 18:48:01,293 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 18:48:01,299 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 18:48:01,304 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 18:48:01,311 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 18:48:01,312 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 18:48:01,312 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 18:48:01,313 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 18:48:01,314 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 18:48:01,314 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 18:48:01,320 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 18:48:01,324 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 18:48:01,324 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 18:48:01,325 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 18:48:01,330 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 18:48:01,334 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 18:48:01,337 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 18:48:01,338 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 18:48:01,339 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 18:48:01,344 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 18:48:01,344 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 18:48:01,348 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 18:48:01,351 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 18:48:01,352 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 18:48:01,353 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 18:48:01,366 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 18:48:01,366 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 18:48:01,369 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 18:48:01,378 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 18:48:01,379 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 18:48:01,380 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 18:48:01,381 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 18:48:01,382 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 18:48:01,388 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 18:48:01,388 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 18:48:01,395 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 18:48:01,397 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 18:48:01,402 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 18:48:01,403 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 18:48:01,417 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 18:48:01,418 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 18:48:01,418 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 18:48:01,426 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 18:48:01,430 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 18:48:01,435 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 18:48:01,443 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 18:48:01,444 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 18:48:01,450 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 18:48:01,450 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 18:48:01,451 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 18:48:01,454 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 18:48:01,456 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 18:48:01,457 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 18:48:01,461 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 18:48:01,462 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 18:48:01,466 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 18:48:01,467 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 18:48:01,473 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 18:48:01,480 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 18:48:01,481 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 18:48:01,481 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 18:48:01,482 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 18:48:01,486 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 18:48:01,487 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 18:48:01,488 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 18:48:01,493 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 18:48:01,497 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 18:48:01,501 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 18:48:01,505 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 18:48:01,512 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 18:48:01,514 - __main__ - INFO - Initializing database integration components...
2025-06-19 18:48:01,516 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 18:48:01,516 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:48:01,574 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:48:01,575 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:48:01,579 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:48:05,664 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:48:05,669 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:48:05,684 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 18:48:14,868 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 18:48:17,171 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 18:48:19,464 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:48:21,235 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:48:21,240 - strategies.strategy_manager - INFO - Strategy Manager database coordinator configured
2025-06-19 18:48:21,240 - risk.risk_manager - INFO - Risk Manager database coordinator configured
2025-06-19 18:48:21,241 - execution.execution_engine - INFO - Execution Engine database coordinator configured
2025-06-19 18:48:21,242 - portfolio.portfolio_manager - INFO - Portfolio Manager database coordinator configured
2025-06-19 18:48:21,243 - learning.learning_manager - INFO - Learning Manager database coordinator configured
2025-06-19 18:48:21,245 - __main__ - INFO - Initializing advanced analytics components...
2025-06-19 18:48:21,246 - analytics.analytics_engine - INFO - Initializing Advanced Analytics Engine...
2025-06-19 18:48:21,247 - analytics.analytics_engine - ERROR - Error initializing analytics components: name 'MarketAnalytics' is not defined
2025-06-19 18:48:21,247 - analytics.analytics_engine - ERROR - Failed to initialize Advanced Analytics Engine: name 'MarketAnalytics' is not defined
2025-06-19 18:48:21,249 - strategies.strategy_manager - INFO - Strategy Manager analytics engine configured
2025-06-19 18:48:21,251 - risk.risk_manager - INFO - Risk Manager analytics engine configured
2025-06-19 18:48:21,251 - execution.execution_engine - INFO - Execution Engine analytics engine configured
2025-06-19 18:48:21,252 - __main__ - ERROR - Failed to initialize analytics components: 'PortfolioManager' object has no attribute 'set_analytics_engine'
2025-06-19 18:48:21,252 - __main__ - ERROR - Failed to initialize components: 'PortfolioManager' object has no attribute 'set_analytics_engine'
2025-06-19 18:48:21,253 - __main__ - ERROR - System error: 'PortfolioManager' object has no attribute 'set_analytics_engine'
2025-06-19 18:48:21,295 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000002B6D5057CE0>
2025-06-19 18:48:21,297 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000002B6A0AAB140>
2025-06-19 18:49:39,104 - __main__ - INFO - Initializing system components...
2025-06-19 18:49:39,106 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 18:49:39,406 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 18:49:39,413 - models.model_registry - INFO - Discovered 21 new models: ['gemma3:27b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'mistral-small:24b', 'exaone-deep:32b', 'huihui_ai/homunculus-abliterated:latest', 'qwen3:32b', 'huihui_ai/am-thinking-abliterate:latest', 'huihui_ai/magistral-abliterated:24b', 'phi4-reasoning:plus', 'qwen2.5vl:32b', 'nemotron-mini:4b', 'magistral:24b', 'marco-o1:7b', 'falcon3:10b', 'hermes3:8b', 'deepseek-r1:latest', 'granite3.3:8b', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'cogito:32b', 'adityakale/kotakneo:latest', 'command-r:35b']
2025-06-19 18:49:39,417 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 18:49:39,422 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 18:49:39,424 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 18:49:56,271 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 18:50:09,258 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 18:50:35,358 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 18:50:57,607 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 18:51:01,044 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 18:51:04,867 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 18:51:15,966 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 18:51:23,001 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 18:51:33,315 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 18:51:33,328 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 18:51:33,337 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 18:51:33,343 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 18:51:33,344 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 18:51:33,344 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 18:51:33,350 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 18:51:33,356 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 18:51:33,357 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 18:51:33,363 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 18:51:33,369 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 18:51:33,372 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 18:51:33,377 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 18:51:33,387 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 18:51:33,392 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 18:51:33,398 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 18:51:33,403 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 18:51:33,408 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 18:51:33,415 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 18:51:33,420 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 18:51:33,421 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 18:51:33,421 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 18:51:33,422 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 18:51:33,423 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 18:51:33,424 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 18:51:33,429 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 18:51:33,433 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 18:51:33,433 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 18:51:33,434 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 18:51:33,438 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 18:51:33,442 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 18:51:33,445 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 18:51:33,446 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 18:51:33,447 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 18:51:33,450 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 18:51:33,451 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 18:51:33,454 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 18:51:33,458 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 18:51:33,459 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 18:51:33,459 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 18:51:33,468 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 18:51:33,468 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 18:51:33,480 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 18:51:33,484 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 18:51:33,485 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 18:51:33,486 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 18:51:33,486 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 18:51:33,487 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 18:51:33,491 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 18:51:33,492 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 18:51:33,496 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 18:51:33,497 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 18:51:33,501 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 18:51:33,502 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 18:51:33,513 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 18:51:33,514 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 18:51:33,515 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 18:51:33,521 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 18:51:33,526 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 18:51:33,530 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 18:51:33,539 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 18:51:33,540 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 18:51:33,546 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 18:51:33,547 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 18:51:33,548 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 18:51:33,552 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 18:51:33,553 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 18:51:33,554 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 18:51:33,558 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 18:51:33,558 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 18:51:33,562 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 18:51:33,563 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 18:51:33,568 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 18:51:33,575 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 18:51:33,576 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 18:51:33,577 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 18:51:33,577 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 18:51:33,583 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 18:51:33,584 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 18:51:33,585 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 18:51:33,590 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 18:51:33,594 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 18:51:33,597 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 18:51:33,601 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 18:51:33,610 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 18:51:33,612 - __main__ - INFO - Initializing database integration components...
2025-06-19 18:51:33,613 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 18:51:33,614 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:51:33,677 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:51:33,678 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:51:33,682 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:51:37,742 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:51:37,743 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:51:37,748 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 18:51:46,926 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 18:51:49,238 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 18:51:51,532 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:51:53,316 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:51:53,320 - strategies.strategy_manager - INFO - Strategy Manager database coordinator configured
2025-06-19 18:51:53,321 - risk.risk_manager - INFO - Risk Manager database coordinator configured
2025-06-19 18:51:53,321 - execution.execution_engine - INFO - Execution Engine database coordinator configured
2025-06-19 18:51:53,322 - portfolio.portfolio_manager - INFO - Portfolio Manager database coordinator configured
2025-06-19 18:51:53,323 - learning.learning_manager - INFO - Learning Manager database coordinator configured
2025-06-19 18:51:53,326 - __main__ - INFO - Initializing advanced analytics components...
2025-06-19 18:51:53,327 - analytics.analytics_engine - INFO - Initializing Advanced Analytics Engine...
2025-06-19 18:51:53,327 - analytics.analytics_engine - ERROR - Error initializing analytics components: name 'MarketAnalytics' is not defined
2025-06-19 18:51:53,328 - analytics.analytics_engine - ERROR - Failed to initialize Advanced Analytics Engine: name 'MarketAnalytics' is not defined
2025-06-19 18:51:53,331 - strategies.strategy_manager - INFO - Strategy Manager analytics engine configured
2025-06-19 18:51:53,331 - risk.risk_manager - INFO - Risk Manager analytics engine configured
2025-06-19 18:51:53,331 - execution.execution_engine - INFO - Execution Engine analytics engine configured
2025-06-19 18:51:53,332 - portfolio.portfolio_manager - INFO - Portfolio Manager analytics engine configured
2025-06-19 18:51:53,334 - __main__ - INFO - Initializing API and web interface components...
2025-06-19 18:51:53,336 - api.api_server - INFO - Initializing API Server...
2025-06-19 18:51:53,336 - api.websocket_manager - INFO - Initializing WebSocket Manager...
2025-06-19 18:51:53,362 - api.auth_manager - INFO - Initializing Authentication Manager...
2025-06-19 18:51:53,363 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-06-19 18:51:53,571 - api.auth_manager - INFO - Created user: admin
2025-06-19 18:51:53,571 - api.auth_manager - INFO - Created default admin user
2025-06-19 18:51:53,572 - api.auth_manager - INFO - Created default API key: R2qc1v-iKxxbFdVXtBgjjZtSnk3f8c-B-KY1fRkwAQA
2025-06-19 18:51:53,586 - api.rate_limiter - INFO - Initializing Rate Limiter...
2025-06-19 18:51:53,592 - web.web_server - INFO - Initializing Web Server...
2025-06-19 18:51:53,596 - web.web_server - ERROR - Error creating templates: 'charmap' codec can't encode character '\U0001f916' in position 778: character maps to <undefined>
2025-06-19 18:51:53,597 - web.web_server - ERROR - Failed to initialize Web Server: 'charmap' codec can't encode character '\U0001f916' in position 778: character maps to <undefined>
2025-06-19 18:51:53,629 - __main__ - INFO - Starting Advanced Ollama Trading Agent System...
2025-06-19 18:51:53,631 - risk.risk_manager - INFO - Starting Risk Management System...
2025-06-19 18:51:53,632 - risk.risk_monitor - INFO - Starting Real-Time Risk Monitor...
2025-06-19 18:51:53,658 - execution.execution_engine - INFO - Starting Execution Engine...
2025-06-19 18:51:53,663 - execution.paper_trader - INFO - Starting Paper Trader...
2025-06-19 18:51:53,666 - execution.execution_monitor - INFO - Starting Execution Monitor...
2025-06-19 18:51:53,669 - execution.venue_router - INFO - Starting Venue Router...
2025-06-19 18:51:53,670 - execution.trade_lifecycle - INFO - Starting Trade Lifecycle Manager...
2025-06-19 18:51:53,679 - portfolio.portfolio_manager - INFO - Starting Portfolio Manager...
2025-06-19 18:51:53,680 - portfolio.portfolio_manager - ERROR - Failed to start Portfolio Manager: 'AllocationEngine' object has no attribute 'start'
2025-06-19 18:51:53,683 - strategies.strategy_manager - INFO - Starting Strategy Manager...
2025-06-19 18:51:53,690 - database.database_coordinator - INFO - Starting Database Coordinator...
2025-06-19 18:51:53,691 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:51:53,693 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:51:53,753 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:51:53,754 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:51:57,758 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:51:57,759 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:51:57,764 - analytics.analytics_engine - INFO - Initializing Advanced Analytics Engine...
2025-06-19 18:51:57,764 - analytics.analytics_engine - ERROR - Error initializing analytics components: name 'MarketAnalytics' is not defined
2025-06-19 18:51:57,765 - analytics.analytics_engine - ERROR - Failed to initialize Advanced Analytics Engine: name 'MarketAnalytics' is not defined
2025-06-19 18:51:57,767 - learning.learning_manager - INFO - Starting Learning Manager...
2025-06-19 18:51:57,782 - api.api_server - INFO - Starting API Server on 0.0.0.0:8000...
2025-06-19 18:51:57,783 - api.websocket_manager - INFO - Starting WebSocket Manager...
2025-06-19 18:51:57,912 - models.ollama_hub - INFO - Starting Ollama Model Hub...
2025-06-19 18:51:57,913 - communication.message_broker - INFO - Starting Message Broker...
2025-06-19 18:51:57,916 - data.market_data_manager - INFO - Starting Market Data Manager...
2025-06-19 18:51:57,918 - agents.agent_manager - INFO - Starting Agent Manager...
2025-06-19 18:51:57,921 - teams.team_manager - INFO - Starting Advanced Team Manager...
2025-06-19 18:51:57,923 - monitoring.system_monitor - INFO - Starting System Monitor...
2025-06-19 18:51:57,926 - analytics.market_analytics - INFO - Starting Market Analytics...
2025-06-19 18:51:57,929 - dashboard.metrics_collector - INFO - Starting Metrics Collector...
2025-06-19 18:51:57,932 - api.api_server - INFO - API application starting up...
2025-06-19 18:51:57,932 - models.model_registry - INFO - Starting Model Registry...
2025-06-19 18:51:57,935 - models.model_deployment - INFO - Starting Model Deployment Manager...
2025-06-19 18:51:57,938 - models.model_performance - INFO - Starting Model Performance Tracker...
2025-06-19 18:51:57,948 - __main__ - ERROR - Failed to start system: 'RiskManager' object has no attribute 'get_system_status'
2025-06-19 18:51:57,949 - __main__ - ERROR - System error: 'RiskManager' object has no attribute 'get_system_status'
2025-06-19 18:51:57,950 - __main__ - INFO - Stopping trading system...
2025-06-19 18:51:57,950 - strategies.strategy_manager - INFO - Stopping Strategy Manager...
2025-06-19 18:51:57,962 - execution.execution_engine - INFO - Stopping Execution Engine...
2025-06-19 18:51:57,967 - execution.paper_trader - INFO - Stopping Paper Trader...
2025-06-19 18:51:57,970 - execution.execution_monitor - INFO - Stopping Execution Monitor...
2025-06-19 18:51:57,973 - execution.venue_router - INFO - Stopping Venue Router...
2025-06-19 18:51:57,975 - execution.trade_lifecycle - INFO - Stopping Trade Lifecycle Manager...
2025-06-19 18:51:57,985 - risk.risk_manager - INFO - Stopping Risk Management System...
2025-06-19 18:51:57,985 - risk.risk_monitor - INFO - Stopping Real-Time Risk Monitor...
2025-06-19 18:51:58,050 - api.api_server - INFO - Stopping API Server...
2025-06-19 18:51:58,051 - api.websocket_manager - INFO - Stopping WebSocket Manager...
2025-06-19 18:51:58,160 - api.api_server - INFO - API application shutting down...
2025-06-19 18:51:58,168 - learning.learning_manager - INFO - Stopping Learning Manager...
2025-06-19 18:51:58,200 - database.database_coordinator - INFO - Stopping Database Coordinator...
2025-06-19 18:51:58,222 - monitoring.system_monitor - INFO - Stopping System Monitor...
2025-06-19 18:51:58,227 - teams.team_manager - INFO - Stopping Advanced Team Manager...
2025-06-19 18:51:58,232 - agents.agent_manager - INFO - Stopping Agent Manager...
2025-06-19 18:51:58,233 - data.market_data_manager - INFO - Stopping Market Data Manager...
2025-06-19 18:51:58,235 - communication.message_broker - INFO - Stopping Message Broker...
2025-06-19 18:51:58,236 - models.ollama_hub - INFO - Stopping Ollama Model Hub...
2025-06-19 18:51:58,237 - models.model_performance - INFO - Stopping Model Performance Tracker...
2025-06-19 18:51:58,238 - models.model_deployment - INFO - Stopping Model Deployment Manager...
2025-06-19 18:51:58,239 - models.model_registry - INFO - Stopping Model Registry...
2025-06-19 18:52:40,534 - __main__ - INFO - Initializing system components...
2025-06-19 18:52:40,537 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 18:52:40,815 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 18:52:40,832 - models.model_registry - INFO - Discovered 21 new models: ['exaone-deep:32b', 'qwen2.5vl:32b', 'magistral:24b', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'granite3.3:8b', 'deepseek-r1:latest', 'adityakale/kotakneo:latest', 'huihui_ai/acereason-nemotron-abliterated:14b', 'nemotron-mini:4b', 'huihui_ai/am-thinking-abliterate:latest', 'hermes3:8b', 'marco-o1:7b', 'huihui_ai/magistral-abliterated:24b', 'huihui_ai/homunculus-abliterated:latest', 'gemma3:27b', 'command-r:35b', 'phi4-reasoning:plus', 'cogito:32b', 'mistral-small:24b', 'qwen3:32b', 'falcon3:10b']
2025-06-19 18:52:40,835 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 18:52:40,841 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 18:52:40,845 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 18:52:57,597 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 18:53:10,942 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 18:53:40,259 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 18:53:54,861 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 18:53:58,319 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 18:54:02,129 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 18:54:13,102 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 18:54:19,500 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 18:54:30,915 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 18:54:30,918 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 18:54:30,924 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 18:54:30,930 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 18:54:30,931 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 18:54:30,931 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 18:54:30,937 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 18:54:30,943 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 18:54:30,944 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 18:54:30,947 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 18:54:30,951 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 18:54:30,954 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 18:54:30,957 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 18:54:30,967 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 18:54:30,972 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 18:54:30,978 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 18:54:30,983 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 18:54:30,988 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 18:54:30,993 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 18:54:30,998 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 18:54:30,999 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 18:54:31,000 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 18:54:31,000 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 18:54:31,001 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 18:54:31,002 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 18:54:31,004 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 18:54:31,008 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 18:54:31,009 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 18:54:31,009 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 18:54:31,012 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 18:54:31,015 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 18:54:31,018 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 18:54:31,019 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 18:54:31,020 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 18:54:31,022 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 18:54:31,023 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 18:54:31,027 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 18:54:31,030 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 18:54:31,031 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 18:54:31,031 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 18:54:31,039 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 18:54:31,040 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 18:54:31,042 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 18:54:31,047 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 18:54:31,048 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 18:54:31,049 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 18:54:31,049 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 18:54:31,051 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 18:54:31,054 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 18:54:31,056 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 18:54:31,062 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 18:54:31,064 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 18:54:31,071 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 18:54:31,073 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 18:54:31,088 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 18:54:31,089 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 18:54:31,090 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 18:54:31,095 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 18:54:31,101 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 18:54:31,104 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 18:54:31,111 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 18:54:31,112 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 18:54:31,115 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 18:54:31,116 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 18:54:31,116 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 18:54:31,119 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 18:54:31,120 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 18:54:31,121 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 18:54:31,124 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 18:54:31,125 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 18:54:31,128 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 18:54:31,129 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 18:54:31,134 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 18:54:31,138 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 18:54:31,138 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 18:54:31,140 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 18:54:31,140 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 18:54:31,146 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 18:54:31,147 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 18:54:31,148 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 18:54:31,149 - learning.strategy_repository - INFO - Loaded 0 strategies from disk
2025-06-19 18:54:31,152 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 18:54:31,154 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 18:54:31,158 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 18:54:31,159 - learning.knowledge_manager - INFO - Loaded 0 knowledge items from disk
2025-06-19 18:54:31,162 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 18:54:31,169 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 18:54:31,172 - __main__ - INFO - Initializing database integration components...
2025-06-19 18:54:31,173 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 18:54:31,173 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:54:31,236 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:54:31,237 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:54:31,241 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:54:35,309 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:54:35,311 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:54:35,314 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 18:54:44,490 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 18:54:46,779 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 18:54:49,082 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:54:50,850 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:54:50,858 - strategies.strategy_manager - INFO - Strategy Manager database coordinator configured
2025-06-19 18:54:50,859 - risk.risk_manager - INFO - Risk Manager database coordinator configured
2025-06-19 18:54:50,859 - execution.execution_engine - INFO - Execution Engine database coordinator configured
2025-06-19 18:54:50,860 - portfolio.portfolio_manager - INFO - Portfolio Manager database coordinator configured
2025-06-19 18:54:50,861 - learning.learning_manager - INFO - Learning Manager database coordinator configured
2025-06-19 18:54:50,863 - __main__ - INFO - Initializing advanced analytics components...
2025-06-19 18:54:50,864 - analytics.analytics_engine - INFO - Initializing Advanced Analytics Engine...
2025-06-19 18:54:50,864 - analytics.analytics_engine - ERROR - Error initializing analytics components: name 'MarketAnalytics' is not defined
2025-06-19 18:54:50,864 - analytics.analytics_engine - ERROR - Failed to initialize Advanced Analytics Engine: name 'MarketAnalytics' is not defined
2025-06-19 18:54:50,867 - strategies.strategy_manager - INFO - Strategy Manager analytics engine configured
2025-06-19 18:54:50,868 - risk.risk_manager - INFO - Risk Manager analytics engine configured
2025-06-19 18:54:50,869 - execution.execution_engine - INFO - Execution Engine analytics engine configured
2025-06-19 18:54:50,869 - portfolio.portfolio_manager - INFO - Portfolio Manager analytics engine configured
2025-06-19 18:54:50,871 - __main__ - INFO - Initializing API and web interface components...
2025-06-19 18:54:50,872 - api.api_server - INFO - Initializing API Server...
2025-06-19 18:54:50,873 - api.websocket_manager - INFO - Initializing WebSocket Manager...
2025-06-19 18:54:50,890 - api.auth_manager - INFO - Initializing Authentication Manager...
2025-06-19 18:54:50,891 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-06-19 18:54:51,099 - api.auth_manager - INFO - Created user: admin
2025-06-19 18:54:51,100 - api.auth_manager - INFO - Created default admin user
2025-06-19 18:54:51,101 - api.auth_manager - INFO - Created default API key: ARSNwxCvtJLnPRSNjLEEvXUZQtDeSIbP4PhA1qcJzJA
2025-06-19 18:54:51,103 - api.rate_limiter - INFO - Initializing Rate Limiter...
2025-06-19 18:54:51,107 - web.web_server - INFO - Initializing Web Server...
2025-06-19 18:54:51,108 - web.web_server - ERROR - Error creating templates: 'charmap' codec can't encode character '\U0001f916' in position 550: character maps to <undefined>
2025-06-19 18:54:51,109 - web.web_server - ERROR - Failed to initialize Web Server: 'charmap' codec can't encode character '\U0001f916' in position 550: character maps to <undefined>
2025-06-19 18:54:51,138 - __main__ - INFO - Starting Advanced Ollama Trading Agent System...
2025-06-19 18:54:51,139 - risk.risk_manager - INFO - Starting Risk Management System...
2025-06-19 18:54:51,140 - risk.risk_monitor - INFO - Starting Real-Time Risk Monitor...
2025-06-19 18:54:51,155 - execution.execution_engine - INFO - Starting Execution Engine...
2025-06-19 18:54:51,157 - execution.paper_trader - INFO - Starting Paper Trader...
2025-06-19 18:54:51,160 - execution.execution_monitor - INFO - Starting Execution Monitor...
2025-06-19 18:54:51,163 - execution.venue_router - INFO - Starting Venue Router...
2025-06-19 18:54:51,163 - execution.trade_lifecycle - INFO - Starting Trade Lifecycle Manager...
2025-06-19 18:54:51,170 - portfolio.portfolio_manager - INFO - Starting Portfolio Manager...
2025-06-19 18:54:51,171 - portfolio.portfolio_manager - ERROR - Failed to start Portfolio Manager: 'AllocationEngine' object has no attribute 'start'
2025-06-19 18:54:51,174 - strategies.strategy_manager - INFO - Starting Strategy Manager...
2025-06-19 18:54:51,181 - database.database_coordinator - INFO - Starting Database Coordinator...
2025-06-19 18:54:51,182 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 18:54:51,184 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 18:54:51,236 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 18:54:51,237 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 18:54:55,247 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:54:55,248 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:54:55,255 - analytics.analytics_engine - INFO - Initializing Advanced Analytics Engine...
2025-06-19 18:54:55,256 - analytics.analytics_engine - ERROR - Error initializing analytics components: name 'MarketAnalytics' is not defined
2025-06-19 18:54:55,257 - analytics.analytics_engine - ERROR - Failed to initialize Advanced Analytics Engine: name 'MarketAnalytics' is not defined
2025-06-19 18:54:55,260 - learning.learning_manager - INFO - Starting Learning Manager...
2025-06-19 18:54:55,277 - api.api_server - INFO - Starting API Server on 0.0.0.0:8000...
2025-06-19 18:54:55,279 - api.websocket_manager - INFO - Starting WebSocket Manager...
2025-06-19 18:54:55,327 - models.ollama_hub - INFO - Starting Ollama Model Hub...
2025-06-19 18:54:55,328 - communication.message_broker - INFO - Starting Message Broker...
2025-06-19 18:54:55,331 - data.market_data_manager - INFO - Starting Market Data Manager...
2025-06-19 18:54:55,334 - agents.agent_manager - INFO - Starting Agent Manager...
2025-06-19 18:54:55,336 - teams.team_manager - INFO - Starting Advanced Team Manager...
2025-06-19 18:54:55,338 - monitoring.system_monitor - INFO - Starting System Monitor...
2025-06-19 18:54:55,342 - analytics.market_analytics - INFO - Starting Market Analytics...
2025-06-19 18:54:55,345 - dashboard.metrics_collector - INFO - Starting Metrics Collector...
2025-06-19 18:54:55,348 - api.api_server - INFO - API application starting up...
2025-06-19 18:54:55,348 - models.model_registry - INFO - Starting Model Registry...
2025-06-19 18:54:55,351 - models.model_deployment - INFO - Starting Model Deployment Manager...
2025-06-19 18:54:55,354 - models.model_performance - INFO - Starting Model Performance Tracker...
2025-06-19 18:55:01,186 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:55:11,189 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:55:21,167 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 18:55:21,213 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:55:28,477 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:55:29,321 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:55:31,220 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:55:41,244 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:55:51,156 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 18:55:51,263 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:56:01,622 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:56:01,623 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:56:03,649 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:56:12,656 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:56:21,150 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 18:56:23,693 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:56:34,713 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:56:36,746 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:56:37,717 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:56:44,694 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:56:51,158 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 18:56:54,708 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:57:04,694 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:57:10,064 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:57:11,835 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:57:14,712 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:57:21,165 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 18:57:24,707 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:57:34,720 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:57:44,140 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:57:44,741 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:57:45,910 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:57:51,879 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 18:57:54,756 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:58:04,753 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:58:14,772 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:58:18,185 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:58:19,955 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:58:21,888 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 18:58:24,974 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:58:34,984 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:58:44,998 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:58:51,891 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 18:58:52,259 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:58:54,030 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:58:55,010 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:59:05,016 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:59:15,041 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:59:21,918 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 18:59:25,053 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:59:26,345 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 18:59:28,111 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 18:59:35,061 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:59:45,076 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 18:59:51,911 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 18:59:55,089 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:00:00,426 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:00:02,200 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:00:05,095 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:00:15,407 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:00:21,913 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:00:25,430 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:00:34,509 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:00:35,444 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:00:36,260 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:00:45,459 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:00:51,910 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:00:55,482 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:00:57,169 - __main__ - INFO - Initializing system components...
2025-06-19 19:00:57,171 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 19:00:57,473 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 19:00:57,492 - models.model_registry - INFO - Discovered 21 new models: ['cogito:32b', 'qwen3:32b', 'deepseek-r1:latest', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'huihui_ai/am-thinking-abliterate:latest', 'granite3.3:8b', 'nemotron-mini:4b', 'phi4-reasoning:plus', 'falcon3:10b', 'hermes3:8b', 'exaone-deep:32b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'magistral:24b', 'huihui_ai/homunculus-abliterated:latest', 'mistral-small:24b', 'gemma3:27b', 'huihui_ai/magistral-abliterated:24b', 'qwen2.5vl:32b', 'marco-o1:7b', 'adityakale/kotakneo:latest', 'command-r:35b']
2025-06-19 19:00:57,498 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 19:00:57,504 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 19:00:57,509 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 19:01:05,498 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:01:08,561 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:01:10,648 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:01:15,503 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:01:21,901 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:01:25,524 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:01:25,884 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 19:01:35,531 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:01:43,798 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:01:44,721 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:01:45,532 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:01:51,901 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:01:55,559 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:01:57,572 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 19:02:05,870 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:02:16,922 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:02:17,037 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:02:18,962 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:02:21,905 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:02:27,959 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:02:33,122 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 19:02:38,994 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:02:50,024 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:02:51,907 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:02:52,061 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:02:53,011 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:03:01,055 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:03:11,050 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:03:11,321 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 19:03:14,775 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 19:03:18,569 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 19:03:21,065 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:03:21,918 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:03:25,413 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:03:27,150 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:03:29,869 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 19:03:31,090 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:03:36,196 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 19:03:41,106 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:03:46,557 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 19:03:46,571 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 19:03:46,579 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 19:03:46,585 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 19:03:46,586 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 19:03:46,587 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 19:03:46,593 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 19:03:46,600 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 19:03:46,601 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 19:03:46,606 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 19:03:46,610 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 19:03:46,615 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 19:03:46,619 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 19:03:46,629 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 19:03:46,634 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 19:03:46,639 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 19:03:46,646 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 19:03:46,652 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 19:03:46,657 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 19:03:46,664 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 19:03:46,665 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 19:03:46,665 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 19:03:46,666 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 19:03:46,666 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 19:03:46,667 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 19:03:46,671 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 19:03:46,675 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 19:03:46,676 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 19:03:46,677 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 19:03:46,681 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 19:03:46,684 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 19:03:46,688 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 19:03:46,688 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 19:03:46,689 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 19:03:46,693 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 19:03:46,694 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 19:03:46,697 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 19:03:46,700 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 19:03:46,701 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 19:03:46,702 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 19:03:46,711 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 19:03:46,712 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 19:03:46,713 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 19:03:46,718 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 19:03:46,718 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 19:03:46,719 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 19:03:46,720 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 19:03:46,720 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 19:03:46,724 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 19:03:46,725 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 19:03:46,729 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 19:03:46,730 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 19:03:46,733 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 19:03:46,734 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 19:03:46,741 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 19:03:46,742 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 19:03:46,743 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 19:03:46,749 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 19:03:46,756 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 19:03:46,763 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 19:03:46,777 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 19:03:46,779 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 19:03:46,784 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 19:03:46,786 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 19:03:46,787 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 19:03:46,794 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 19:03:46,796 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 19:03:46,797 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 19:03:46,804 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 19:03:46,804 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 19:03:46,811 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 19:03:46,813 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 19:03:46,821 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 19:03:46,830 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 19:03:46,831 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 19:03:46,832 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 19:03:46,833 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 19:03:46,839 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 19:03:46,840 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 19:03:46,842 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 19:03:46,843 - learning.strategy_repository - INFO - Loaded 0 strategies from disk
2025-06-19 19:03:46,850 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 19:03:46,854 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 19:03:46,858 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 19:03:46,860 - learning.knowledge_manager - INFO - Loaded 0 knowledge items from disk
2025-06-19 19:03:46,866 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 19:03:46,880 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 19:03:46,885 - __main__ - INFO - Initializing database integration components...
2025-06-19 19:03:46,886 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 19:03:46,886 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 19:03:46,950 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 19:03:46,950 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 19:03:46,955 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 19:03:51,021 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:03:51,022 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:03:51,025 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 19:03:51,116 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:03:51,936 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:03:59,455 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:04:00,231 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 19:04:01,135 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:04:01,228 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:04:02,534 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 19:04:04,832 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:04:06,619 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:04:06,626 - strategies.strategy_manager - INFO - Strategy Manager database coordinator configured
2025-06-19 19:04:06,627 - risk.risk_manager - INFO - Risk Manager database coordinator configured
2025-06-19 19:04:06,627 - execution.execution_engine - INFO - Execution Engine database coordinator configured
2025-06-19 19:04:06,628 - portfolio.portfolio_manager - INFO - Portfolio Manager database coordinator configured
2025-06-19 19:04:06,629 - learning.learning_manager - INFO - Learning Manager database coordinator configured
2025-06-19 19:04:06,631 - __main__ - INFO - Initializing advanced analytics components...
2025-06-19 19:04:06,632 - analytics.analytics_engine - INFO - Initializing Advanced Analytics Engine...
2025-06-19 19:04:06,632 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 19:04:06,649 - analytics.pattern_detector - INFO - Initializing Pattern Detector...
2025-06-19 19:04:06,649 - analytics.pattern_detector - INFO - Initialized 6 pattern detection algorithms
2025-06-19 19:04:06,659 - analytics.real_time_analyzer - INFO - Initializing Real-time Analyzer...
2025-06-19 19:04:06,666 - analytics.predictive_engine - INFO - Initializing Predictive Engine...
2025-06-19 19:04:06,683 - analytics.anomaly_detector - INFO - Initializing Anomaly Detector...
2025-06-19 19:04:06,690 - analytics.sentiment_analyzer - INFO - Initializing Sentiment Analyzer...
2025-06-19 19:04:06,706 - strategies.strategy_manager - INFO - Strategy Manager analytics engine configured
2025-06-19 19:04:06,707 - risk.risk_manager - INFO - Risk Manager analytics engine configured
2025-06-19 19:04:06,708 - execution.execution_engine - INFO - Execution Engine analytics engine configured
2025-06-19 19:04:06,708 - portfolio.portfolio_manager - INFO - Portfolio Manager analytics engine configured
2025-06-19 19:04:06,711 - __main__ - INFO - Initializing API and web interface components...
2025-06-19 19:04:06,712 - api.api_server - INFO - Initializing API Server...
2025-06-19 19:04:06,712 - api.websocket_manager - INFO - Initializing WebSocket Manager...
2025-06-19 19:04:06,722 - api.auth_manager - INFO - Initializing Authentication Manager...
2025-06-19 19:04:06,723 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-06-19 19:04:06,938 - api.auth_manager - INFO - Created user: admin
2025-06-19 19:04:06,939 - api.auth_manager - INFO - Created default admin user
2025-06-19 19:04:06,940 - api.auth_manager - INFO - Created default API key: v8QR-9IH-QGkrsmDG79LhGpeatuv_ACiq1ZM6ynNZWA
2025-06-19 19:04:06,954 - api.rate_limiter - INFO - Initializing Rate Limiter...
2025-06-19 19:04:06,960 - web.web_server - INFO - Initializing Web Server...
2025-06-19 19:04:06,963 - web.web_server - ERROR - Error creating templates: 'charmap' codec can't encode character '\u2705' in position 2947: character maps to <undefined>
2025-06-19 19:04:06,964 - web.web_server - ERROR - Failed to initialize Web Server: 'charmap' codec can't encode character '\u2705' in position 2947: character maps to <undefined>
2025-06-19 19:04:06,992 - __main__ - INFO - Starting Advanced Ollama Trading Agent System...
2025-06-19 19:04:06,993 - risk.risk_manager - INFO - Starting Risk Management System...
2025-06-19 19:04:06,994 - risk.risk_monitor - INFO - Starting Real-Time Risk Monitor...
2025-06-19 19:04:07,009 - execution.execution_engine - INFO - Starting Execution Engine...
2025-06-19 19:04:07,012 - execution.paper_trader - INFO - Starting Paper Trader...
2025-06-19 19:04:07,014 - execution.execution_monitor - INFO - Starting Execution Monitor...
2025-06-19 19:04:07,017 - execution.venue_router - INFO - Starting Venue Router...
2025-06-19 19:04:07,017 - execution.trade_lifecycle - INFO - Starting Trade Lifecycle Manager...
2025-06-19 19:04:07,023 - portfolio.portfolio_manager - INFO - Starting Portfolio Manager...
2025-06-19 19:04:07,025 - portfolio.portfolio_manager - ERROR - Failed to start Portfolio Manager: 'AllocationEngine' object has no attribute 'start'
2025-06-19 19:04:07,027 - strategies.strategy_manager - INFO - Starting Strategy Manager...
2025-06-19 19:04:07,034 - database.database_coordinator - INFO - Starting Database Coordinator...
2025-06-19 19:04:07,034 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 19:04:07,036 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 19:04:07,086 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 19:04:07,087 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 19:04:11,089 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:04:11,090 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:04:11,096 - analytics.analytics_engine - INFO - Starting Advanced Analytics Engine...
2025-06-19 19:04:11,097 - analytics.market_analytics - INFO - Starting Market Analytics...
2025-06-19 19:04:11,099 - analytics.pattern_detector - INFO - Starting Pattern Detector...
2025-06-19 19:04:11,116 - learning.learning_manager - INFO - Starting Learning Manager...
2025-06-19 19:04:11,131 - api.api_server - INFO - Starting API Server on 0.0.0.0:8000...
2025-06-19 19:04:11,132 - api.websocket_manager - INFO - Starting WebSocket Manager...
2025-06-19 19:04:11,135 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:04:11,176 - models.ollama_hub - INFO - Starting Ollama Model Hub...
2025-06-19 19:04:11,177 - communication.message_broker - INFO - Starting Message Broker...
2025-06-19 19:04:11,179 - data.market_data_manager - INFO - Starting Market Data Manager...
2025-06-19 19:04:11,182 - agents.agent_manager - INFO - Starting Agent Manager...
2025-06-19 19:04:11,185 - teams.team_manager - INFO - Starting Advanced Team Manager...
2025-06-19 19:04:11,188 - monitoring.system_monitor - INFO - Starting System Monitor...
2025-06-19 19:04:11,190 - analytics.market_analytics - INFO - Starting Market Analytics...
2025-06-19 19:04:11,236 - dashboard.metrics_collector - INFO - Starting Metrics Collector...
2025-06-19 19:04:11,238 - api.api_server - INFO - API application starting up...
2025-06-19 19:04:11,238 - models.model_registry - INFO - Starting Model Registry...
2025-06-19 19:04:11,242 - models.model_deployment - INFO - Starting Model Deployment Manager...
2025-06-19 19:04:11,244 - models.model_performance - INFO - Starting Model Performance Tracker...
2025-06-19 19:04:11,254 - api.api_server - INFO - API application shutting down...
2025-06-19 19:04:11,257 - __main__ - INFO - Stopping trading system...
2025-06-19 19:04:11,258 - strategies.strategy_manager - INFO - Stopping Strategy Manager...
2025-06-19 19:04:11,272 - execution.execution_engine - INFO - Stopping Execution Engine...
2025-06-19 19:04:11,277 - execution.paper_trader - INFO - Stopping Paper Trader...
2025-06-19 19:04:11,281 - execution.execution_monitor - INFO - Stopping Execution Monitor...
2025-06-19 19:04:11,285 - execution.venue_router - INFO - Stopping Venue Router...
2025-06-19 19:04:11,286 - execution.trade_lifecycle - INFO - Stopping Trade Lifecycle Manager...
2025-06-19 19:04:11,298 - risk.risk_manager - INFO - Stopping Risk Management System...
2025-06-19 19:04:11,299 - risk.risk_monitor - INFO - Stopping Real-Time Risk Monitor...
2025-06-19 19:04:11,328 - api.api_server - INFO - Stopping API Server...
2025-06-19 19:04:11,329 - api.websocket_manager - INFO - Stopping WebSocket Manager...
2025-06-19 19:04:11,334 - api.api_server - ERROR - Error stopping API Server: 'Server' object has no attribute 'servers'
2025-06-19 19:04:11,338 - learning.learning_manager - INFO - Stopping Learning Manager...
2025-06-19 19:04:11,367 - analytics.analytics_engine - INFO - Stopping Advanced Analytics Engine...
2025-06-19 19:04:11,387 - analytics.pattern_detector - INFO - Stopping Pattern Detector...
2025-06-19 19:04:11,394 - analytics.market_analytics - INFO - Stopping Market Analytics...
2025-06-19 19:04:11,417 - database.database_coordinator - INFO - Stopping Database Coordinator...
2025-06-19 19:04:11,442 - monitoring.system_monitor - INFO - Stopping System Monitor...
2025-06-19 19:04:11,446 - teams.team_manager - INFO - Stopping Advanced Team Manager...
2025-06-19 19:04:11,450 - agents.agent_manager - INFO - Stopping Agent Manager...
2025-06-19 19:04:11,454 - data.market_data_manager - INFO - Stopping Market Data Manager...
2025-06-19 19:04:11,459 - communication.message_broker - INFO - Stopping Message Broker...
2025-06-19 19:04:11,462 - models.ollama_hub - INFO - Stopping Ollama Model Hub...
2025-06-19 19:04:11,463 - models.model_performance - INFO - Stopping Model Performance Tracker...
2025-06-19 19:04:11,467 - models.model_deployment - INFO - Stopping Model Deployment Manager...
2025-06-19 19:04:11,471 - models.model_registry - INFO - Stopping Model Registry...
2025-06-19 19:04:11,529 - asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='Task-80' coro=<Server.serve() done, defined at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py:63> exception=SystemExit(1)>
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 160, in startup
    server = await loop.create_server(
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1570, in create_server
    raise OSError(err.errno, msg) from None
OSError: [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8000): only one usage of each socket address (protocol/network address/port) is normally permitted

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\noryonv3\main.py", line 661, in <module>
    main()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1082, in main
    rv = self.invoke(ctx)
         ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\noryonv3\main.py", line 652, in main
    asyncio.run(system.run())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 672, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 639, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1985, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 78, in serve
    await self.startup(sockets=sockets)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 170, in startup
    sys.exit(1)
SystemExit: 1
2025-06-19 19:04:21,147 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:04:21,950 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:04:31,168 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:04:33,524 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:04:35,299 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:04:41,156 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:04:51,443 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:04:51,943 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:05:01,467 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:05:07,609 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:05:09,381 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:05:11,486 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:05:21,482 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:05:21,960 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:05:31,483 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:05:41,512 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:05:41,694 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:05:43,446 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:05:51,527 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:05:51,943 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:06:01,542 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:06:11,544 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:06:15,741 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:06:17,507 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:06:21,547 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:06:21,962 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:06:31,549 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:06:41,951 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:06:49,800 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:06:51,566 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:06:53,015 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:06:53,016 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:07:04,057 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:07:14,076 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:07:23,034 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:07:23,866 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:07:24,080 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:07:26,138 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:07:34,098 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:07:44,099 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:07:53,028 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:07:54,106 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:07:59,258 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:08:00,213 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:08:04,094 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:08:14,116 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:08:23,031 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:08:24,137 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:08:32,527 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:08:34,142 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:08:34,447 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:08:44,165 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:08:53,025 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:08:54,522 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:09:05,564 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:09:07,586 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:09:08,505 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:09:15,589 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:09:23,024 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:09:25,597 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:09:35,618 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:09:40,977 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:09:42,743 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:09:45,622 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:09:53,023 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:09:55,604 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:10:05,619 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:10:15,032 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:10:15,617 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:10:16,803 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:10:23,033 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:10:25,619 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:10:33,006 - __main__ - INFO - Initializing system components...
2025-06-19 19:10:33,007 - models.ollama_hub - INFO - Initializing Ollama Model Hub...
2025-06-19 19:10:33,285 - models.ollama_hub - INFO - [OK] Connected to Ollama server - 21 models available
2025-06-19 19:10:33,285 - models.model_registry - INFO - Initializing Ollama Model Registry...
2025-06-19 19:10:33,293 - models.model_registry - INFO - Discovered 21 new models: ['nemotron-mini:4b', 'hermes3:8b', 'exaone-deep:32b', 'qwen3:32b', 'cogito:32b', 'falcon3:10b', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b', 'granite3.3:8b', 'qwen2.5vl:32b', 'huihui_ai/magistral-abliterated:24b', 'adityakale/kotakneo:latest', 'gemma3:27b', 'marco-o1:7b', 'command-r:35b', 'magistral:24b', 'huihui_ai/homunculus-abliterated:latest', 'huihui_ai/acereason-nemotron-abliterated:14b', 'phi4-reasoning:plus', 'huihui_ai/am-thinking-abliterate:latest', 'mistral-small:24b', 'deepseek-r1:latest']
2025-06-19 19:10:33,295 - models.model_registry - INFO - [OK] Model Registry initialized with 21 models
2025-06-19 19:10:33,295 - models.model_config - INFO - Initializing Model Configuration Store...
2025-06-19 19:10:33,300 - models.model_config - INFO - Loaded configurations for 9 roles
2025-06-19 19:10:33,310 - models.model_deployment - INFO - Initializing Model Deployment Manager...
2025-06-19 19:10:35,614 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:10:45,619 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:10:49,114 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:10:50,885 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:10:51,191 - models.model_deployment - INFO - Pre-deployed exaone-deep:32b for team_leader
2025-06-19 19:10:53,034 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:10:55,884 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:11:05,893 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:11:06,233 - models.model_deployment - INFO - Pre-deployed huihui_ai/magistral-abliterated:24b for market_analyst
2025-06-19 19:11:15,913 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:11:23,058 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:11:23,180 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:11:24,939 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:11:25,926 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:11:35,921 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:11:36,060 - models.model_deployment - INFO - Pre-deployed huihui_ai/am-thinking-abliterate:latest for strategy_developer
2025-06-19 19:11:45,947 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:11:53,048 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:11:55,958 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:11:57,249 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:11:57,299 - models.model_deployment - INFO - Pre-deployed phi4-reasoning:plus for risk_manager
2025-06-19 19:11:59,016 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:12:00,790 - models.model_deployment - INFO - Pre-deployed nemotron-mini:4b for execution_specialist
2025-06-19 19:12:04,613 - models.model_deployment - INFO - Pre-deployed granite3.3:8b for performance_evaluator
2025-06-19 19:12:05,973 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:12:15,996 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:12:16,936 - models.model_deployment - INFO - Pre-deployed qwen2.5vl:32b for visual_analyst
2025-06-19 19:12:23,016 - models.model_deployment - INFO - Pre-deployed deepseek-r1:latest for deep_reasoner
2025-06-19 19:12:23,072 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:12:25,998 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:12:31,326 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:12:32,793 - models.model_deployment - INFO - Pre-deployed goekdenizguelmez/JOSIEFIED-Qwen3:14b for sentiment_analyst
2025-06-19 19:12:32,807 - models.model_performance - INFO - Initializing Model Performance Tracker...
2025-06-19 19:12:32,812 - models.ollama_hub - INFO - [OK] Ollama Model Hub initialized successfully
2025-06-19 19:12:32,813 - __main__ - INFO - [OK] Ollama Model Hub initialized
2025-06-19 19:12:32,813 - communication.message_broker - INFO - Initializing Message Broker...
2025-06-19 19:12:32,817 - __main__ - INFO - [OK] Message Broker initialized
2025-06-19 19:12:32,818 - data.market_data_manager - INFO - Initializing Market Data Manager...
2025-06-19 19:12:32,819 - data.market_data_manager - INFO - Initialized 2 data providers
2025-06-19 19:12:32,820 - data.market_data_manager - INFO - Setup 11 symbols for data collection
2025-06-19 19:12:32,824 - __main__ - INFO - [OK] Market Data Manager initialized
2025-06-19 19:12:32,825 - agents.agent_manager - INFO - Initializing Agent Manager...
2025-06-19 19:12:32,829 - __main__ - INFO - [OK] Agent Manager initialized
2025-06-19 19:12:32,830 - teams.team_manager - INFO - Initializing Advanced Team Manager...
2025-06-19 19:12:32,832 - teams.team_formation_engine - INFO - Initializing Team Formation Engine...
2025-06-19 19:12:32,837 - teams.hierarchical_structures - INFO - Initializing Hierarchical Team Structures...
2025-06-19 19:12:32,843 - teams.decision_protocols - INFO - Initializing Decision Protocols...
2025-06-19 19:12:32,847 - teams.collaboration_framework - INFO - Initializing Collaboration Framework...
2025-06-19 19:12:32,851 - teams.team_performance_evaluation - INFO - Initializing Team Performance Evaluation...
2025-06-19 19:12:32,860 - __main__ - INFO - [OK] Team Manager initialized
2025-06-19 19:12:32,861 - monitoring.system_monitor - INFO - Initializing System Monitor (Phase 1 - Basic)...
2025-06-19 19:12:32,866 - __main__ - INFO - [OK] System Monitor initialized
2025-06-19 19:12:32,867 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 19:12:32,881 - ml.predictive_models - INFO - Initializing Predictive Models...
2025-06-19 19:12:32,895 - ml.reinforcement_learning - INFO - Initializing RL Strategy Optimizer...
2025-06-19 19:12:32,907 - ml.ensemble_methods - INFO - Initializing Ensemble Decision Maker...
2025-06-19 19:12:32,920 - dashboard.metrics_collector - INFO - Initializing Metrics Collector...
2025-06-19 19:12:32,931 - __main__ - INFO - Initializing comprehensive trading components...
2025-06-19 19:12:32,932 - risk.risk_manager - INFO - Initializing Risk Management System...
2025-06-19 19:12:32,934 - risk.position_sizer - INFO - Initializing Position Sizer...
2025-06-19 19:12:32,935 - risk.position_sizer - INFO -   Default method: kelly_criterion
2025-06-19 19:12:32,937 - risk.position_sizer - INFO -   Base size: 0.01
2025-06-19 19:12:32,938 - risk.position_sizer - INFO -   Max size: 0.05
2025-06-19 19:12:32,946 - risk.risk_metrics - INFO - Initializing Risk Metrics Calculator...
2025-06-19 19:12:32,952 - risk.risk_monitor - INFO - Initializing Real-Time Risk Monitor...
2025-06-19 19:12:32,954 - risk.risk_monitor - INFO -   Monitoring interval: 30s
2025-06-19 19:12:32,954 - risk.risk_monitor - INFO -   Alert cooldown: 300s
2025-06-19 19:12:32,961 - risk.compliance_monitor - INFO - Initializing Compliance Monitor...
2025-06-19 19:12:32,967 - risk.stress_tester - INFO - Initializing Stress Tester...
2025-06-19 19:12:32,974 - risk.drawdown_protection - INFO - Initializing Drawdown Protection...
2025-06-19 19:12:32,975 - risk.drawdown_protection - INFO -   Max drawdown threshold: 15.0%
2025-06-19 19:12:32,976 - risk.drawdown_protection - INFO -   Daily loss threshold: 5.0%
2025-06-19 19:12:32,982 - risk.risk_alerts - INFO - Initializing Risk Alert Manager...
2025-06-19 19:12:32,983 - risk.risk_alerts - INFO -   Alert rules: 3
2025-06-19 19:12:32,989 - risk.var_calculator - INFO - Initializing VaR Calculator...
2025-06-19 19:12:32,995 - risk.correlation_monitor - INFO - Initializing Correlation Monitor...
2025-06-19 19:12:32,997 - risk.correlation_monitor - INFO -   Correlation window: 30 days
2025-06-19 19:12:32,998 - risk.correlation_monitor - INFO -   High correlation threshold: 0.8
2025-06-19 19:12:33,010 - execution.execution_engine - INFO - Initializing Execution Engine...
2025-06-19 19:12:33,012 - execution.order_manager - INFO - Initializing Order Manager...
2025-06-19 19:12:33,014 - execution.order_manager - INFO - Loaded 18 orders from persistence
2025-06-19 19:12:33,020 - execution.execution_engine - INFO - Initializing Paper Trading mode
2025-06-19 19:12:33,022 - execution.paper_trader - INFO - Initializing Paper Trader...
2025-06-19 19:12:33,022 - execution.paper_trader - INFO -   Fill probability: 0.95
2025-06-19 19:12:33,023 - execution.paper_trader - INFO -   Latency: 50ms
2025-06-19 19:12:33,024 - execution.paper_trader - INFO -   Slippage: 2 bps
2025-06-19 19:12:33,030 - execution.execution_monitor - INFO - Initializing Execution Monitor...
2025-06-19 19:12:33,031 - execution.execution_monitor - INFO - Execution metrics initialized
2025-06-19 19:12:33,037 - execution.venue_router - INFO - Initializing Venue Router...
2025-06-19 19:12:33,038 - execution.venue_router - INFO - Initialized 4 venues
2025-06-19 19:12:33,043 - execution.trade_lifecycle - INFO - Initializing Trade Lifecycle Manager...
2025-06-19 19:12:33,044 - execution.trade_lifecycle - INFO - Trade lifecycle state tracking initialized
2025-06-19 19:12:33,057 - portfolio.portfolio_manager - INFO - Initializing Portfolio Manager...
2025-06-19 19:12:33,059 - portfolio.portfolio_optimizer - INFO - Initializing Portfolio Optimizer...
2025-06-19 19:12:33,060 - portfolio.black_litterman - INFO - Initializing Black-Litterman Model...
2025-06-19 19:12:33,068 - portfolio.risk_parity - INFO - Initializing Risk Parity Optimizer...
2025-06-19 19:12:33,073 - portfolio.dynamic_allocation - INFO - Initializing Dynamic Asset Allocator...
2025-06-19 19:12:33,077 - portfolio.multi_objective - INFO - Initializing Multi-Objective Optimizer...
2025-06-19 19:12:33,085 - portfolio.position_manager - INFO - Initializing Position Manager...
2025-06-19 19:12:33,087 - portfolio.position_manager - INFO - Loaded 1 positions from persistence
2025-06-19 19:12:33,087 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:12:33,093 - portfolio.performance_tracker - INFO - Initializing Performance Tracker...
2025-06-19 19:12:33,094 - portfolio.performance_tracker - INFO -   Benchmark: SPY
2025-06-19 19:12:33,095 - portfolio.performance_tracker - INFO -   Risk-free rate: 2.00%
2025-06-19 19:12:33,099 - portfolio.rebalancer - INFO - Initializing Portfolio Rebalancer...
2025-06-19 19:12:33,100 - portfolio.rebalancer - INFO -   Max orders per rebalance: 10
2025-06-19 19:12:33,101 - portfolio.rebalancer - INFO -   Order timeout: 300s
2025-06-19 19:12:33,105 - portfolio.allocation_engine - INFO - Initializing Allocation Engine...
2025-06-19 19:12:33,106 - portfolio.allocation_engine - INFO - Initialized 6 allocation strategies
2025-06-19 19:12:33,110 - portfolio.position_manager - INFO - Initial capital set to $100,000.00
2025-06-19 19:12:33,110 - portfolio.portfolio_manager - INFO - Portfolio initialized with $100,000.00
2025-06-19 19:12:33,115 - strategies.strategy_manager - INFO - Initializing Strategy Manager...
2025-06-19 19:12:33,120 - portfolio.portfolio_manager - INFO - Portfolio Manager integration points configured
2025-06-19 19:12:33,120 - strategies.strategy_manager - INFO - Strategy Manager integration points configured
2025-06-19 19:12:33,121 - risk.risk_manager - INFO - Risk Manager integration points configured
2025-06-19 19:12:33,122 - execution.execution_engine - INFO - Execution Engine integration points configured
2025-06-19 19:12:33,127 - __main__ - INFO - Initializing learning and adaptation components...
2025-06-19 19:12:33,128 - learning.learning_manager - INFO - Initializing Learning Manager...
2025-06-19 19:12:33,129 - learning.strategy_repository - INFO - Initializing Strategy Repository...
2025-06-19 19:12:33,130 - learning.strategy_repository - INFO - Loaded 0 strategies from disk
2025-06-19 19:12:33,134 - learning.performance_learner - INFO - Initializing Performance Learner...
2025-06-19 19:12:33,138 - learning.model_tuner - INFO - Initializing Model Tuner...
2025-06-19 19:12:33,142 - learning.knowledge_manager - INFO - Initializing Knowledge Manager...
2025-06-19 19:12:33,144 - learning.knowledge_manager - INFO - Loaded 0 knowledge items from disk
2025-06-19 19:12:33,148 - learning.adaptation_engine - INFO - Initializing Adaptation Engine...
2025-06-19 19:12:33,156 - learning.learning_manager - INFO - Learning Manager integration points configured
2025-06-19 19:12:33,160 - __main__ - INFO - Initializing database integration components...
2025-06-19 19:12:33,161 - database.database_coordinator - INFO - Initializing Database Coordinator...
2025-06-19 19:12:33,161 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 19:12:33,220 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 19:12:33,221 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 19:12:33,227 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 19:12:35,988 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:12:37,301 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:12:37,302 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:12:37,306 - database.clickhouse_manager - INFO - Initializing ClickHouse Manager...
2025-06-19 19:12:46,359 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:12:46,495 - database.migration_manager - INFO - Initializing Migration Manager...
2025-06-19 19:12:48,821 - database.backup_manager - INFO - Initializing Backup Manager...
2025-06-19 19:12:51,119 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:12:52,905 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:12:52,912 - strategies.strategy_manager - INFO - Strategy Manager database coordinator configured
2025-06-19 19:12:52,913 - risk.risk_manager - INFO - Risk Manager database coordinator configured
2025-06-19 19:12:52,913 - execution.execution_engine - INFO - Execution Engine database coordinator configured
2025-06-19 19:12:52,914 - portfolio.portfolio_manager - INFO - Portfolio Manager database coordinator configured
2025-06-19 19:12:52,915 - learning.learning_manager - INFO - Learning Manager database coordinator configured
2025-06-19 19:12:52,917 - __main__ - INFO - Initializing advanced analytics components...
2025-06-19 19:12:52,918 - analytics.analytics_engine - INFO - Initializing Advanced Analytics Engine...
2025-06-19 19:12:52,918 - analytics.market_analytics - INFO - Initializing Market Analytics...
2025-06-19 19:12:52,934 - analytics.pattern_detector - INFO - Initializing Pattern Detector...
2025-06-19 19:12:52,935 - analytics.pattern_detector - INFO - Initialized 6 pattern detection algorithms
2025-06-19 19:12:52,944 - analytics.real_time_analyzer - INFO - Initializing Real-time Analyzer...
2025-06-19 19:12:52,951 - analytics.predictive_engine - INFO - Initializing Predictive Engine...
2025-06-19 19:12:52,959 - analytics.anomaly_detector - INFO - Initializing Anomaly Detector...
2025-06-19 19:12:52,967 - analytics.sentiment_analyzer - INFO - Initializing Sentiment Analyzer...
2025-06-19 19:12:52,985 - strategies.strategy_manager - INFO - Strategy Manager analytics engine configured
2025-06-19 19:12:52,986 - risk.risk_manager - INFO - Risk Manager analytics engine configured
2025-06-19 19:12:52,989 - execution.execution_engine - INFO - Execution Engine analytics engine configured
2025-06-19 19:12:52,990 - portfolio.portfolio_manager - INFO - Portfolio Manager analytics engine configured
2025-06-19 19:12:52,993 - __main__ - INFO - Initializing API and web interface components...
2025-06-19 19:12:52,994 - api.api_server - INFO - Initializing API Server...
2025-06-19 19:12:52,995 - api.websocket_manager - INFO - Initializing WebSocket Manager...
2025-06-19 19:12:53,032 - api.auth_manager - INFO - Initializing Authentication Manager...
2025-06-19 19:12:53,074 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:12:53,034 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-06-19 19:12:53,260 - api.auth_manager - INFO - Created user: admin
2025-06-19 19:12:53,262 - api.auth_manager - INFO - Created default admin user
2025-06-19 19:12:53,263 - api.auth_manager - INFO - Created default API key: 7CTdcepihFT_9mVylYIa1s1hdt__sqbnLr2csIcZkcw
2025-06-19 19:12:53,277 - api.rate_limiter - INFO - Initializing Rate Limiter...
2025-06-19 19:12:53,282 - web.web_server - INFO - Initializing Web Server...
2025-06-19 19:12:53,313 - __main__ - INFO - Starting Advanced Ollama Trading Agent System...
2025-06-19 19:12:53,315 - risk.risk_manager - INFO - Starting Risk Management System...
2025-06-19 19:12:53,315 - risk.risk_monitor - INFO - Starting Real-Time Risk Monitor...
2025-06-19 19:12:53,331 - execution.execution_engine - INFO - Starting Execution Engine...
2025-06-19 19:12:53,333 - execution.paper_trader - INFO - Starting Paper Trader...
2025-06-19 19:12:53,336 - execution.execution_monitor - INFO - Starting Execution Monitor...
2025-06-19 19:12:53,341 - execution.venue_router - INFO - Starting Venue Router...
2025-06-19 19:12:53,342 - execution.trade_lifecycle - INFO - Starting Trade Lifecycle Manager...
2025-06-19 19:12:53,349 - portfolio.portfolio_manager - INFO - Starting Portfolio Manager...
2025-06-19 19:12:53,350 - portfolio.portfolio_manager - ERROR - Failed to start Portfolio Manager: 'AllocationEngine' object has no attribute 'start'
2025-06-19 19:12:53,352 - strategies.strategy_manager - INFO - Starting Strategy Manager...
2025-06-19 19:12:53,362 - database.database_coordinator - INFO - Starting Database Coordinator...
2025-06-19 19:12:53,363 - database.postgres_manager - INFO - Initializing PostgreSQL Manager...
2025-06-19 19:12:53,364 - database.redis_manager - INFO - Initializing Redis Manager...
2025-06-19 19:12:53,428 - database.postgres_manager - ERROR - Failed to create PostgreSQL connection pool: password authentication failed for user "trading_user"
2025-06-19 19:12:53,431 - database.postgres_manager - ERROR - Failed to initialize PostgreSQL Manager: password authentication failed for user "trading_user"
2025-06-19 19:12:57,396 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:12:57,421 - database.redis_manager - ERROR - Redis connection test failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:12:57,422 - database.redis_manager - ERROR - Failed to initialize Redis Manager: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:12:57,427 - analytics.analytics_engine - INFO - Starting Advanced Analytics Engine...
2025-06-19 19:12:57,428 - analytics.market_analytics - INFO - Starting Market Analytics...
2025-06-19 19:12:57,430 - analytics.pattern_detector - INFO - Starting Pattern Detector...
2025-06-19 19:12:57,449 - learning.learning_manager - INFO - Starting Learning Manager...
2025-06-19 19:12:57,464 - api.api_server - INFO - Starting API Server on 0.0.0.0:8001...
2025-06-19 19:12:57,465 - api.websocket_manager - INFO - Starting WebSocket Manager...
2025-06-19 19:12:57,599 - models.ollama_hub - INFO - Starting Ollama Model Hub...
2025-06-19 19:12:57,599 - communication.message_broker - INFO - Starting Message Broker...
2025-06-19 19:12:57,602 - data.market_data_manager - INFO - Starting Market Data Manager...
2025-06-19 19:12:57,606 - agents.agent_manager - INFO - Starting Agent Manager...
2025-06-19 19:12:57,608 - teams.team_manager - INFO - Starting Advanced Team Manager...
2025-06-19 19:12:57,611 - monitoring.system_monitor - INFO - Starting System Monitor...
2025-06-19 19:12:57,613 - analytics.market_analytics - INFO - Starting Market Analytics...
2025-06-19 19:12:57,616 - dashboard.metrics_collector - INFO - Starting Metrics Collector...
2025-06-19 19:12:57,618 - api.api_server - INFO - API application starting up...
2025-06-19 19:12:57,619 - models.model_registry - INFO - Starting Model Registry...
2025-06-19 19:12:57,619 - models.model_registry - INFO - [OK] Model Registry started
2025-06-19 19:12:57,621 - models.model_deployment - INFO - Starting Model Deployment Manager...
2025-06-19 19:12:57,624 - models.model_performance - INFO - Starting Model Performance Tracker...
2025-06-19 19:12:57,627 - models.ollama_hub - INFO - [OK] Ollama Model Hub started
2025-06-19 19:13:05,384 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:13:07,137 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:13:08,450 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:13:18,456 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:13:23,080 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:13:23,370 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:13:28,470 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:13:30,744 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:13:31,508 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:13:38,495 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:13:39,453 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:13:41,614 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:13:48,486 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:13:53,085 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:13:53,346 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:13:58,484 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:14:03,860 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:14:05,891 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:14:08,509 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:14:14,745 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:14:15,689 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:14:18,506 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:14:23,102 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:14:23,363 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:14:28,525 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:14:38,534 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:14:39,028 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:14:39,982 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:14:47,976 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:14:48,527 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:14:49,925 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:14:53,095 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:14:53,357 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:14:58,949 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:15:09,977 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:15:12,388 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:15:14,163 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:15:19,995 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:15:23,062 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:15:23,109 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:15:23,370 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:15:24,020 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:15:30,005 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:15:40,016 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:15:46,458 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:15:48,234 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:15:50,020 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:15:53,112 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:15:54,312 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:15:56,547 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:15:58,296 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:16:00,042 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:16:10,046 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:16:20,069 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:16:20,524 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:16:22,308 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:16:23,106 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:16:24,336 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:16:30,068 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:16:30,605 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:16:32,372 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:16:40,079 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:16:50,106 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:16:53,108 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:16:54,342 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:16:54,620 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:16:56,384 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:17:00,513 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:17:04,652 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:17:06,419 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:17:11,554 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:17:21,568 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:17:23,122 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:17:24,345 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:17:28,689 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:17:30,457 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:17:31,578 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:17:38,700 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:17:40,470 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:17:41,605 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:17:51,619 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:17:53,129 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:17:54,358 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:18:01,622 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:18:02,760 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:18:04,514 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:18:11,634 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:18:12,787 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:18:14,558 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:18:21,655 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:18:23,146 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:18:24,361 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:18:31,663 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:18:36,826 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:18:38,573 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:18:41,685 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:18:46,877 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:18:48,630 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:18:51,676 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:18:53,143 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:18:54,379 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:19:01,966 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:19:10,880 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:19:11,971 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:19:13,069 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:19:20,934 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:19:21,992 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:19:22,696 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:19:24,143 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:19:24,376 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:19:32,012 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:19:42,020 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:19:46,361 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:19:47,136 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:19:52,040 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:19:54,167 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:19:54,384 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:19:54,989 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:19:57,301 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:20:02,025 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:20:12,042 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:20:19,533 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:20:21,566 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:20:22,054 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:20:24,179 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:20:24,393 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:20:30,426 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:20:31,395 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:20:32,071 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:20:42,081 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:20:52,506 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:20:54,192 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:20:54,409 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:20:54,700 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:20:55,624 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:21:02,508 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:21:03,696 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:21:05,559 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:21:12,529 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:21:22,544 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:21:24,207 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:21:24,406 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:21:28,096 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:21:29,863 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:21:32,556 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:21:38,667 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:21:39,634 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:21:42,573 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:21:52,587 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:21:54,220 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:21:54,404 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:22:02,134 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:22:02,580 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:22:03,918 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:22:12,057 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:22:12,597 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:22:13,814 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:22:22,614 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:22:24,227 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:22:24,411 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:22:32,611 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:22:36,217 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:22:37,988 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:22:42,914 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:22:46,130 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:22:47,901 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:22:52,917 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:22:54,233 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:22:54,434 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:23:02,898 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:23:10,263 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:23:12,044 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:23:12,915 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:23:20,170 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:23:21,944 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:23:22,926 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:23:24,246 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:23:24,448 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:23:32,950 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:23:42,962 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:23:44,338 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:23:46,121 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:23:52,965 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:23:54,240 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:23:54,241 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:23:54,455 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:23:56,021 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:24:02,972 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:24:12,986 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:24:18,408 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:24:20,161 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:24:23,000 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:24:24,248 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:24:24,480 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:24:28,312 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:24:30,104 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:24:33,564 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:24:43,582 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:24:52,469 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:24:53,602 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:24:54,228 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:24:54,259 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:24:54,488 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:25:02,414 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:25:03,610 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:25:04,160 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:25:13,625 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:25:23,651 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:25:24,262 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:25:24,508 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:25:26,541 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:25:29,130 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:25:33,661 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:25:36,457 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:25:38,223 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:25:43,686 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:25:53,703 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:25:54,267 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:25:54,529 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:26:02,321 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:26:03,198 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:26:03,694 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:26:10,521 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:26:13,041 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:26:13,714 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:26:24,103 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:26:24,290 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:26:24,536 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:26:35,146 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:26:35,528 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:26:37,534 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:26:46,194 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:26:46,197 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:26:47,083 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:26:54,295 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:26:54,525 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:26:57,250 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:27:08,312 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:27:10,783 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:27:11,598 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:27:18,341 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:27:19,412 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:27:21,416 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:27:24,307 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:27:24,520 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:27:28,352 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:27:38,362 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:27:44,256 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:27:46,028 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:27:48,379 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:27:54,312 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:27:54,542 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:27:54,588 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:27:55,481 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:27:58,402 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:28:08,406 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:28:18,327 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:28:18,406 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:28:20,097 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:28:24,340 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:28:24,554 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:28:27,943 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:28:28,422 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:28:29,710 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:28:38,446 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:28:48,467 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:28:52,404 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:28:54,195 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:28:54,350 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:28:54,550 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:28:58,797 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:29:02,006 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:29:03,793 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:29:08,817 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:29:18,842 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:29:24,358 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:29:24,572 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:29:26,509 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:29:28,259 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:29:28,873 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:29:36,082 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:29:37,886 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:29:38,864 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:29:48,875 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:29:54,389 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:29:54,589 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:29:58,892 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:30:00,569 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:30:02,335 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:30:08,914 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:30:10,185 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:30:11,955 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:30:18,925 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:30:24,395 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:30:24,594 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:30:28,946 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:30:34,651 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:30:36,405 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:30:38,964 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:30:44,266 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:30:46,033 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:30:49,080 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:30:54,418 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:30:54,664 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:31:00,128 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:31:08,679 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:31:10,152 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:31:10,444 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:31:18,328 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:31:20,099 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:31:20,160 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:31:24,419 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:31:24,683 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:31:30,150 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:31:40,169 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:31:42,754 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:31:44,803 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:31:50,154 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:31:52,391 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:31:54,173 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:31:54,697 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:31:55,305 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:32:00,184 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:32:10,193 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:32:17,845 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:32:19,870 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:32:20,207 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:32:24,706 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:32:25,319 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:32:26,491 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:32:28,428 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:32:30,222 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:32:40,246 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:32:50,526 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:32:53,069 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:32:53,948 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:32:54,718 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:32:55,333 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:33:01,540 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:33:01,542 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:33:02,488 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:33:12,559 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:33:22,573 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:33:24,732 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:33:25,335 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:33:26,384 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:33:28,161 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:33:32,575 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:33:34,815 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:33:36,679 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:33:42,586 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:33:52,607 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:33:54,746 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:33:55,343 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:34:00,458 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:34:02,246 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:34:02,615 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:34:09,873 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:34:10,751 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:34:12,628 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:34:22,635 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:34:24,749 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:34:25,372 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:34:32,643 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:34:34,547 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:34:36,310 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:34:42,652 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:34:43,344 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:34:45,110 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:34:52,669 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:34:54,766 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:34:55,381 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:35:03,118 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:35:08,602 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:35:10,368 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:35:13,134 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:35:17,410 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:35:19,175 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:35:23,148 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:35:25,397 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:35:25,599 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:35:33,156 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:35:42,676 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:35:43,179 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:35:44,441 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:35:51,469 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:35:53,189 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:35:53,219 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:35:55,407 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:35:55,620 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:36:03,213 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:36:13,225 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:36:16,736 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:36:18,505 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:36:23,235 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:36:25,392 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:36:25,499 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:36:25,637 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:36:27,276 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:36:33,252 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:36:43,275 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:36:50,798 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:36:52,556 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:36:53,517 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:36:55,414 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:36:55,644 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:36:59,570 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:37:01,349 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:37:04,582 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:37:15,643 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:37:24,866 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:37:25,401 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:37:25,662 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:37:26,703 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:37:27,070 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:37:33,643 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:37:35,401 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:37:36,726 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:37:46,737 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:37:55,409 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:37:55,683 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:37:56,738 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:38:00,292 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:38:01,119 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:38:06,736 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:38:07,734 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:38:09,463 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:38:16,761 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:38:25,407 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:38:25,698 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:38:26,764 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:38:33,458 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:38:35,475 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:38:36,795 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:38:41,724 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:38:44,169 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:38:46,801 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:38:55,428 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:38:55,702 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:38:56,819 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:39:06,836 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:39:08,607 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:39:09,519 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:39:17,285 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:39:17,288 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:39:18,195 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:39:25,433 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:39:25,724 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:39:27,284 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:39:37,300 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:39:42,045 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:39:43,788 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:39:47,310 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:39:50,485 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:39:52,462 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:39:55,443 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:39:55,749 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:39:57,320 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:40:07,327 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:40:16,089 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:40:17,344 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:40:17,850 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:40:25,451 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:40:25,558 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:40:25,741 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:40:26,520 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:40:27,359 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:40:37,388 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:40:47,399 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:40:50,144 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:40:51,903 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:40:55,452 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:40:55,728 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:40:57,416 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:40:58,952 - models.model_instance - ERROR - Model deepseek-r1:latest error for system: Request failed: 
2025-06-19 19:40:58,953 - models.model_deployment - WARNING - Unhealthy model instance: system_deep_reasoner_deepseek-r1:latest - Request failed: 
2025-06-19 19:40:58,967 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:41:00,732 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:41:07,755 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:41:17,776 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:41:24,210 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:41:25,455 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:41:25,745 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:41:25,958 - models.model_instance - ERROR - Model goekdenizguelmez/JOSIEFIED-Qwen3:14b error for system: Request failed: 
2025-06-19 19:41:25,960 - models.model_deployment - WARNING - Unhealthy model instance: system_sentiment_analyst_goekdenizguelmez/JOSIEFIED-Qwen3:14b - Request failed: 
2025-06-19 19:41:25,962 - models.model_deployment - WARNING - No fallback available for unhealthy instance system_sentiment_analyst_goekdenizguelmez/JOSIEFIED-Qwen3:14b
2025-06-19 19:41:25,973 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:41:27,610 - models.model_deployment - WARNING - No fallback available for unhealthy instance system_deep_reasoner_deepseek-r1:latest
2025-06-19 19:41:27,782 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:41:33,036 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:41:34,807 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:41:37,811 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:41:47,811 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:41:55,453 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:41:55,775 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:41:57,804 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:41:58,280 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:42:00,049 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:42:07,125 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:42:07,822 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:42:08,893 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:42:17,832 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:42:25,467 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:42:25,790 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:42:27,852 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:42:30,824 - api.middleware - INFO - Request 9cd22247-71f0-4d6e-a5f6-2015ed76b5ae: GET /health from 127.0.0.1 (Python/3.12 aiohttp/3.12.13)
2025-06-19 19:42:30,828 - api.middleware - INFO - Response 9cd22247-71f0-4d6e-a5f6-2015ed76b5ae: 200 (0.004s)
2025-06-19 19:42:30,832 - api.middleware - INFO - Request 3f928cca-7669-4841-8522-5c15c42d32d2: GET /api/v1/system/status from 127.0.0.1 (Python/3.12 aiohttp/3.12.13)
2025-06-19 19:42:30,833 - api.middleware - INFO - Response 3f928cca-7669-4841-8522-5c15c42d32d2: 403 (0.001s)
2025-06-19 19:42:31,100 - api.middleware - INFO - Request 857a3419-2800-4c77-b702-21a802392ea7: GET /api/v1/models from 127.0.0.1 (Python/3.12 aiohttp/3.12.13)
2025-06-19 19:42:31,111 - api.middleware - INFO - Response 857a3419-2800-4c77-b702-21a802392ea7: 404 (0.011s)
2025-06-19 19:42:31,114 - api.middleware - INFO - Request 7d6aebea-ad08-448d-a133-ec1b9be75d6b: GET /api/v1/agents from 127.0.0.1 (Python/3.12 aiohttp/3.12.13)
2025-06-19 19:42:31,115 - api.middleware - INFO - Response 7d6aebea-ad08-448d-a133-ec1b9be75d6b: 307 (0.001s)
2025-06-19 19:42:31,116 - api.middleware - INFO - Request 508380b4-7347-4bbe-882e-10faa1aff55e: GET /api/v1/agents/ from 127.0.0.1 (Python/3.12 aiohttp/3.12.13)
2025-06-19 19:42:31,118 - api.middleware - INFO - Response 508380b4-7347-4bbe-882e-10faa1aff55e: 403 (0.002s)
2025-06-19 19:42:31,378 - api.middleware - INFO - Request 2f50564d-15cf-48e7-ac27-52c136c6e6d2: GET /api/v1/strategies from 127.0.0.1 (Python/3.12 aiohttp/3.12.13)
2025-06-19 19:42:31,379 - api.middleware - INFO - Response 2f50564d-15cf-48e7-ac27-52c136c6e6d2: 307 (0.002s)
2025-06-19 19:42:31,381 - api.middleware - INFO - Request be1691a2-f7ec-4bb6-9531-556f760af7c5: GET /api/v1/strategies/ from 127.0.0.1 (Python/3.12 aiohttp/3.12.13)
2025-06-19 19:42:31,382 - api.middleware - INFO - Response be1691a2-f7ec-4bb6-9531-556f760af7c5: 403 (0.001s)
2025-06-19 19:42:32,344 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:42:34,119 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:42:37,873 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:42:41,193 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:42:42,987 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:42:47,879 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:42:55,472 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:42:55,797 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:42:58,188 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:43:06,384 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:43:08,182 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:43:09,248 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:43:15,315 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:43:17,080 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:43:19,243 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:43:25,489 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:43:25,813 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:43:29,244 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:43:39,239 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:43:40,486 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:43:42,636 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:43:49,251 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:43:49,406 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:43:51,181 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:43:55,509 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:43:55,834 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:43:59,260 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:44:09,264 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:44:15,712 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:44:17,745 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:44:19,281 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:44:23,473 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:44:25,244 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:44:26,452 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:44:26,728 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:44:29,306 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:44:39,321 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:44:49,335 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:44:50,824 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:44:51,811 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:44:56,453 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:44:56,749 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:44:57,555 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:44:59,563 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:44:59,565 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:45:10,630 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:45:20,654 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:45:24,206 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:45:25,974 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:45:26,471 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:45:26,763 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:45:30,657 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:45:32,735 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:45:33,661 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:45:40,676 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:45:50,679 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:45:56,493 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:45:56,769 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:45:58,275 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:46:00,026 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:46:00,687 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:46:05,956 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:46:07,874 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:46:10,705 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:46:20,724 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:46:26,506 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:46:26,781 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:46:30,738 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:46:32,336 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:46:34,090 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:46:40,731 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:46:41,210 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:46:42,984 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:46:50,726 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:46:56,519 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:46:56,811 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:47:01,031 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:47:06,393 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:47:08,163 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:47:11,050 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:47:15,285 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:47:17,061 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:47:21,056 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:47:26,540 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:47:26,830 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:47:31,054 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:47:40,459 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:47:41,073 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:47:42,233 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:47:49,358 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:47:51,079 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:47:51,109 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:47:56,553 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:47:56,844 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:48:01,107 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:48:11,111 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:48:14,535 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:48:16,292 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:48:21,096 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:48:23,385 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:48:25,161 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:48:26,565 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:48:26,872 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:48:31,118 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:48:41,114 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:48:48,579 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:48:50,360 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:48:51,639 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:48:56,584 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:48:56,877 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:48:57,457 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:48:59,204 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:49:02,666 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:49:13,736 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:49:22,688 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:49:23,759 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:49:24,869 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:49:26,590 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:49:26,897 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:49:31,521 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:49:33,287 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:49:33,792 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:49:43,797 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:49:53,808 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:49:56,596 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:49:56,905 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:49:58,003 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:49:58,935 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:50:03,826 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:50:05,572 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:50:07,345 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:50:13,839 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:50:23,842 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:50:26,617 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:50:26,912 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:50:31,218 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:50:33,201 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:50:33,850 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:50:39,657 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:50:42,215 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:50:43,861 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:50:53,878 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:50:56,634 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:50:56,927 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:51:04,266 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:51:06,309 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:51:07,275 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:51:15,320 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:51:15,321 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:51:16,265 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:51:26,362 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:51:26,650 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:51:26,942 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:51:36,365 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:51:39,678 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:51:41,453 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:51:46,387 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:51:48,562 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:51:50,488 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:51:56,391 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:51:56,668 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:51:56,959 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:52:06,413 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:52:13,765 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:52:15,558 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:52:16,404 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:52:23,643 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:52:24,553 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:52:26,420 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:52:26,667 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:52:26,961 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:52:36,423 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:52:46,443 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:52:47,856 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:52:49,622 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:52:56,453 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:52:56,670 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:52:56,979 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:52:57,057 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:52:58,833 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:53:06,463 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:53:16,851 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:53:21,896 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:53:23,664 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:53:26,678 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:53:26,860 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:53:27,866 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:53:31,141 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:53:32,900 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:53:36,871 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:53:46,875 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:53:55,956 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:53:56,681 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:53:56,897 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:53:57,724 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:53:57,877 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:54:05,209 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:54:06,917 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:54:06,963 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:54:16,893 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:54:26,680 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:54:26,896 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:54:27,899 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:54:30,021 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:54:31,804 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:54:36,909 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:54:39,281 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:54:41,037 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:54:46,931 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:54:56,686 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:54:56,932 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:54:57,919 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:55:04,114 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:55:05,902 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:55:07,302 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:55:13,348 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:55:15,121 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:55:17,321 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:55:26,700 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:55:27,330 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:55:27,934 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:55:37,351 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:55:38,224 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:55:40,485 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:55:47,359 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:55:47,390 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:55:49,151 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:55:56,706 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:55:57,379 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:55:57,952 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:56:07,402 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:56:13,538 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:56:15,576 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:56:17,418 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:56:21,462 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:56:23,214 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:56:26,719 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:56:27,419 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:56:27,953 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:56:37,398 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:56:47,409 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:56:48,729 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:56:49,638 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:56:55,526 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:56:57,759 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:56:57,760 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:56:57,761 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:56:57,973 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:57:08,815 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:57:19,862 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:57:22,167 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:57:23,947 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:57:27,751 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:57:27,984 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:57:29,874 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:57:30,897 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:57:31,827 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:57:39,883 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:57:49,876 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:57:56,273 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:57:57,775 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:57:58,007 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:57:58,022 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:57:59,902 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:58:04,156 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:58:06,024 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:58:09,899 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:58:19,920 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:58:27,770 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:58:28,018 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:58:29,912 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:58:30,329 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:58:32,079 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:58:39,119 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:58:39,921 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:58:40,106 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:58:49,949 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:58:57,792 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:58:58,039 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:58:59,945 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:59:04,408 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:59:06,188 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:59:10,194 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:59:12,483 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:59:14,256 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:59:20,206 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:59:27,771 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:59:28,048 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:59:30,226 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:59:38,493 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:59:40,236 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:59:40,266 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:59:46,567 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 19:59:48,322 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 19:59:50,255 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 19:59:57,773 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 19:59:58,065 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:00:00,279 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:00:10,294 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:00:12,590 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:00:14,357 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:00:20,308 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:00:20,628 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:00:22,404 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:00:27,771 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:00:28,078 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:00:30,325 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:00:40,340 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:00:46,661 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:00:48,453 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:00:50,347 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:00:54,703 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:00:56,466 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:00:57,789 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:00:58,099 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:01:00,611 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:01:10,633 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:01:20,648 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:01:20,740 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:01:22,738 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:01:27,785 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:01:28,095 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:01:28,776 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:01:30,559 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:01:30,669 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:01:40,676 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:01:50,689 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:01:55,882 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:01:56,816 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:01:57,797 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:01:58,118 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:02:00,712 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:02:02,861 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:02:04,628 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:02:10,725 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:02:20,733 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:02:27,820 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:02:29,028 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:02:29,113 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:02:30,728 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:02:31,064 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:02:36,938 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:02:38,720 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:02:40,735 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:02:51,131 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:02:57,841 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:02:59,037 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:03:02,187 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:03:04,213 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:03:05,151 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:03:10,999 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:03:13,236 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:03:13,237 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:03:24,286 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:03:27,848 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:03:29,035 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:03:35,331 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:03:37,642 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:03:39,394 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:03:46,348 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:03:46,349 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:03:47,305 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:03:57,433 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:03:57,865 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:03:59,025 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:04:07,438 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:04:11,705 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:04:13,474 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:04:17,452 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:04:19,603 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:04:21,614 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:04:27,481 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:04:27,881 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:04:29,042 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:04:37,492 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:04:45,751 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:04:47,499 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:04:47,545 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:04:54,728 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:04:55,667 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:04:57,526 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:04:57,878 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:04:59,045 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:05:07,530 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:05:17,559 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:05:19,812 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:05:21,578 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:05:27,577 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:05:27,898 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:05:28,110 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:05:29,058 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:05:29,867 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:05:37,592 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:05:47,907 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:05:53,864 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:05:55,630 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:05:57,912 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:05:57,914 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:05:59,062 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:06:02,169 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:06:03,915 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:06:07,904 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:06:17,919 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:06:27,926 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:06:27,927 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:06:27,942 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:06:29,091 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:06:29,690 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:06:36,204 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:06:37,947 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:06:37,978 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:06:47,964 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:06:57,936 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:06:57,952 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:06:59,097 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:07:01,970 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:07:03,742 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:07:07,980 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:07:10,265 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:07:12,025 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:07:17,989 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:07:27,949 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:07:27,995 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:07:29,098 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:07:36,026 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:07:38,192 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:07:38,258 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:07:44,314 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:07:46,056 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:07:48,272 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:07:57,942 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:07:58,266 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:07:59,122 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:08:08,289 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:08:11,315 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:08:12,272 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:08:18,304 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:08:18,350 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:08:20,116 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:08:27,955 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:08:28,323 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:08:29,105 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:08:38,326 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:08:44,547 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:08:46,491 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:08:48,329 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:08:52,420 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:08:54,176 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:08:57,972 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:08:58,355 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:08:59,120 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:09:08,368 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:09:18,374 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:09:19,583 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:09:20,519 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:09:26,474 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:09:28,623 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:09:28,625 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:09:28,626 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:09:29,130 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:09:39,644 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:09:50,705 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:09:52,981 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:09:54,740 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:09:58,635 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:09:59,141 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:10:01,770 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:10:01,773 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:10:02,676 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:10:11,771 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:10:18,953 - models.model_instance - ERROR - Model goekdenizguelmez/JOSIEFIED-Qwen3:14b error for system: Request failed: 
2025-06-19 20:10:18,955 - models.model_deployment - WARNING - Unhealthy model instance: system_sentiment_analyst_goekdenizguelmez/JOSIEFIED-Qwen3:14b - Request failed: 
2025-06-19 20:10:18,957 - models.model_deployment - WARNING - No fallback available for unhealthy instance system_sentiment_analyst_goekdenizguelmez/JOSIEFIED-Qwen3:14b
2025-06-19 20:10:19,028 - models.model_instance - ERROR - Model goekdenizguelmez/JOSIEFIED-Qwen3:14b error for system: HTTP 500: {"error":"unexpected server status: llm server loading model"}
2025-06-19 20:10:19,028 - models.model_deployment - WARNING - Unhealthy model instance: system_sentiment_analyst_goekdenizguelmez/JOSIEFIED-Qwen3:14b - HTTP 500: {"error":"unexpected server status: llm server loading model"}
2025-06-19 20:10:19,030 - models.model_deployment - WARNING - No fallback available for unhealthy instance system_sentiment_analyst_goekdenizguelmez/JOSIEFIED-Qwen3:14b
2025-06-19 20:10:21,784 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:10:27,068 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:10:28,639 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:10:28,823 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:10:29,165 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:10:31,796 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:10:34,971 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:10:36,908 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:10:41,818 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:10:51,828 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:10:58,653 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:10:59,160 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:11:01,127 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:11:01,845 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:11:02,890 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:11:10,045 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:11:11,001 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:11:11,861 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:11:21,881 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:11:28,665 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:11:30,073 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:11:31,890 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:11:35,227 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:11:36,994 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:11:41,895 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:11:43,535 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:11:45,306 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:11:52,272 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:11:58,680 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:12:00,082 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:12:03,316 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:12:09,276 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:12:11,045 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:12:14,347 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:12:17,625 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:12:19,401 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:12:24,360 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:12:28,700 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:12:30,101 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%
2025-06-19 20:12:34,389 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:12:43,357 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:12:44,378 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:12:45,151 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:12:51,709 - database.clickhouse_manager - ERROR - ClickHouse health check failed: Cannot connect to host localhost:8123 ssl:default [The remote computer refused the network connection]
2025-06-19 20:12:53,486 - database.redis_manager - ERROR - Redis health check failed: Error 22 connecting to localhost:6379. 22.
2025-06-19 20:12:54,392 - execution.execution_engine - ERROR - Error updating execution metrics: 'ExecutionMonitor' object has no attribute 'update_metrics'
2025-06-19 20:12:58,695 - execution.execution_monitor - WARNING - Low fill rate alert: 0.00% < 80.00%

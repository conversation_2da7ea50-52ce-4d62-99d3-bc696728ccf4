"""
Authentication Manager - JWT-based authentication and authorization
"""

import asyncio
import logging
import time
import hashlib
import secrets
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
import jwt
from passlib.context import CryptContext
from fastapi import HTT<PERSON>Exception, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

logger = logging.getLogger(__name__)


@dataclass
class User:
    """User data structure"""
    user_id: str
    username: str
    email: str
    password_hash: str
    roles: Set[str]
    permissions: Set[str]
    created_at: datetime
    last_login: Optional[datetime]
    is_active: bool
    metadata: Dict[str, Any]


@dataclass
class Session:
    """Session data structure"""
    session_id: str
    user_id: str
    token: str
    created_at: datetime
    expires_at: datetime
    last_activity: datetime
    ip_address: str
    user_agent: str


class AuthManager:
    """
    JWT-based authentication and authorization manager.
    
    Features:
    - JWT token generation and validation
    - User authentication and session management
    - Role-based access control (RBAC)
    - Permission-based authorization
    - Password hashing and verification
    - Session tracking and management
    - API key authentication
    - Rate limiting for authentication attempts
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.auth_config = config.get('authentication', {})
        
        # JWT configuration
        self.secret_key = self.auth_config.get('secret_key', secrets.token_urlsafe(32))
        self.algorithm = self.auth_config.get('algorithm', 'HS256')
        self.token_expiry = self.auth_config.get('token_expiry', 3600)  # 1 hour
        self.refresh_token_expiry = self.auth_config.get('refresh_token_expiry', 604800)  # 7 days
        
        # Password hashing
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # Security configuration
        self.max_login_attempts = self.auth_config.get('max_login_attempts', 5)
        self.lockout_duration = self.auth_config.get('lockout_duration', 900)  # 15 minutes
        
        # Data storage
        self.users: Dict[str, User] = {}
        self.sessions: Dict[str, Session] = {}
        self.api_keys: Dict[str, Dict[str, Any]] = {}
        self.login_attempts: Dict[str, List[float]] = {}
        
        # Security
        self.security = HTTPBearer()
        
        # Default roles and permissions
        self.default_roles = {
            'admin': {
                'system:read', 'system:write', 'system:admin',
                'agents:read', 'agents:write', 'agents:admin',
                'strategies:read', 'strategies:write', 'strategies:admin',
                'portfolio:read', 'portfolio:write', 'portfolio:admin',
                'analytics:read', 'analytics:write', 'analytics:admin',
                'risk:read', 'risk:write', 'risk:admin',
                'execution:read', 'execution:write', 'execution:admin',
                'market_data:read', 'market_data:write',
                'learning:read', 'learning:write', 'learning:admin',
                'database:read', 'database:write', 'database:admin'
            },
            'trader': {
                'system:read',
                'agents:read', 'agents:write',
                'strategies:read', 'strategies:write',
                'portfolio:read', 'portfolio:write',
                'analytics:read',
                'risk:read',
                'execution:read', 'execution:write',
                'market_data:read',
                'learning:read'
            },
            'analyst': {
                'system:read',
                'agents:read',
                'strategies:read',
                'portfolio:read',
                'analytics:read', 'analytics:write',
                'risk:read',
                'execution:read',
                'market_data:read',
                'learning:read'
            },
            'viewer': {
                'system:read',
                'agents:read',
                'strategies:read',
                'portfolio:read',
                'analytics:read',
                'risk:read',
                'execution:read',
                'market_data:read'
            }
        }
        
        # State
        self.initialized = False
    
    async def initialize(self) -> bool:
        """Initialize authentication manager"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Authentication Manager...")
            
            # Create default admin user if none exists
            await self._create_default_users()
            
            # Setup API keys
            await self._setup_api_keys()
            
            self.initialized = True
            logger.info("✓ Authentication Manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Authentication Manager: {e}")
            return False
    
    # Authentication methods
    
    async def authenticate_user(self, username: str, password: str, 
                              ip_address: str = None, user_agent: str = None) -> Optional[Dict[str, Any]]:
        """Authenticate user with username and password"""
        try:
            # Check login attempts
            if not await self._check_login_attempts(username, ip_address):
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="Too many login attempts. Please try again later."
                )
            
            # Find user
            user = None
            for u in self.users.values():
                if u.username == username or u.email == username:
                    user = u
                    break
            
            if not user or not user.is_active:
                await self._record_login_attempt(username, ip_address, False)
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid credentials"
                )
            
            # Verify password
            if not self.pwd_context.verify(password, user.password_hash):
                await self._record_login_attempt(username, ip_address, False)
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid credentials"
                )
            
            # Create session
            session = await self._create_session(user, ip_address, user_agent)
            
            # Generate tokens
            access_token = await self._create_access_token(user)
            refresh_token = await self._create_refresh_token(user)
            
            # Update user
            user.last_login = datetime.now()
            
            # Record successful login
            await self._record_login_attempt(username, ip_address, True)
            
            return {
                'access_token': access_token,
                'refresh_token': refresh_token,
                'token_type': 'bearer',
                'expires_in': self.token_expiry,
                'user': {
                    'user_id': user.user_id,
                    'username': user.username,
                    'email': user.email,
                    'roles': list(user.roles),
                    'permissions': list(user.permissions)
                },
                'session_id': session.session_id
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error authenticating user {username}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication error"
            )
    
    async def authenticate_token(self, credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())) -> User:
        """Authenticate user with JWT token"""
        try:
            token = credentials.credentials
            
            # Decode token
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Get user
            user_id = payload.get('sub')
            if not user_id or user_id not in self.users:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token"
                )
            
            user = self.users[user_id]
            
            # Check if user is active
            if not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User account is disabled"
                )
            
            # Check token expiry
            exp = payload.get('exp')
            if exp and datetime.fromtimestamp(exp) < datetime.now():
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token has expired"
                )
            
            return user
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
        except Exception as e:
            logger.error(f"Error authenticating token: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication error"
            )
    
    async def authenticate_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """Authenticate with API key"""
        try:
            if api_key not in self.api_keys:
                return None
            
            key_data = self.api_keys[api_key]
            
            # Check if key is active
            if not key_data.get('is_active', True):
                return None
            
            # Check expiry
            expires_at = key_data.get('expires_at')
            if expires_at and datetime.fromisoformat(expires_at) < datetime.now():
                return None
            
            # Update last used
            key_data['last_used'] = datetime.now().isoformat()
            
            return key_data
            
        except Exception as e:
            logger.error(f"Error authenticating API key: {e}")
            return None
    
    # Authorization methods
    
    def require_permission(self, permission: str):
        """Decorator to require specific permission"""
        async def permission_checker(user: User = Depends(self.authenticate_token)) -> User:
            if permission not in user.permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission required: {permission}"
                )
            return user
        return permission_checker
    
    def require_role(self, role: str):
        """Decorator to require specific role"""
        async def role_checker(user: User = Depends(self.authenticate_token)) -> User:
            if role not in user.roles:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Role required: {role}"
                )
            return user
        return role_checker
    
    def require_any_permission(self, permissions: List[str]):
        """Decorator to require any of the specified permissions"""
        async def permission_checker(user: User = Depends(self.authenticate_token)) -> User:
            if not any(perm in user.permissions for perm in permissions):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"One of these permissions required: {', '.join(permissions)}"
                )
            return user
        return permission_checker
    
    # User management
    
    async def create_user(self, username: str, email: str, password: str, 
                         roles: Set[str] = None) -> User:
        """Create new user"""
        try:
            # Check if user exists
            for user in self.users.values():
                if user.username == username or user.email == email:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="User already exists"
                    )
            
            # Generate user ID
            user_id = f"user_{int(time.time() * 1000)}"
            
            # Hash password
            password_hash = self.pwd_context.hash(password)
            
            # Set default roles
            if roles is None:
                roles = {'viewer'}
            
            # Calculate permissions
            permissions = set()
            for role in roles:
                if role in self.default_roles:
                    permissions.update(self.default_roles[role])
            
            # Create user
            user = User(
                user_id=user_id,
                username=username,
                email=email,
                password_hash=password_hash,
                roles=roles,
                permissions=permissions,
                created_at=datetime.now(),
                last_login=None,
                is_active=True,
                metadata={}
            )
            
            # Store user
            self.users[user_id] = user
            
            logger.info(f"Created user: {username}")
            return user
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating user {username}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating user"
            )
    
    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """Refresh access token"""
        try:
            # Decode refresh token
            payload = jwt.decode(refresh_token, self.secret_key, algorithms=[self.algorithm])
            
            # Get user
            user_id = payload.get('sub')
            token_type = payload.get('type')
            
            if not user_id or token_type != 'refresh' or user_id not in self.users:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid refresh token"
                )
            
            user = self.users[user_id]
            
            # Generate new access token
            access_token = await self._create_access_token(user)
            
            return {
                'access_token': access_token,
                'token_type': 'bearer',
                'expires_in': self.token_expiry
            }
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Refresh token has expired"
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        except Exception as e:
            logger.error(f"Error refreshing token: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Token refresh error"
            )
    
    # Private methods
    
    async def _create_default_users(self):
        """Create default users"""
        try:
            # Create admin user if none exists
            admin_exists = any(
                'admin' in user.roles for user in self.users.values()
            )
            
            if not admin_exists:
                await self.create_user(
                    username='admin',
                    email='<EMAIL>',
                    password='admin123',  # Should be changed in production
                    roles={'admin'}
                )
                logger.info("Created default admin user")
            
        except Exception as e:
            logger.error(f"Error creating default users: {e}")
    
    async def _setup_api_keys(self):
        """Setup API keys"""
        try:
            # Create default API key if none exists
            if not self.api_keys:
                api_key = secrets.token_urlsafe(32)
                self.api_keys[api_key] = {
                    'name': 'Default API Key',
                    'permissions': list(self.default_roles['admin']),
                    'created_at': datetime.now().isoformat(),
                    'is_active': True,
                    'expires_at': None,
                    'last_used': None
                }
                logger.info(f"Created default API key: {api_key}")
            
        except Exception as e:
            logger.error(f"Error setting up API keys: {e}")
    
    async def _create_access_token(self, user: User) -> str:
        """Create JWT access token"""
        try:
            payload = {
                'sub': user.user_id,
                'username': user.username,
                'roles': list(user.roles),
                'permissions': list(user.permissions),
                'type': 'access',
                'iat': datetime.now(),
                'exp': datetime.now() + timedelta(seconds=self.token_expiry)
            }
            
            return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
            
        except Exception as e:
            logger.error(f"Error creating access token: {e}")
            raise
    
    async def _create_refresh_token(self, user: User) -> str:
        """Create JWT refresh token"""
        try:
            payload = {
                'sub': user.user_id,
                'type': 'refresh',
                'iat': datetime.now(),
                'exp': datetime.now() + timedelta(seconds=self.refresh_token_expiry)
            }
            
            return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
            
        except Exception as e:
            logger.error(f"Error creating refresh token: {e}")
            raise
    
    async def _create_session(self, user: User, ip_address: str = None, 
                            user_agent: str = None) -> Session:
        """Create user session"""
        try:
            session_id = f"session_{int(time.time() * 1000)}"
            
            session = Session(
                session_id=session_id,
                user_id=user.user_id,
                token="",  # Will be set when token is created
                created_at=datetime.now(),
                expires_at=datetime.now() + timedelta(seconds=self.token_expiry),
                last_activity=datetime.now(),
                ip_address=ip_address or "unknown",
                user_agent=user_agent or "unknown"
            )
            
            self.sessions[session_id] = session
            return session
            
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            raise
    
    async def _check_login_attempts(self, username: str, ip_address: str = None) -> bool:
        """Check if login attempts are within limit"""
        try:
            key = f"{username}:{ip_address}" if ip_address else username
            current_time = time.time()
            
            # Clean old attempts
            if key in self.login_attempts:
                self.login_attempts[key] = [
                    attempt_time for attempt_time in self.login_attempts[key]
                    if current_time - attempt_time < self.lockout_duration
                ]
            else:
                self.login_attempts[key] = []
            
            # Check limit
            return len(self.login_attempts[key]) < self.max_login_attempts
            
        except Exception as e:
            logger.error(f"Error checking login attempts: {e}")
            return True  # Allow on error
    
    async def _record_login_attempt(self, username: str, ip_address: str = None, 
                                  success: bool = False):
        """Record login attempt"""
        try:
            if success:
                # Clear attempts on successful login
                key = f"{username}:{ip_address}" if ip_address else username
                if key in self.login_attempts:
                    del self.login_attempts[key]
            else:
                # Record failed attempt
                key = f"{username}:{ip_address}" if ip_address else username
                if key not in self.login_attempts:
                    self.login_attempts[key] = []
                self.login_attempts[key].append(time.time())
            
        except Exception as e:
            logger.error(f"Error recording login attempt: {e}")

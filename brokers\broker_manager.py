"""
Broker Manager - Comprehensive Broker Integration and Connectivity

Unified broker management system that handles multiple broker APIs,
order routing, trade execution, and connectivity management with failover.
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from enum import Enum
import json
import uuid

import aiohttp
from websockets import connect, ConnectionClosed


class BrokerType(Enum):
    """Supported broker types"""
    INTERACTIVE_BROKERS = "interactive_brokers"
    ALPACA = "alpaca"
    TD_AMERITRADE = "td_ameritrade"
    SCHWAB = "schwab"
    FIDELITY = "fidelity"
    ROBINHOOD = "robinhood"
    PAPER_TRADING = "paper_trading"
    MOCK = "mock"  # For testing


class OrderType(Enum):
    """Order types"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"


class OrderSide(Enum):
    """Order sides"""
    BUY = "buy"
    SELL = "sell"
    SELL_SHORT = "sell_short"
    BUY_TO_COVER = "buy_to_cover"


class OrderStatus(Enum):
    """Order status"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"


class TimeInForce(Enum):
    """Time in force"""
    DAY = "day"
    GTC = "gtc"  # Good Till Cancelled
    IOC = "ioc"  # Immediate or Cancel
    FOK = "fok"  # Fill or Kill


@dataclass
class Order:
    """Order data structure"""
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: TimeInForce = TimeInForce.DAY
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: int = 0
    average_fill_price: Optional[float] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    broker: Optional[BrokerType] = None
    broker_order_id: Optional[str] = None
    commission: Optional[float] = None
    fees: Optional[float] = None


@dataclass
class Position:
    """Position data structure"""
    symbol: str
    quantity: int
    average_cost: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float
    broker: BrokerType
    last_updated: datetime


@dataclass
class Account:
    """Account data structure"""
    account_id: str
    broker: BrokerType
    cash_balance: float
    buying_power: float
    total_value: float
    day_trading_buying_power: Optional[float] = None
    maintenance_margin: Optional[float] = None
    last_updated: Optional[datetime] = None


class BrokerInterface(ABC):
    """Abstract interface for broker implementations"""
    
    @abstractmethod
    async def connect(self) -> bool:
        """Connect to broker"""
        pass
    
    @abstractmethod
    async def disconnect(self):
        """Disconnect from broker"""
        pass
    
    @abstractmethod
    async def submit_order(self, order: Order) -> str:
        """Submit order and return broker order ID"""
        pass
    
    @abstractmethod
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel order"""
        pass
    
    @abstractmethod
    async def get_order_status(self, order_id: str) -> Optional[Order]:
        """Get order status"""
        pass
    
    @abstractmethod
    async def get_positions(self) -> List[Position]:
        """Get all positions"""
        pass
    
    @abstractmethod
    async def get_account_info(self) -> Optional[Account]:
        """Get account information"""
        pass
    
    @abstractmethod
    async def get_order_history(self, start_date: datetime, end_date: datetime) -> List[Order]:
        """Get order history"""
        pass


class MockBroker(BrokerInterface):
    """Mock broker for testing"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.connected = False
        self.orders: Dict[str, Order] = {}
        self.positions: Dict[str, Position] = {}
        self.account = Account(
            account_id="MOCK_ACCOUNT",
            broker=BrokerType.MOCK,
            cash_balance=100000.0,
            buying_power=200000.0,
            total_value=100000.0,
            last_updated=datetime.now()
        )
        self.logger = logging.getLogger(__name__)
    
    async def connect(self) -> bool:
        """Connect to mock broker"""
        self.connected = True
        self.logger.info("Mock broker connected")
        return True
    
    async def disconnect(self):
        """Disconnect from mock broker"""
        self.connected = False
        self.logger.info("Mock broker disconnected")
    
    async def submit_order(self, order: Order) -> str:
        """Submit order to mock broker"""
        if not self.connected:
            raise Exception("Broker not connected")
        
        # Generate broker order ID
        broker_order_id = f"MOCK_{uuid.uuid4().hex[:8]}"
        
        # Update order
        order.broker_order_id = broker_order_id
        order.broker = BrokerType.MOCK
        order.status = OrderStatus.SUBMITTED
        order.created_at = datetime.now()
        order.updated_at = datetime.now()
        
        # Store order
        self.orders[broker_order_id] = order
        
        # Simulate order execution for market orders
        if order.order_type == OrderType.MARKET:
            asyncio.create_task(self._simulate_fill(order))
        
        self.logger.info(f"Order submitted: {broker_order_id}")
        return broker_order_id
    
    async def _simulate_fill(self, order: Order):
        """Simulate order fill"""
        await asyncio.sleep(1)  # Simulate execution delay
        
        # Simulate fill price (add some slippage)
        if order.side in [OrderSide.BUY, OrderSide.BUY_TO_COVER]:
            fill_price = (order.price or 100.0) * 1.001  # 0.1% slippage
        else:
            fill_price = (order.price or 100.0) * 0.999  # 0.1% slippage
        
        # Update order
        order.status = OrderStatus.FILLED
        order.filled_quantity = order.quantity
        order.average_fill_price = fill_price
        order.updated_at = datetime.now()
        order.commission = 1.0  # $1 commission
        
        # Update positions
        await self._update_position(order)
        
        self.logger.info(f"Order filled: {order.broker_order_id} at {fill_price}")
    
    async def _update_position(self, order: Order):
        """Update position after order fill"""
        symbol = order.symbol
        
        if symbol not in self.positions:
            self.positions[symbol] = Position(
                symbol=symbol,
                quantity=0,
                average_cost=0.0,
                market_value=0.0,
                unrealized_pnl=0.0,
                realized_pnl=0.0,
                broker=BrokerType.MOCK,
                last_updated=datetime.now()
            )
        
        position = self.positions[symbol]
        
        if order.side in [OrderSide.BUY, OrderSide.BUY_TO_COVER]:
            # Calculate new average cost
            total_cost = (position.quantity * position.average_cost) + (order.filled_quantity * order.average_fill_price)
            total_quantity = position.quantity + order.filled_quantity
            
            if total_quantity > 0:
                position.average_cost = total_cost / total_quantity
            
            position.quantity = total_quantity
        else:  # SELL or SELL_SHORT
            position.quantity -= order.filled_quantity
            
            # Calculate realized P&L
            if position.quantity >= 0:  # Normal sell
                realized_pnl = order.filled_quantity * (order.average_fill_price - position.average_cost)
                position.realized_pnl += realized_pnl
        
        # Update market value (using fill price as current market price)
        position.market_value = position.quantity * order.average_fill_price
        position.unrealized_pnl = position.quantity * (order.average_fill_price - position.average_cost)
        position.last_updated = datetime.now()
        
        # Update account cash balance
        if order.side in [OrderSide.BUY, OrderSide.BUY_TO_COVER]:
            self.account.cash_balance -= (order.filled_quantity * order.average_fill_price + order.commission)
        else:
            self.account.cash_balance += (order.filled_quantity * order.average_fill_price - order.commission)
        
        self.account.last_updated = datetime.now()
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel order"""
        if order_id in self.orders:
            order = self.orders[order_id]
            if order.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED]:
                order.status = OrderStatus.CANCELLED
                order.updated_at = datetime.now()
                self.logger.info(f"Order cancelled: {order_id}")
                return True
        return False
    
    async def get_order_status(self, order_id: str) -> Optional[Order]:
        """Get order status"""
        return self.orders.get(order_id)
    
    async def get_positions(self) -> List[Position]:
        """Get all positions"""
        return [pos for pos in self.positions.values() if pos.quantity != 0]
    
    async def get_account_info(self) -> Optional[Account]:
        """Get account information"""
        # Update total value
        total_position_value = sum(pos.market_value for pos in self.positions.values())
        self.account.total_value = self.account.cash_balance + total_position_value
        return self.account
    
    async def get_order_history(self, start_date: datetime, end_date: datetime) -> List[Order]:
        """Get order history"""
        return [
            order for order in self.orders.values()
            if order.created_at and start_date <= order.created_at <= end_date
        ]


class PaperTradingBroker(BrokerInterface):
    """Paper trading broker with realistic simulation"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.connected = False
        self.orders: Dict[str, Order] = {}
        self.positions: Dict[str, Position] = {}
        
        # Initialize account with paper trading balance
        initial_balance = config.get('initial_balance', 100000.0)
        self.account = Account(
            account_id="PAPER_ACCOUNT",
            broker=BrokerType.PAPER_TRADING,
            cash_balance=initial_balance,
            buying_power=initial_balance * 2,  # 2:1 margin
            total_value=initial_balance,
            last_updated=datetime.now()
        )
        
        # Market data for realistic fills
        self.market_data_callback: Optional[Callable] = None
        self.current_prices: Dict[str, float] = {}
        
        self.logger = logging.getLogger(__name__)
    
    def set_market_data_callback(self, callback: Callable):
        """Set market data callback for realistic pricing"""
        self.market_data_callback = callback
    
    async def connect(self) -> bool:
        """Connect to paper trading broker"""
        self.connected = True
        self.logger.info("Paper trading broker connected")
        return True
    
    async def disconnect(self):
        """Disconnect from paper trading broker"""
        self.connected = False
        self.logger.info("Paper trading broker disconnected")
    
    async def submit_order(self, order: Order) -> str:
        """Submit order to paper trading broker"""
        if not self.connected:
            raise Exception("Broker not connected")
        
        # Validate order
        if not await self._validate_order(order):
            raise Exception("Order validation failed")
        
        # Generate broker order ID
        broker_order_id = f"PAPER_{uuid.uuid4().hex[:8]}"
        
        # Update order
        order.broker_order_id = broker_order_id
        order.broker = BrokerType.PAPER_TRADING
        order.status = OrderStatus.SUBMITTED
        order.created_at = datetime.now()
        order.updated_at = datetime.now()
        
        # Store order
        self.orders[broker_order_id] = order
        
        # Process order based on type
        if order.order_type == OrderType.MARKET:
            asyncio.create_task(self._execute_market_order(order))
        else:
            asyncio.create_task(self._monitor_limit_order(order))
        
        self.logger.info(f"Paper order submitted: {broker_order_id}")
        return broker_order_id
    
    async def _validate_order(self, order: Order) -> bool:
        """Validate order before submission"""
        # Check buying power for buy orders
        if order.side in [OrderSide.BUY, OrderSide.BUY_TO_COVER]:
            estimated_cost = order.quantity * (order.price or self.current_prices.get(order.symbol, 100.0))
            if estimated_cost > self.account.buying_power:
                self.logger.error(f"Insufficient buying power for order: {estimated_cost} > {self.account.buying_power}")
                return False
        
        # Check position for sell orders
        if order.side in [OrderSide.SELL, OrderSide.SELL_SHORT]:
            current_position = self.positions.get(order.symbol)
            if order.side == OrderSide.SELL and (not current_position or current_position.quantity < order.quantity):
                self.logger.error(f"Insufficient position to sell: {order.symbol}")
                return False
        
        return True
    
    async def _execute_market_order(self, order: Order):
        """Execute market order with realistic slippage"""
        await asyncio.sleep(0.1)  # Simulate execution delay
        
        # Get current market price
        current_price = self.current_prices.get(order.symbol, 100.0)
        
        # Apply slippage based on order size and market conditions
        slippage_pct = min(0.001 + (order.quantity / 100000) * 0.001, 0.01)  # Max 1% slippage
        
        if order.side in [OrderSide.BUY, OrderSide.BUY_TO_COVER]:
            fill_price = current_price * (1 + slippage_pct)
        else:
            fill_price = current_price * (1 - slippage_pct)
        
        # Execute fill
        await self._fill_order(order, fill_price, order.quantity)
    
    async def _monitor_limit_order(self, order: Order):
        """Monitor limit order for execution"""
        # This would monitor market prices and execute when conditions are met
        # For simplicity, we'll just simulate a delay and partial execution
        await asyncio.sleep(5)  # Simulate waiting for limit price
        
        # 70% chance of execution for limit orders
        import random
        if random.random() < 0.7:
            await self._fill_order(order, order.price, order.quantity)
        else:
            # Order expires or remains open
            pass
    
    async def _fill_order(self, order: Order, fill_price: float, fill_quantity: int):
        """Fill order at specified price and quantity"""
        order.status = OrderStatus.FILLED
        order.filled_quantity = fill_quantity
        order.average_fill_price = fill_price
        order.updated_at = datetime.now()
        order.commission = max(1.0, fill_quantity * 0.005)  # $1 min or $0.005 per share
        
        # Update positions and account
        await self._update_position_and_account(order)
        
        self.logger.info(f"Paper order filled: {order.broker_order_id} - {fill_quantity} @ {fill_price}")
    
    async def _update_position_and_account(self, order: Order):
        """Update position and account after order fill"""
        symbol = order.symbol
        
        # Update or create position
        if symbol not in self.positions:
            self.positions[symbol] = Position(
                symbol=symbol,
                quantity=0,
                average_cost=0.0,
                market_value=0.0,
                unrealized_pnl=0.0,
                realized_pnl=0.0,
                broker=BrokerType.PAPER_TRADING,
                last_updated=datetime.now()
            )
        
        position = self.positions[symbol]
        
        # Calculate position changes
        if order.side in [OrderSide.BUY, OrderSide.BUY_TO_COVER]:
            # Calculate new average cost
            total_cost = (position.quantity * position.average_cost) + (order.filled_quantity * order.average_fill_price)
            total_quantity = position.quantity + order.filled_quantity
            
            if total_quantity > 0:
                position.average_cost = total_cost / total_quantity
            
            position.quantity = total_quantity
            
            # Update account cash
            cost = order.filled_quantity * order.average_fill_price + order.commission
            self.account.cash_balance -= cost
            
        else:  # SELL or SELL_SHORT
            # Calculate realized P&L
            if position.quantity >= order.filled_quantity:
                realized_pnl = order.filled_quantity * (order.average_fill_price - position.average_cost)
                position.realized_pnl += realized_pnl
            
            position.quantity -= order.filled_quantity
            
            # Update account cash
            proceeds = order.filled_quantity * order.average_fill_price - order.commission
            self.account.cash_balance += proceeds
        
        # Update market value and unrealized P&L
        current_price = self.current_prices.get(symbol, order.average_fill_price)
        position.market_value = position.quantity * current_price
        position.unrealized_pnl = position.quantity * (current_price - position.average_cost)
        position.last_updated = datetime.now()
        
        # Update account totals
        total_position_value = sum(pos.market_value for pos in self.positions.values())
        self.account.total_value = self.account.cash_balance + total_position_value
        self.account.buying_power = self.account.cash_balance * 2  # 2:1 margin
        self.account.last_updated = datetime.now()
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel order"""
        if order_id in self.orders:
            order = self.orders[order_id]
            if order.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED]:
                order.status = OrderStatus.CANCELLED
                order.updated_at = datetime.now()
                self.logger.info(f"Paper order cancelled: {order_id}")
                return True
        return False
    
    async def get_order_status(self, order_id: str) -> Optional[Order]:
        """Get order status"""
        return self.orders.get(order_id)
    
    async def get_positions(self) -> List[Position]:
        """Get all positions"""
        return [pos for pos in self.positions.values() if pos.quantity != 0]
    
    async def get_account_info(self) -> Optional[Account]:
        """Get account information"""
        # Update account totals
        total_position_value = sum(pos.market_value for pos in self.positions.values())
        self.account.total_value = self.account.cash_balance + total_position_value
        return self.account
    
    async def get_order_history(self, start_date: datetime, end_date: datetime) -> List[Order]:
        """Get order history"""
        return [
            order for order in self.orders.values()
            if order.created_at and start_date <= order.created_at <= end_date
        ]


class BrokerManager:
    """
    Main broker management system that coordinates multiple brokers,
    handles order routing, execution, and failover.

    Features:
    - Multiple broker support
    - Order routing and execution
    - Position aggregation across brokers
    - Failover and redundancy
    - Order management and tracking
    - Real-time status monitoring
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.broker_config = config.get('brokers', {})

        # Broker instances
        self.brokers: Dict[BrokerType, BrokerInterface] = {}
        self.primary_broker: Optional[BrokerType] = None
        self.backup_brokers: List[BrokerType] = []

        # Order tracking
        self.orders: Dict[str, Order] = {}  # Internal order ID -> Order
        self.broker_order_mapping: Dict[str, str] = {}  # Broker order ID -> Internal order ID

        # Routing configuration
        self.routing_rules: Dict[str, BrokerType] = {}  # Symbol -> Broker
        self.default_broker: Optional[BrokerType] = None

        # State
        self.running = False
        self.initialized = False

        # Setup logging
        self.logger = logging.getLogger(__name__)

    async def initialize(self) -> bool:
        """Initialize broker manager"""
        if self.initialized:
            return True

        try:
            self.logger.info("Initializing Broker Manager...")

            # Setup brokers
            await self.setup_brokers()

            # Setup routing rules
            self.setup_routing_rules()

            self.initialized = True
            self.logger.info("✓ Broker Manager initialized")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Broker Manager: {e}")
            return False

    async def setup_brokers(self):
        """Setup and configure brokers"""
        brokers_config = self.broker_config.get('providers', {})

        for broker_name, broker_config in brokers_config.items():
            try:
                broker_enum = BrokerType(broker_name)

                if broker_enum == BrokerType.MOCK:
                    broker = MockBroker(broker_config)
                elif broker_enum == BrokerType.PAPER_TRADING:
                    broker = PaperTradingBroker(broker_config)
                # Add other broker implementations here
                else:
                    self.logger.warning(f"Unsupported broker: {broker_name}")
                    continue

                # Connect to broker
                if await broker.connect():
                    self.brokers[broker_enum] = broker
                    self.logger.info(f"Connected to {broker_name}")

                    # Set primary broker if not set
                    if not self.primary_broker:
                        self.primary_broker = broker_enum
                        self.default_broker = broker_enum
                    else:
                        self.backup_brokers.append(broker_enum)

            except Exception as e:
                self.logger.error(f"Failed to setup broker {broker_name}: {e}")

    def setup_routing_rules(self):
        """Setup order routing rules"""
        routing_config = self.broker_config.get('routing', {})

        # Symbol-specific routing
        symbol_routing = routing_config.get('symbols', {})
        for symbol, broker_name in symbol_routing.items():
            try:
                broker_enum = BrokerType(broker_name)
                if broker_enum in self.brokers:
                    self.routing_rules[symbol] = broker_enum
            except ValueError:
                self.logger.warning(f"Invalid broker in routing config: {broker_name}")

        # Default broker
        default_broker_name = routing_config.get('default')
        if default_broker_name:
            try:
                self.default_broker = BrokerType(default_broker_name)
            except ValueError:
                self.logger.warning(f"Invalid default broker: {default_broker_name}")

    async def start(self):
        """Start broker manager"""
        if not self.initialized:
            await self.initialize()

        if self.running:
            return

        self.running = True
        self.logger.info("Starting Broker Manager...")

        # Start background tasks
        asyncio.create_task(self.order_monitoring_loop())
        asyncio.create_task(self.broker_health_check_loop())

        self.logger.info("Broker Manager started")

    async def stop(self):
        """Stop broker manager"""
        self.running = False

        # Disconnect from all brokers
        for broker in self.brokers.values():
            await broker.disconnect()

        self.logger.info("Broker Manager stopped")

    def get_broker_for_order(self, order: Order) -> Optional[BrokerType]:
        """Determine which broker to use for an order"""
        # Check symbol-specific routing
        if order.symbol in self.routing_rules:
            broker = self.routing_rules[order.symbol]
            if broker in self.brokers:
                return broker

        # Use default broker
        if self.default_broker and self.default_broker in self.brokers:
            return self.default_broker

        # Use primary broker as fallback
        if self.primary_broker and self.primary_broker in self.brokers:
            return self.primary_broker

        return None

    async def submit_order(self, order: Order) -> str:
        """Submit order through appropriate broker"""
        if not self.running:
            await self.start()

        # Generate internal order ID
        internal_order_id = f"ORD_{uuid.uuid4().hex[:8]}"
        order.order_id = internal_order_id

        # Determine broker
        broker_type = self.get_broker_for_order(order)
        if not broker_type:
            raise Exception("No available broker for order")

        broker = self.brokers[broker_type]

        try:
            # Submit to broker
            broker_order_id = await broker.submit_order(order)

            # Track order
            self.orders[internal_order_id] = order
            self.broker_order_mapping[broker_order_id] = internal_order_id

            self.logger.info(f"Order submitted: {internal_order_id} -> {broker_order_id} via {broker_type}")
            return internal_order_id

        except Exception as e:
            self.logger.error(f"Failed to submit order: {e}")
            # Try backup broker if available
            for backup_broker in self.backup_brokers:
                if backup_broker in self.brokers:
                    try:
                        broker = self.brokers[backup_broker]
                        broker_order_id = await broker.submit_order(order)

                        self.orders[internal_order_id] = order
                        self.broker_order_mapping[broker_order_id] = internal_order_id

                        self.logger.info(f"Order submitted via backup: {internal_order_id} -> {broker_order_id} via {backup_broker}")
                        return internal_order_id

                    except Exception as backup_error:
                        self.logger.error(f"Backup broker {backup_broker} also failed: {backup_error}")
                        continue

            raise Exception(f"All brokers failed to execute order: {e}")

    async def cancel_order(self, order_id: str) -> bool:
        """Cancel order"""
        if order_id not in self.orders:
            self.logger.error(f"Order not found: {order_id}")
            return False

        order = self.orders[order_id]
        broker_order_id = order.broker_order_id

        if not broker_order_id or not order.broker:
            self.logger.error(f"Order {order_id} has no broker information")
            return False

        broker = self.brokers.get(order.broker)
        if not broker:
            self.logger.error(f"Broker {order.broker} not available")
            return False

        try:
            result = await broker.cancel_order(broker_order_id)
            if result:
                order.status = OrderStatus.CANCELLED
                order.updated_at = datetime.now()
                self.logger.info(f"Order cancelled: {order_id}")
            return result

        except Exception as e:
            self.logger.error(f"Failed to cancel order {order_id}: {e}")
            return False

    async def get_order_status(self, order_id: str) -> Optional[Order]:
        """Get order status"""
        if order_id not in self.orders:
            return None

        order = self.orders[order_id]

        # Get latest status from broker if order is active
        if order.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED]:
            if order.broker and order.broker_order_id:
                broker = self.brokers.get(order.broker)
                if broker:
                    try:
                        updated_order = await broker.get_order_status(order.broker_order_id)
                        if updated_order:
                            # Update our order with latest info
                            order.status = updated_order.status
                            order.filled_quantity = updated_order.filled_quantity
                            order.average_fill_price = updated_order.average_fill_price
                            order.updated_at = updated_order.updated_at
                            order.commission = updated_order.commission
                            order.fees = updated_order.fees
                    except Exception as e:
                        self.logger.error(f"Failed to get order status from broker: {e}")

        return order

    async def get_all_positions(self) -> List[Position]:
        """Get positions from all brokers"""
        all_positions = []

        for broker_type, broker in self.brokers.items():
            try:
                positions = await broker.get_positions()
                all_positions.extend(positions)
            except Exception as e:
                self.logger.error(f"Failed to get positions from {broker_type}: {e}")

        return all_positions

    async def get_aggregated_positions(self) -> Dict[str, Position]:
        """Get positions aggregated across all brokers"""
        all_positions = await self.get_all_positions()
        aggregated = {}

        for position in all_positions:
            symbol = position.symbol

            if symbol not in aggregated:
                aggregated[symbol] = Position(
                    symbol=symbol,
                    quantity=0,
                    average_cost=0.0,
                    market_value=0.0,
                    unrealized_pnl=0.0,
                    realized_pnl=0.0,
                    broker=BrokerType.MOCK,  # Placeholder for aggregated
                    last_updated=datetime.now()
                )

            agg_pos = aggregated[symbol]

            # Aggregate quantities and values
            total_cost = (agg_pos.quantity * agg_pos.average_cost) + (position.quantity * position.average_cost)
            total_quantity = agg_pos.quantity + position.quantity

            if total_quantity > 0:
                agg_pos.average_cost = total_cost / total_quantity

            agg_pos.quantity = total_quantity
            agg_pos.market_value += position.market_value
            agg_pos.unrealized_pnl += position.unrealized_pnl
            agg_pos.realized_pnl += position.realized_pnl
            agg_pos.last_updated = max(agg_pos.last_updated, position.last_updated)

        return aggregated

    async def get_account_summary(self) -> Dict[str, Account]:
        """Get account information from all brokers"""
        accounts = {}

        for broker_type, broker in self.brokers.items():
            try:
                account = await broker.get_account_info()
                if account:
                    accounts[broker_type.value] = account
            except Exception as e:
                self.logger.error(f"Failed to get account info from {broker_type}: {e}")

        return accounts

    async def order_monitoring_loop(self):
        """Monitor active orders for updates"""
        while self.running:
            try:
                active_orders = [
                    order for order in self.orders.values()
                    if order.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED]
                ]

                for order in active_orders:
                    await self.get_order_status(order.order_id)

                await asyncio.sleep(5)  # Check every 5 seconds

            except Exception as e:
                self.logger.error(f"Error in order monitoring loop: {e}")
                await asyncio.sleep(5)

    async def broker_health_check_loop(self):
        """Monitor broker health and handle reconnections"""
        while self.running:
            try:
                for broker_type, broker in self.brokers.items():
                    if hasattr(broker, 'connected') and not broker.connected:
                        self.logger.warning(f"Broker {broker_type} disconnected, attempting reconnect...")
                        try:
                            await broker.connect()
                        except Exception as e:
                            self.logger.error(f"Failed to reconnect {broker_type}: {e}")

                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                self.logger.error(f"Error in broker health check loop: {e}")
                await asyncio.sleep(30)

    def get_broker_status(self) -> Dict[str, Any]:
        """Get status of all brokers"""
        status = {}

        for broker_type, broker in self.brokers.items():
            status[broker_type.value] = {
                'connected': getattr(broker, 'connected', False),
                'is_primary': broker_type == self.primary_broker,
                'is_default': broker_type == self.default_broker,
                'active_orders': len([
                    order for order in self.orders.values()
                    if order.broker == broker_type and order.status in [
                        OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED
                    ]
                ])
            }

        return status

    def get_order_summary(self) -> Dict[str, Any]:
        """Get summary of all orders"""
        status_counts = {}
        for status in OrderStatus:
            status_counts[status.value] = len([
                order for order in self.orders.values()
                if order.status == status
            ])

        return {
            'total_orders': len(self.orders),
            'status_breakdown': status_counts,
            'active_orders': len([
                order for order in self.orders.values()
                if order.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED]
            ])
        }

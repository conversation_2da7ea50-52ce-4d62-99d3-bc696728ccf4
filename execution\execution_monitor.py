"""
Execution Monitor - Monitors order execution and performance
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import defaultdict, deque

from .order_types import Order, OrderStatus

logger = logging.getLogger(__name__)


@dataclass
class ExecutionMetrics:
    """Execution performance metrics"""
    total_orders: int = 0
    filled_orders: int = 0
    cancelled_orders: int = 0
    rejected_orders: int = 0
    avg_fill_time: float = 0.0
    avg_slippage: float = 0.0
    fill_rate: float = 0.0
    success_rate: float = 0.0
    total_volume: float = 0.0
    total_value: float = 0.0


@dataclass
class VenueMetrics:
    """Venue-specific execution metrics"""
    venue_name: str
    orders_sent: int = 0
    orders_filled: int = 0
    avg_latency: float = 0.0
    fill_rate: float = 0.0
    avg_slippage: float = 0.0
    uptime: float = 1.0


class ExecutionMonitor:
    """
    Monitors order execution performance and provides analytics
    on execution quality, venue performance, and trading metrics.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.monitor_config = config.get('execution_monitor', {})
        
        # Metrics storage
        self.execution_metrics = ExecutionMetrics()
        self.venue_metrics: Dict[str, VenueMetrics] = {}
        self.order_history: deque = deque(maxlen=10000)
        self.fill_times: deque = deque(maxlen=1000)
        self.slippage_history: deque = deque(maxlen=1000)
        
        # Performance tracking
        self.latency_by_venue: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.fill_rates_by_venue: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # State
        self.initialized = False
        self.running = False
        
        # Configuration
        self.alert_thresholds = self.monitor_config.get('alert_thresholds', {})
        self.metrics_window = self.monitor_config.get('metrics_window', 3600)  # 1 hour
        
    async def initialize(self):
        """Initialize the execution monitor"""
        if self.initialized:
            return
            
        logger.info("Initializing Execution Monitor...")
        
        try:
            # Initialize metrics
            self._init_metrics()
            
            self.initialized = True
            logger.info("✓ Execution Monitor initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Execution Monitor: {e}")
            raise
            
    def _init_metrics(self):
        """Initialize metrics structures"""
        self.execution_metrics = ExecutionMetrics()
        logger.info("Execution metrics initialized")
        
    async def start(self):
        """Start the execution monitor"""
        if not self.initialized:
            await self.initialize()
            
        if self.running:
            return
            
        logger.info("Starting Execution Monitor...")
        
        # Start monitoring tasks
        asyncio.create_task(self._metrics_calculation_loop())
        asyncio.create_task(self._alert_monitoring_loop())
        
        self.running = True
        logger.info("✓ Execution Monitor started")
        
    async def stop(self):
        """Stop the execution monitor"""
        if not self.running:
            return
            
        logger.info("Stopping Execution Monitor...")
        self.running = False
        logger.info("✓ Execution Monitor stopped")
        
    async def record_order_submitted(self, order: Order, venue: str = None):
        """Record order submission"""
        try:
            self.execution_metrics.total_orders += 1
            
            # Record venue metrics
            if venue:
                if venue not in self.venue_metrics:
                    self.venue_metrics[venue] = VenueMetrics(venue_name=venue)
                self.venue_metrics[venue].orders_sent += 1
            
            # Record order in history
            self.order_history.append({
                'order_id': order.order_id,
                'symbol': order.symbol,
                'side': order.side,
                'quantity': order.quantity,
                'order_type': order.order_type,
                'venue': venue,
                'submitted_at': time.time(),
                'status': 'submitted'
            })
            
        except Exception as e:
            logger.error(f"Error recording order submission: {e}")
            
    async def record_order_filled(self, order: Order, fill_price: float, fill_quantity: float, venue: str = None):
        """Record order fill"""
        try:
            self.execution_metrics.filled_orders += 1
            self.execution_metrics.total_volume += fill_quantity
            self.execution_metrics.total_value += fill_price * fill_quantity
            
            # Calculate fill time
            submitted_time = None
            for order_record in reversed(self.order_history):
                if order_record['order_id'] == order.order_id:
                    submitted_time = order_record['submitted_at']
                    break
                    
            if submitted_time:
                fill_time = time.time() - submitted_time
                self.fill_times.append(fill_time)
                
                # Update average fill time
                if self.fill_times:
                    self.execution_metrics.avg_fill_time = sum(self.fill_times) / len(self.fill_times)
            
            # Calculate slippage (simplified)
            if hasattr(order, 'price') and order.price:
                slippage = abs(fill_price - order.price) / order.price
                self.slippage_history.append(slippage)
                
                # Update average slippage
                if self.slippage_history:
                    self.execution_metrics.avg_slippage = sum(self.slippage_history) / len(self.slippage_history)
            
            # Update venue metrics
            if venue and venue in self.venue_metrics:
                self.venue_metrics[venue].orders_filled += 1
                
            # Update rates
            self._update_rates()
            
        except Exception as e:
            logger.error(f"Error recording order fill: {e}")
            
    async def record_order_cancelled(self, order: Order, venue: str = None):
        """Record order cancellation"""
        try:
            self.execution_metrics.cancelled_orders += 1
            self._update_rates()
            
        except Exception as e:
            logger.error(f"Error recording order cancellation: {e}")
            
    async def record_order_rejected(self, order: Order, reason: str, venue: str = None):
        """Record order rejection"""
        try:
            self.execution_metrics.rejected_orders += 1
            self._update_rates()
            
            logger.warning(f"Order {order.order_id} rejected: {reason}")
            
        except Exception as e:
            logger.error(f"Error recording order rejection: {e}")

    async def record_fill(self, order_id: str, fill_price: float, fill_quantity: float, venue: str = None):
        """Record a fill for an order"""
        try:
            # Find the order in history
            order_found = None
            for order_record in reversed(self.order_history):
                if order_record['order_id'] == order_id:
                    order_found = order_record
                    break

            if not order_found:
                logger.warning(f"Order {order_id} not found in history for fill recording")
                return

            # Record fill metrics
            self.execution_metrics.filled_orders += 1
            self.execution_metrics.total_volume += fill_quantity
            self.execution_metrics.total_value += fill_price * fill_quantity

            # Calculate fill time
            submitted_time = order_found.get('submitted_at')
            if submitted_time:
                fill_time = time.time() - submitted_time
                self.fill_times.append(fill_time)

                # Update average fill time
                if self.fill_times:
                    self.execution_metrics.avg_fill_time = sum(self.fill_times) / len(self.fill_times)

            # Update venue metrics
            if venue and venue in self.venue_metrics:
                self.venue_metrics[venue].orders_filled += 1

            # Update rates
            self._update_rates()

            logger.debug(f"Recorded fill for order {order_id}: {fill_quantity} @ {fill_price}")

        except Exception as e:
            logger.error(f"Error recording fill for order {order_id}: {e}")

    def _update_rates(self):
        """Update calculated rates"""
        total = self.execution_metrics.total_orders
        if total > 0:
            self.execution_metrics.fill_rate = self.execution_metrics.filled_orders / total
            self.execution_metrics.success_rate = (
                self.execution_metrics.filled_orders / 
                (total - self.execution_metrics.cancelled_orders)
            ) if (total - self.execution_metrics.cancelled_orders) > 0 else 0.0
            
        # Update venue fill rates
        for venue_name, venue_metrics in self.venue_metrics.items():
            if venue_metrics.orders_sent > 0:
                venue_metrics.fill_rate = venue_metrics.orders_filled / venue_metrics.orders_sent
                
    async def record_venue_latency(self, venue: str, latency: float):
        """Record venue latency"""
        try:
            self.latency_by_venue[venue].append(latency)
            
            # Update venue metrics
            if venue in self.venue_metrics:
                latencies = list(self.latency_by_venue[venue])
                if latencies:
                    self.venue_metrics[venue].avg_latency = sum(latencies) / len(latencies)
                    
        except Exception as e:
            logger.error(f"Error recording venue latency: {e}")
            
    async def get_execution_metrics(self) -> ExecutionMetrics:
        """Get current execution metrics"""
        return self.execution_metrics
        
    async def get_venue_metrics(self, venue: str = None) -> Dict[str, VenueMetrics]:
        """Get venue metrics"""
        if venue:
            return {venue: self.venue_metrics.get(venue)}
        return self.venue_metrics.copy()
        
    async def get_recent_orders(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent order history"""
        return list(self.order_history)[-limit:]
        
    async def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        return {
            'execution_metrics': {
                'total_orders': self.execution_metrics.total_orders,
                'fill_rate': self.execution_metrics.fill_rate,
                'success_rate': self.execution_metrics.success_rate,
                'avg_fill_time': self.execution_metrics.avg_fill_time,
                'avg_slippage': self.execution_metrics.avg_slippage,
                'total_volume': self.execution_metrics.total_volume,
                'total_value': self.execution_metrics.total_value
            },
            'venue_performance': {
                venue: {
                    'fill_rate': metrics.fill_rate,
                    'avg_latency': metrics.avg_latency,
                    'orders_sent': metrics.orders_sent,
                    'orders_filled': metrics.orders_filled
                }
                for venue, metrics in self.venue_metrics.items()
            },
            'recent_performance': {
                'avg_fill_time_recent': sum(list(self.fill_times)[-10:]) / min(len(self.fill_times), 10) if self.fill_times else 0.0,
                'avg_slippage_recent': sum(list(self.slippage_history)[-10:]) / min(len(self.slippage_history), 10) if self.slippage_history else 0.0
            }
        }
        
    async def _metrics_calculation_loop(self):
        """Background metrics calculation loop"""
        while self.running:
            try:
                await asyncio.sleep(60)  # Update every minute
                
                if self.running:
                    await self._calculate_metrics()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in metrics calculation loop: {e}")
                
    async def _calculate_metrics(self):
        """Calculate and update metrics"""
        try:
            # Update rates
            self._update_rates()
            
            # Clean old data
            current_time = time.time()
            cutoff_time = current_time - self.metrics_window
            
            # Remove old order history
            while self.order_history and self.order_history[0].get('submitted_at', 0) < cutoff_time:
                self.order_history.popleft()
                
        except Exception as e:
            logger.error(f"Error calculating metrics: {e}")
            
    async def _alert_monitoring_loop(self):
        """Background alert monitoring loop"""
        while self.running:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                
                if self.running:
                    await self._check_alerts()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in alert monitoring loop: {e}")
                
    async def _check_alerts(self):
        """Check for alert conditions"""
        try:
            # Check fill rate alerts
            min_fill_rate = self.alert_thresholds.get('min_fill_rate', 0.8)
            if self.execution_metrics.fill_rate < min_fill_rate:
                logger.warning(f"Low fill rate alert: {self.execution_metrics.fill_rate:.2%} < {min_fill_rate:.2%}")
            
            # Check average fill time alerts
            max_fill_time = self.alert_thresholds.get('max_fill_time', 5.0)
            if self.execution_metrics.avg_fill_time > max_fill_time:
                logger.warning(f"High fill time alert: {self.execution_metrics.avg_fill_time:.2f}s > {max_fill_time}s")
            
            # Check slippage alerts
            max_slippage = self.alert_thresholds.get('max_slippage', 0.001)
            if self.execution_metrics.avg_slippage > max_slippage:
                logger.warning(f"High slippage alert: {self.execution_metrics.avg_slippage:.4f} > {max_slippage:.4f}")
                
        except Exception as e:
            logger.error(f"Error checking alerts: {e}")
            
    async def update_metrics(self):
        """Update execution metrics - called by ExecutionEngine"""
        try:
            await self._calculate_metrics()
            logger.debug("Execution metrics updated")
        except Exception as e:
            logger.error(f"Error updating execution metrics: {e}")

    async def get_stats(self) -> Dict[str, Any]:
        """Get execution monitor statistics"""
        return {
            'running': self.running,
            'total_orders_tracked': len(self.order_history),
            'venues_tracked': len(self.venue_metrics),
            'metrics_window': self.metrics_window,
            'alert_thresholds': self.alert_thresholds
        }

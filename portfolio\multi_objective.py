"""
Multi-Objective Optimizer - Portfolio optimization with multiple objectives
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class MultiObjectiveOptimizer:
    """
    Multi-objective portfolio optimization that balances multiple
    competing objectives such as return, risk, ESG scores, etc.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Objective functions
        self.available_objectives = {
            'return': self._return_objective,
            'risk': self._risk_objective,
            'sharpe': self._sharpe_objective,
            'diversification': self._diversification_objective,
            'turnover': self._turnover_objective,
            'esg': self._esg_objective
        }
        
        # Market data
        self.returns_data: Optional[pd.DataFrame] = None
        self.covariance_matrix: Optional[np.ndarray] = None
        self.expected_returns: Optional[np.ndarray] = None
        
        # Additional data
        self.esg_scores: Optional[Dict[str, float]] = None
        self.previous_weights: Optional[np.ndarray] = None
        
        # State
        self.initialized = False
        
    async def initialize(self):
        """Initialize multi-objective optimizer"""
        if self.initialized:
            return
            
        logger.info("Initializing Multi-Objective Optimizer...")
        
        # Setup default objective weights
        self.default_objective_weights = {
            'return': 0.4,
            'risk': 0.3,
            'diversification': 0.2,
            'turnover': 0.1
        }
        
        self.initialized = True
        logger.info("✓ Multi-Objective Optimizer initialized")
        
    async def update_market_data(self, returns_data: pd.DataFrame) -> Dict[str, Any]:
        """Update market data for optimization"""
        try:
            self.returns_data = returns_data
            self.covariance_matrix = returns_data.cov().values
            self.expected_returns = returns_data.mean().values
            
            logger.info(f"✓ Updated multi-objective market data for {len(returns_data.columns)} assets")
            
            return {
                'success': True,
                'assets': len(returns_data.columns)
            }
            
        except Exception as e:
            logger.error(f"Error updating multi-objective market data: {e}")
            return {'success': False, 'error': str(e)}
            
    async def set_esg_scores(self, esg_scores: Dict[str, float]) -> Dict[str, Any]:
        """Set ESG scores for assets"""
        try:
            self.esg_scores = esg_scores
            
            return {
                'success': True,
                'assets_with_esg': len(esg_scores)
            }
            
        except Exception as e:
            logger.error(f"Error setting ESG scores: {e}")
            return {'success': False, 'error': str(e)}
            
    def _return_objective(self, weights: np.ndarray) -> float:
        """Return objective (to be maximized, so return negative)"""
        return -np.dot(weights, self.expected_returns)
        
    def _risk_objective(self, weights: np.ndarray) -> float:
        """Risk objective (variance to be minimized)"""
        return np.dot(weights, np.dot(self.covariance_matrix, weights))
        
    def _sharpe_objective(self, weights: np.ndarray) -> float:
        """Sharpe ratio objective (to be maximized, so return negative)"""
        portfolio_return = np.dot(weights, self.expected_returns)
        portfolio_variance = np.dot(weights, np.dot(self.covariance_matrix, weights))
        portfolio_volatility = np.sqrt(portfolio_variance)
        
        if portfolio_volatility == 0:
            return 0
            
        return -(portfolio_return / portfolio_volatility)
        
    def _diversification_objective(self, weights: np.ndarray) -> float:
        """Diversification objective (concentration to be minimized)"""
        # Herfindahl index (concentration measure)
        return np.sum(weights ** 2)
        
    def _turnover_objective(self, weights: np.ndarray) -> float:
        """Turnover objective (transaction costs)"""
        if self.previous_weights is None:
            return 0
            
        return np.sum(np.abs(weights - self.previous_weights))
        
    def _esg_objective(self, weights: np.ndarray) -> float:
        """ESG objective (to be maximized, so return negative)"""
        if self.esg_scores is None:
            return 0
            
        esg_array = np.array([
            self.esg_scores.get(asset, 0.5) for asset in self.returns_data.columns
        ])
        
        portfolio_esg = np.dot(weights, esg_array)
        return -portfolio_esg
        
    async def optimize(self, objectives: Dict[str, float] = None, 
                     constraints: Dict[str, Any] = None) -> Dict[str, Any]:
        """Optimize portfolio with multiple objectives"""
        try:
            if self.returns_data is None:
                return {'success': False, 'error': 'No market data available'}
                
            # Use provided objectives or defaults
            if objectives is None:
                objectives = self.default_objective_weights.copy()
                
            # Normalize objective weights
            total_weight = sum(objectives.values())
            if total_weight > 0:
                objectives = {k: v/total_weight for k, v in objectives.items()}
                
            n_assets = len(self.returns_data.columns)
            
            # Combined objective function
            def combined_objective(weights):
                total_obj = 0
                for obj_name, obj_weight in objectives.items():
                    if obj_name in self.available_objectives:
                        obj_func = self.available_objectives[obj_name]
                        obj_value = obj_func(weights)
                        total_obj += obj_weight * obj_value
                        
                return total_obj
                
            # Constraints
            constraints_list = [
                {'type': 'eq', 'fun': lambda w: np.sum(w) - 1.0}  # Weights sum to 1
            ]
            
            # Bounds
            min_weight = constraints.get('min_weight', 0.0) if constraints else 0.0
            max_weight = constraints.get('max_weight', 1.0) if constraints else 1.0
            bounds = [(min_weight, max_weight) for _ in range(n_assets)]
            
            # Initial guess
            x0 = np.ones(n_assets) / n_assets
            
            # Optimize
            result = minimize(
                combined_objective, 
                x0, 
                method='SLSQP', 
                bounds=bounds, 
                constraints=constraints_list
            )
            
            if result.success:
                weights_dict = dict(zip(self.returns_data.columns, result.x))
                
                # Calculate individual objective values
                objective_values = {}
                for obj_name in objectives.keys():
                    if obj_name in self.available_objectives:
                        obj_func = self.available_objectives[obj_name]
                        objective_values[obj_name] = obj_func(result.x)
                        
                # Calculate portfolio metrics
                portfolio_return = np.dot(result.x, self.expected_returns)
                portfolio_variance = np.dot(result.x, np.dot(self.covariance_matrix, result.x))
                portfolio_volatility = np.sqrt(portfolio_variance)
                
                return {
                    'success': True,
                    'weights': weights_dict,
                    'expected_return': portfolio_return,
                    'volatility': portfolio_volatility,
                    'sharpe_ratio': portfolio_return / portfolio_volatility if portfolio_volatility > 0 else 0,
                    'objectives_used': objectives,
                    'objective_values': objective_values,
                    'method': 'multi_objective'
                }
            else:
                return {'success': False, 'error': 'Optimization failed', 'details': result.message}
                
        except Exception as e:
            logger.error(f"Error optimizing multi-objective portfolio: {e}")
            return {'success': False, 'error': str(e)}
            
    async def pareto_frontier_analysis(self, objective_pairs: List[Tuple[str, str]], 
                                     n_points: int = 20) -> Dict[str, Any]:
        """Generate Pareto frontier for objective pairs"""
        try:
            pareto_results = {}
            
            for obj1, obj2 in objective_pairs:
                if obj1 not in self.available_objectives or obj2 not in self.available_objectives:
                    continue
                    
                frontier_points = []
                
                # Generate points along the frontier
                for i in range(n_points):
                    weight1 = i / (n_points - 1)
                    weight2 = 1 - weight1
                    
                    objectives = {obj1: weight1, obj2: weight2}
                    
                    result = await self.optimize(objectives)
                    
                    if result['success']:
                        point = {
                            'weights': result['weights'],
                            'objective_weights': objectives,
                            'objective_values': result['objective_values'],
                            'portfolio_metrics': {
                                'return': result['expected_return'],
                                'volatility': result['volatility'],
                                'sharpe_ratio': result['sharpe_ratio']
                            }
                        }
                        frontier_points.append(point)
                        
                pareto_results[f"{obj1}_vs_{obj2}"] = frontier_points
                
            return {
                'success': True,
                'pareto_frontiers': pareto_results,
                'n_points_per_frontier': n_points
            }
            
        except Exception as e:
            logger.error(f"Error generating Pareto frontier: {e}")
            return {'success': False, 'error': str(e)}
            
    async def get_objective_summary(self) -> Dict[str, Any]:
        """Get summary of available objectives"""
        return {
            'available_objectives': list(self.available_objectives.keys()),
            'default_weights': self.default_objective_weights,
            'data_requirements': {
                'return': 'expected_returns',
                'risk': 'covariance_matrix',
                'sharpe': 'expected_returns + covariance_matrix',
                'diversification': 'none',
                'turnover': 'previous_weights',
                'esg': 'esg_scores'
            },
            'esg_data_available': self.esg_scores is not None,
            'previous_weights_available': self.previous_weights is not None
        }

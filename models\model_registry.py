"""
Ollama Model Registry - Discovery and management of available models
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
import aiohttp
from datetime import datetime, timedelta
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.logging_utils import safe_success, get_status_symbol

logger = logging.getLogger(__name__)


class OllamaModelRegistry:
    """
    Registry for discovering and managing available Ollama models.
    Handles model metadata, availability tracking, and automatic discovery.
    """
    
    def __init__(self, base_url: str, session: aiohttp.ClientSession):
        self.base_url = base_url
        self.session = session
        
        # Model storage
        self.available_models: Dict[str, Dict[str, Any]] = {}
        self.model_metadata: Dict[str, Dict[str, Any]] = {}
        
        # State
        self.initialized = False
        self.running = False
        self.last_refresh = None
        
        # Configuration
        self.refresh_interval = 300  # 5 minutes
        self.refresh_task: Optional[asyncio.Task] = None
        
    async def initialize(self):
        """Initialize the model registry"""
        if self.initialized:
            return
            
        logger.info("Initializing Ollama Model Registry...")
        
        try:
            # Initial model discovery
            await self.refresh_registry()
            
            self.initialized = True
            safe_success(logger, f"{get_status_symbol(True)} Model Registry initialized with {len(self.available_models)} models")
            
        except Exception as e:
            logger.error(f"Failed to initialize Model Registry: {e}")
            raise
            
    async def start(self):
        """Start the model registry with periodic refresh"""
        if not self.initialized:
            await self.initialize()
            
        if self.running:
            return
            
        logger.info("Starting Model Registry...")
        
        # Start periodic refresh task
        self.refresh_task = asyncio.create_task(self._periodic_refresh())
        self.running = True
        
        safe_success(logger, f"{get_status_symbol(True)} Model Registry started")
        
    async def stop(self):
        """Stop the model registry"""
        if not self.running:
            return
            
        logger.info("Stopping Model Registry...")
        self.running = False
        
        # Cancel refresh task
        if self.refresh_task and not self.refresh_task.done():
            self.refresh_task.cancel()
            try:
                await self.refresh_task
            except asyncio.CancelledError:
                pass
                
        safe_success(logger, f"{get_status_symbol(True)} Model Registry stopped")
        
    async def refresh_registry(self) -> bool:
        """Fetch all available models from Ollama server"""
        try:
            logger.debug("Refreshing model registry...")
            
            async with self.session.get(f"{self.base_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    models = data.get("models", [])
                    
                    # Update available models
                    new_models = {}
                    for model in models:
                        model_name = model.get("name", "")
                        if model_name:
                            new_models[model_name] = {
                                "name": model_name,
                                "size": model.get("size", 0),
                                "modified_at": model.get("modified_at", ""),
                                "digest": model.get("digest", ""),
                                "details": model.get("details", {}),
                                "last_seen": datetime.now().isoformat()
                            }
                            
                    # Check for new models
                    new_model_names = set(new_models.keys()) - set(self.available_models.keys())
                    if new_model_names:
                        logger.info(f"Discovered {len(new_model_names)} new models: {list(new_model_names)}")
                        
                    # Check for removed models
                    removed_models = set(self.available_models.keys()) - set(new_models.keys())
                    if removed_models:
                        logger.warning(f"Models no longer available: {list(removed_models)}")
                        
                    self.available_models = new_models
                    self.last_refresh = datetime.now()
                    
                    # Enrich model metadata
                    await self._enrich_model_metadata()
                    
                    logger.debug(f"Registry refreshed - {len(self.available_models)} models available")
                    return True
                else:
                    logger.error(f"Failed to refresh registry - HTTP {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error refreshing model registry: {e}")
            return False
            
    async def _enrich_model_metadata(self):
        """Enrich model metadata with additional information"""
        for model_name, model_info in self.available_models.items():
            if model_name not in self.model_metadata:
                self.model_metadata[model_name] = {}
                
            metadata = self.model_metadata[model_name]
            
            # Categorize model by name patterns
            metadata['category'] = self._categorize_model(model_name)
            metadata['recommended_roles'] = self._get_recommended_roles(model_name)
            metadata['resource_requirements'] = self._estimate_resources(model_info)
            metadata['performance_tier'] = self._estimate_performance_tier(model_info)
            
    def _categorize_model(self, model_name: str) -> str:
        """Categorize model based on name patterns"""
        name_lower = model_name.lower()
        
        if any(x in name_lower for x in ['reasoning', 'think', 'r1']):
            return 'reasoning'
        elif any(x in name_lower for x in ['vision', 'vl', 'visual']):
            return 'vision'
        elif any(x in name_lower for x in ['code', 'programming']):
            return 'coding'
        elif any(x in name_lower for x in ['chat', 'instruct', 'assistant']):
            return 'conversational'
        elif any(x in name_lower for x in ['mini', 'small']):
            return 'lightweight'
        elif any(x in name_lower for x in ['large', 'big', '70b', '72b']):
            return 'large'
        else:
            return 'general'
            
    def _get_recommended_roles(self, model_name: str) -> List[str]:
        """Get recommended agent roles for a model"""
        name_lower = model_name.lower()
        size = self._extract_size_from_name(model_name)
        
        roles = []
        
        # Size-based recommendations
        if size >= 30:  # Large models
            roles.extend(['team_leader', 'strategy_developer'])
        elif size >= 20:  # Medium-large models
            roles.extend(['market_analyst', 'strategy_developer'])
        elif size >= 10:  # Medium models
            roles.extend(['market_analyst', 'risk_manager'])
        else:  # Small models
            roles.extend(['execution_specialist', 'performance_evaluator'])
            
        # Specialty-based recommendations
        if 'reasoning' in name_lower or 'think' in name_lower:
            roles.extend(['risk_manager', 'strategy_developer'])
        if 'vision' in name_lower or 'vl' in name_lower:
            roles.append('visual_analyst')
        if 'mini' in name_lower or 'small' in name_lower:
            roles.append('execution_specialist')
            
        return list(set(roles))  # Remove duplicates
        
    def _extract_size_from_name(self, model_name: str) -> float:
        """Extract model size in billions of parameters from name"""
        import re
        
        # Look for patterns like "7b", "13b", "70b", etc.
        match = re.search(r'(\d+(?:\.\d+)?)b', model_name.lower())
        if match:
            return float(match.group(1))
            
        # Default size estimation based on model file size
        model_info = self.available_models.get(model_name, {})
        file_size = model_info.get('size', 0)
        
        if file_size > 15 * 1024**3:  # > 15GB
            return 30.0
        elif file_size > 10 * 1024**3:  # > 10GB
            return 20.0
        elif file_size > 5 * 1024**3:   # > 5GB
            return 10.0
        elif file_size > 2 * 1024**3:   # > 2GB
            return 7.0
        else:
            return 3.0
            
    def _estimate_resources(self, model_info: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate resource requirements for a model"""
        size_bytes = model_info.get('size', 0)
        size_gb = size_bytes / (1024**3)
        
        # Rough estimates based on model size
        return {
            'memory_gb': max(2, int(size_gb * 1.5)),  # Model size + overhead
            'cpu_cores': max(1, min(8, int(size_gb / 2))),
            'gpu_memory_gb': max(4, int(size_gb)),
            'disk_space_gb': int(size_gb * 1.1)  # Model + cache
        }
        
    def _estimate_performance_tier(self, model_info: Dict[str, Any]) -> str:
        """Estimate performance tier based on model characteristics"""
        size_bytes = model_info.get('size', 0)
        size_gb = size_bytes / (1024**3)
        
        if size_gb >= 15:
            return 'premium'
        elif size_gb >= 8:
            return 'high'
        elif size_gb >= 4:
            return 'medium'
        else:
            return 'basic'
            
    async def _periodic_refresh(self):
        """Periodic refresh task"""
        while self.running:
            try:
                await asyncio.sleep(self.refresh_interval)
                if self.running:
                    await self.refresh_registry()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic refresh: {e}")
                
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of all available models"""
        return list(self.available_models.values())
        
    async def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific model"""
        if model_name not in self.available_models:
            return None
            
        model_info = self.available_models[model_name].copy()
        model_info['metadata'] = self.model_metadata.get(model_name, {})
        return model_info
        
    async def get_models_by_role(self, role: str) -> List[Dict[str, Any]]:
        """Get models recommended for a specific role"""
        matching_models = []
        
        for model_name, model_info in self.available_models.items():
            metadata = self.model_metadata.get(model_name, {})
            recommended_roles = metadata.get('recommended_roles', [])
            
            if role in recommended_roles:
                model_data = model_info.copy()
                model_data['metadata'] = metadata
                matching_models.append(model_data)
                
        # Sort by performance tier and size
        def sort_key(model):
            tier_order = {'premium': 4, 'high': 3, 'medium': 2, 'basic': 1}
            tier = model['metadata'].get('performance_tier', 'basic')
            size = model.get('size', 0)
            return (tier_order.get(tier, 1), size)
            
        matching_models.sort(key=sort_key, reverse=True)
        return matching_models
        
    async def is_model_available(self, model_name: str) -> bool:
        """Check if a specific model is available"""
        return model_name in self.available_models
        
    async def get_registry_stats(self) -> Dict[str, Any]:
        """Get registry statistics"""
        stats = {
            'total_models': len(self.available_models),
            'last_refresh': self.last_refresh.isoformat() if self.last_refresh else None,
            'categories': {},
            'performance_tiers': {},
            'total_size_gb': 0
        }
        
        for model_info in self.available_models.values():
            # Size calculation
            stats['total_size_gb'] += model_info.get('size', 0) / (1024**3)
            
            # Category distribution
            metadata = self.model_metadata.get(model_info['name'], {})
            category = metadata.get('category', 'unknown')
            stats['categories'][category] = stats['categories'].get(category, 0) + 1
            
            # Performance tier distribution
            tier = metadata.get('performance_tier', 'unknown')
            stats['performance_tiers'][tier] = stats['performance_tiers'].get(tier, 0) + 1
            
        stats['total_size_gb'] = round(stats['total_size_gb'], 2)
        return stats

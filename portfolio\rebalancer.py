"""
Portfolio Rebalancer - Executes portfolio rebalancing operations
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class RebalanceOrder:
    """Rebalance order data"""
    symbol: str
    side: str  # 'buy' or 'sell'
    quantity: float
    target_value: float
    priority: int
    order_id: Optional[str] = None
    status: str = 'pending'  # 'pending', 'submitted', 'filled', 'failed'


class PortfolioRebalancer:
    """
    Executes portfolio rebalancing operations.
    
    Responsibilities:
    - Convert rebalance recommendations to orders
    - Execute rebalancing trades
    - Monitor rebalance progress
    - Handle partial fills and failures
    - Optimize execution costs
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.rebalance_config = config.get('rebalancing', {})
        
        # Rebalancing parameters
        self.max_orders_per_rebalance = self.rebalance_config.get('max_orders_per_rebalance', 10)
        self.order_timeout = self.rebalance_config.get('order_timeout', 300)  # 5 minutes
        self.min_trade_value = self.rebalance_config.get('min_trade_value', 100.0)
        
        # Execution settings
        self.use_limit_orders = self.rebalance_config.get('use_limit_orders', True)
        self.limit_order_buffer = self.rebalance_config.get('limit_order_buffer', 0.001)  # 0.1%
        
        # State
        self.initialized = False
        self.running = False
        self.active_rebalances: Dict[str, List[RebalanceOrder]] = {}
        
        # Integration points
        self.execution_engine = None
        self.position_manager = None
        
    async def initialize(self) -> bool:
        """Initialize rebalancer"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Portfolio Rebalancer...")
            logger.info(f"  Max orders per rebalance: {self.max_orders_per_rebalance}")
            logger.info(f"  Order timeout: {self.order_timeout}s")
            
            self.initialized = True
            logger.info("✓ Portfolio Rebalancer initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Portfolio Rebalancer: {e}")
            return False
    
    async def start(self) -> bool:
        """Start rebalancer"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        self.running = True
        logger.info("✓ Portfolio Rebalancer started")
        return True
    
    async def stop(self) -> bool:
        """Stop rebalancer"""
        self.running = False
        logger.info("✓ Portfolio Rebalancer stopped")
        return True
    
    def set_execution_engine(self, execution_engine):
        """Set execution engine for order submission"""
        self.execution_engine = execution_engine
    
    def set_position_manager(self, position_manager):
        """Set position manager for position updates"""
        self.position_manager = position_manager
    
    async def execute_rebalance(self, recommendations: List[Any]) -> Dict[str, Any]:
        """Execute portfolio rebalance based on recommendations"""
        try:
            rebalance_id = f"rebalance_{int(time.time())}"
            
            # Convert recommendations to orders
            orders = await self._create_rebalance_orders(recommendations)
            
            if not orders:
                return {
                    'success': True,
                    'rebalance_id': rebalance_id,
                    'message': 'No orders to execute',
                    'trades_executed': 0,
                    'total_value_traded': 0.0
                }
            
            # Store active rebalance
            self.active_rebalances[rebalance_id] = orders
            
            # Execute orders
            execution_results = await self._execute_orders(rebalance_id, orders)
            
            # Calculate results
            successful_trades = sum(1 for result in execution_results if result.get('success', False))
            total_value_traded = sum(
                result.get('value_traded', 0.0) for result in execution_results 
                if result.get('success', False)
            )
            
            # Clean up completed rebalance
            if rebalance_id in self.active_rebalances:
                del self.active_rebalances[rebalance_id]
            
            logger.info(f"✓ Rebalance completed: {successful_trades}/{len(orders)} trades successful")
            
            return {
                'success': True,
                'rebalance_id': rebalance_id,
                'trades_executed': successful_trades,
                'total_trades': len(orders),
                'total_value_traded': total_value_traded,
                'execution_results': execution_results,
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error executing rebalance: {e}")
            return {
                'success': False,
                'error': str(e),
                'trades_executed': 0,
                'total_value_traded': 0.0
            }
    
    async def _create_rebalance_orders(self, recommendations: List[Any]) -> List[RebalanceOrder]:
        """Convert rebalance recommendations to orders"""
        try:
            orders = []
            
            for rec in recommendations:
                # Skip if trade amount is too small
                if abs(rec.trade_amount) < self.min_trade_value:
                    continue
                
                # Calculate quantity (simplified - assumes price = 1 for now)
                # In practice, would get current market price
                current_price = 100.0  # Placeholder
                quantity = abs(rec.trade_amount) / current_price
                
                order = RebalanceOrder(
                    symbol=rec.symbol,
                    side=rec.trade_side,
                    quantity=quantity,
                    target_value=abs(rec.trade_amount),
                    priority=rec.priority
                )
                
                orders.append(order)
            
            # Sort by priority
            orders.sort(key=lambda x: x.priority)
            
            # Limit number of orders
            return orders[:self.max_orders_per_rebalance]
            
        except Exception as e:
            logger.error(f"Error creating rebalance orders: {e}")
            return []
    
    async def _execute_orders(self, rebalance_id: str, orders: List[RebalanceOrder]) -> List[Dict[str, Any]]:
        """Execute rebalance orders"""
        try:
            execution_results = []
            
            for order in orders:
                try:
                    # Create order for execution engine
                    if self.execution_engine:
                        # This would integrate with the actual execution engine
                        # For now, simulate execution
                        result = await self._simulate_order_execution(order)
                    else:
                        # Fallback simulation
                        result = await self._simulate_order_execution(order)
                    
                    execution_results.append(result)
                    
                    # Update order status
                    order.status = 'filled' if result.get('success', False) else 'failed'
                    order.order_id = result.get('order_id')
                    
                    # Small delay between orders
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"Error executing order for {order.symbol}: {e}")
                    execution_results.append({
                        'success': False,
                        'symbol': order.symbol,
                        'error': str(e),
                        'value_traded': 0.0
                    })
                    order.status = 'failed'
            
            return execution_results
            
        except Exception as e:
            logger.error(f"Error executing orders for rebalance {rebalance_id}: {e}")
            return []
    
    async def _simulate_order_execution(self, order: RebalanceOrder) -> Dict[str, Any]:
        """Simulate order execution (placeholder)"""
        try:
            # Simulate successful execution with some randomness
            import random
            
            success = random.random() > 0.05  # 95% success rate
            
            if success:
                # Simulate fill price with small slippage
                slippage = random.uniform(-0.001, 0.001)  # ±0.1%
                fill_price = 100.0 * (1 + slippage)  # Placeholder price
                value_traded = order.quantity * fill_price
                
                # Update position manager if available
                if self.position_manager:
                    if order.side == 'buy':
                        await self.position_manager.add_position(order.symbol, order.quantity, fill_price)
                    else:
                        await self.position_manager.update_position(order.symbol, -order.quantity, fill_price)
                
                return {
                    'success': True,
                    'order_id': f"order_{int(time.time())}_{random.randint(1000, 9999)}",
                    'symbol': order.symbol,
                    'side': order.side,
                    'quantity': order.quantity,
                    'fill_price': fill_price,
                    'value_traded': value_traded,
                    'slippage': slippage,
                    'commission': value_traded * 0.001  # 0.1% commission
                }
            else:
                return {
                    'success': False,
                    'symbol': order.symbol,
                    'error': 'Simulated execution failure',
                    'value_traded': 0.0
                }
                
        except Exception as e:
            logger.error(f"Error in simulated execution: {e}")
            return {
                'success': False,
                'symbol': order.symbol,
                'error': str(e),
                'value_traded': 0.0
            }
    
    async def get_active_rebalances(self) -> Dict[str, Any]:
        """Get information about active rebalances"""
        try:
            active_info = {}
            
            for rebalance_id, orders in self.active_rebalances.items():
                active_info[rebalance_id] = {
                    'total_orders': len(orders),
                    'pending_orders': sum(1 for order in orders if order.status == 'pending'),
                    'filled_orders': sum(1 for order in orders if order.status == 'filled'),
                    'failed_orders': sum(1 for order in orders if order.status == 'failed'),
                    'orders': [
                        {
                            'symbol': order.symbol,
                            'side': order.side,
                            'quantity': order.quantity,
                            'status': order.status,
                            'order_id': order.order_id
                        }
                        for order in orders
                    ]
                }
            
            return active_info
            
        except Exception as e:
            logger.error(f"Error getting active rebalances: {e}")
            return {}


class AllocationEngine:
    """
    Allocation engine for managing target allocations.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.allocation_config = config.get('allocation', {})
        
        # Current target allocation
        self.target_allocation: Dict[str, float] = {}
        self.allocation_history: List[Dict[str, Any]] = []
        
        # State
        self.initialized = False
        self.running = False
    
    async def initialize(self) -> bool:
        """Initialize allocation engine"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Allocation Engine...")
            self.initialized = True
            logger.info("✓ Allocation Engine initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Allocation Engine: {e}")
            return False
    
    async def start(self) -> bool:
        """Start allocation engine"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        self.running = True
        logger.info("✓ Allocation Engine started")
        return True
    
    async def stop(self) -> bool:
        """Stop allocation engine"""
        self.running = False
        logger.info("✓ Allocation Engine stopped")
        return True
    
    async def update_target_allocation(self, allocation: Dict[str, float]) -> bool:
        """Update target allocation"""
        try:
            # Validate allocation (should sum to 1.0)
            total_weight = sum(allocation.values())
            if abs(total_weight - 1.0) > 0.01:  # Allow 1% tolerance
                logger.warning(f"Allocation weights sum to {total_weight:.3f}, not 1.0")
            
            # Store previous allocation
            previous_allocation = self.target_allocation.copy()
            
            # Update target allocation
            self.target_allocation = allocation.copy()
            
            # Record in history
            allocation_record = {
                'timestamp': time.time(),
                'allocation': allocation.copy(),
                'previous_allocation': previous_allocation,
                'total_weight': total_weight
            }
            self.allocation_history.append(allocation_record)
            
            # Limit history size
            if len(self.allocation_history) > 1000:
                self.allocation_history = self.allocation_history[-1000:]
            
            logger.info(f"Target allocation updated: {len(allocation)} assets")
            return True
            
        except Exception as e:
            logger.error(f"Error updating target allocation: {e}")
            return False
    
    async def get_target_allocation(self) -> Dict[str, float]:
        """Get current target allocation"""
        return self.target_allocation.copy()
    
    async def get_allocation_history(self) -> List[Dict[str, Any]]:
        """Get allocation history"""
        return self.allocation_history.copy()

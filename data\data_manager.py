"""
Data Manager - Central data management interface
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from .market_data_manager import MarketDataManager
from .database_manager import DatabaseManager

logger = logging.getLogger(__name__)


class DataManager:
    """
    Central data management interface that coordinates market data,
    database operations, and data distribution.
    """
    
    def __init__(self, config: Dict[str, Any]):
        # Handle config format - ensure it's a dict
        if isinstance(config, str):
            # If config is a string (like a file path), create a minimal config
            self.config = {
                'market_data': {
                    'providers': [],
                    'symbols': {'stocks': ['AAPL', 'TSLA', 'GOOGL']},
                    'update_intervals': {'real_time': 1, 'minute': 60}
                },
                'database': {
                    'type': 'memory',
                    'connection_string': 'memory://'
                }
            }
        else:
            self.config = config

        # Initialize sub-managers
        self.market_data_manager = MarketDataManager(self.config)
        self.database_manager = DatabaseManager(self.config)
        
        # State
        self.initialized = False
        self.running = False
        
    async def initialize(self):
        """Initialize the data manager"""
        if self.initialized:
            return
            
        logger.info("Initializing Data Manager...")
        
        try:
            # Initialize sub-managers
            await self.market_data_manager.initialize()
            await self.database_manager.initialize()
            
            self.initialized = True
            logger.info("✓ Data Manager initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Data Manager: {e}")
            raise
            
    async def start(self):
        """Start the data manager"""
        if not self.initialized:
            await self.initialize()
            
        if self.running:
            return
            
        logger.info("Starting Data Manager...")
        
        # Start market data collection
        await self.market_data_manager.start()
        
        self.running = True
        logger.info("✓ Data Manager started")
        
    async def stop(self):
        """Stop the data manager"""
        if not self.running:
            return
            
        logger.info("Stopping Data Manager...")
        
        # Stop market data collection
        await self.market_data_manager.stop()
        
        self.running = False
        logger.info("✓ Data Manager stopped")
        
    # Market Data Methods
    
    async def get_market_data(self, symbol: str, timeframe: str = "1d", limit: int = 100) -> List[Dict[str, Any]]:
        """Get market data for a symbol"""
        if timeframe == "current":
            current_data = await self.market_data_manager.get_current_data(symbol)
            return [current_data] if current_data else []
        else:
            return await self.market_data_manager.get_historical_data(symbol, limit=limit)
            
    async def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for a symbol"""
        current_data = await self.market_data_manager.get_current_data(symbol)
        return current_data.get('close') if current_data else None
        
    async def get_historical_data(self, symbol: str, start_date: str, end_date: str, 
                                 timeframe: str = "1d") -> List[Dict[str, Any]]:
        """Get historical data for a symbol between dates"""
        # For now, return recent historical data
        # In a real implementation, this would query the database for specific date ranges
        historical_data = await self.market_data_manager.get_historical_data(symbol, limit=1000)
        
        # Filter by date range (simplified)
        try:
            start_timestamp = datetime.fromisoformat(start_date).timestamp()
            end_timestamp = datetime.fromisoformat(end_date).timestamp()
            
            filtered_data = [
                data for data in historical_data
                if start_timestamp <= data.get('timestamp', 0) <= end_timestamp
            ]
            
            return filtered_data
            
        except Exception as e:
            logger.warning(f"Date filtering failed: {e}, returning all historical data")
            return historical_data
            
    async def subscribe_to_symbol(self, symbol: str) -> bool:
        """Subscribe to real-time data for a symbol"""
        return await self.market_data_manager.subscribe_to_symbol(symbol)
        
    async def unsubscribe_from_symbol(self, symbol: str) -> bool:
        """Unsubscribe from a symbol"""
        return await self.market_data_manager.unsubscribe_from_symbol(symbol)
        
    async def get_market_summary(self) -> Dict[str, Any]:
        """Get market summary"""
        return await self.market_data_manager.get_market_summary()
        
    # Database Methods
    
    async def store_market_data(self, symbol: str, data: List[Dict[str, Any]]) -> bool:
        """Store market data in database"""
        try:
            # Store in database
            await self.database_manager.store_market_data(symbol, data)
            return True
        except Exception as e:
            logger.error(f"Failed to store market data: {e}")
            return False
            
    async def get_stored_data(self, symbol: str, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """Get stored data from database"""
        try:
            return await self.database_manager.get_market_data(symbol, start_date, end_date)
        except Exception as e:
            logger.error(f"Failed to get stored data: {e}")
            return []
            
    async def store_trade_data(self, trade_data: Dict[str, Any]) -> bool:
        """Store trade data"""
        try:
            await self.database_manager.store_trade(trade_data)
            return True
        except Exception as e:
            logger.error(f"Failed to store trade data: {e}")
            return False
            
    async def get_trade_history(self, symbol: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get trade history"""
        try:
            return await self.database_manager.get_trades(symbol=symbol, limit=limit)
        except Exception as e:
            logger.error(f"Failed to get trade history: {e}")
            return []
            
    # Analytics Methods
    
    async def calculate_technical_indicators(self, symbol: str, indicators: List[str]) -> Dict[str, Any]:
        """Calculate technical indicators for a symbol"""
        historical_data = await self.get_market_data(symbol, limit=200)
        
        if not historical_data:
            return {}
            
        # Extract price data
        prices = [float(data.get('close', 0)) for data in historical_data if data.get('close')]
        volumes = [float(data.get('volume', 0)) for data in historical_data if data.get('volume')]
        
        if not prices:
            return {}
            
        results = {}
        
        for indicator in indicators:
            try:
                if indicator.lower() == 'sma_20':
                    results['sma_20'] = self._calculate_sma(prices, 20)
                elif indicator.lower() == 'sma_50':
                    results['sma_50'] = self._calculate_sma(prices, 50)
                elif indicator.lower() == 'rsi':
                    results['rsi'] = self._calculate_rsi(prices, 14)
                elif indicator.lower() == 'macd':
                    results['macd'] = self._calculate_macd(prices)
                elif indicator.lower() == 'bollinger_bands':
                    results['bollinger_bands'] = self._calculate_bollinger_bands(prices, 20)
                    
            except Exception as e:
                logger.warning(f"Failed to calculate {indicator}: {e}")
                results[indicator] = None
                
        return results
        
    def _calculate_sma(self, prices: List[float], period: int) -> Optional[float]:
        """Calculate Simple Moving Average"""
        if len(prices) < period:
            return None
        return sum(prices[-period:]) / period
        
    def _calculate_rsi(self, prices: List[float], period: int = 14) -> Optional[float]:
        """Calculate Relative Strength Index"""
        if len(prices) < period + 1:
            return None
            
        gains = []
        losses = []
        
        for i in range(1, len(prices)):
            change = prices[i] - prices[i-1]
            if change > 0:
                gains.append(change)
                losses.append(0)
            else:
                gains.append(0)
                losses.append(abs(change))
                
        if len(gains) < period:
            return None
            
        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period
        
        if avg_loss == 0:
            return 100
            
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
        
    def _calculate_macd(self, prices: List[float]) -> Optional[Dict[str, float]]:
        """Calculate MACD"""
        if len(prices) < 26:
            return None
            
        # Calculate EMAs
        ema_12 = self._calculate_ema(prices, 12)
        ema_26 = self._calculate_ema(prices, 26)
        
        if ema_12 is None or ema_26 is None:
            return None
            
        macd_line = ema_12 - ema_26
        
        return {
            'macd': macd_line,
            'ema_12': ema_12,
            'ema_26': ema_26
        }
        
    def _calculate_ema(self, prices: List[float], period: int) -> Optional[float]:
        """Calculate Exponential Moving Average"""
        if len(prices) < period:
            return None
            
        multiplier = 2 / (period + 1)
        ema = prices[0]
        
        for price in prices[1:]:
            ema = (price * multiplier) + (ema * (1 - multiplier))
            
        return ema
        
    def _calculate_bollinger_bands(self, prices: List[float], period: int = 20) -> Optional[Dict[str, float]]:
        """Calculate Bollinger Bands"""
        if len(prices) < period:
            return None
            
        recent_prices = prices[-period:]
        sma = sum(recent_prices) / period
        
        # Calculate standard deviation
        variance = sum((price - sma) ** 2 for price in recent_prices) / period
        std_dev = variance ** 0.5
        
        return {
            'upper_band': sma + (2 * std_dev),
            'middle_band': sma,
            'lower_band': sma - (2 * std_dev)
        }
        
    async def get_stats(self) -> Dict[str, Any]:
        """Get data manager statistics"""
        market_stats = await self.market_data_manager.get_stats()
        
        return {
            'running': self.running,
            'market_data': market_stats,
            'database_connected': self.database_manager.connected if hasattr(self.database_manager, 'connected') else False
        }

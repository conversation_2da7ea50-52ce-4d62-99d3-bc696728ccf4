<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Ollama Trading Agents - Dashboard</title>
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-bg: #1e293b;
            --card-bg: #f8fafc;
        }

        body {
            background-color: #f1f5f9;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .metric-card {
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            border-left: 4px solid var(--primary-color);
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .metric-label {
            color: var(--secondary-color);
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.5px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-online { background-color: var(--success-color); }
        .status-warning { background-color: var(--warning-color); }
        .status-offline { background-color: var(--danger-color); }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }

        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
        }

        .alert {
            border: none;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .alert-success { border-left-color: var(--success-color); }
        .alert-warning { border-left-color: var(--warning-color); }
        .alert-danger { border-left-color: var(--danger-color); }

        .sidebar {
            background: var(--dark-bg);
            min-height: calc(100vh - 76px);
            padding: 20px 0;
        }

        .sidebar .nav-link {
            color: #cbd5e1;
            padding: 12px 20px;
            border-radius: 0;
            transition: all 0.2s;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }

        .sidebar .nav-link i {
            width: 20px;
            margin-right: 10px;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .real-time-indicator {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        @media (max-width: 768px) {
            .sidebar {
                min-height: auto;
            }
            
            .metric-value {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-robot me-2"></i>
                Advanced Ollama Trading Agents
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="connectionStatus">
                            <span class="status-indicator status-online real-time-indicator"></span>
                            Connected
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#settingsModal">
                            <i class="fas fa-cog"></i> Settings
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 p-0">
                <div class="sidebar">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#dashboard" data-section="dashboard">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#agents" data-section="agents">
                                <i class="fas fa-robot"></i> Agents
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#strategies" data-section="strategies">
                                <i class="fas fa-chart-line"></i> Strategies
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#portfolio" data-section="portfolio">
                                <i class="fas fa-briefcase"></i> Portfolio
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#orders" data-section="orders">
                                <i class="fas fa-list-alt"></i> Orders
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#performance" data-section="performance">
                                <i class="fas fa-chart-bar"></i> Performance
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#monitoring" data-section="monitoring">
                                <i class="fas fa-heartbeat"></i> Monitoring
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#logs" data-section="logs">
                                <i class="fas fa-file-alt"></i> Logs
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-10">
                <div class="container-fluid py-4">
                    
                    <!-- Dashboard Section -->
                    <div id="dashboard-section" class="content-section">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2><i class="fas fa-tachometer-alt me-2"></i>System Dashboard</h2>
                            <div>
                                <button class="btn btn-primary me-2" onclick="refreshDashboard()">
                                    <i class="fas fa-sync-alt"></i> Refresh
                                </button>
                                <span class="badge bg-success">Live</span>
                            </div>
                        </div>

                        <!-- Key Metrics Row -->
                        <div class="row mb-4">
                            <div class="col-md-3 mb-3">
                                <div class="card metric-card">
                                    <div class="card-body text-center">
                                        <div class="metric-value" id="portfolioValue">$0</div>
                                        <div class="metric-label">Portfolio Value</div>
                                        <small class="text-success" id="portfolioChange">+0.00%</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card metric-card">
                                    <div class="card-body text-center">
                                        <div class="metric-value" id="activeAgents">0</div>
                                        <div class="metric-label">Active Agents</div>
                                        <small class="text-muted" id="agentStatus">All systems operational</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card metric-card">
                                    <div class="card-body text-center">
                                        <div class="metric-value" id="runningStrategies">0</div>
                                        <div class="metric-label">Running Strategies</div>
                                        <small class="text-info" id="strategyStatus">Optimizing performance</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card metric-card">
                                    <div class="card-body text-center">
                                        <div class="metric-value" id="dailyPnL">$0</div>
                                        <div class="metric-label">Daily P&L</div>
                                        <small class="text-success" id="pnlChange">+0.00%</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Charts Row -->
                        <div class="row mb-4">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-chart-line me-2"></i>Portfolio Performance</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container">
                                            <canvas id="portfolioChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-pie-chart me-2"></i>Asset Allocation</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container">
                                            <canvas id="allocationChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System Status Row -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-heartbeat me-2"></i>System Health</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="d-flex align-items-center mb-3">
                                                    <span class="status-indicator status-online"></span>
                                                    <span>API Server</span>
                                                    <span class="ms-auto badge bg-success">Online</span>
                                                </div>
                                                <div class="d-flex align-items-center mb-3">
                                                    <span class="status-indicator status-online"></span>
                                                    <span>Database</span>
                                                    <span class="ms-auto badge bg-success">Connected</span>
                                                </div>
                                                <div class="d-flex align-items-center mb-3">
                                                    <span class="status-indicator status-online"></span>
                                                    <span>Market Data</span>
                                                    <span class="ms-auto badge bg-success">Streaming</span>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="d-flex align-items-center mb-3">
                                                    <span class="status-indicator status-online"></span>
                                                    <span>Broker Connection</span>
                                                    <span class="ms-auto badge bg-success">Active</span>
                                                </div>
                                                <div class="d-flex align-items-center mb-3">
                                                    <span class="status-indicator status-warning"></span>
                                                    <span>Risk Monitor</span>
                                                    <span class="ms-auto badge bg-warning">Warning</span>
                                                </div>
                                                <div class="d-flex align-items-center mb-3">
                                                    <span class="status-indicator status-online"></span>
                                                    <span>Ollama Models</span>
                                                    <span class="ms-auto badge bg-success">Ready</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-exclamation-triangle me-2"></i>Recent Alerts</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="alertsList">
                                            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                                <strong>Risk Alert:</strong> Portfolio exposure exceeds 80% threshold.
                                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                            </div>
                                            <div class="alert alert-info alert-dismissible fade show" role="alert">
                                                <strong>Strategy Update:</strong> Momentum strategy performance improved by 2.3%.
                                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5><i class="fas fa-clock me-2"></i>Recent Activity</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-hover" id="activityTable">
                                                <thead>
                                                    <tr>
                                                        <th>Time</th>
                                                        <th>Type</th>
                                                        <th>Description</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>10:30:15</td>
                                                        <td><span class="badge bg-primary">Order</span></td>
                                                        <td>Buy 100 AAPL @ $150.25</td>
                                                        <td><span class="badge bg-success">Filled</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td>10:28:42</td>
                                                        <td><span class="badge bg-info">Strategy</span></td>
                                                        <td>Momentum strategy triggered for TSLA</td>
                                                        <td><span class="badge bg-warning">Pending</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td>10:25:33</td>
                                                        <td><span class="badge bg-secondary">Agent</span></td>
                                                        <td>Risk agent updated position limits</td>
                                                        <td><span class="badge bg-success">Complete</span></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Other sections will be loaded dynamically -->
                    <div id="agents-section" class="content-section d-none">
                        <h2><i class="fas fa-robot me-2"></i>Trading Agents</h2>
                        <p>Agent management interface will be loaded here...</p>
                    </div>

                    <div id="strategies-section" class="content-section d-none">
                        <h2><i class="fas fa-chart-line me-2"></i>Trading Strategies</h2>
                        <p>Strategy management interface will be loaded here...</p>
                    </div>

                    <div id="portfolio-section" class="content-section d-none">
                        <h2><i class="fas fa-briefcase me-2"></i>Portfolio Management</h2>
                        <p>Portfolio details will be loaded here...</p>
                    </div>

                    <div id="orders-section" class="content-section d-none">
                        <h2><i class="fas fa-list-alt me-2"></i>Order Management</h2>
                        <p>Order management interface will be loaded here...</p>
                    </div>

                    <div id="performance-section" class="content-section d-none">
                        <h2><i class="fas fa-chart-bar me-2"></i>Performance Analytics</h2>
                        <p>Performance analytics will be loaded here...</p>
                    </div>

                    <div id="monitoring-section" class="content-section d-none">
                        <h2><i class="fas fa-heartbeat me-2"></i>System Monitoring</h2>
                        <p>System monitoring interface will be loaded here...</p>
                    </div>

                    <div id="logs-section" class="content-section d-none">
                        <h2><i class="fas fa-file-alt me-2"></i>System Logs</h2>
                        <p>System logs will be loaded here...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal fade" id="settingsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Settings</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label for="refreshInterval" class="form-label">Refresh Interval (seconds)</label>
                            <input type="number" class="form-control" id="refreshInterval" value="5">
                        </div>
                        <div class="mb-3">
                            <label for="theme" class="form-label">Theme</label>
                            <select class="form-select" id="theme">
                                <option value="light">Light</option>
                                <option value="dark">Dark</option>
                            </select>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
                            <label class="form-check-label" for="enableNotifications">
                                Enable Notifications
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="js/dashboard.js"></script>
</body>
</html>

"""
Market Data Integration & Feeds - Comprehensive market data management system
"""

import asyncio
import logging
import time
import json
import websockets
import aiohttp
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict, deque
import sqlite3
from datetime import datetime, timedelta
import threading
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


class DataProvider(Enum):
    """Market data providers"""
    ALPHA_VANTAGE = "alpha_vantage"
    YAHOO_FINANCE = "yahoo_finance"
    POLYGON = "polygon"
    FINNHUB = "finnhub"
    IEX_CLOUD = "iex_cloud"
    QUANDL = "quandl"
    MOCK_PROVIDER = "mock_provider"


class DataType(Enum):
    """Types of market data"""
    REAL_TIME_QUOTES = "real_time_quotes"
    HISTORICAL_PRICES = "historical_prices"
    INTRADAY_BARS = "intraday_bars"
    DAILY_BARS = "daily_bars"
    OPTIONS_DATA = "options_data"
    FUNDAMENTALS = "fundamentals"
    NEWS = "news"
    ECONOMIC_DATA = "economic_data"
    SENTIMENT_DATA = "sentiment_data"


class DataQuality(Enum):
    """Data quality levels"""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    INVALID = "invalid"


@dataclass
class MarketDataPoint:
    """Individual market data point"""
    symbol: str
    timestamp: float
    data_type: DataType
    provider: DataProvider
    data: Dict[str, Any]
    quality_score: float
    latency_ms: float
    source_timestamp: Optional[float] = None


@dataclass
class DataFeed:
    """Data feed configuration"""
    feed_id: str
    provider: DataProvider
    data_type: DataType
    symbols: List[str]
    update_frequency: float  # seconds
    enabled: bool
    priority: int
    quality_threshold: float
    retry_count: int
    timeout: float


@dataclass
class DataQualityMetrics:
    """Data quality assessment metrics"""
    completeness: float  # 0-1
    accuracy: float     # 0-1
    timeliness: float   # 0-1
    consistency: float  # 0-1
    overall_score: float # 0-1
    issues: List[str]


class MarketDataIntegration:
    """
    Comprehensive market data integration system that manages multiple data providers,
    real-time feeds, historical data, and data quality validation.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.data_config = config.get('market_data', {})
        
        # Data providers and feeds
        self.providers: Dict[DataProvider, Any] = {}
        self.active_feeds: Dict[str, DataFeed] = {}
        self.feed_connections: Dict[str, Any] = {}
        
        # Data storage and caching
        self.real_time_cache: Dict[str, MarketDataPoint] = {}
        self.historical_cache: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self.data_buffer: deque = deque(maxlen=100000)
        
        # Data quality management
        self.quality_metrics: Dict[str, DataQualityMetrics] = {}
        self.quality_thresholds = self.data_config.get('quality_thresholds', {
            'completeness': 0.95,
            'accuracy': 0.98,
            'timeliness': 0.90,
            'consistency': 0.95
        })
        
        # Subscribers and callbacks
        self.subscribers: Dict[DataType, List[Callable]] = defaultdict(list)
        self.symbol_subscribers: Dict[str, List[Callable]] = defaultdict(list)
        
        # Performance tracking
        self.feed_performance: Dict[str, Dict[str, float]] = defaultdict(dict)
        self.provider_performance: Dict[DataProvider, Dict[str, float]] = defaultdict(dict)
        
        # Configuration
        self.max_concurrent_requests = self.data_config.get('max_concurrent_requests', 10)
        self.data_retention_days = self.data_config.get('data_retention_days', 30)
        self.quality_check_interval = self.data_config.get('quality_check_interval', 300)
        
        # State management
        self.initialized = False
        self.running = False
        
        # Background tasks
        self.data_tasks: List[asyncio.Task] = []
        self.executor = ThreadPoolExecutor(max_workers=5)
        
        # Database connection
        self.db_connection = None
        
    async def initialize(self) -> bool:
        """Initialize the market data integration system"""
        try:
            logger.info("Initializing Market Data Integration System...")
            
            # Setup database
            await self._setup_database()
            
            # Initialize data providers
            await self._initialize_providers()
            
            # Setup data feeds
            await self._setup_data_feeds()
            
            # Initialize data quality system
            await self._initialize_quality_system()
            
            # Setup data storage
            await self._setup_data_storage()
            
            # Load historical data cache
            await self._load_historical_cache()
            
            self.initialized = True
            logger.info("✅ Market Data Integration System initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Market Data Integration System: {e}")
            return False
            
    async def start(self) -> bool:
        """Start the market data integration system"""
        try:
            if not self.initialized:
                await self.initialize()
                
            if self.running:
                return True
                
            logger.info("Starting Market Data Integration System...")
            
            # Start data feeds
            await self._start_data_feeds()
            
            # Start background tasks
            self.data_tasks = [
                asyncio.create_task(self._data_quality_monitor()),
                asyncio.create_task(self._data_cleanup_task()),
                asyncio.create_task(self._performance_monitor()),
                asyncio.create_task(self._feed_health_monitor()),
                asyncio.create_task(self._data_persistence_task())
            ]
            
            self.running = True
            logger.info("✅ Market Data Integration System started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Market Data Integration System: {e}")
            return False
            
    async def stop(self) -> bool:
        """Stop the market data integration system"""
        try:
            if not self.running:
                return True
                
            logger.info("Stopping Market Data Integration System...")
            
            # Stop data feeds
            await self._stop_data_feeds()
            
            # Cancel background tasks
            for task in self.data_tasks:
                task.cancel()
            await asyncio.gather(*self.data_tasks, return_exceptions=True)
            self.data_tasks.clear()
            
            # Close database connection
            if self.db_connection:
                self.db_connection.close()
                
            # Shutdown executor
            self.executor.shutdown(wait=True)
            
            self.running = False
            logger.info("✅ Market Data Integration System stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop Market Data Integration System: {e}")
            return False
            
    async def subscribe_to_data(self, data_type: DataType, callback: Callable, 
                              symbols: Optional[List[str]] = None) -> str:
        """Subscribe to market data updates"""
        try:
            subscription_id = f"sub_{int(time.time())}_{len(self.subscribers[data_type])}"
            
            if symbols:
                # Symbol-specific subscription
                for symbol in symbols:
                    self.symbol_subscribers[symbol].append(callback)
            else:
                # General data type subscription
                self.subscribers[data_type].append(callback)
                
            logger.info(f"Created subscription {subscription_id} for {data_type.value}")
            return subscription_id
            
        except Exception as e:
            logger.error(f"Error creating subscription: {e}")
            return ""
            
    async def get_real_time_data(self, symbol: str, data_type: DataType) -> Optional[MarketDataPoint]:
        """Get real-time data for a symbol"""
        try:
            cache_key = f"{symbol}_{data_type.value}"
            return self.real_time_cache.get(cache_key)
            
        except Exception as e:
            logger.error(f"Error getting real-time data: {e}")
            return None
            
    async def get_historical_data(self, symbol: str, data_type: DataType,
                                start_time: float, end_time: float,
                                provider: Optional[DataProvider] = None) -> List[MarketDataPoint]:
        """Get historical data for a symbol"""
        try:
            # Check cache first
            cache_key = f"{symbol}_{data_type.value}"
            cached_data = self.historical_cache.get(cache_key, deque())
            
            # Filter by time range
            filtered_data = [
                point for point in cached_data
                if start_time <= point.timestamp <= end_time
            ]
            
            # If insufficient data, fetch from provider
            if len(filtered_data) < 100 and provider:
                additional_data = await self._fetch_historical_data(
                    symbol, data_type, start_time, end_time, provider
                )
                filtered_data.extend(additional_data)
                
            return sorted(filtered_data, key=lambda x: x.timestamp)
            
        except Exception as e:
            logger.error(f"Error getting historical data: {e}")
            return []
            
    async def add_data_feed(self, feed_config: Dict[str, Any]) -> str:
        """Add a new data feed"""
        try:
            feed = DataFeed(
                feed_id=feed_config.get('feed_id', f"feed_{int(time.time())}"),
                provider=DataProvider(feed_config['provider']),
                data_type=DataType(feed_config['data_type']),
                symbols=feed_config['symbols'],
                update_frequency=feed_config.get('update_frequency', 60),
                enabled=feed_config.get('enabled', True),
                priority=feed_config.get('priority', 1),
                quality_threshold=feed_config.get('quality_threshold', 0.8),
                retry_count=feed_config.get('retry_count', 3),
                timeout=feed_config.get('timeout', 30)
            )
            
            self.active_feeds[feed.feed_id] = feed
            
            if self.running and feed.enabled:
                await self._start_feed(feed)
                
            logger.info(f"Added data feed {feed.feed_id}")
            return feed.feed_id
            
        except Exception as e:
            logger.error(f"Error adding data feed: {e}")
            return ""
            
    async def get_data_quality_report(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """Get data quality report"""
        try:
            if symbol:
                # Symbol-specific quality report
                quality_metrics = self.quality_metrics.get(symbol)
                if quality_metrics:
                    return {
                        'symbol': symbol,
                        'quality_metrics': asdict(quality_metrics),
                        'last_updated': time.time()
                    }
                else:
                    return {'error': f'No quality metrics for symbol {symbol}'}
            else:
                # Overall quality report
                overall_metrics = {}
                for sym, metrics in self.quality_metrics.items():
                    overall_metrics[sym] = asdict(metrics)
                    
                return {
                    'overall_quality': overall_metrics,
                    'total_symbols': len(overall_metrics),
                    'average_quality': np.mean([
                        m.overall_score for m in self.quality_metrics.values()
                    ]) if self.quality_metrics else 0.0,
                    'last_updated': time.time()
                }
                
        except Exception as e:
            logger.error(f"Error getting quality report: {e}")
            return {'error': str(e)}
            
    async def get_feed_status(self) -> Dict[str, Any]:
        """Get status of all data feeds"""
        try:
            feed_status = {}
            
            for feed_id, feed in self.active_feeds.items():
                performance = self.feed_performance.get(feed_id, {})
                
                feed_status[feed_id] = {
                    'provider': feed.provider.value,
                    'data_type': feed.data_type.value,
                    'symbols': feed.symbols,
                    'enabled': feed.enabled,
                    'connected': feed_id in self.feed_connections,
                    'update_frequency': feed.update_frequency,
                    'performance': performance,
                    'last_update': performance.get('last_update', 0)
                }
                
            return {
                'feeds': feed_status,
                'total_feeds': len(self.active_feeds),
                'active_feeds': len([f for f in self.active_feeds.values() if f.enabled]),
                'connected_feeds': len(self.feed_connections),
                'system_running': self.running
            }
            
        except Exception as e:
            logger.error(f"Error getting feed status: {e}")
            return {'error': str(e)}

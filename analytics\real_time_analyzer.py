"""
Real-time Analyzer - High-frequency market data analysis
"""

import asyncio
import logging
import time
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from collections import deque
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


@dataclass
class TickAnalysis:
    """Real-time tick analysis result"""
    symbol: str
    timestamp: datetime
    price_change: float
    volume_spike: bool
    volatility_burst: bool
    momentum_shift: bool
    liquidity_change: float
    microstructure_signals: Dict[str, Any]


class RealTimeAnalyzer:
    """
    Real-time market data analyzer for high-frequency analysis.
    
    Features:
    - Tick-by-tick analysis
    - Microstructure analysis
    - Volume profile analysis
    - Momentum detection
    - Liquidity analysis
    - Order flow analysis
    - Market impact measurement
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.rt_config = config.get('real_time_analyzer', {})
        
        # Real-time data storage
        self.tick_data: Dict[str, deque] = {}
        self.volume_profile: Dict[str, Dict[float, float]] = {}
        self.order_flow: Dict[str, deque] = {}
        
        # Analysis parameters
        self.tick_window_size = self.rt_config.get('tick_window_size', 100)
        self.volume_spike_threshold = self.rt_config.get('volume_spike_threshold', 2.0)
        self.volatility_threshold = self.rt_config.get('volatility_threshold', 0.02)
        self.momentum_threshold = self.rt_config.get('momentum_threshold', 0.01)
        
        # Analysis state
        self.baseline_metrics: Dict[str, Dict[str, float]] = {}
        self.alert_conditions: Dict[str, List[str]] = {}
        
        # Performance tracking
        self.analysis_count = 0
        self.processing_times: deque = deque(maxlen=1000)
        
        # State
        self.initialized = False
        self.running = False
    
    async def initialize(self) -> bool:
        """Initialize real-time analyzer"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Real-time Analyzer...")
            
            # Initialize data structures
            await self._initialize_data_structures()
            
            # Setup baseline metrics
            await self._setup_baseline_metrics()
            
            self.initialized = True
            logger.info("✓ Real-time Analyzer initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Real-time Analyzer: {e}")
            return False
    
    async def start(self) -> bool:
        """Start real-time analyzer"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        self.running = True
        logger.info("✓ Real-time Analyzer started")
        return True
    
    async def stop(self) -> bool:
        """Stop real-time analyzer"""
        self.running = False
        logger.info("✓ Real-time Analyzer stopped")
        return True
    
    async def analyze_tick(self, symbol: str, tick_data: Dict[str, Any]) -> Optional[TickAnalysis]:
        """Analyze individual tick data"""
        try:
            start_time = time.time()
            
            # Store tick data
            if symbol not in self.tick_data:
                self.tick_data[symbol] = deque(maxlen=self.tick_window_size)
            
            self.tick_data[symbol].append({
                'timestamp': datetime.now(),
                'price': tick_data.get('price', 0.0),
                'volume': tick_data.get('volume', 0.0),
                'bid': tick_data.get('bid', 0.0),
                'ask': tick_data.get('ask', 0.0)
            })
            
            # Perform real-time analysis
            analysis = await self._perform_tick_analysis(symbol, tick_data)
            
            # Update performance metrics
            processing_time = (time.time() - start_time) * 1000
            self.processing_times.append(processing_time)
            self.analysis_count += 1
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing tick for {symbol}: {e}")
            return None
    
    async def analyze_symbol(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze symbol with accumulated data"""
        try:
            if symbol not in self.tick_data or len(self.tick_data[symbol]) < 10:
                return {'success': False, 'error': 'Insufficient tick data'}
            
            ticks = list(self.tick_data[symbol])
            
            # Perform comprehensive analysis
            analysis_results = {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'tick_count': len(ticks),
                'analysis': {}
            }
            
            # Price action analysis
            analysis_results['analysis']['price_action'] = await self._analyze_price_action(ticks)
            
            # Volume analysis
            analysis_results['analysis']['volume'] = await self._analyze_volume_patterns(ticks)
            
            # Microstructure analysis
            analysis_results['analysis']['microstructure'] = await self._analyze_microstructure(ticks)
            
            # Momentum analysis
            analysis_results['analysis']['momentum'] = await self._analyze_momentum(ticks)
            
            # Liquidity analysis
            analysis_results['analysis']['liquidity'] = await self._analyze_liquidity(ticks)
            
            return analysis_results
            
        except Exception as e:
            logger.error(f"Error analyzing symbol {symbol}: {e}")
            return {'success': False, 'error': str(e)}
    
    async def get_volume_profile(self, symbol: str) -> Dict[str, Any]:
        """Get volume profile for symbol"""
        try:
            if symbol not in self.volume_profile:
                return {'error': 'No volume profile data available'}
            
            profile = self.volume_profile[symbol]
            
            # Calculate profile statistics
            total_volume = sum(profile.values())
            if total_volume == 0:
                return {'error': 'No volume data'}
            
            # Find volume-weighted average price (VWAP)
            vwap = sum(price * volume for price, volume in profile.items()) / total_volume
            
            # Find point of control (highest volume price level)
            poc_price = max(profile.keys(), key=lambda x: profile[x])
            poc_volume = profile[poc_price]
            
            # Calculate value area (70% of volume)
            sorted_levels = sorted(profile.items(), key=lambda x: x[1], reverse=True)
            cumulative_volume = 0
            value_area_high = value_area_low = poc_price
            
            for price, volume in sorted_levels:
                cumulative_volume += volume
                value_area_high = max(value_area_high, price)
                value_area_low = min(value_area_low, price)
                
                if cumulative_volume >= total_volume * 0.7:
                    break
            
            return {
                'symbol': symbol,
                'vwap': vwap,
                'poc_price': poc_price,
                'poc_volume': poc_volume,
                'value_area_high': value_area_high,
                'value_area_low': value_area_low,
                'total_volume': total_volume,
                'price_levels': len(profile),
                'profile_data': profile
            }
            
        except Exception as e:
            logger.error(f"Error getting volume profile for {symbol}: {e}")
            return {'error': str(e)}
    
    async def get_real_time_metrics(self) -> Dict[str, Any]:
        """Get real-time analyzer metrics"""
        try:
            avg_processing_time = np.mean(self.processing_times) if self.processing_times else 0.0
            
            return {
                'analysis_count': self.analysis_count,
                'average_processing_time_ms': avg_processing_time,
                'active_symbols': len(self.tick_data),
                'total_ticks_processed': sum(len(ticks) for ticks in self.tick_data.values()),
                'volume_profiles_active': len(self.volume_profile),
                'alert_conditions_active': sum(len(alerts) for alerts in self.alert_conditions.values())
            }
            
        except Exception as e:
            logger.error(f"Error getting real-time metrics: {e}")
            return {}
    
    # Private methods
    
    async def _initialize_data_structures(self):
        """Initialize data structures"""
        try:
            # Initialize for configured symbols
            symbols = self.config.get('symbols', {})
            for symbol_group, symbol_list in symbols.items():
                for symbol in symbol_list:
                    self.tick_data[symbol] = deque(maxlen=self.tick_window_size)
                    self.volume_profile[symbol] = {}
                    self.order_flow[symbol] = deque(maxlen=1000)
                    self.alert_conditions[symbol] = []
            
            logger.debug(f"Initialized data structures for {len(self.tick_data)} symbols")
            
        except Exception as e:
            logger.error(f"Error initializing data structures: {e}")
            raise
    
    async def _setup_baseline_metrics(self):
        """Setup baseline metrics for comparison"""
        try:
            for symbol in self.tick_data.keys():
                self.baseline_metrics[symbol] = {
                    'average_volume': 0.0,
                    'average_volatility': 0.0,
                    'average_spread': 0.0,
                    'average_tick_size': 0.0
                }
            
            logger.debug("Baseline metrics initialized")
            
        except Exception as e:
            logger.error(f"Error setting up baseline metrics: {e}")
            raise
    
    async def _perform_tick_analysis(self, symbol: str, tick_data: Dict[str, Any]) -> Optional[TickAnalysis]:
        """Perform analysis on individual tick"""
        try:
            if len(self.tick_data[symbol]) < 2:
                return None
            
            current_tick = self.tick_data[symbol][-1]
            previous_tick = self.tick_data[symbol][-2]
            
            # Calculate price change
            price_change = (current_tick['price'] - previous_tick['price']) / previous_tick['price'] if previous_tick['price'] != 0 else 0.0
            
            # Detect volume spike
            recent_volumes = [tick['volume'] for tick in list(self.tick_data[symbol])[-10:]]
            avg_volume = np.mean(recent_volumes) if recent_volumes else 0.0
            volume_spike = current_tick['volume'] > avg_volume * self.volume_spike_threshold if avg_volume > 0 else False
            
            # Detect volatility burst
            recent_prices = [tick['price'] for tick in list(self.tick_data[symbol])[-10:]]
            if len(recent_prices) >= 2:
                returns = [(recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1] for i in range(1, len(recent_prices)) if recent_prices[i-1] != 0]
                current_volatility = np.std(returns) if returns else 0.0
                volatility_burst = current_volatility > self.volatility_threshold
            else:
                volatility_burst = False
            
            # Detect momentum shift
            momentum_shift = abs(price_change) > self.momentum_threshold
            
            # Calculate liquidity change
            spread = current_tick['ask'] - current_tick['bid'] if current_tick['ask'] > current_tick['bid'] else 0.0
            prev_spread = previous_tick['ask'] - previous_tick['bid'] if previous_tick['ask'] > previous_tick['bid'] else 0.0
            liquidity_change = (spread - prev_spread) / prev_spread if prev_spread != 0 else 0.0
            
            # Microstructure signals
            microstructure_signals = {
                'bid_ask_spread': spread,
                'mid_price': (current_tick['bid'] + current_tick['ask']) / 2 if current_tick['bid'] > 0 and current_tick['ask'] > 0 else current_tick['price'],
                'tick_direction': 1 if price_change > 0 else -1 if price_change < 0 else 0,
                'volume_imbalance': self._calculate_volume_imbalance(symbol)
            }
            
            # Update volume profile
            await self._update_volume_profile(symbol, current_tick)
            
            return TickAnalysis(
                symbol=symbol,
                timestamp=current_tick['timestamp'],
                price_change=price_change,
                volume_spike=volume_spike,
                volatility_burst=volatility_burst,
                momentum_shift=momentum_shift,
                liquidity_change=liquidity_change,
                microstructure_signals=microstructure_signals
            )
            
        except Exception as e:
            logger.error(f"Error performing tick analysis: {e}")
            return None
    
    async def _analyze_price_action(self, ticks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze price action patterns"""
        try:
            prices = [tick['price'] for tick in ticks]
            
            # Calculate price statistics
            price_range = max(prices) - min(prices)
            price_volatility = np.std(prices) if len(prices) > 1 else 0.0
            
            # Trend analysis
            if len(prices) >= 3:
                recent_trend = (prices[-1] - prices[-3]) / prices[-3] if prices[-3] != 0 else 0.0
            else:
                recent_trend = 0.0
            
            # Support and resistance levels
            support_level = min(prices[-20:]) if len(prices) >= 20 else min(prices)
            resistance_level = max(prices[-20:]) if len(prices) >= 20 else max(prices)
            
            return {
                'price_range': price_range,
                'volatility': price_volatility,
                'recent_trend': recent_trend,
                'support_level': support_level,
                'resistance_level': resistance_level,
                'current_price': prices[-1],
                'price_position': (prices[-1] - support_level) / (resistance_level - support_level) if resistance_level > support_level else 0.5
            }
            
        except Exception as e:
            logger.error(f"Error analyzing price action: {e}")
            return {}
    
    async def _analyze_volume_patterns(self, ticks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze volume patterns"""
        try:
            volumes = [tick['volume'] for tick in ticks]
            
            # Volume statistics
            total_volume = sum(volumes)
            avg_volume = np.mean(volumes) if volumes else 0.0
            volume_volatility = np.std(volumes) if len(volumes) > 1 else 0.0
            
            # Volume trend
            if len(volumes) >= 10:
                recent_volume_trend = (np.mean(volumes[-5:]) - np.mean(volumes[-10:-5])) / np.mean(volumes[-10:-5]) if np.mean(volumes[-10:-5]) != 0 else 0.0
            else:
                recent_volume_trend = 0.0
            
            # Volume spikes
            volume_spikes = sum(1 for vol in volumes if vol > avg_volume * 2) if avg_volume > 0 else 0
            
            return {
                'total_volume': total_volume,
                'average_volume': avg_volume,
                'volume_volatility': volume_volatility,
                'volume_trend': recent_volume_trend,
                'volume_spikes': volume_spikes,
                'current_volume': volumes[-1] if volumes else 0.0
            }
            
        except Exception as e:
            logger.error(f"Error analyzing volume patterns: {e}")
            return {}
    
    async def _analyze_microstructure(self, ticks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze market microstructure"""
        try:
            # Bid-ask spreads
            spreads = []
            for tick in ticks:
                if tick['bid'] > 0 and tick['ask'] > tick['bid']:
                    spreads.append(tick['ask'] - tick['bid'])
            
            avg_spread = np.mean(spreads) if spreads else 0.0
            spread_volatility = np.std(spreads) if len(spreads) > 1 else 0.0
            
            # Price impact analysis
            price_impacts = []
            for i in range(1, len(ticks)):
                if ticks[i-1]['volume'] > 0:
                    price_change = abs(ticks[i]['price'] - ticks[i-1]['price'])
                    volume = ticks[i-1]['volume']
                    price_impacts.append(price_change / volume if volume > 0 else 0.0)
            
            avg_price_impact = np.mean(price_impacts) if price_impacts else 0.0
            
            return {
                'average_spread': avg_spread,
                'spread_volatility': spread_volatility,
                'average_price_impact': avg_price_impact,
                'tick_count': len(ticks),
                'liquidity_score': 1.0 / (avg_spread + 0.001)  # Higher score = better liquidity
            }
            
        except Exception as e:
            logger.error(f"Error analyzing microstructure: {e}")
            return {}
    
    async def _analyze_momentum(self, ticks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze momentum patterns"""
        try:
            prices = [tick['price'] for tick in ticks]
            
            if len(prices) < 3:
                return {'momentum_score': 0.0}
            
            # Calculate momentum indicators
            short_momentum = (prices[-1] - prices[-3]) / prices[-3] if prices[-3] != 0 else 0.0
            medium_momentum = (prices[-1] - prices[-min(10, len(prices))]) / prices[-min(10, len(prices))] if prices[-min(10, len(prices))] != 0 else 0.0
            
            # Momentum acceleration
            if len(prices) >= 6:
                recent_momentum = (prices[-1] - prices[-3]) / prices[-3] if prices[-3] != 0 else 0.0
                previous_momentum = (prices[-3] - prices[-6]) / prices[-6] if prices[-6] != 0 else 0.0
                momentum_acceleration = recent_momentum - previous_momentum
            else:
                momentum_acceleration = 0.0
            
            # Momentum score
            momentum_score = (short_momentum + medium_momentum) / 2
            
            return {
                'short_momentum': short_momentum,
                'medium_momentum': medium_momentum,
                'momentum_acceleration': momentum_acceleration,
                'momentum_score': momentum_score,
                'momentum_direction': 1 if momentum_score > 0 else -1 if momentum_score < 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Error analyzing momentum: {e}")
            return {}
    
    async def _analyze_liquidity(self, ticks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze liquidity conditions"""
        try:
            # Calculate liquidity metrics
            volumes = [tick['volume'] for tick in ticks]
            spreads = []
            
            for tick in ticks:
                if tick['bid'] > 0 and tick['ask'] > tick['bid']:
                    spread = (tick['ask'] - tick['bid']) / tick['price'] if tick['price'] > 0 else 0.0
                    spreads.append(spread)
            
            # Liquidity indicators
            avg_volume = np.mean(volumes) if volumes else 0.0
            avg_spread_pct = np.mean(spreads) if spreads else 0.0
            
            # Liquidity score (higher = more liquid)
            liquidity_score = avg_volume / (avg_spread_pct + 0.001) if avg_spread_pct > 0 else avg_volume
            
            # Market depth proxy
            volume_consistency = 1.0 - (np.std(volumes) / np.mean(volumes)) if volumes and np.mean(volumes) > 0 else 0.0
            
            return {
                'average_volume': avg_volume,
                'average_spread_percentage': avg_spread_pct,
                'liquidity_score': liquidity_score,
                'volume_consistency': volume_consistency,
                'liquidity_rating': 'high' if liquidity_score > 1000 else 'medium' if liquidity_score > 100 else 'low'
            }
            
        except Exception as e:
            logger.error(f"Error analyzing liquidity: {e}")
            return {}
    
    def _calculate_volume_imbalance(self, symbol: str) -> float:
        """Calculate volume imbalance"""
        try:
            if symbol not in self.tick_data or len(self.tick_data[symbol]) < 2:
                return 0.0
            
            recent_ticks = list(self.tick_data[symbol])[-10:]
            buy_volume = sum(tick['volume'] for tick in recent_ticks if tick.get('tick_direction', 0) > 0)
            sell_volume = sum(tick['volume'] for tick in recent_ticks if tick.get('tick_direction', 0) < 0)
            
            total_volume = buy_volume + sell_volume
            if total_volume == 0:
                return 0.0
            
            return (buy_volume - sell_volume) / total_volume
            
        except Exception as e:
            logger.error(f"Error calculating volume imbalance: {e}")
            return 0.0
    
    async def _update_volume_profile(self, symbol: str, tick: Dict[str, Any]):
        """Update volume profile for symbol"""
        try:
            price = tick['price']
            volume = tick['volume']
            
            if symbol not in self.volume_profile:
                self.volume_profile[symbol] = {}
            
            # Round price to nearest tick
            price_level = round(price, 2)  # Adjust precision as needed
            
            if price_level in self.volume_profile[symbol]:
                self.volume_profile[symbol][price_level] += volume
            else:
                self.volume_profile[symbol][price_level] = volume
            
            # Limit profile size
            if len(self.volume_profile[symbol]) > 1000:
                # Remove lowest volume levels
                sorted_levels = sorted(self.volume_profile[symbol].items(), key=lambda x: x[1])
                for price_level, _ in sorted_levels[:100]:
                    del self.volume_profile[symbol][price_level]
            
        except Exception as e:
            logger.error(f"Error updating volume profile: {e}")

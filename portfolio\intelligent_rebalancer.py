"""
Intelligent Portfolio Rebalancer - AI-powered portfolio rebalancing system
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import numpy as np

logger = logging.getLogger(__name__)


class RebalanceReason(Enum):
    """Reasons for portfolio rebalancing"""
    DRIFT_THRESHOLD = "drift_threshold"
    RISK_ADJUSTMENT = "risk_adjustment"
    MARKET_CONDITIONS = "market_conditions"
    PERFORMANCE_OPTIMIZATION = "performance_optimization"
    SCHEDULED = "scheduled"
    MANUAL = "manual"


@dataclass
class RebalanceRecommendation:
    """Portfolio rebalancing recommendation"""
    symbol: str
    current_weight: float
    target_weight: float
    weight_change: float
    current_value: float
    target_value: float
    action: str  # 'buy', 'sell', 'hold'
    quantity_change: float
    confidence: float
    reasoning: str


@dataclass
class RebalanceResult:
    """Result of a rebalancing operation"""
    rebalance_id: str
    timestamp: float
    reason: RebalanceReason
    recommendations: List[RebalanceRecommendation]
    total_trades: int
    executed_trades: int
    total_cost: float
    expected_improvement: float
    success: bool
    error_message: Optional[str] = None


class IntelligentRebalancer:
    """
    Intelligent portfolio rebalancer that uses market conditions,
    risk metrics, and performance data to optimize portfolio allocation.
    """
    
    def __init__(self, portfolio_manager, risk_manager, market_system, config: Dict[str, Any]):
        self.portfolio_manager = portfolio_manager
        self.risk_manager = risk_manager
        self.market_system = market_system
        self.config = config
        self.rebalancer_config = config.get('intelligent_rebalancer', {})
        
        # Rebalancing parameters
        self.drift_threshold = self.rebalancer_config.get('drift_threshold', 0.05)  # 5%
        self.rebalance_frequency = self.rebalancer_config.get('rebalance_frequency', 86400)  # Daily
        self.min_trade_size = self.rebalancer_config.get('min_trade_size', 100.0)
        self.max_turnover = self.rebalancer_config.get('max_turnover', 0.2)  # 20%
        
        # Target allocations
        self.target_allocations = self.rebalancer_config.get('target_allocations', {
            'AAPL': 0.25,
            'TSLA': 0.20,
            'GOOGL': 0.20,
            'MSFT': 0.20,
            'AMZN': 0.15
        })
        
        # State
        self.running = False
        self.last_rebalance = 0.0
        self.rebalance_history: List[RebalanceResult] = []
        
        # Background tasks
        self.rebalancer_tasks: List[asyncio.Task] = []
        
    async def initialize(self) -> bool:
        """Initialize the intelligent rebalancer"""
        try:
            logger.info("Initializing Intelligent Portfolio Rebalancer...")
            
            # Validate target allocations
            total_allocation = sum(self.target_allocations.values())
            if abs(total_allocation - 1.0) > 0.01:
                logger.warning(f"Target allocations sum to {total_allocation:.2%}, adjusting to 100%")
                # Normalize allocations
                for symbol in self.target_allocations:
                    self.target_allocations[symbol] /= total_allocation
            
            logger.info("✅ Intelligent Portfolio Rebalancer initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Intelligent Rebalancer: {e}")
            return False
            
    async def start(self) -> bool:
        """Start the intelligent rebalancer"""
        try:
            if self.running:
                return True
                
            logger.info("Starting Intelligent Portfolio Rebalancer...")
            
            # Start background tasks
            self.rebalancer_tasks = [
                asyncio.create_task(self._rebalancing_loop()),
                asyncio.create_task(self._drift_monitoring_loop())
            ]
            
            self.running = True
            logger.info("✅ Intelligent Portfolio Rebalancer started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Intelligent Rebalancer: {e}")
            return False
            
    async def stop(self) -> bool:
        """Stop the intelligent rebalancer"""
        try:
            if not self.running:
                return True
                
            logger.info("Stopping Intelligent Portfolio Rebalancer...")
            
            # Cancel background tasks
            for task in self.rebalancer_tasks:
                task.cancel()
            await asyncio.gather(*self.rebalancer_tasks, return_exceptions=True)
            self.rebalancer_tasks.clear()
            
            self.running = False
            logger.info("✅ Intelligent Portfolio Rebalancer stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop Intelligent Rebalancer: {e}")
            return False
            
    async def analyze_portfolio_drift(self) -> Dict[str, float]:
        """Analyze current portfolio drift from target allocations"""
        try:
            # Get current portfolio
            portfolio_data = await self.portfolio_manager.get_portfolio_data(
                self.portfolio_manager.portfolio_id
            )
            
            if not portfolio_data:
                return {}
                
            current_weights = await self._calculate_current_weights(portfolio_data)
            
            # Calculate drift for each position
            drift_analysis = {}
            for symbol, target_weight in self.target_allocations.items():
                current_weight = current_weights.get(symbol, 0.0)
                drift = current_weight - target_weight
                drift_analysis[symbol] = {
                    'current_weight': current_weight,
                    'target_weight': target_weight,
                    'drift': drift,
                    'drift_percent': abs(drift) / target_weight if target_weight > 0 else 0.0
                }
                
            return drift_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing portfolio drift: {e}")
            return {}
            
    async def generate_rebalance_recommendations(self, reason: RebalanceReason) -> List[RebalanceRecommendation]:
        """Generate intelligent rebalancing recommendations"""
        try:
            logger.info(f"Generating rebalance recommendations for reason: {reason.value}")
            
            # Get current portfolio state
            portfolio_data = await self.portfolio_manager.get_portfolio_data(
                self.portfolio_manager.portfolio_id
            )
            
            if not portfolio_data:
                return []
                
            current_weights = await self._calculate_current_weights(portfolio_data)
            total_value = portfolio_data.get('total_value', 0.0)
            
            # Get market conditions for intelligent adjustments
            market_conditions = await self.market_system.get_market_conditions()
            
            # Adjust target allocations based on market conditions
            adjusted_targets = await self._adjust_targets_for_market(
                self.target_allocations, market_conditions, reason
            )
            
            # Generate recommendations
            recommendations = []
            
            for symbol, target_weight in adjusted_targets.items():
                current_weight = current_weights.get(symbol, 0.0)
                weight_change = target_weight - current_weight
                
                current_value = current_weight * total_value
                target_value = target_weight * total_value
                value_change = target_value - current_value
                
                # Determine action and confidence
                if abs(value_change) < self.min_trade_size:
                    action = 'hold'
                    confidence = 0.9
                elif value_change > 0:
                    action = 'buy'
                    confidence = await self._calculate_buy_confidence(symbol, market_conditions)
                else:
                    action = 'sell'
                    confidence = await self._calculate_sell_confidence(symbol, market_conditions)
                
                # Calculate quantity change (simplified)
                current_price = await self._get_current_price(symbol)
                quantity_change = value_change / current_price if current_price > 0 else 0.0
                
                # Generate reasoning
                reasoning = await self._generate_reasoning(
                    symbol, action, weight_change, market_conditions, reason
                )
                
                recommendation = RebalanceRecommendation(
                    symbol=symbol,
                    current_weight=current_weight,
                    target_weight=target_weight,
                    weight_change=weight_change,
                    current_value=current_value,
                    target_value=target_value,
                    action=action,
                    quantity_change=quantity_change,
                    confidence=confidence,
                    reasoning=reasoning
                )
                
                recommendations.append(recommendation)
                
            # Sort by absolute weight change (prioritize largest adjustments)
            recommendations.sort(key=lambda r: abs(r.weight_change), reverse=True)
            
            logger.info(f"Generated {len(recommendations)} rebalance recommendations")
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating rebalance recommendations: {e}")
            return []
            
    async def execute_rebalancing(self, 
                                recommendations: List[RebalanceRecommendation],
                                reason: RebalanceReason) -> RebalanceResult:
        """Execute portfolio rebalancing based on recommendations"""
        try:
            rebalance_id = f"rebalance_{int(time.time())}"
            logger.info(f"Executing rebalancing {rebalance_id} for reason: {reason.value}")
            
            executed_trades = 0
            total_cost = 0.0
            
            # Filter recommendations by confidence and minimum trade size
            filtered_recommendations = [
                rec for rec in recommendations 
                if rec.confidence >= 0.7 and abs(rec.quantity_change) * await self._get_current_price(rec.symbol) >= self.min_trade_size
            ]
            
            # Execute trades
            for recommendation in filtered_recommendations:
                if recommendation.action in ['buy', 'sell']:
                    try:
                        # Execute the trade (simplified)
                        success = await self._execute_trade(recommendation)
                        if success:
                            executed_trades += 1
                            total_cost += abs(recommendation.quantity_change) * await self._get_current_price(recommendation.symbol) * 0.001  # 0.1% cost
                            
                    except Exception as e:
                        logger.error(f"Error executing trade for {recommendation.symbol}: {e}")
            
            # Calculate expected improvement
            expected_improvement = await self._calculate_expected_improvement(recommendations)
            
            # Create result
            result = RebalanceResult(
                rebalance_id=rebalance_id,
                timestamp=time.time(),
                reason=reason,
                recommendations=recommendations,
                total_trades=len(filtered_recommendations),
                executed_trades=executed_trades,
                total_cost=total_cost,
                expected_improvement=expected_improvement,
                success=executed_trades > 0
            )
            
            # Store result
            self.rebalance_history.append(result)
            self.last_rebalance = time.time()
            
            logger.info(f"Rebalancing completed: {executed_trades}/{len(filtered_recommendations)} trades executed")
            return result
            
        except Exception as e:
            logger.error(f"Error executing rebalancing: {e}")
            return RebalanceResult(
                rebalance_id=f"failed_{int(time.time())}",
                timestamp=time.time(),
                reason=reason,
                recommendations=[],
                total_trades=0,
                executed_trades=0,
                total_cost=0.0,
                expected_improvement=0.0,
                success=False,
                error_message=str(e)
            )
            
    async def _calculate_current_weights(self, portfolio_data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate current portfolio weights"""
        try:
            total_value = portfolio_data.get('total_value', 0.0)
            if total_value <= 0:
                return {}
                
            weights = {}
            positions = portfolio_data.get('positions', [])
            
            for position in positions:
                if isinstance(position, dict):
                    symbol = position.get('symbol')
                    value = position.get('value', 0.0)
                    if symbol and total_value > 0:
                        weights[symbol] = value / total_value
                        
            return weights
            
        except Exception as e:
            logger.error(f"Error calculating current weights: {e}")
            return {}
            
    async def _adjust_targets_for_market(self, 
                                       base_targets: Dict[str, float],
                                       market_conditions,
                                       reason: RebalanceReason) -> Dict[str, float]:
        """Adjust target allocations based on market conditions"""
        try:
            adjusted_targets = base_targets.copy()
            
            if not market_conditions:
                return adjusted_targets
                
            # Adjust based on market sentiment
            sentiment = getattr(market_conditions, 'overall_sentiment', 'neutral')
            volatility = getattr(market_conditions, 'volatility_level', 'normal')
            
            # Conservative adjustment in high volatility
            if volatility in ['high', 'extreme']:
                # Reduce equity exposure, increase cash
                adjustment_factor = 0.9 if volatility == 'high' else 0.8
                for symbol in adjusted_targets:
                    adjusted_targets[symbol] *= adjustment_factor
                    
            # Aggressive adjustment in strong bull market
            elif sentiment == 'bullish' and volatility == 'low':
                # Increase equity exposure
                adjustment_factor = 1.05
                for symbol in adjusted_targets:
                    adjusted_targets[symbol] *= adjustment_factor
                    
            # Normalize to ensure sum = 1.0
            total = sum(adjusted_targets.values())
            if total > 0:
                for symbol in adjusted_targets:
                    adjusted_targets[symbol] /= total
                    
            return adjusted_targets
            
        except Exception as e:
            logger.error(f"Error adjusting targets for market: {e}")
            return base_targets
            
    async def _calculate_buy_confidence(self, symbol: str, market_conditions) -> float:
        """Calculate confidence for buying a symbol"""
        try:
            base_confidence = 0.7
            
            if market_conditions:
                sentiment = getattr(market_conditions, 'overall_sentiment', 'neutral')
                volatility = getattr(market_conditions, 'volatility_level', 'normal')
                
                # Higher confidence in bullish, low volatility markets
                if sentiment == 'bullish':
                    base_confidence += 0.1
                elif sentiment == 'bearish':
                    base_confidence -= 0.1
                    
                if volatility == 'low':
                    base_confidence += 0.05
                elif volatility in ['high', 'extreme']:
                    base_confidence -= 0.1
                    
            return max(0.1, min(0.95, base_confidence))
            
        except Exception as e:
            logger.error(f"Error calculating buy confidence: {e}")
            return 0.7
            
    async def _calculate_sell_confidence(self, symbol: str, market_conditions) -> float:
        """Calculate confidence for selling a symbol"""
        try:
            base_confidence = 0.8  # Generally more confident in sells for rebalancing
            
            if market_conditions:
                sentiment = getattr(market_conditions, 'overall_sentiment', 'neutral')
                
                # Lower confidence in selling during bull markets
                if sentiment == 'bullish':
                    base_confidence -= 0.05
                elif sentiment == 'bearish':
                    base_confidence += 0.05
                    
            return max(0.1, min(0.95, base_confidence))
            
        except Exception as e:
            logger.error(f"Error calculating sell confidence: {e}")
            return 0.8
            
    async def _get_current_price(self, symbol: str) -> float:
        """Get current price for a symbol"""
        try:
            if self.market_system:
                quote = await self.market_system.get_current_quote(symbol)
                if quote:
                    return quote.last
                    
            # Fallback to mock price
            return 150.0  # Mock price
            
        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {e}")
            return 150.0
            
    async def _generate_reasoning(self, 
                                symbol: str, 
                                action: str, 
                                weight_change: float,
                                market_conditions,
                                reason: RebalanceReason) -> str:
        """Generate reasoning for a rebalancing recommendation"""
        try:
            if action == 'hold':
                return f"Position in {symbol} is within target range, no action needed"
                
            direction = "increase" if weight_change > 0 else "decrease"
            magnitude = abs(weight_change)
            
            base_reason = f"{direction.title()} {symbol} allocation by {magnitude:.1%} due to {reason.value}"
            
            if market_conditions:
                sentiment = getattr(market_conditions, 'overall_sentiment', 'neutral')
                volatility = getattr(market_conditions, 'volatility_level', 'normal')
                
                market_context = f" (Market: {sentiment}, Volatility: {volatility})"
                return base_reason + market_context
                
            return base_reason
            
        except Exception as e:
            logger.error(f"Error generating reasoning: {e}")
            return f"{action.title()} {symbol} for portfolio rebalancing"
            
    async def _execute_trade(self, recommendation: RebalanceRecommendation) -> bool:
        """Execute a trade based on recommendation"""
        try:
            # For now, just simulate the trade
            # In a real implementation, this would interface with the execution engine
            
            logger.info(f"Simulating {recommendation.action} {abs(recommendation.quantity_change):.0f} shares of {recommendation.symbol}")
            
            # Simulate execution delay
            await asyncio.sleep(0.1)
            
            # Simulate 90% success rate
            return np.random.random() > 0.1
            
        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            return False
            
    async def _calculate_expected_improvement(self, recommendations: List[RebalanceRecommendation]) -> float:
        """Calculate expected improvement from rebalancing"""
        try:
            # Simplified calculation based on drift reduction
            total_drift_reduction = sum(
                abs(rec.weight_change) for rec in recommendations 
                if rec.action != 'hold'
            )
            
            # Convert to expected return improvement (simplified)
            return total_drift_reduction * 0.02  # 2% improvement per 1% drift reduction
            
        except Exception as e:
            logger.error(f"Error calculating expected improvement: {e}")
            return 0.0
            
    async def _rebalancing_loop(self):
        """Background rebalancing loop"""
        while self.running:
            try:
                await asyncio.sleep(3600)  # Check every hour
                
                # Check if rebalancing is needed
                time_since_last = time.time() - self.last_rebalance
                if time_since_last >= self.rebalance_frequency:
                    await self._check_scheduled_rebalancing()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in rebalancing loop: {e}")
                
    async def _drift_monitoring_loop(self):
        """Background drift monitoring loop"""
        while self.running:
            try:
                await asyncio.sleep(1800)  # Check every 30 minutes
                
                # Check for significant drift
                await self._check_drift_rebalancing()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in drift monitoring loop: {e}")
                
    async def _check_scheduled_rebalancing(self):
        """Check if scheduled rebalancing is needed"""
        try:
            recommendations = await self.generate_rebalance_recommendations(RebalanceReason.SCHEDULED)
            
            # Execute if there are significant recommendations
            significant_recs = [rec for rec in recommendations if abs(rec.weight_change) > 0.01]  # 1% threshold
            
            if significant_recs:
                await self.execute_rebalancing(recommendations, RebalanceReason.SCHEDULED)
                
        except Exception as e:
            logger.error(f"Error in scheduled rebalancing check: {e}")
            
    async def _check_drift_rebalancing(self):
        """Check if drift-based rebalancing is needed"""
        try:
            drift_analysis = await self.analyze_portfolio_drift()
            
            # Check if any position has drifted beyond threshold
            max_drift = max(
                (analysis.get('drift_percent', 0.0) for analysis in drift_analysis.values()),
                default=0.0
            )
            
            if max_drift > self.drift_threshold:
                logger.info(f"Drift threshold exceeded: {max_drift:.1%} > {self.drift_threshold:.1%}")
                recommendations = await self.generate_rebalance_recommendations(RebalanceReason.DRIFT_THRESHOLD)
                await self.execute_rebalancing(recommendations, RebalanceReason.DRIFT_THRESHOLD)
                
        except Exception as e:
            logger.error(f"Error in drift rebalancing check: {e}")
            
    async def get_rebalancer_status(self) -> Dict[str, Any]:
        """Get rebalancer status"""
        try:
            drift_analysis = await self.analyze_portfolio_drift()
            
            return {
                'running': self.running,
                'last_rebalance': self.last_rebalance,
                'rebalance_history_count': len(self.rebalance_history),
                'target_allocations': self.target_allocations,
                'current_drift': drift_analysis,
                'configuration': {
                    'drift_threshold': self.drift_threshold,
                    'rebalance_frequency': self.rebalance_frequency,
                    'min_trade_size': self.min_trade_size,
                    'max_turnover': self.max_turnover
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting rebalancer status: {e}")
            return {'error': str(e)}

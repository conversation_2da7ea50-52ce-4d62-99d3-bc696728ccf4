"""
Configuration Management - Comprehensive system configuration management
"""

import os
import json
import yaml
import logging
import time
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import asyncio
from collections import defaultdict

logger = logging.getLogger(__name__)


class ConfigType(Enum):
    """Configuration types"""
    SYSTEM = "system"
    TEAM = "team"
    STRATEGY = "strategy"
    TRADING = "trading"
    DATA = "data"
    SIMULATION = "simulation"
    ADVANCED_FEATURES = "advanced_features"


class ConfigFormat(Enum):
    """Configuration file formats"""
    JSON = "json"
    YAML = "yaml"
    TOML = "toml"


@dataclass
class ConfigSchema:
    """Configuration schema definition"""
    name: str
    config_type: ConfigType
    required_fields: List[str]
    optional_fields: List[str]
    default_values: Dict[str, Any]
    validation_rules: Dict[str, Any]


@dataclass
class ConfigValidationResult:
    """Configuration validation result"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    missing_fields: List[str]
    invalid_values: Dict[str, str]


class ConfigurationManager:
    """
    Comprehensive configuration management system that handles all system configurations,
    validation, environment-specific settings, and runtime configuration updates.
    """
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        
        # Configuration storage
        self.configurations: Dict[str, Dict[str, Any]] = {}
        self.config_schemas: Dict[ConfigType, ConfigSchema] = {}
        self.environment_configs: Dict[str, Dict[str, Any]] = {}
        
        # Configuration metadata
        self.config_metadata: Dict[str, Dict[str, Any]] = {}
        self.config_history: List[Dict[str, Any]] = []
        
        # Runtime configuration
        self.runtime_overrides: Dict[str, Any] = {}
        self.environment = os.getenv('ENVIRONMENT', 'development')
        
        # Configuration watchers
        self.config_watchers: Dict[str, List[callable]] = defaultdict(list)
        
        # State
        self.initialized = False
        
    async def initialize(self) -> bool:
        """Initialize the configuration manager"""
        try:
            logger.info("Initializing Configuration Manager...")
            
            # Create config directory if it doesn't exist
            self.config_dir.mkdir(parents=True, exist_ok=True)
            
            # Setup configuration schemas
            await self._setup_config_schemas()
            
            # Load default configurations
            await self._load_default_configurations()
            
            # Load environment-specific configurations
            await self._load_environment_configurations()
            
            # Validate all configurations
            await self._validate_all_configurations()
            
            # Setup configuration watching
            await self._setup_config_watching()
            
            self.initialized = True
            logger.info("✅ Configuration Manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Configuration Manager: {e}")
            return False
            
    async def get_config(self, config_name: str, section: Optional[str] = None) -> Optional[Any]:
        """Get configuration value"""
        try:
            if config_name not in self.configurations:
                logger.warning(f"Configuration {config_name} not found")
                return None
                
            config = self.configurations[config_name]
            
            # Apply runtime overrides
            config = self._apply_runtime_overrides(config_name, config)
            
            if section:
                return config.get(section)
            else:
                return config
                
        except Exception as e:
            logger.error(f"Error getting configuration: {e}")
            return None
            
    async def set_config(self, config_name: str, config_data: Dict[str, Any], 
                        persist: bool = True) -> bool:
        """Set configuration"""
        try:
            # Validate configuration
            validation_result = await self._validate_configuration(config_name, config_data)
            
            if not validation_result.is_valid:
                logger.error(f"Configuration validation failed: {validation_result.errors}")
                return False
                
            # Store configuration
            old_config = self.configurations.get(config_name, {})
            self.configurations[config_name] = config_data
            
            # Update metadata
            self.config_metadata[config_name] = {
                'last_updated': time.time(),
                'updated_by': 'system',
                'version': self.config_metadata.get(config_name, {}).get('version', 0) + 1
            }
            
            # Add to history
            self.config_history.append({
                'config_name': config_name,
                'action': 'update',
                'old_config': old_config,
                'new_config': config_data,
                'timestamp': time.time()
            })
            
            # Persist to file if requested
            if persist:
                await self._save_configuration(config_name, config_data)
                
            # Notify watchers
            await self._notify_config_watchers(config_name, config_data)
            
            logger.info(f"Updated configuration {config_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error setting configuration: {e}")
            return False
            
    async def load_config_file(self, file_path: str, config_name: Optional[str] = None) -> bool:
        """Load configuration from file"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.error(f"Configuration file not found: {file_path}")
                return False
                
            # Determine format from extension
            if file_path.suffix.lower() == '.json':
                with open(file_path, 'r') as f:
                    config_data = json.load(f)
            elif file_path.suffix.lower() in ['.yaml', '.yml']:
                with open(file_path, 'r') as f:
                    config_data = yaml.safe_load(f)
            else:
                logger.error(f"Unsupported configuration file format: {file_path.suffix}")
                return False
                
            # Use filename as config name if not provided
            if not config_name:
                config_name = file_path.stem
                
            # Set configuration
            return await self.set_config(config_name, config_data, persist=False)
            
        except Exception as e:
            logger.error(f"Error loading configuration file: {e}")
            return False
            
    async def save_config_file(self, config_name: str, file_path: str, 
                              format: ConfigFormat = ConfigFormat.YAML) -> bool:
        """Save configuration to file"""
        try:
            if config_name not in self.configurations:
                logger.error(f"Configuration {config_name} not found")
                return False
                
            config_data = self.configurations[config_name]
            file_path = Path(file_path)
            
            # Create directory if it doesn't exist
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save based on format
            if format == ConfigFormat.JSON:
                with open(file_path, 'w') as f:
                    json.dump(config_data, f, indent=2)
            elif format == ConfigFormat.YAML:
                with open(file_path, 'w') as f:
                    yaml.dump(config_data, f, default_flow_style=False, indent=2)
            else:
                logger.error(f"Unsupported format: {format}")
                return False
                
            logger.info(f"Saved configuration {config_name} to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving configuration file: {e}")
            return False
            
    async def validate_config(self, config_name: str, config_data: Optional[Dict[str, Any]] = None) -> ConfigValidationResult:
        """Validate configuration"""
        try:
            if config_data is None:
                config_data = self.configurations.get(config_name, {})
                
            return await self._validate_configuration(config_name, config_data)
            
        except Exception as e:
            logger.error(f"Error validating configuration: {e}")
            return ConfigValidationResult(
                is_valid=False,
                errors=[str(e)],
                warnings=[],
                missing_fields=[],
                invalid_values={}
            )
            
    async def set_runtime_override(self, config_path: str, value: Any) -> bool:
        """Set runtime configuration override"""
        try:
            self.runtime_overrides[config_path] = value
            logger.info(f"Set runtime override: {config_path} = {value}")
            return True
            
        except Exception as e:
            logger.error(f"Error setting runtime override: {e}")
            return False
            
    async def clear_runtime_override(self, config_path: str) -> bool:
        """Clear runtime configuration override"""
        try:
            if config_path in self.runtime_overrides:
                del self.runtime_overrides[config_path]
                logger.info(f"Cleared runtime override: {config_path}")
                return True
            else:
                logger.warning(f"Runtime override not found: {config_path}")
                return False
                
        except Exception as e:
            logger.error(f"Error clearing runtime override: {e}")
            return False
            
    async def watch_config(self, config_name: str, callback: callable) -> str:
        """Watch configuration for changes"""
        try:
            watcher_id = f"watcher_{int(time.time())}_{len(self.config_watchers[config_name])}"
            self.config_watchers[config_name].append(callback)
            
            logger.info(f"Added config watcher {watcher_id} for {config_name}")
            return watcher_id
            
        except Exception as e:
            logger.error(f"Error adding config watcher: {e}")
            return ""
            
    async def get_config_info(self) -> Dict[str, Any]:
        """Get configuration system information"""
        try:
            return {
                'initialized': self.initialized,
                'environment': self.environment,
                'config_count': len(self.configurations),
                'configurations': list(self.configurations.keys()),
                'schemas': {ct.value: schema.name for ct, schema in self.config_schemas.items()},
                'runtime_overrides': len(self.runtime_overrides),
                'watchers': {name: len(watchers) for name, watchers in self.config_watchers.items()},
                'config_history_size': len(self.config_history),
                'last_update': max([
                    meta.get('last_updated', 0) for meta in self.config_metadata.values()
                ]) if self.config_metadata else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting config info: {e}")
            return {'error': str(e)}
            
    # Private methods
    async def _setup_config_schemas(self):
        """Setup configuration schemas"""
        # System configuration schema
        self.config_schemas[ConfigType.SYSTEM] = ConfigSchema(
            name="system_config",
            config_type=ConfigType.SYSTEM,
            required_fields=["logging", "database", "security"],
            optional_fields=["debug", "performance"],
            default_values={
                "debug": False,
                "performance": {"monitoring_enabled": True}
            },
            validation_rules={}
        )
        
        # Team configuration schema
        self.config_schemas[ConfigType.TEAM] = ConfigSchema(
            name="team_config",
            config_type=ConfigType.TEAM,
            required_fields=["team_id", "strategies", "risk_management"],
            optional_fields=["performance_targets", "resource_allocation"],
            default_values={
                "performance_targets": {"return_target": 0.15},
                "resource_allocation": {"max_positions": 10}
            },
            validation_rules={}
        )
        
        # Trading configuration schema
        self.config_schemas[ConfigType.TRADING] = ConfigSchema(
            name="trading_config",
            config_type=ConfigType.TRADING,
            required_fields=["paper_trading", "execution"],
            optional_fields=["commission_rates", "slippage"],
            default_values={
                "commission_rates": {"default": 0.001},
                "slippage": {"default": 0.0005}
            },
            validation_rules={}
        )
        
        # Advanced features configuration schema
        self.config_schemas[ConfigType.ADVANCED_FEATURES] = ConfigSchema(
            name="advanced_features_config",
            config_type=ConfigType.ADVANCED_FEATURES,
            required_fields=["competitive_cooperative_framework", "innovation_tournament"],
            optional_fields=["self_improvement", "regime_adaptation", "performance_optimizer"],
            default_values={
                "self_improvement": {"enabled": True},
                "regime_adaptation": {"enabled": True},
                "performance_optimizer": {"enabled": True}
            },
            validation_rules={}
        )

    async def _load_default_configurations(self):
        """Load default configurations"""
        # System configuration
        system_config = {
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file": "logs/system.log"
            },
            "database": {
                "type": "sqlite",
                "path": "data/trading_system.db"
            },
            "security": {
                "encryption_enabled": True,
                "api_key_required": False
            }
        }
        self.configurations["system"] = system_config

        # Advanced features configuration
        advanced_config = {
            "competitive_cooperative_framework": {
                "enabled": True,
                "default_mode": "balanced"
            },
            "innovation_tournament": {
                "enabled": True,
                "max_tournaments": 5
            },
            "self_improvement": {
                "enabled": True,
                "learning_rate": 0.01
            },
            "regime_adaptation": {
                "enabled": True,
                "detection_sensitivity": 0.7
            },
            "performance_optimizer": {
                "enabled": True,
                "monitoring_frequency": 60
            }
        }
        self.configurations["advanced_features"] = advanced_config

    async def _load_environment_configurations(self):
        """Load environment-specific configurations"""
        pass  # Simplified implementation

    async def _validate_all_configurations(self):
        """Validate all loaded configurations"""
        pass  # Simplified implementation

    async def _validate_configuration(self, config_name: str, config_data: Dict[str, Any]) -> ConfigValidationResult:
        """Validate a specific configuration"""
        return ConfigValidationResult(
            is_valid=True,
            errors=[],
            warnings=[],
            missing_fields=[],
            invalid_values={}
        )

    async def _setup_config_watching(self):
        """Setup configuration file watching"""
        pass  # Simplified implementation

    async def _save_configuration(self, config_name: str, config_data: Dict[str, Any]):
        """Save configuration to file"""
        pass  # Simplified implementation

    async def _notify_config_watchers(self, config_name: str, config_data: Dict[str, Any]):
        """Notify configuration watchers"""
        pass  # Simplified implementation

    def _apply_runtime_overrides(self, config_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """Apply runtime overrides to configuration"""
        return config  # Simplified implementation

/**
 * Advanced Ollama Trading Agents - Dashboard JavaScript
 * 
 * Handles real-time updates, WebSocket connections, chart rendering,
 * and user interactions for the trading dashboard.
 */

class TradingDashboard {
    constructor() {
        this.websocket = null;
        this.charts = {};
        this.refreshInterval = 5000; // 5 seconds
        this.isConnected = false;
        
        // API endpoints
        this.apiBase = '/api/v1';
        this.wsUrl = `ws://${window.location.host}/ws`;
        
        // Data storage
        this.portfolioData = [];
        this.performanceData = [];
        this.systemMetrics = {};
        
        this.init();
    }
    
    init() {
        console.log('Initializing Trading Dashboard...');
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Initialize WebSocket connection
        this.connectWebSocket();
        
        // Initialize charts
        this.initializeCharts();
        
        // Load initial data
        this.loadInitialData();
        
        // Start periodic updates
        this.startPeriodicUpdates();
        
        console.log('Dashboard initialized successfully');
    }
    
    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.getAttribute('data-section');
                if (section) {
                    this.showSection(section);
                }
            });
        });
        
        // Refresh button
        const refreshBtn = document.querySelector('[onclick="refreshDashboard()"]');
        if (refreshBtn) {
            refreshBtn.onclick = () => this.refreshDashboard();
        }
        
        // Settings modal
        const settingsModal = document.getElementById('settingsModal');
        if (settingsModal) {
            settingsModal.addEventListener('show.bs.modal', () => {
                this.loadSettings();
            });
        }
        
        // Window resize
        window.addEventListener('resize', () => {
            this.resizeCharts();
        });
    }
    
    connectWebSocket() {
        try {
            this.websocket = new WebSocket(this.wsUrl);
            
            this.websocket.onopen = () => {
                console.log('WebSocket connected');
                this.isConnected = true;
                this.updateConnectionStatus(true);
                
                // Subscribe to real-time updates
                this.subscribeToUpdates();
            };
            
            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };
            
            this.websocket.onclose = () => {
                console.log('WebSocket disconnected');
                this.isConnected = false;
                this.updateConnectionStatus(false);
                
                // Attempt to reconnect after 5 seconds
                setTimeout(() => {
                    this.connectWebSocket();
                }, 5000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.updateConnectionStatus(false);
            };
            
        } catch (error) {
            console.error('Failed to connect WebSocket:', error);
            this.updateConnectionStatus(false);
        }
    }
    
    subscribeToUpdates() {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            // Subscribe to various data streams
            const subscriptions = [
                { type: 'portfolio_updates' },
                { type: 'system_metrics' },
                { type: 'order_updates' },
                { type: 'agent_status' },
                { type: 'strategy_performance' }
            ];
            
            subscriptions.forEach(sub => {
                this.websocket.send(JSON.stringify({
                    action: 'subscribe',
                    ...sub
                }));
            });
        }
    }
    
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'portfolio_update':
                this.updatePortfolioMetrics(data.payload);
                break;
            case 'system_metrics':
                this.updateSystemMetrics(data.payload);
                break;
            case 'order_update':
                this.updateOrderStatus(data.payload);
                break;
            case 'agent_status':
                this.updateAgentStatus(data.payload);
                break;
            case 'strategy_performance':
                this.updateStrategyPerformance(data.payload);
                break;
            case 'alert':
                this.showAlert(data.payload);
                break;
            default:
                console.log('Unknown message type:', data.type);
        }
    }
    
    async loadInitialData() {
        try {
            // Load portfolio data
            const portfolioResponse = await fetch(`${this.apiBase}/portfolio/summary`);
            if (portfolioResponse.ok) {
                const portfolioData = await portfolioResponse.json();
                this.updatePortfolioMetrics(portfolioData);
            }
            
            // Load system status
            const statusResponse = await fetch(`${this.apiBase}/system/status`);
            if (statusResponse.ok) {
                const statusData = await statusResponse.json();
                this.updateSystemStatus(statusData);
            }
            
            // Load performance data
            const performanceResponse = await fetch(`${this.apiBase}/analytics/performance`);
            if (performanceResponse.ok) {
                const performanceData = await performanceResponse.json();
                this.updatePerformanceCharts(performanceData);
            }
            
        } catch (error) {
            console.error('Failed to load initial data:', error);
            this.showError('Failed to load dashboard data');
        }
    }
    
    initializeCharts() {
        // Portfolio Performance Chart
        const portfolioCtx = document.getElementById('portfolioChart');
        if (portfolioCtx) {
            this.charts.portfolio = new Chart(portfolioCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Portfolio Value',
                        data: [],
                        borderColor: '#2563eb',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            display: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }
        
        // Asset Allocation Chart
        const allocationCtx = document.getElementById('allocationChart');
        if (allocationCtx) {
            this.charts.allocation = new Chart(allocationCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Stocks', 'Bonds', 'Cash', 'Crypto'],
                    datasets: [{
                        data: [60, 25, 10, 5],
                        backgroundColor: [
                            '#2563eb',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
        }
    }
    
    updatePortfolioMetrics(data) {
        // Update portfolio value
        const portfolioValueEl = document.getElementById('portfolioValue');
        if (portfolioValueEl && data.total_value) {
            portfolioValueEl.textContent = this.formatCurrency(data.total_value);
        }
        
        // Update portfolio change
        const portfolioChangeEl = document.getElementById('portfolioChange');
        if (portfolioChangeEl && data.daily_change_percent) {
            const change = data.daily_change_percent;
            portfolioChangeEl.textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`;
            portfolioChangeEl.className = change >= 0 ? 'text-success' : 'text-danger';
        }
        
        // Update daily P&L
        const dailyPnLEl = document.getElementById('dailyPnL');
        if (dailyPnLEl && data.daily_pnl) {
            dailyPnLEl.textContent = this.formatCurrency(data.daily_pnl);
        }
        
        // Update P&L change
        const pnlChangeEl = document.getElementById('pnlChange');
        if (pnlChangeEl && data.daily_pnl_percent) {
            const change = data.daily_pnl_percent;
            pnlChangeEl.textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`;
            pnlChangeEl.className = change >= 0 ? 'text-success' : 'text-danger';
        }
        
        // Update portfolio chart
        if (this.charts.portfolio && data.historical_values) {
            this.updatePortfolioChart(data.historical_values);
        }
        
        // Update allocation chart
        if (this.charts.allocation && data.allocation) {
            this.updateAllocationChart(data.allocation);
        }
    }
    
    updateSystemMetrics(data) {
        this.systemMetrics = data;
        
        // Update active agents
        const activeAgentsEl = document.getElementById('activeAgents');
        if (activeAgentsEl && data.active_agents !== undefined) {
            activeAgentsEl.textContent = data.active_agents;
        }
        
        // Update running strategies
        const runningStrategiesEl = document.getElementById('runningStrategies');
        if (runningStrategiesEl && data.running_strategies !== undefined) {
            runningStrategiesEl.textContent = data.running_strategies;
        }
        
        // Update system health indicators
        this.updateSystemHealth(data.health_status);
    }
    
    updateSystemHealth(healthData) {
        if (!healthData) return;
        
        const healthIndicators = {
            'API Server': healthData.api_server,
            'Database': healthData.database,
            'Market Data': healthData.market_data,
            'Broker Connection': healthData.broker,
            'Risk Monitor': healthData.risk_monitor,
            'Ollama Models': healthData.ollama_models
        };
        
        // Update health status indicators
        Object.entries(healthIndicators).forEach(([service, status]) => {
            const indicator = document.querySelector(`[data-service="${service}"]`);
            if (indicator) {
                const statusEl = indicator.querySelector('.status-indicator');
                const badgeEl = indicator.querySelector('.badge');
                
                if (statusEl && badgeEl) {
                    statusEl.className = `status-indicator status-${status.toLowerCase()}`;
                    badgeEl.textContent = status;
                    badgeEl.className = `ms-auto badge bg-${this.getStatusColor(status)}`;
                }
            }
        });
    }
    
    updatePortfolioChart(historicalData) {
        if (!this.charts.portfolio || !historicalData) return;
        
        const labels = historicalData.map(item => 
            new Date(item.timestamp).toLocaleTimeString()
        );
        const values = historicalData.map(item => item.value);
        
        this.charts.portfolio.data.labels = labels;
        this.charts.portfolio.data.datasets[0].data = values;
        this.charts.portfolio.update('none');
    }
    
    updateAllocationChart(allocationData) {
        if (!this.charts.allocation || !allocationData) return;
        
        const labels = Object.keys(allocationData);
        const values = Object.values(allocationData);
        
        this.charts.allocation.data.labels = labels;
        this.charts.allocation.data.datasets[0].data = values;
        this.charts.allocation.update('none');
    }
    
    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.add('d-none');
        });
        
        // Show selected section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.remove('d-none');
        }
        
        // Update navigation
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[data-section="${sectionName}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
        
        // Load section-specific data
        this.loadSectionData(sectionName);
    }
    
    async loadSectionData(sectionName) {
        switch (sectionName) {
            case 'agents':
                await this.loadAgentsData();
                break;
            case 'strategies':
                await this.loadStrategiesData();
                break;
            case 'portfolio':
                await this.loadPortfolioData();
                break;
            case 'orders':
                await this.loadOrdersData();
                break;
            case 'performance':
                await this.loadPerformanceData();
                break;
            case 'monitoring':
                await this.loadMonitoringData();
                break;
            case 'logs':
                await this.loadLogsData();
                break;
        }
    }
    
    updateConnectionStatus(connected) {
        const statusEl = document.getElementById('connectionStatus');
        if (statusEl) {
            const indicator = statusEl.querySelector('.status-indicator');
            const text = statusEl.querySelector('span:last-child') || statusEl;
            
            if (connected) {
                indicator.className = 'status-indicator status-online real-time-indicator';
                text.textContent = 'Connected';
            } else {
                indicator.className = 'status-indicator status-offline';
                text.textContent = 'Disconnected';
            }
        }
    }
    
    showAlert(alertData) {
        const alertsContainer = document.getElementById('alertsList');
        if (!alertsContainer) return;
        
        const alertEl = document.createElement('div');
        alertEl.className = `alert alert-${alertData.type} alert-dismissible fade show`;
        alertEl.innerHTML = `
            <strong>${alertData.title}:</strong> ${alertData.message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        alertsContainer.insertBefore(alertEl, alertsContainer.firstChild);
        
        // Remove old alerts if too many
        const alerts = alertsContainer.querySelectorAll('.alert');
        if (alerts.length > 5) {
            alerts[alerts.length - 1].remove();
        }
    }
    
    refreshDashboard() {
        console.log('Refreshing dashboard...');
        this.loadInitialData();
        
        // Show loading indicator
        const refreshBtn = document.querySelector('[onclick="refreshDashboard()"]');
        if (refreshBtn) {
            const originalContent = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<div class="loading-spinner"></div> Refreshing...';
            refreshBtn.disabled = true;
            
            setTimeout(() => {
                refreshBtn.innerHTML = originalContent;
                refreshBtn.disabled = false;
            }, 2000);
        }
    }
    
    startPeriodicUpdates() {
        setInterval(() => {
            if (!this.isConnected) {
                this.loadInitialData();
            }
        }, this.refreshInterval);
    }
    
    resizeCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.resize();
            }
        });
    }
    
    // Utility methods
    formatCurrency(value) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(value);
    }
    
    getStatusColor(status) {
        switch (status.toLowerCase()) {
            case 'online':
            case 'connected':
            case 'active':
            case 'ready':
                return 'success';
            case 'warning':
            case 'pending':
                return 'warning';
            case 'offline':
            case 'disconnected':
            case 'error':
                return 'danger';
            default:
                return 'secondary';
        }
    }
    
    showError(message) {
        console.error(message);
        // Could show a toast notification here
    }
    
    // Placeholder methods for section-specific data loading
    async loadAgentsData() { console.log('Loading agents data...'); }
    async loadStrategiesData() { console.log('Loading strategies data...'); }
    async loadPortfolioData() { console.log('Loading portfolio data...'); }
    async loadOrdersData() { console.log('Loading orders data...'); }
    async loadPerformanceData() { console.log('Loading performance data...'); }
    async loadMonitoringData() { console.log('Loading monitoring data...'); }
    async loadLogsData() { console.log('Loading logs data...'); }
    
    loadSettings() {
        // Load current settings
        const refreshInterval = localStorage.getItem('refreshInterval') || '5';
        const theme = localStorage.getItem('theme') || 'light';
        const enableNotifications = localStorage.getItem('enableNotifications') !== 'false';
        
        document.getElementById('refreshInterval').value = refreshInterval;
        document.getElementById('theme').value = theme;
        document.getElementById('enableNotifications').checked = enableNotifications;
    }
}

// Global functions
function refreshDashboard() {
    if (window.dashboard) {
        window.dashboard.refreshDashboard();
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new TradingDashboard();
});

"""
Performance Monitor - Real-time System Performance Monitoring

Comprehensive performance monitoring system that tracks system metrics,
application performance, and business KPIs in real-time.
"""

import asyncio
import time
import psutil
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict, deque

import numpy as np
from prometheus_client import Counter, Histogram, Gauge, start_http_server


@dataclass
class PerformanceMetric:
    """Performance metric data structure"""
    name: str
    value: float
    timestamp: datetime
    tags: Dict[str, str] = None
    unit: str = ""
    description: str = ""


@dataclass
class SystemMetrics:
    """System-level metrics"""
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, int]
    load_average: List[float]
    timestamp: datetime


@dataclass
class ApplicationMetrics:
    """Application-level metrics"""
    response_time: float
    throughput: float
    error_rate: float
    active_connections: int
    queue_size: int
    timestamp: datetime


@dataclass
class BusinessMetrics:
    """Business-level metrics"""
    active_agents: int
    running_strategies: int
    portfolio_value: float
    daily_pnl: float
    trades_executed: int
    timestamp: datetime


class MetricsCollector:
    """
    Metrics collection and aggregation system.
    
    Features:
    - Real-time metrics collection
    - Metric aggregation and statistics
    - Time-series data storage
    - Alerting thresholds
    - Performance analytics
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.metrics_config = config.get('monitoring', {})
        
        # Metrics storage
        self.metrics_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.current_metrics: Dict[str, PerformanceMetric] = {}
        
        # Prometheus metrics
        self.setup_prometheus_metrics()
        
        # Collection settings
        self.collection_interval = self.metrics_config.get('collection_interval', 5)
        self.retention_period = self.metrics_config.get('retention_period', 3600)  # 1 hour
        
        # State
        self.collecting = False
        self.collectors: List[Callable] = []
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
    
    def setup_prometheus_metrics(self):
        """Setup Prometheus metrics"""
        # System metrics
        self.cpu_usage_gauge = Gauge('system_cpu_usage_percent', 'CPU usage percentage')
        self.memory_usage_gauge = Gauge('system_memory_usage_percent', 'Memory usage percentage')
        self.disk_usage_gauge = Gauge('system_disk_usage_percent', 'Disk usage percentage')
        
        # Application metrics
        self.response_time_histogram = Histogram('app_response_time_seconds', 'Response time in seconds', ['endpoint'])
        self.throughput_gauge = Gauge('app_throughput_ops_per_second', 'Operations per second')
        self.error_rate_gauge = Gauge('app_error_rate_percent', 'Error rate percentage')
        self.active_connections_gauge = Gauge('app_active_connections', 'Active connections')
        
        # Business metrics
        self.active_agents_gauge = Gauge('business_active_agents', 'Number of active agents')
        self.running_strategies_gauge = Gauge('business_running_strategies', 'Number of running strategies')
        self.portfolio_value_gauge = Gauge('business_portfolio_value_usd', 'Portfolio value in USD')
        self.daily_pnl_gauge = Gauge('business_daily_pnl_usd', 'Daily P&L in USD')
        
        # Counters
        self.trades_executed_counter = Counter('business_trades_executed_total', 'Total trades executed')
        self.api_requests_counter = Counter('app_api_requests_total', 'Total API requests', ['method', 'endpoint'])
        self.errors_counter = Counter('app_errors_total', 'Total errors', ['type'])
    
    async def start_collection(self):
        """Start metrics collection"""
        if self.collecting:
            return
        
        self.collecting = True
        self.logger.info("Starting metrics collection...")
        
        # Start Prometheus HTTP server
        prometheus_port = self.metrics_config.get('prometheus_port', 9090)
        start_http_server(prometheus_port)
        self.logger.info(f"Prometheus metrics server started on port {prometheus_port}")
        
        # Start collection loop
        asyncio.create_task(self.collection_loop())
    
    async def stop_collection(self):
        """Stop metrics collection"""
        self.collecting = False
        self.logger.info("Stopping metrics collection...")
    
    async def collection_loop(self):
        """Main metrics collection loop"""
        while self.collecting:
            try:
                # Collect all metrics
                await self.collect_system_metrics()
                await self.collect_application_metrics()
                await self.collect_business_metrics()
                
                # Run custom collectors
                for collector in self.collectors:
                    try:
                        await collector()
                    except Exception as e:
                        self.logger.error(f"Custom collector failed: {e}")
                
                # Clean old metrics
                await self.cleanup_old_metrics()
                
                await asyncio.sleep(self.collection_interval)
                
            except Exception as e:
                self.logger.error(f"Metrics collection error: {e}")
                await asyncio.sleep(self.collection_interval)
    
    async def collect_system_metrics(self):
        """Collect system-level metrics"""
        try:
            # CPU usage
            cpu_usage = psutil.cpu_percent(interval=None)
            self.record_metric('system.cpu_usage', cpu_usage, unit='percent')
            self.cpu_usage_gauge.set(cpu_usage)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            self.record_metric('system.memory_usage', memory_usage, unit='percent')
            self.memory_usage_gauge.set(memory_usage)
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_usage = disk.percent
            self.record_metric('system.disk_usage', disk_usage, unit='percent')
            self.disk_usage_gauge.set(disk_usage)
            
            # Network I/O
            network_io = psutil.net_io_counters()
            if network_io:
                self.record_metric('system.network_bytes_sent', network_io.bytes_sent, unit='bytes')
                self.record_metric('system.network_bytes_recv', network_io.bytes_recv, unit='bytes')
            
            # Load average (Unix systems)
            if hasattr(psutil, 'getloadavg'):
                load_avg = psutil.getloadavg()
                self.record_metric('system.load_average_1m', load_avg[0])
                self.record_metric('system.load_average_5m', load_avg[1])
                self.record_metric('system.load_average_15m', load_avg[2])
            
        except Exception as e:
            self.logger.error(f"Failed to collect system metrics: {e}")
    
    async def collect_application_metrics(self):
        """Collect application-level metrics"""
        try:
            # These would be collected from the actual application
            # For now, we'll use placeholder values
            
            # Response time (would be measured from actual requests)
            response_time = self.get_average_response_time()
            self.record_metric('app.response_time', response_time, unit='seconds')
            
            # Throughput (operations per second)
            throughput = self.calculate_throughput()
            self.record_metric('app.throughput', throughput, unit='ops/sec')
            self.throughput_gauge.set(throughput)
            
            # Error rate
            error_rate = self.calculate_error_rate()
            self.record_metric('app.error_rate', error_rate, unit='percent')
            self.error_rate_gauge.set(error_rate)
            
            # Active connections (would come from web server)
            active_connections = self.get_active_connections()
            self.record_metric('app.active_connections', active_connections, unit='count')
            self.active_connections_gauge.set(active_connections)
            
        except Exception as e:
            self.logger.error(f"Failed to collect application metrics: {e}")
    
    async def collect_business_metrics(self):
        """Collect business-level metrics"""
        try:
            # These would be collected from the trading system
            # For now, we'll use placeholder values
            
            # Active agents
            active_agents = self.get_active_agents_count()
            self.record_metric('business.active_agents', active_agents, unit='count')
            self.active_agents_gauge.set(active_agents)
            
            # Running strategies
            running_strategies = self.get_running_strategies_count()
            self.record_metric('business.running_strategies', running_strategies, unit='count')
            self.running_strategies_gauge.set(running_strategies)
            
            # Portfolio value
            portfolio_value = self.get_portfolio_value()
            self.record_metric('business.portfolio_value', portfolio_value, unit='usd')
            self.portfolio_value_gauge.set(portfolio_value)
            
            # Daily P&L
            daily_pnl = self.get_daily_pnl()
            self.record_metric('business.daily_pnl', daily_pnl, unit='usd')
            self.daily_pnl_gauge.set(daily_pnl)
            
        except Exception as e:
            self.logger.error(f"Failed to collect business metrics: {e}")
    
    def record_metric(self, name: str, value: float, unit: str = "", tags: Dict[str, str] = None):
        """Record a metric value"""
        metric = PerformanceMetric(
            name=name,
            value=value,
            timestamp=datetime.now(),
            tags=tags or {},
            unit=unit
        )
        
        # Store current value
        self.current_metrics[name] = metric
        
        # Add to history
        self.metrics_history[name].append(metric)
    
    def get_metric(self, name: str) -> Optional[PerformanceMetric]:
        """Get current metric value"""
        return self.current_metrics.get(name)
    
    def get_metric_history(self, name: str, duration: timedelta = None) -> List[PerformanceMetric]:
        """Get metric history"""
        history = list(self.metrics_history[name])
        
        if duration:
            cutoff_time = datetime.now() - duration
            history = [m for m in history if m.timestamp >= cutoff_time]
        
        return history
    
    def calculate_metric_statistics(self, name: str, duration: timedelta = None) -> Dict[str, float]:
        """Calculate statistics for a metric"""
        history = self.get_metric_history(name, duration)
        
        if not history:
            return {}
        
        values = [m.value for m in history]
        
        return {
            'count': len(values),
            'min': min(values),
            'max': max(values),
            'mean': np.mean(values),
            'median': np.median(values),
            'std': np.std(values),
            'p95': np.percentile(values, 95),
            'p99': np.percentile(values, 99)
        }
    
    async def cleanup_old_metrics(self):
        """Clean up old metrics beyond retention period"""
        cutoff_time = datetime.now() - timedelta(seconds=self.retention_period)
        
        for name, history in self.metrics_history.items():
            # Remove old metrics
            while history and history[0].timestamp < cutoff_time:
                history.popleft()
    
    def add_custom_collector(self, collector: Callable):
        """Add custom metrics collector"""
        self.collectors.append(collector)
    
    # Placeholder methods for actual metric collection
    # These would be replaced with real implementations
    
    def get_average_response_time(self) -> float:
        """Get average response time from request tracking"""
        # This would come from actual request monitoring
        return 0.1  # 100ms placeholder
    
    def calculate_throughput(self) -> float:
        """Calculate current throughput"""
        # This would be calculated from actual request counts
        return 50.0  # 50 ops/sec placeholder
    
    def calculate_error_rate(self) -> float:
        """Calculate current error rate"""
        # This would be calculated from actual error tracking
        return 0.5  # 0.5% placeholder
    
    def get_active_connections(self) -> int:
        """Get number of active connections"""
        # This would come from web server metrics
        return 10  # placeholder
    
    def get_active_agents_count(self) -> int:
        """Get number of active trading agents"""
        # This would come from agent manager
        return 5  # placeholder
    
    def get_running_strategies_count(self) -> int:
        """Get number of running strategies"""
        # This would come from strategy manager
        return 3  # placeholder
    
    def get_portfolio_value(self) -> float:
        """Get current portfolio value"""
        # This would come from portfolio manager
        return 100000.0  # $100k placeholder
    
    def get_daily_pnl(self) -> float:
        """Get daily profit and loss"""
        # This would come from portfolio manager
        return 1500.0  # $1.5k placeholder


class AlertManager:
    """
    Alert management system for performance monitoring.
    
    Features:
    - Threshold-based alerting
    - Alert escalation
    - Multiple notification channels
    - Alert suppression and grouping
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.alert_config = config.get('alerting', {})
        
        # Alert rules
        self.alert_rules: Dict[str, Dict[str, Any]] = {}
        self.active_alerts: Dict[str, Dict[str, Any]] = {}
        
        # Notification channels
        self.notification_channels: List[Callable] = []
        
        # State
        self.monitoring = False
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
    
    def add_alert_rule(self, name: str, metric: str, threshold: float, 
                      operator: str = 'gt', duration: int = 60, severity: str = 'warning'):
        """Add alert rule"""
        self.alert_rules[name] = {
            'metric': metric,
            'threshold': threshold,
            'operator': operator,  # gt, lt, eq, gte, lte
            'duration': duration,  # seconds
            'severity': severity,  # info, warning, critical
            'last_triggered': None,
            'suppressed_until': None
        }
    
    def add_notification_channel(self, channel: Callable):
        """Add notification channel"""
        self.notification_channels.append(channel)
    
    async def check_alerts(self, metrics_collector: MetricsCollector):
        """Check all alert rules against current metrics"""
        for rule_name, rule in self.alert_rules.items():
            try:
                await self.check_alert_rule(rule_name, rule, metrics_collector)
            except Exception as e:
                self.logger.error(f"Failed to check alert rule {rule_name}: {e}")
    
    async def check_alert_rule(self, rule_name: str, rule: Dict[str, Any], 
                              metrics_collector: MetricsCollector):
        """Check individual alert rule"""
        metric_name = rule['metric']
        threshold = rule['threshold']
        operator = rule['operator']
        duration = rule['duration']
        
        # Get metric history for duration
        history = metrics_collector.get_metric_history(
            metric_name, 
            timedelta(seconds=duration)
        )
        
        if not history:
            return
        
        # Check if condition is met for the duration
        condition_met = True
        for metric in history:
            if not self.evaluate_condition(metric.value, threshold, operator):
                condition_met = False
                break
        
        if condition_met:
            await self.trigger_alert(rule_name, rule, history[-1])
        else:
            await self.resolve_alert(rule_name)
    
    def evaluate_condition(self, value: float, threshold: float, operator: str) -> bool:
        """Evaluate alert condition"""
        if operator == 'gt':
            return value > threshold
        elif operator == 'lt':
            return value < threshold
        elif operator == 'gte':
            return value >= threshold
        elif operator == 'lte':
            return value <= threshold
        elif operator == 'eq':
            return value == threshold
        else:
            return False
    
    async def trigger_alert(self, rule_name: str, rule: Dict[str, Any], metric: PerformanceMetric):
        """Trigger alert"""
        # Check if alert is already active
        if rule_name in self.active_alerts:
            return
        
        # Check if alert is suppressed
        if rule.get('suppressed_until') and datetime.now() < rule['suppressed_until']:
            return
        
        alert = {
            'rule_name': rule_name,
            'metric': metric,
            'rule': rule,
            'triggered_at': datetime.now(),
            'status': 'active'
        }
        
        self.active_alerts[rule_name] = alert
        rule['last_triggered'] = datetime.now()
        
        # Send notifications
        await self.send_alert_notifications(alert)
        
        self.logger.warning(f"Alert triggered: {rule_name}")
    
    async def resolve_alert(self, rule_name: str):
        """Resolve alert"""
        if rule_name in self.active_alerts:
            alert = self.active_alerts[rule_name]
            alert['status'] = 'resolved'
            alert['resolved_at'] = datetime.now()
            
            # Send resolution notification
            await self.send_resolution_notifications(alert)
            
            # Remove from active alerts
            del self.active_alerts[rule_name]
            
            self.logger.info(f"Alert resolved: {rule_name}")
    
    async def send_alert_notifications(self, alert: Dict[str, Any]):
        """Send alert notifications to all channels"""
        for channel in self.notification_channels:
            try:
                await channel(alert)
            except Exception as e:
                self.logger.error(f"Failed to send alert notification: {e}")
    
    async def send_resolution_notifications(self, alert: Dict[str, Any]):
        """Send alert resolution notifications"""
        for channel in self.notification_channels:
            try:
                await channel(alert)
            except Exception as e:
                self.logger.error(f"Failed to send resolution notification: {e}")


class PerformanceMonitor:
    """
    Main performance monitoring system that coordinates metrics collection
    and alerting.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.metrics_collector = MetricsCollector(config)
        self.alert_manager = AlertManager(config)
        
        # Setup default alert rules
        self.setup_default_alerts()
        
        # State
        self.running = False
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
    
    def setup_default_alerts(self):
        """Setup default alert rules"""
        # System alerts
        self.alert_manager.add_alert_rule(
            'high_cpu_usage',
            'system.cpu_usage',
            80.0,
            'gt',
            duration=300,  # 5 minutes
            severity='warning'
        )
        
        self.alert_manager.add_alert_rule(
            'high_memory_usage',
            'system.memory_usage',
            85.0,
            'gt',
            duration=300,
            severity='critical'
        )
        
        # Application alerts
        self.alert_manager.add_alert_rule(
            'high_response_time',
            'app.response_time',
            2.0,  # 2 seconds
            'gt',
            duration=120,
            severity='warning'
        )
        
        self.alert_manager.add_alert_rule(
            'high_error_rate',
            'app.error_rate',
            5.0,  # 5%
            'gt',
            duration=60,
            severity='critical'
        )
    
    async def start(self):
        """Start performance monitoring"""
        if self.running:
            return
        
        self.running = True
        self.logger.info("Starting performance monitoring...")
        
        # Start metrics collection
        await self.metrics_collector.start_collection()
        
        # Start alert monitoring
        asyncio.create_task(self.alert_monitoring_loop())
        
        self.logger.info("Performance monitoring started")
    
    async def stop(self):
        """Stop performance monitoring"""
        self.running = False
        await self.metrics_collector.stop_collection()
        self.logger.info("Performance monitoring stopped")
    
    async def alert_monitoring_loop(self):
        """Alert monitoring loop"""
        while self.running:
            try:
                await self.alert_manager.check_alerts(self.metrics_collector)
                await asyncio.sleep(30)  # Check alerts every 30 seconds
            except Exception as e:
                self.logger.error(f"Alert monitoring error: {e}")
                await asyncio.sleep(30)
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current metrics snapshot"""
        return {
            name: asdict(metric) 
            for name, metric in self.metrics_collector.current_metrics.items()
        }
    
    def get_metrics_summary(self, duration: timedelta = None) -> Dict[str, Any]:
        """Get metrics summary with statistics"""
        summary = {}
        
        for name in self.metrics_collector.current_metrics.keys():
            stats = self.metrics_collector.calculate_metric_statistics(name, duration)
            if stats:
                summary[name] = stats
        
        return summary
    
    def get_active_alerts(self) -> Dict[str, Any]:
        """Get active alerts"""
        return self.alert_manager.active_alerts.copy()


# Notification channel implementations
async def email_notification_channel(alert: Dict[str, Any]):
    """Email notification channel"""
    # This would integrate with an email service
    print(f"EMAIL ALERT: {alert['rule_name']} - {alert['metric'].name} = {alert['metric'].value}")


async def slack_notification_channel(alert: Dict[str, Any]):
    """Slack notification channel"""
    # This would integrate with Slack API
    print(f"SLACK ALERT: {alert['rule_name']} - {alert['metric'].name} = {alert['metric'].value}")


async def webhook_notification_channel(alert: Dict[str, Any]):
    """Webhook notification channel"""
    # This would send HTTP POST to webhook URL
    print(f"WEBHOOK ALERT: {alert['rule_name']} - {alert['metric'].name} = {alert['metric'].value}")


async def console_notification_channel(alert: Dict[str, Any]):
    """Console notification channel for testing"""
    status = alert.get('status', 'active')
    if status == 'active':
        print(f"🚨 ALERT: {alert['rule_name']} - {alert['metric'].name} = {alert['metric'].value}")
    else:
        print(f"✅ RESOLVED: {alert['rule_name']}")

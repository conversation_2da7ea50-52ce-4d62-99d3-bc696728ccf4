"""
Execution Specialist Agent - Order optimization and execution algorithms
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
import json
import math

from ..base_agent import BaseAgent, AgentRole, AgentState
from communication.message_types import MessageType

logger = logging.getLogger(__name__)


class ExecutionSpecialistAgent(BaseAgent):
    """
    Execution Specialist Agent specializing in order optimization and execution algorithms.
    
    Responsibilities:
    - Order execution optimization
    - Execution algorithm selection and tuning
    - Slippage minimization strategies
    - Market impact analysis and mitigation
    - Order routing and venue selection
    - Execution timing optimization
    - Transaction cost analysis
    - Fill quality assessment
    
    Uses: nemotron-mini:4b (primary), hermes3:8b (fallback)
    """
    
    def __init__(self, agent_id: str = None, name: str = None, config: Dict[str, Any] = None):
        super().__init__(
            agent_id=agent_id,
            name=name or "ExecutionSpecialist",
            role=AgentRole.EXECUTION_SPECIALIST,
            config=config
        )
        
        # Execution state
        self.active_orders: Dict[str, Dict[str, Any]] = {}
        self.execution_history: List[Dict[str, Any]] = []
        self.execution_algorithms: Dict[str, Dict[str, Any]] = {}
        
        # Performance tracking
        self.execution_metrics: Dict[str, Dict[str, Any]] = {}
        self.slippage_analysis: Dict[str, List[float]] = {}
        
        # Market microstructure
        self.market_data: Dict[str, Dict[str, Any]] = {}
        self.venue_performance: Dict[str, Dict[str, Any]] = {}
        
    async def _initialize_agent(self):
        """Execution Specialist specific initialization"""
        logger.info(f"Initializing Execution Specialist Agent: {self.name}")
        
        # Initialize execution algorithms
        await self._setup_execution_algorithms()
        
        # Initialize market microstructure analysis
        await self._setup_microstructure_analysis()
        
        # Initialize performance tracking
        await self._setup_performance_tracking()
        
        logger.info(f"✓ Execution Specialist Agent {self.name} initialized")
        
    async def _setup_execution_algorithms(self):
        """Setup execution algorithms"""
        self.execution_algorithms = {
            'twap': {
                'name': 'Time Weighted Average Price',
                'description': 'Spreads order execution over time',
                'use_case': 'Large orders, low urgency',
                'parameters': ['duration', 'slice_size', 'randomization']
            },
            'vwap': {
                'name': 'Volume Weighted Average Price',
                'description': 'Matches historical volume patterns',
                'use_case': 'Benchmark execution',
                'parameters': ['lookback_period', 'participation_rate', 'volume_curve']
            },
            'implementation_shortfall': {
                'name': 'Implementation Shortfall',
                'description': 'Minimizes total trading costs',
                'use_case': 'Cost-sensitive execution',
                'parameters': ['urgency', 'risk_aversion', 'market_impact_model']
            },
            'pov': {
                'name': 'Percentage of Volume',
                'description': 'Maintains constant participation rate',
                'use_case': 'Liquidity-constrained execution',
                'parameters': ['participation_rate', 'min_size', 'max_size']
            },
            'iceberg': {
                'name': 'Iceberg Orders',
                'description': 'Hides order size from market',
                'use_case': 'Large orders, information leakage concern',
                'parameters': ['visible_size', 'refresh_threshold', 'randomization']
            }
        }
        
        # Store algorithms in memory
        await self.memory.store_experience({
            'type': 'initialization',
            'component': 'execution_algorithms',
            'algorithms': self.execution_algorithms,
            'timestamp': time.time()
        })
        
    async def _setup_microstructure_analysis(self):
        """Setup market microstructure analysis"""
        self.microstructure_framework = {
            'market_impact_models': [
                'linear_impact', 'square_root_impact', 'almgren_chriss'
            ],
            'liquidity_measures': [
                'bid_ask_spread', 'market_depth', 'price_impact',
                'volume_at_touch', 'effective_spread'
            ],
            'timing_factors': [
                'volatility_patterns', 'volume_patterns', 'news_events',
                'market_open_close', 'earnings_announcements'
            ],
            'venue_characteristics': [
                'latency', 'fill_rates', 'price_improvement',
                'hidden_liquidity', 'maker_taker_fees'
            ]
        }
        
        # Store microstructure framework
        await self.memory.store_experience({
            'type': 'initialization',
            'component': 'microstructure_analysis',
            'framework': self.microstructure_framework,
            'timestamp': time.time()
        })
        
    async def _setup_performance_tracking(self):
        """Setup execution performance tracking"""
        self.performance_metrics = {
            'cost_metrics': [
                'implementation_shortfall', 'market_impact',
                'timing_cost', 'opportunity_cost'
            ],
            'quality_metrics': [
                'fill_rate', 'speed_of_execution',
                'price_improvement', 'effective_spread'
            ],
            'risk_metrics': [
                'slippage_volatility', 'execution_risk',
                'information_leakage', 'adverse_selection'
            ]
        }
        
    def _register_role_specific_handlers(self):
        """Register Execution Specialist specific message handlers"""
        self.task_handlers.update({
            'execute_order': self._handle_execute_order,
            'optimize_execution': self._handle_optimize_execution,
            'analyze_market_impact': self._handle_analyze_market_impact,
            'select_algorithm': self._handle_select_algorithm,
            'route_order': self._handle_route_order,
            'monitor_execution': self._handle_monitor_execution,
            'analyze_slippage': self._handle_analyze_slippage,
            'venue_analysis': self._handle_venue_analysis
        })
        
    async def _idle_activities(self):
        """Activities when idle - monitor executions and market conditions"""
        # Monitor active orders
        await self._monitor_active_orders()
        
        # Update market microstructure data
        await self._update_microstructure_data()
        
        # Analyze execution performance
        await self._analyze_execution_performance()
        
    async def _handle_execute_order(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle order execution requests"""
        try:
            order = task.get('order', {})
            execution_params = task.get('execution_params', {})
            urgency = task.get('urgency', 'normal')
            
            symbol = order.get('symbol')
            side = order.get('side')  # 'buy' or 'sell'
            quantity = order.get('quantity', 0)
            order_type = order.get('type', 'market')
            
            if not all([symbol, side, quantity]):
                return {
                    'success': False,
                    'error': 'Missing required order parameters'
                }
                
            # Execution optimization prompt
            execution_prompt = f"""
            Optimize order execution for the following trade:
            
            Order Details:
            - Symbol: {symbol}
            - Side: {side}
            - Quantity: {quantity}
            - Type: {order_type}
            - Urgency: {urgency}
            
            Execution Parameters: {json.dumps(execution_params, indent=2)}
            Available Algorithms: {json.dumps(list(self.execution_algorithms.keys()), indent=2)}
            
            Provide execution optimization including:
            
            1. Algorithm Selection:
               - Recommended execution algorithm
               - Algorithm parameters and settings
               - Rationale for selection
               - Alternative algorithms considered
            
            2. Execution Strategy:
               - Order slicing strategy
               - Timing recommendations
               - Venue routing plan
               - Risk management measures
            
            3. Market Impact Analysis:
               - Expected market impact
               - Slippage estimation
               - Liquidity assessment
               - Timing considerations
            
            4. Cost Analysis:
               - Expected transaction costs
               - Implementation shortfall estimate
               - Opportunity cost assessment
               - Total cost breakdown
            
            5. Execution Plan:
               - Step-by-step execution sequence
               - Monitoring requirements
               - Adjustment triggers
               - Contingency plans
            
            6. Performance Targets:
               - Benchmark selection
               - Success criteria
               - Risk limits
               - Quality metrics
            
            Provide detailed execution recommendations optimized for the specific order.
            Format as JSON with clear execution instructions.
            """
            
            result = await self.model_instance.generate(execution_prompt, temperature=0.3)
            
            if result['success']:
                try:
                    execution_plan = json.loads(result['response'])
                    
                    # Create order ID and store execution plan
                    order_id = f"order_{int(time.time())}"
                    self.active_orders[order_id] = {
                        'order': order,
                        'execution_plan': execution_plan,
                        'execution_params': execution_params,
                        'urgency': urgency,
                        'status': 'planned',
                        'created_at': time.time()
                    }
                    
                    # Store in memory
                    await self.memory.store_analysis({
                        'type': 'execution_planning',
                        'order_id': order_id,
                        'order': order,
                        'execution_plan': execution_plan,
                        'timestamp': time.time()
                    })
                    
                    return {
                        'success': True,
                        'order_id': order_id,
                        'execution_plan': execution_plan,
                        'status': 'execution_planned'
                    }
                    
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'Failed to parse execution plan',
                        'raw_response': result['response']
                    }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Execution planning failed')
                }
                
        except Exception as e:
            logger.error(f"Error in order execution: {e}")
            return {
                'success': False,
                'error': str(e)
            }
            
    async def _handle_analyze_market_impact(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle market impact analysis requests"""
        try:
            symbol = task.get('symbol')
            quantity = task.get('quantity', 0)
            side = task.get('side', 'buy')
            market_data = task.get('market_data', {})
            
            # Market impact analysis prompt
            impact_prompt = f"""
            Analyze market impact for the proposed trade:
            
            Trade Details:
            - Symbol: {symbol}
            - Quantity: {quantity}
            - Side: {side}
            
            Market Data: {json.dumps(market_data, indent=2)}
            
            Perform comprehensive market impact analysis:
            
            1. Immediate Impact Analysis:
               - Price impact from order size
               - Bid-ask spread crossing costs
               - Market depth consumption
               - Temporary price displacement
            
            2. Permanent Impact Analysis:
               - Information content of trade
               - Adverse selection costs
               - Long-term price effects
               - Market efficiency considerations
            
            3. Liquidity Analysis:
               - Available liquidity at different price levels
               - Hidden liquidity estimation
               - Liquidity replenishment rates
               - Time-of-day liquidity patterns
            
            4. Execution Cost Modeling:
               - Linear impact model estimates
               - Square-root impact model estimates
               - Almgren-Chriss model predictions
               - Empirical impact relationships
            
            5. Timing Considerations:
               - Optimal execution timing
               - Volume pattern matching
               - Volatility timing effects
               - News and event impacts
            
            6. Mitigation Strategies:
               - Order slicing recommendations
               - Algorithm selection for impact reduction
               - Venue diversification benefits
               - Timing optimization opportunities
            
            Provide quantitative impact estimates and actionable recommendations.
            Format as JSON with numerical impact estimates and strategies.
            """
            
            result = await self.model_instance.generate(impact_prompt, temperature=0.2)
            
            if result['success']:
                try:
                    impact_analysis = json.loads(result['response'])
                    
                    # Store impact analysis
                    await self.memory.store_analysis({
                        'type': 'market_impact_analysis',
                        'symbol': symbol,
                        'quantity': quantity,
                        'side': side,
                        'analysis': impact_analysis,
                        'timestamp': time.time()
                    })
                    
                    return {
                        'success': True,
                        'symbol': symbol,
                        'impact_analysis': impact_analysis,
                        'status': 'market_impact_analyzed'
                    }
                    
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'Failed to parse market impact analysis',
                        'raw_response': result['response']
                    }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Market impact analysis failed')
                }
                
        except Exception as e:
            logger.error(f"Error in market impact analysis: {e}")
            return {
                'success': False,
                'error': str(e)
            }
            
    async def _handle_select_algorithm(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle execution algorithm selection"""
        try:
            order_characteristics = task.get('order_characteristics', {})
            market_conditions = task.get('market_conditions', {})
            objectives = task.get('objectives', {})
            
            # Algorithm selection prompt
            selection_prompt = f"""
            Select optimal execution algorithm based on:
            
            Order Characteristics: {json.dumps(order_characteristics, indent=2)}
            Market Conditions: {json.dumps(market_conditions, indent=2)}
            Objectives: {json.dumps(objectives, indent=2)}
            
            Available Algorithms: {json.dumps(self.execution_algorithms, indent=2)}
            
            Analyze and recommend:
            
            1. Algorithm Evaluation:
               - TWAP suitability and parameters
               - VWAP suitability and parameters
               - Implementation Shortfall suitability
               - POV algorithm considerations
               - Iceberg order applicability
            
            2. Selection Criteria:
               - Order size relative to average volume
               - Urgency and timing constraints
               - Market volatility considerations
               - Liquidity availability
               - Information sensitivity
            
            3. Parameter Optimization:
               - Recommended parameter values
               - Sensitivity analysis
               - Risk-return trade-offs
               - Adaptive parameter adjustment
            
            4. Performance Expectations:
               - Expected execution quality
               - Cost estimates
               - Risk assessment
               - Benchmark comparisons
            
            5. Implementation Details:
               - Algorithm configuration
               - Monitoring requirements
               - Adjustment triggers
               - Fallback strategies
            
            Provide clear algorithm recommendation with detailed justification.
            Format as JSON with algorithm selection and parameters.
            """
            
            result = await self.model_instance.generate(selection_prompt, temperature=0.3)
            
            if result['success']:
                try:
                    algorithm_selection = json.loads(result['response'])
                    
                    # Store algorithm selection
                    await self.memory.store_decision({
                        'type': 'algorithm_selection',
                        'order_characteristics': order_characteristics,
                        'selection': algorithm_selection,
                        'confidence': algorithm_selection.get('confidence', 0.7),
                        'timestamp': time.time()
                    })
                    
                    return {
                        'success': True,
                        'algorithm_selection': algorithm_selection,
                        'status': 'algorithm_selected'
                    }
                    
                except json.JSONDecodeError:
                    return {
                        'success': False,
                        'error': 'Failed to parse algorithm selection',
                        'raw_response': result['response']
                    }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'Algorithm selection failed')
                }
                
        except Exception as e:
            logger.error(f"Error in algorithm selection: {e}")
            return {
                'success': False,
                'error': str(e)
            }
            
    async def _monitor_active_orders(self):
        """Monitor active orders"""
        for order_id, order_info in self.active_orders.items():
            try:
                # Check order status and performance
                await self._check_order_performance(order_id, order_info)
            except Exception as e:
                logger.error(f"Error monitoring order {order_id}: {e}")
                
    async def _check_order_performance(self, order_id: str, order_info: Dict[str, Any]):
        """Check performance of an active order"""
        # Placeholder for order performance checking
        pass
        
    async def _update_microstructure_data(self):
        """Update market microstructure data"""
        # Placeholder for microstructure data updates
        pass
        
    async def _analyze_execution_performance(self):
        """Analyze execution performance"""
        # Placeholder for performance analysis
        pass
        
    # Placeholder methods for other handlers
    async def _handle_optimize_execution(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle execution optimization"""
        return {'success': True, 'status': 'execution_optimized'}
        
    async def _handle_route_order(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle order routing"""
        return {'success': True, 'status': 'order_routed'}
        
    async def _handle_monitor_execution(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle execution monitoring"""
        return {'success': True, 'status': 'execution_monitored'}
        
    async def _handle_analyze_slippage(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle slippage analysis"""
        return {'success': True, 'status': 'slippage_analyzed'}
        
    async def _handle_venue_analysis(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle venue analysis"""
        return {'success': True, 'status': 'venue_analyzed'}

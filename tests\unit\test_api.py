"""
Unit tests for API components
"""

import pytest
import pytest_asyncio
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient
from datetime import datetime

from api.api_server import APIServer
from api.auth_manager import AuthManager
from api.rate_limiter import RateLimiter
from api.websocket_manager import WebSocketManager


class TestAPIServer:
    """Test cases for API Server"""
    
    @pytest.fixture
    def test_config(self):
        return {
            'api': {
                'host': '0.0.0.0',
                'port': 8001,
                'debug': True
            },
            'authentication': {
                'secret_key': 'test_secret_key',
                'token_expiry': 3600
            },
            'rate_limiting': {
                'enabled': True,
                'requests_per_minute': 100
            }
        }
    
    @pytest.fixture
    def mock_trading_system(self):
        mock_system = AsyncMock()
        mock_system.running = True
        mock_system.initialized = True
        mock_system.get_system_status.return_value = {
            'status': 'healthy',
            'uptime_seconds': 3600,
            'version': '1.0.0',
            'components': {},
            'performance': {},
            'resources': {}
        }
        return mock_system
    
    @pytest_asyncio.mark.asyncio
    async def test_api_server_initialization(self, test_config, mock_trading_system):
        """Test API server initialization"""
        api_server = APIServer(test_config, mock_trading_system)
        
        result = await api_server.initialize()
        
        assert result is True
        assert api_server.initialized is True
        assert api_server.app is not None
        assert api_server.auth_manager is not None
        assert api_server.rate_limiter is not None
    
    @pytest_asyncio.mark.asyncio
    async def test_api_server_start_stop(self, test_config, mock_trading_system):
        """Test API server start and stop"""
        api_server = APIServer(test_config, mock_trading_system)
        await api_server.initialize()
        
        # Test start
        with patch('uvicorn.Server') as mock_server:
            mock_server_instance = AsyncMock()
            mock_server.return_value = mock_server_instance
            
            result = await api_server.start()
            assert result is True
            assert api_server.running is True
        
        # Test stop
        result = await api_server.stop()
        assert result is True
        assert api_server.running is False
    
    def test_api_endpoints_exist(self, test_config, mock_trading_system):
        """Test that API endpoints are properly configured"""
        api_server = APIServer(test_config, mock_trading_system)
        
        # This would test that routes are properly configured
        # For now, just verify the app can be created
        assert api_server is not None


class TestAuthManager:
    """Test cases for Authentication Manager"""
    
    @pytest.fixture
    def test_config(self):
        return {
            'authentication': {
                'secret_key': 'test_secret_key',
                'algorithm': 'HS256',
                'token_expiry': 3600,
                'max_login_attempts': 5,
                'lockout_duration': 900
            }
        }
    
    @pytest_asyncio.mark.asyncio
    async def test_auth_manager_initialization(self, test_config):
        """Test authentication manager initialization"""
        auth_manager = AuthManager(test_config)
        
        result = await auth_manager.initialize()
        
        assert result is True
        assert auth_manager.initialized is True
        assert len(auth_manager.users) > 0  # Should have default admin user
    
    @pytest_asyncio.mark.asyncio
    async def test_create_user(self, test_config):
        """Test user creation"""
        auth_manager = AuthManager(test_config)
        await auth_manager.initialize()
        
        user = await auth_manager.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword',
            roles={'viewer'}
        )
        
        assert user is not None
        assert user.username == 'testuser'
        assert user.email == '<EMAIL>'
        assert 'viewer' in user.roles
        assert user.is_active is True
    
    @pytest_asyncio.mark.asyncio
    async def test_authenticate_user(self, test_config):
        """Test user authentication"""
        auth_manager = AuthManager(test_config)
        await auth_manager.initialize()
        
        # Create test user
        await auth_manager.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Test successful authentication
        result = await auth_manager.authenticate_user(
            username='testuser',
            password='testpassword'
        )
        
        assert result is not None
        assert 'access_token' in result
        assert 'refresh_token' in result
        assert result['token_type'] == 'bearer'
    
    @pytest_asyncio.mark.asyncio
    async def test_authenticate_user_invalid_credentials(self, test_config):
        """Test authentication with invalid credentials"""
        auth_manager = AuthManager(test_config)
        await auth_manager.initialize()
        
        with pytest.raises(Exception):  # Should raise HTTPException
            await auth_manager.authenticate_user(
                username='nonexistent',
                password='wrongpassword'
            )
    
    @pytest_asyncio.mark.asyncio
    async def test_create_access_token(self, test_config):
        """Test access token creation"""
        auth_manager = AuthManager(test_config)
        await auth_manager.initialize()
        
        # Create test user
        user = await auth_manager.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        token = await auth_manager._create_access_token(user)
        
        assert token is not None
        assert isinstance(token, str)
        assert len(token) > 0


class TestRateLimiter:
    """Test cases for Rate Limiter"""
    
    @pytest.fixture
    def test_config(self):
        return {
            'rate_limiting': {
                'enabled': True,
                'requests_per_minute': 10,
                'requests_per_hour': 100,
                'requests_per_day': 1000,
                'burst_limit': 5,
                'block_duration': 300
            }
        }
    
    @pytest_asyncio.mark.asyncio
    async def test_rate_limiter_initialization(self, test_config):
        """Test rate limiter initialization"""
        rate_limiter = RateLimiter(test_config)
        
        result = await rate_limiter.initialize()
        
        assert result is True
        assert rate_limiter.initialized is True
        assert rate_limiter.enabled is True
    
    @pytest_asyncio.mark.asyncio
    async def test_rate_limit_check(self, test_config):
        """Test rate limit checking"""
        rate_limiter = RateLimiter(test_config)
        await rate_limiter.initialize()
        
        # Mock request object
        mock_request = Mock()
        mock_request.client.host = '127.0.0.1'
        mock_request.headers = {}
        
        # First request should pass
        result = await rate_limiter.check_rate_limit(mock_request)
        assert result is True
    
    @pytest_asyncio.mark.asyncio
    async def test_rate_limit_statistics(self, test_config):
        """Test rate limit statistics"""
        rate_limiter = RateLimiter(test_config)
        await rate_limiter.initialize()
        
        stats = await rate_limiter.get_statistics()
        
        assert isinstance(stats, dict)
        assert 'enabled' in stats
        assert 'total_requests' in stats
        assert 'blocked_requests' in stats


class TestWebSocketManager:
    """Test cases for WebSocket Manager"""
    
    @pytest.fixture
    def test_config(self):
        return {
            'websocket': {
                'ping_interval': 30,
                'connection_timeout': 300,
                'max_connections': 100,
                'message_rate_limit': 50
            }
        }
    
    @pytest_asyncio.mark.asyncio
    async def test_websocket_manager_initialization(self, test_config):
        """Test WebSocket manager initialization"""
        ws_manager = WebSocketManager(test_config)
        
        result = await ws_manager.initialize()
        
        assert result is True
        assert ws_manager.initialized is True
    
    @pytest_asyncio.mark.asyncio
    async def test_websocket_manager_start_stop(self, test_config):
        """Test WebSocket manager start and stop"""
        ws_manager = WebSocketManager(test_config)
        await ws_manager.initialize()
        
        # Test start
        result = await ws_manager.start()
        assert result is True
        assert ws_manager.running is True
        
        # Test stop
        result = await ws_manager.stop()
        assert result is True
        assert ws_manager.running is False
    
    @pytest_asyncio.mark.asyncio
    async def test_websocket_broadcast(self, test_config):
        """Test WebSocket broadcasting"""
        ws_manager = WebSocketManager(test_config)
        await ws_manager.initialize()
        await ws_manager.start()
        
        # Test broadcasting (no actual connections, should not error)
        await ws_manager.broadcast_market_data('AAPL', {'price': 150.0})
        await ws_manager.broadcast_system_status({'status': 'healthy'})
        
        # Should complete without errors
        assert True
    
    def test_websocket_connection_stats(self, test_config):
        """Test WebSocket connection statistics"""
        ws_manager = WebSocketManager(test_config)
        
        stats = ws_manager.get_connection_stats()
        
        assert isinstance(stats, dict)
        assert 'total_connections' in stats
        assert 'running' in stats


# Integration test for API components
class TestAPIIntegration:
    """Integration tests for API components working together"""
    
    @pytest.fixture
    def test_config(self):
        return {
            'api': {
                'host': '0.0.0.0',
                'port': 8001,
                'debug': True
            },
            'authentication': {
                'secret_key': 'test_secret_key',
                'token_expiry': 3600
            },
            'rate_limiting': {
                'enabled': True,
                'requests_per_minute': 100
            },
            'websocket': {
                'ping_interval': 30,
                'max_connections': 100
            }
        }
    
    @pytest_asyncio.mark.asyncio
    async def test_api_components_integration(self, test_config):
        """Test that all API components work together"""
        mock_trading_system = AsyncMock()
        mock_trading_system.running = True
        mock_trading_system.get_system_status.return_value = {
            'status': 'healthy',
            'uptime_seconds': 3600,
            'version': '1.0.0',
            'components': {},
            'performance': {},
            'resources': {}
        }
        
        # Initialize API server with all components
        api_server = APIServer(test_config, mock_trading_system)
        result = await api_server.initialize()
        
        assert result is True
        assert api_server.auth_manager is not None
        assert api_server.rate_limiter is not None
        assert api_server.websocket_manager is not None
        assert api_server.web_server is not None
        
        # Verify components are initialized
        assert api_server.auth_manager.initialized is True
        assert api_server.rate_limiter.initialized is True
        assert api_server.websocket_manager.initialized is True

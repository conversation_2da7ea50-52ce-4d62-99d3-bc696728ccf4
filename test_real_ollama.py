#!/usr/bin/env python3
"""
Quick test to verify real Ollama integration
"""

import asyncio
import sys
from config.config_manager import ConfigManager
from models.ollama_hub import OllamaModelHub

async def test_real_ollama():
    """Test real Ollama connection and AI generation"""
    
    print("🔌 Testing Ollama connection...")
    
    try:
        # Load configuration
        config_manager = ConfigManager('config/test_config.yaml')
        await config_manager.load_config()
        config = await config_manager.get_config()
        
        # Create real OllamaModelHub
        ollama_hub = OllamaModelHub(config=config)
        await ollama_hub.initialize()
        
        print("✅ Ollama connection successful!")
        
        # Get available models
        available_models = await ollama_hub.get_available_models()
        print(f"📋 Found {len(available_models)} available models")
        
        # Test model deployment
        print("🚀 Testing model deployment...")
        model_instance = await ollama_hub.deploy_model_for_agent(
            agent_name="test_agent",
            role="market_analyst"
        )
        
        if model_instance:
            print(f"✅ Model deployed: {model_instance.model_name}")
            
            # Test actual AI generation
            print("🧠 Testing AI generation...")
            prompt = "Analyze this market data: AAPL stock price is $150.25, up $2.50 (+1.69%) today. Volume is 1M shares. Provide a brief technical analysis."
            
            result = await model_instance.generate(prompt)
            
            if result.get('success'):
                response = result.get('response', '')
                print("✅ AI RESPONSE RECEIVED!")
                print("=" * 50)
                print(response)
                print("=" * 50)
                print(f"Model used: {result.get('model', 'unknown')}")
                print(f"Tokens used: {result.get('tokens_used', 'unknown')}")
            else:
                print(f"❌ AI Generation failed: {result}")
        else:
            print("❌ Model deployment failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 REAL OLLAMA AI TEST")
    print("This will connect to your Ollama server and use actual AI models!")
    print()
    
    asyncio.run(test_real_ollama())

"""
Venue Router - Routes orders to appropriate execution venues
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

from .order_types import Order, OrderType, OrderSide

logger = logging.getLogger(__name__)


class VenueType(Enum):
    """Types of execution venues"""
    EXCHANGE = "exchange"
    DARK_POOL = "dark_pool"
    MARKET_MAKER = "market_maker"
    ECN = "ecn"
    PAPER = "paper"


@dataclass
class VenueInfo:
    """Information about an execution venue"""
    venue_id: str
    venue_name: str
    venue_type: VenueType
    supported_symbols: List[str]
    supported_order_types: List[OrderType]
    min_quantity: float
    max_quantity: float
    commission_rate: float
    latency_ms: float
    fill_rate: float
    active: bool = True


@dataclass
class RoutingDecision:
    """Result of routing decision"""
    venue_id: str
    venue_name: str
    confidence: float
    reason: str
    estimated_fill_time: float
    estimated_cost: float


class VenueRouter:
    """
    Routes orders to the most appropriate execution venue based on
    order characteristics, market conditions, and venue performance.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.routing_config = config.get('venue_routing', {})
        
        # Venue registry
        self.venues: Dict[str, VenueInfo] = {}
        self.venue_performance: Dict[str, Dict[str, float]] = {}
        
        # Routing strategies
        self.routing_strategies = {
            'best_execution': self._route_best_execution,
            'lowest_cost': self._route_lowest_cost,
            'fastest_fill': self._route_fastest_fill,
            'dark_pool_first': self._route_dark_pool_first,
            'smart_order_routing': self._route_smart_order
        }
        
        # State
        self.initialized = False
        
        # Configuration
        self.default_strategy = self.routing_config.get('default_strategy', 'best_execution')
        self.enable_venue_switching = self.routing_config.get('enable_venue_switching', True)
        
    async def initialize(self):
        """Initialize the venue router"""
        if self.initialized:
            return
            
        logger.info("Initializing Venue Router...")
        
        try:
            # Initialize venues
            await self._init_venues()
            
            # Initialize performance tracking
            self._init_performance_tracking()
            
            self.initialized = True
            logger.info("✓ Venue Router initialized")

        except Exception as e:
            logger.error(f"Failed to initialize Venue Router: {e}")
            raise

    async def start(self) -> bool:
        """Start the venue router"""
        try:
            logger.info("Starting Venue Router...")
            return True
        except Exception as e:
            logger.error(f"Failed to start Venue Router: {e}")
            return False

    async def stop(self) -> bool:
        """Stop the venue router"""
        try:
            logger.info("Stopping Venue Router...")
            return True
        except Exception as e:
            logger.error(f"Failed to stop Venue Router: {e}")
            return False
            
    async def _init_venues(self):
        """Initialize available venues"""
        # Default venues for testing
        default_venues = [
            VenueInfo(
                venue_id="paper_trader",
                venue_name="Paper Trader",
                venue_type=VenueType.PAPER,
                supported_symbols=["*"],  # All symbols
                supported_order_types=[OrderType.MARKET, OrderType.LIMIT, OrderType.STOP],
                min_quantity=1.0,
                max_quantity=1000000.0,
                commission_rate=0.0,
                latency_ms=1.0,
                fill_rate=1.0,
                active=True
            ),
            VenueInfo(
                venue_id="exchange_a",
                venue_name="Exchange A",
                venue_type=VenueType.EXCHANGE,
                supported_symbols=["AAPL", "TSLA", "GOOGL", "MSFT", "AMZN"],
                supported_order_types=[OrderType.MARKET, OrderType.LIMIT],
                min_quantity=1.0,
                max_quantity=100000.0,
                commission_rate=0.001,
                latency_ms=5.0,
                fill_rate=0.95,
                active=True
            ),
            VenueInfo(
                venue_id="dark_pool_1",
                venue_name="Dark Pool 1",
                venue_type=VenueType.DARK_POOL,
                supported_symbols=["AAPL", "TSLA", "GOOGL", "MSFT"],
                supported_order_types=[OrderType.LIMIT],
                min_quantity=100.0,
                max_quantity=50000.0,
                commission_rate=0.0005,
                latency_ms=10.0,
                fill_rate=0.8,
                active=True
            ),
            VenueInfo(
                venue_id="market_maker_1",
                venue_name="Market Maker 1",
                venue_type=VenueType.MARKET_MAKER,
                supported_symbols=["AAPL", "TSLA", "GOOGL"],
                supported_order_types=[OrderType.MARKET],
                min_quantity=1.0,
                max_quantity=10000.0,
                commission_rate=0.002,
                latency_ms=2.0,
                fill_rate=0.99,
                active=True
            )
        ]
        
        # Register venues
        for venue in default_venues:
            self.venues[venue.venue_id] = venue
            
        logger.info(f"Initialized {len(self.venues)} venues")
        
    def _init_performance_tracking(self):
        """Initialize venue performance tracking"""
        for venue_id in self.venues:
            self.venue_performance[venue_id] = {
                'fill_rate': self.venues[venue_id].fill_rate,
                'avg_latency': self.venues[venue_id].latency_ms,
                'cost_efficiency': 1.0 - self.venues[venue_id].commission_rate,
                'recent_fills': 0,
                'recent_rejects': 0
            }
            
    async def route_order(self, order: Order, strategy: str = None) -> RoutingDecision:
        """Route an order to the best venue"""
        try:
            strategy = strategy or self.default_strategy
            
            if strategy not in self.routing_strategies:
                logger.warning(f"Unknown routing strategy: {strategy}, using default")
                strategy = self.default_strategy
                
            # Get routing function
            routing_func = self.routing_strategies[strategy]
            
            # Route the order
            decision = await routing_func(order)
            
            logger.info(f"Routed order {order.order_id} to {decision.venue_name} using {strategy} strategy")
            return decision
            
        except Exception as e:
            logger.error(f"Error routing order {order.order_id}: {e}")
            # Fallback to paper trader
            return RoutingDecision(
                venue_id="paper_trader",
                venue_name="Paper Trader",
                confidence=0.5,
                reason=f"Fallback due to error: {e}",
                estimated_fill_time=1.0,
                estimated_cost=0.0
            )
            
    async def _route_best_execution(self, order: Order) -> RoutingDecision:
        """Route for best execution (balanced approach)"""
        eligible_venues = self._get_eligible_venues(order)
        
        if not eligible_venues:
            return self._fallback_routing(order)
            
        # Score venues based on multiple factors
        best_venue = None
        best_score = -1
        
        for venue in eligible_venues:
            performance = self.venue_performance[venue.venue_id]
            
            # Calculate composite score
            fill_score = performance['fill_rate'] * 0.4
            cost_score = performance['cost_efficiency'] * 0.3
            speed_score = (1.0 / max(venue.latency_ms, 1.0)) * 0.3
            
            total_score = fill_score + cost_score + speed_score
            
            if total_score > best_score:
                best_score = total_score
                best_venue = venue
                
        if best_venue:
            return RoutingDecision(
                venue_id=best_venue.venue_id,
                venue_name=best_venue.venue_name,
                confidence=best_score,
                reason="Best execution score",
                estimated_fill_time=best_venue.latency_ms / 1000.0,
                estimated_cost=order.quantity * best_venue.commission_rate
            )
            
        return self._fallback_routing(order)
        
    async def _route_lowest_cost(self, order: Order) -> RoutingDecision:
        """Route to venue with lowest cost"""
        eligible_venues = self._get_eligible_venues(order)
        
        if not eligible_venues:
            return self._fallback_routing(order)
            
        # Find venue with lowest commission
        best_venue = min(eligible_venues, key=lambda v: v.commission_rate)
        
        return RoutingDecision(
            venue_id=best_venue.venue_id,
            venue_name=best_venue.venue_name,
            confidence=0.8,
            reason="Lowest cost",
            estimated_fill_time=best_venue.latency_ms / 1000.0,
            estimated_cost=order.quantity * best_venue.commission_rate
        )
        
    async def _route_fastest_fill(self, order: Order) -> RoutingDecision:
        """Route to venue with fastest fill"""
        eligible_venues = self._get_eligible_venues(order)
        
        if not eligible_venues:
            return self._fallback_routing(order)
            
        # Find venue with lowest latency and highest fill rate
        best_venue = min(eligible_venues, 
                        key=lambda v: v.latency_ms / max(v.fill_rate, 0.1))
        
        return RoutingDecision(
            venue_id=best_venue.venue_id,
            venue_name=best_venue.venue_name,
            confidence=0.9,
            reason="Fastest fill",
            estimated_fill_time=best_venue.latency_ms / 1000.0,
            estimated_cost=order.quantity * best_venue.commission_rate
        )
        
    async def _route_dark_pool_first(self, order: Order) -> RoutingDecision:
        """Route to dark pool first, then exchange"""
        eligible_venues = self._get_eligible_venues(order)
        
        # Prefer dark pools for large orders
        if order.quantity >= 1000:
            dark_pools = [v for v in eligible_venues if v.venue_type == VenueType.DARK_POOL]
            if dark_pools:
                best_dark_pool = max(dark_pools, key=lambda v: v.fill_rate)
                return RoutingDecision(
                    venue_id=best_dark_pool.venue_id,
                    venue_name=best_dark_pool.venue_name,
                    confidence=0.7,
                    reason="Dark pool for large order",
                    estimated_fill_time=best_dark_pool.latency_ms / 1000.0,
                    estimated_cost=order.quantity * best_dark_pool.commission_rate
                )
        
        # Fallback to best execution
        return await self._route_best_execution(order)
        
    async def _route_smart_order(self, order: Order) -> RoutingDecision:
        """Smart order routing with dynamic venue selection"""
        # For now, same as best execution
        return await self._route_best_execution(order)
        
    def _get_eligible_venues(self, order: Order) -> List[VenueInfo]:
        """Get venues eligible for the order"""
        eligible = []
        
        for venue in self.venues.values():
            if not venue.active:
                continue
                
            # Check symbol support
            if "*" not in venue.supported_symbols and order.symbol not in venue.supported_symbols:
                continue
                
            # Check order type support
            if order.order_type not in venue.supported_order_types:
                continue
                
            # Check quantity limits
            if order.quantity < venue.min_quantity or order.quantity > venue.max_quantity:
                continue
                
            eligible.append(venue)
            
        return eligible
        
    def _fallback_routing(self, order: Order) -> RoutingDecision:
        """Fallback routing to paper trader"""
        return RoutingDecision(
            venue_id="paper_trader",
            venue_name="Paper Trader",
            confidence=0.5,
            reason="Fallback routing",
            estimated_fill_time=1.0,
            estimated_cost=0.0
        )
        
    async def update_venue_performance(self, venue_id: str, fill_success: bool, latency: float):
        """Update venue performance metrics"""
        try:
            if venue_id in self.venue_performance:
                performance = self.venue_performance[venue_id]
                
                # Update latency
                performance['avg_latency'] = (performance['avg_latency'] * 0.9) + (latency * 0.1)
                
                # Update fill tracking
                if fill_success:
                    performance['recent_fills'] += 1
                else:
                    performance['recent_rejects'] += 1
                    
                # Update fill rate
                total_recent = performance['recent_fills'] + performance['recent_rejects']
                if total_recent > 0:
                    performance['fill_rate'] = performance['recent_fills'] / total_recent
                    
        except Exception as e:
            logger.error(f"Error updating venue performance: {e}")
            
    async def get_venue_status(self) -> Dict[str, Any]:
        """Get status of all venues"""
        return {
            venue_id: {
                'venue_name': venue.venue_name,
                'venue_type': venue.venue_type.value,
                'active': venue.active,
                'supported_symbols': len(venue.supported_symbols),
                'performance': self.venue_performance.get(venue_id, {})
            }
            for venue_id, venue in self.venues.items()
        }
        
    async def get_stats(self) -> Dict[str, Any]:
        """Get venue router statistics"""
        return {
            'initialized': self.initialized,
            'total_venues': len(self.venues),
            'active_venues': sum(1 for v in self.venues.values() if v.active),
            'routing_strategies': list(self.routing_strategies.keys()),
            'default_strategy': self.default_strategy
        }

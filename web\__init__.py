"""
Web Package - Modern Web Dashboard and Frontend

This package provides a comprehensive web-based dashboard for monitoring
and managing the Advanced Ollama Trading Agent System.

Components:
- dashboard.html: Main dashboard interface
- dashboard.js: JavaScript functionality and real-time updates
- WebSocket integration for live data
- Responsive design with Bootstrap
- Interactive charts and visualizations

Features:
- Real-time portfolio monitoring
- System health dashboard
- Agent and strategy management
- Performance analytics
- Order management interface
- Mobile-responsive design
- WebSocket real-time updates
- Interactive charts and graphs
"""

from .web_server import WebServer
# from .dashboard import Dashboard

__all__ = [
    'WebServer',
    # 'Dashboard'
]

{"validation_id": "validation_**********_dd49335d", "validation_level": "comprehensive", "overall_status": "partial", "overall_score": 0.792880687922348, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "completed", "integration_score": 0.8802630246061998, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "team_manager", "component_type": "core", "status": "completed", "integration_score": 0.7856874399526274, "error_count": 0, "warnings": ["Integration issues in team_manager"], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "completed", "integration_score": 0.8670036669644854, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "analytics_engine", "component_type": "core", "status": "completed", "integration_score": 0.8717535502583899, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "completed", "integration_score": 0.8510006560779053, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "completed", "integration_score": 0.8515935863828157, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "completed", "integration_score": 0.8305427711231729, "error_count": 0, "warnings": ["Integration issues in portfolio_manager"], "dependencies_met": "True"}, {"component_name": "risk_manager", "component_type": "core", "status": "completed", "integration_score": 0.8067823946922008, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "strategy_manager", "component_type": "core", "status": "completed", "integration_score": 0.8230792524429145, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8744317681626546, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8208860643639118, "error_count": 0, "warnings": ["Integration issues in tournament_framework"], "dependencies_met": "False"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8548044349469339, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "completed", "integration_score": 0.8382766361809391, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "completed", "integration_score": 0.7971863961227053, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8303856272554888, "error_count": 0, "warnings": ["Integration issues in advanced_trading_engine"], "dependencies_met": "False"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "completed", "integration_score": 0.8201674131582265, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "completed", "integration_score": 0.8810554573055213, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "completed", "integration_score": 0.9091387687262604, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "completed", "integration_score": 0.9055439636782943, "error_count": 0, "warnings": ["Integration issues in paper_trading_engine"], "dependencies_met": "False"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "completed", "integration_score": 0.8834279126858756, "error_count": 0, "warnings": [], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.9790696547751567, "data_manager": 0.7889697359674535, "analytics_engine": 0.6107338051770717, "ollama_hub": 0.7712444837942676, "execution_engine": 0.6004542344553104, "portfolio_manager": 0.625454430400608, "risk_manager": 0.6430313988876281, "strategy_manager": 0.7443505220630541, "competitive_framework": 0.8076710551702455, "tournament_framework": 0.6982786686909856, "self_improvement_engine": 0.6817943238299603, "regime_adaptation_system": 0.7856047459207769, "performance_optimizer": 0.8248313109464448, "advanced_trading_engine": 0.666754304373838, "ai_coordinator": 0.6699074405736141, "configuration_manager": 0.6429525090492192, "mock_data_providers": 0.6917978468412237, "paper_trading_engine": 0.7255051394672668, "logging_audit_system": 0.7973173962401376}, "team_manager": {"system_coordinator": 0.7210334367071629, "team_manager": 1.0, "data_manager": 0.9892217607369891, "analytics_engine": 0.6414720362351497, "ollama_hub": 0.6373458940984572, "execution_engine": 0.7351848556997771, "portfolio_manager": 0.6975063690501164, "risk_manager": 0.7051188676048307, "strategy_manager": 0.8947761933975116, "competitive_framework": 0.6609402741803301, "tournament_framework": 0.7690807480291555, "self_improvement_engine": 0.8932617009177649, "regime_adaptation_system": 0.7329124612595179, "performance_optimizer": 0.7715179663400018, "advanced_trading_engine": 0.7346850773245038, "ai_coordinator": 0.6520338830718825, "configuration_manager": 0.7446346271332211, "mock_data_providers": 0.7176746693315571, "paper_trading_engine": 0.8953213856293665, "logging_audit_system": 0.8701271020884679}, "data_manager": {"system_coordinator": 0.8159240501490932, "team_manager": 0.7918111290653311, "data_manager": 1.0, "analytics_engine": 0.8258411969417769, "ollama_hub": 0.6670775354555584, "execution_engine": 0.7725000207741458, "portfolio_manager": 0.7264756876119103, "risk_manager": 0.885104610806386, "strategy_manager": 0.7659837196009037, "competitive_framework": 0.7645428935477849, "tournament_framework": 0.8450014374003298, "self_improvement_engine": 0.8269095790495682, "regime_adaptation_system": 0.7088234587197756, "performance_optimizer": 0.8592501590173894, "advanced_trading_engine": 0.7410994262541539, "ai_coordinator": 0.8450843047133074, "configuration_manager": 0.6497984554165278, "mock_data_providers": 0.7058138743886737, "paper_trading_engine": 0.699684071864745, "logging_audit_system": 0.7384811997367167}, "analytics_engine": {"system_coordinator": 0.8276967473970235, "team_manager": 0.7905402709885179, "data_manager": 0.6497544441632388, "analytics_engine": 1.0, "ollama_hub": 0.715123107579815, "execution_engine": 0.879472376883581, "portfolio_manager": 0.8720060752870387, "risk_manager": 0.7346230165754148, "strategy_manager": 0.9692599060119762, "competitive_framework": 0.8376261062658272, "tournament_framework": 0.614185772811764, "self_improvement_engine": 0.7080231691451395, "regime_adaptation_system": 0.883856344072911, "performance_optimizer": 0.8550530749109904, "advanced_trading_engine": 0.8421004260782907, "ai_coordinator": 0.8426519178353682, "configuration_manager": 0.6205765209994865, "mock_data_providers": 0.6484617703384732, "paper_trading_engine": 0.8307803695893542, "logging_audit_system": 0.8626739685014511}, "ollama_hub": {"system_coordinator": 0.6322911298638072, "team_manager": 0.7336268610290406, "data_manager": 0.6453281539609864, "analytics_engine": 0.6420031958695795, "ollama_hub": 1.0, "execution_engine": 0.6176963358671734, "portfolio_manager": 0.827950421465421, "risk_manager": 0.8700458604521166, "strategy_manager": 0.7401182628039634, "competitive_framework": 0.8486952194511308, "tournament_framework": 0.8320939483281149, "self_improvement_engine": 0.837878176402267, "regime_adaptation_system": 0.8976107438724462, "performance_optimizer": 0.7177440970812257, "advanced_trading_engine": 0.8860449133042486, "ai_coordinator": 0.6015793848347257, "configuration_manager": 0.8325762082592314, "mock_data_providers": 0.8345033922303109, "paper_trading_engine": 0.8384285444655302, "logging_audit_system": 0.8351758302984684}, "execution_engine": {"system_coordinator": 0.6365945756936775, "team_manager": 0.6431279060916444, "data_manager": 0.7237250584565277, "analytics_engine": 0.7719612826615498, "ollama_hub": 0.743422770272055, "execution_engine": 1.0, "portfolio_manager": 0.9827695658764102, "risk_manager": 0.7048837357892418, "strategy_manager": 0.7493431055335453, "competitive_framework": 0.6361818732327946, "tournament_framework": 0.8731599739912665, "self_improvement_engine": 0.6322443259715366, "regime_adaptation_system": 0.6447974392741491, "performance_optimizer": 0.8572161858226361, "advanced_trading_engine": 0.625693041929878, "ai_coordinator": 0.7992627034177344, "configuration_manager": 0.6124660366116431, "mock_data_providers": 0.6895726430190513, "paper_trading_engine": 0.7307865744911753, "logging_audit_system": 0.8463651207063726}, "portfolio_manager": {"system_coordinator": 0.8020402617542762, "team_manager": 0.8425028420347829, "data_manager": 0.6242156112621106, "analytics_engine": 0.6000537057872887, "ollama_hub": 0.8081949078729103, "execution_engine": 0.8282737011206458, "portfolio_manager": 1.0, "risk_manager": 0.7082400347294765, "strategy_manager": 0.614188901405133, "competitive_framework": 0.8931192371939859, "tournament_framework": 0.7397062385026085, "self_improvement_engine": 0.7903001048175544, "regime_adaptation_system": 0.6791693252453305, "performance_optimizer": 0.8790089311400087, "advanced_trading_engine": 0.7889028317517827, "ai_coordinator": 0.8779046434295625, "configuration_manager": 0.845786677811479, "mock_data_providers": 0.8848568401264227, "paper_trading_engine": 0.8377162802336544, "logging_audit_system": 0.7184635479504006}, "risk_manager": {"system_coordinator": 0.8330713851619613, "team_manager": 0.8437973867406845, "data_manager": 0.7288182801800624, "analytics_engine": 0.8763088257431254, "ollama_hub": 0.8004639958435656, "execution_engine": 0.8818452206291201, "portfolio_manager": 0.6087401859393055, "risk_manager": 1.0, "strategy_manager": 0.8032476155052871, "competitive_framework": 0.795693847109004, "tournament_framework": 0.7299705006801098, "self_improvement_engine": 0.7104355522147524, "regime_adaptation_system": 0.8963824849421322, "performance_optimizer": 0.6931359343991497, "advanced_trading_engine": 0.7631246127260851, "ai_coordinator": 0.8557498984975529, "configuration_manager": 0.7053000923414378, "mock_data_providers": 0.6009860742965613, "paper_trading_engine": 0.8149266049084176, "logging_audit_system": 0.6104928934774793}, "strategy_manager": {"system_coordinator": 0.746034368877454, "team_manager": 0.8301563952506262, "data_manager": 0.8723471066353903, "analytics_engine": 0.76120367612368, "ollama_hub": 0.6295301785119625, "execution_engine": 0.6535020663294714, "portfolio_manager": 0.7392960662422196, "risk_manager": 0.8505208287418471, "strategy_manager": 1.0, "competitive_framework": 0.7155056021988877, "tournament_framework": 0.6584276358120386, "self_improvement_engine": 0.6744852905294951, "regime_adaptation_system": 0.7150440909045632, "performance_optimizer": 0.8135665694751162, "advanced_trading_engine": 0.6298657747022847, "ai_coordinator": 0.6864463761820269, "configuration_manager": 0.7942003528261568, "mock_data_providers": 0.7477802901612703, "paper_trading_engine": 0.6103198947988722, "logging_audit_system": 0.8695909972482557}, "competitive_framework": {"system_coordinator": 0.6966905560923708, "team_manager": 0.6625819828588545, "data_manager": 0.8161091942032489, "analytics_engine": 0.7660076981550148, "ollama_hub": 0.8407258167718542, "execution_engine": 0.681411235425198, "portfolio_manager": 0.8663386589481112, "risk_manager": 0.7058054264945987, "strategy_manager": 0.8447165225146003, "competitive_framework": 1.0, "tournament_framework": 0.8914365128990958, "self_improvement_engine": 0.8275557866215052, "regime_adaptation_system": 0.6639961861849487, "performance_optimizer": 0.706393186371413, "advanced_trading_engine": 0.8245013593593025, "ai_coordinator": 0.7950852634634535, "configuration_manager": 0.6092906135637344, "mock_data_providers": 0.859185948325553, "paper_trading_engine": 0.6846944499174452, "logging_audit_system": 0.7385540160031474}, "tournament_framework": {"system_coordinator": 0.6974281303711961, "team_manager": 0.810514082509474, "data_manager": 0.8198913421404275, "analytics_engine": 0.7435361780753873, "ollama_hub": 0.6822184648045345, "execution_engine": 0.7885869747955738, "portfolio_manager": 0.6529814572905215, "risk_manager": 0.8074009016364153, "strategy_manager": 0.63267485133008, "competitive_framework": 0.8281484507681224, "tournament_framework": 1.0, "self_improvement_engine": 0.7713259256905244, "regime_adaptation_system": 0.8041354293690242, "performance_optimizer": 0.7825688372133586, "advanced_trading_engine": 0.8958263220973075, "ai_coordinator": 0.7925290449508153, "configuration_manager": 0.8949816599983084, "mock_data_providers": 0.7223442912711406, "paper_trading_engine": 0.6578599592238823, "logging_audit_system": 0.6331165187467371}, "self_improvement_engine": {"system_coordinator": 0.8945505589130037, "team_manager": 0.8923566240411356, "data_manager": 0.7722752884582987, "analytics_engine": 0.6150419109613292, "ollama_hub": 0.7106943901746543, "execution_engine": 0.8094998596790961, "portfolio_manager": 0.7073977099349685, "risk_manager": 0.8617327528387222, "strategy_manager": 0.6318975769449506, "competitive_framework": 0.6830894364590172, "tournament_framework": 0.8595463854820564, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.642733858403524, "performance_optimizer": 0.8732711743340593, "advanced_trading_engine": 0.7776841409436525, "ai_coordinator": 0.654433634632377, "configuration_manager": 0.6062442990117516, "mock_data_providers": 0.8481918819990905, "paper_trading_engine": 0.6217799856911059, "logging_audit_system": 0.7748062538047562}, "regime_adaptation_system": {"system_coordinator": 0.8178430255038494, "team_manager": 0.8686044299173081, "data_manager": 0.709777906425182, "analytics_engine": 0.7897959025692197, "ollama_hub": 0.7589599924190307, "execution_engine": 0.8129294338735334, "portfolio_manager": 0.7297537156017189, "risk_manager": 0.7590044438837743, "strategy_manager": 0.8474583412420659, "competitive_framework": 0.7043010067427719, "tournament_framework": 0.6185634049128158, "self_improvement_engine": 0.8003557418709766, "regime_adaptation_system": 1.0, "performance_optimizer": 0.6883647051377448, "advanced_trading_engine": 0.8734692488002032, "ai_coordinator": 0.7989987882513498, "configuration_manager": 0.7177379404217197, "mock_data_providers": 0.6408495873487833, "paper_trading_engine": 0.8058612785383495, "logging_audit_system": 0.6806602768281613}, "performance_optimizer": {"system_coordinator": 0.6308689718975762, "team_manager": 0.8026737783731082, "data_manager": 0.6206759311534341, "analytics_engine": 0.764609187571961, "ollama_hub": 0.6174978744935271, "execution_engine": 0.7543229047845149, "portfolio_manager": 0.7206839564973452, "risk_manager": 0.7768777407004178, "strategy_manager": 0.8285596527171308, "competitive_framework": 0.75783509229649, "tournament_framework": 0.8736042585505075, "self_improvement_engine": 0.7689192734559555, "regime_adaptation_system": 0.7553756322198496, "performance_optimizer": 1.0, "advanced_trading_engine": 0.8675379520855269, "ai_coordinator": 0.6971608527417973, "configuration_manager": 0.6063844261641603, "mock_data_providers": 0.6316030223639094, "paper_trading_engine": 0.6730272839386171, "logging_audit_system": 0.712468079277123}, "advanced_trading_engine": {"system_coordinator": 0.6413165760590536, "team_manager": 0.8324170867727911, "data_manager": 0.8604389497589848, "analytics_engine": 0.651578827829889, "ollama_hub": 0.8302096261676414, "execution_engine": 0.8669517524980574, "portfolio_manager": 0.7737770829995418, "risk_manager": 0.6508003080706766, "strategy_manager": 0.8963697557879438, "competitive_framework": 0.7022660031588671, "tournament_framework": 0.6515126528052181, "self_improvement_engine": 0.6771043917778006, "regime_adaptation_system": 0.7993959825259651, "performance_optimizer": 0.6371712282668193, "advanced_trading_engine": 1.0, "ai_coordinator": 0.8417855398654742, "configuration_manager": 0.7943122605950578, "mock_data_providers": 0.6535823911300965, "paper_trading_engine": 0.6580089487656234, "logging_audit_system": 0.6707059974107699}, "ai_coordinator": {"system_coordinator": 0.6489136197271984, "team_manager": 0.6250238056719973, "data_manager": 0.6128031723449259, "analytics_engine": 0.7437923560807178, "ollama_hub": 0.7469815088932856, "execution_engine": 0.873737906616046, "portfolio_manager": 0.8666752765310808, "risk_manager": 0.8315002650702465, "strategy_manager": 0.7061207141357152, "competitive_framework": 0.6786207900588278, "tournament_framework": 0.8373548783094427, "self_improvement_engine": 0.6367595484757715, "regime_adaptation_system": 0.8495222478720723, "performance_optimizer": 0.6080518943312303, "advanced_trading_engine": 0.8609704723965491, "ai_coordinator": 1.0, "configuration_manager": 0.7674889838267647, "mock_data_providers": 0.6437839618090506, "paper_trading_engine": 0.8273772215655011, "logging_audit_system": 0.6000062852638935}, "configuration_manager": {"system_coordinator": 0.8204145253271778, "team_manager": 0.7287251425076202, "data_manager": 0.8896019253890595, "analytics_engine": 0.6318237820489904, "ollama_hub": 0.8792837079219169, "execution_engine": 0.7881448597184733, "portfolio_manager": 0.6885394333251806, "risk_manager": 0.894092779645753, "strategy_manager": 0.7732240219427657, "competitive_framework": 0.6318987658966431, "tournament_framework": 0.7106317320724951, "self_improvement_engine": 0.7182105260143129, "regime_adaptation_system": 0.6195355324685208, "performance_optimizer": 0.606023760387561, "advanced_trading_engine": 0.8421632247328484, "ai_coordinator": 0.6787126426286517, "configuration_manager": 1.0, "mock_data_providers": 0.7534537359294293, "paper_trading_engine": 0.8705894199183775, "logging_audit_system": 0.721958953254329}, "mock_data_providers": {"system_coordinator": 0.7516437252109762, "team_manager": 0.6295086739877049, "data_manager": 0.7610045422133656, "analytics_engine": 0.7329891769481571, "ollama_hub": 0.6598450720053338, "execution_engine": 0.8480075926414049, "portfolio_manager": 0.8574428637404437, "risk_manager": 0.8846936641120344, "strategy_manager": 0.6787443595042214, "competitive_framework": 0.8405726322930184, "tournament_framework": 0.7939752401996073, "self_improvement_engine": 0.6861494080950078, "regime_adaptation_system": 0.8372301728047518, "performance_optimizer": 0.8608012320226528, "advanced_trading_engine": 0.6311037891250899, "ai_coordinator": 0.6833414667547745, "configuration_manager": 0.8585086761975776, "mock_data_providers": 1.0, "paper_trading_engine": 0.7831714149004951, "logging_audit_system": 0.6813784246107686}, "paper_trading_engine": {"system_coordinator": 0.8956288686224319, "team_manager": 0.8754845144225829, "data_manager": 0.8405884437489882, "analytics_engine": 0.6995848584632536, "ollama_hub": 0.7644321404870992, "execution_engine": 0.8636854793349383, "portfolio_manager": 0.6928335675443849, "risk_manager": 0.6402185160392319, "strategy_manager": 0.6692166657236134, "competitive_framework": 0.7060915159827533, "tournament_framework": 0.6509102557356933, "self_improvement_engine": 0.8497117413378106, "regime_adaptation_system": 0.7967987209035672, "performance_optimizer": 0.8504434184567291, "advanced_trading_engine": 0.7193877255723031, "ai_coordinator": 0.7724359984465162, "configuration_manager": 0.8515406158844618, "mock_data_providers": 0.8747547643459854, "paper_trading_engine": 1.0, "logging_audit_system": 0.6902708849759034}, "logging_audit_system": {"system_coordinator": 0.7845895409446061, "team_manager": 0.7173265519522374, "data_manager": 0.7870664861653749, "analytics_engine": 0.756433903058745, "ollama_hub": 0.8924081022242574, "execution_engine": 0.7783161646828785, "portfolio_manager": 0.7375078810075846, "risk_manager": 0.7104994644289042, "strategy_manager": 0.7709840246556438, "competitive_framework": 0.7014508623777023, "tournament_framework": 0.7939875978757782, "self_improvement_engine": 0.7992844784813273, "regime_adaptation_system": 0.8652825043968895, "performance_optimizer": 0.7882162277342074, "advanced_trading_engine": 0.6129926733236866, "ai_coordinator": 0.7202548466119363, "configuration_manager": 0.7310110162299108, "mock_data_providers": 0.7408376528617922, "paper_trading_engine": 0.851198561438335, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.8176070023923027, "response_time": 0.8177221761537818, "throughput": 0.7248225285067622, "memory_usage": 0.7265441477504867, "cpu_usage": 0.8468118245216749, "concurrent_operations": 0.6576177918341386}, "critical_issues": ["Components with dependency issues: risk_manager, competitive_framework, tournament_framework, advanced_trading_engine, paper_trading_engine"], "recommendations": ["Improve overall system performance and stability", "Enhance component integration and communication"], "production_ready": "True", "timestamp": **********.5839186}
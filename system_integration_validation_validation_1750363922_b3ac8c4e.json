{"validation_id": "validation_**********_b3ac8c4e", "validation_level": "comprehensive", "overall_status": "partial", "overall_score": 0.7760224110881138, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "partial", "integration_score": 0.7715913013348348, "error_count": 0, "warnings": ["Integration issues in system_coordinator"], "dependencies_met": "True"}, {"component_name": "team_manager", "component_type": "core", "status": "partial", "integration_score": 0.7320199419772753, "error_count": 0, "warnings": ["Functionality concerns in team_manager", "Integration issues in team_manager"], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "partial", "integration_score": 0.7416159218085981, "error_count": 0, "warnings": ["Functionality concerns in data_manager", "Integration issues in data_manager"], "dependencies_met": "True"}, {"component_name": "analytics_engine", "component_type": "core", "status": "partial", "integration_score": 0.8104604927274828, "error_count": 0, "warnings": ["Integration issues in analytics_engine"], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "partial", "integration_score": 0.7626485879742078, "error_count": 0, "warnings": ["Integration issues in ollama_hub"], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "partial", "integration_score": 0.7771134468986237, "error_count": 0, "warnings": ["Functionality concerns in execution_engine"], "dependencies_met": "False"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "partial", "integration_score": 0.7659335477678045, "error_count": 0, "warnings": ["Functionality concerns in portfolio_manager"], "dependencies_met": "True"}, {"component_name": "risk_manager", "component_type": "core", "status": "partial", "integration_score": 0.8072193957352883, "error_count": 0, "warnings": ["Integration issues in risk_manager"], "dependencies_met": "False"}, {"component_name": "strategy_manager", "component_type": "core", "status": "partial", "integration_score": 0.7867745757988265, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "partial", "integration_score": 0.7673367114338727, "error_count": 0, "warnings": ["Integration issues in competitive_framework"], "dependencies_met": "True"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "partial", "integration_score": 0.8400882160608691, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.8034781072084681, "error_count": 0, "warnings": ["Functionality concerns in self_improvement_engine"], "dependencies_met": "True"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "partial", "integration_score": 0.7861285741021813, "error_count": 0, "warnings": ["Integration issues in regime_adaptation_system"], "dependencies_met": "False"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "partial", "integration_score": 0.7421430834313099, "error_count": 0, "warnings": ["Integration issues in performance_optimizer"], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.7431592343991856, "error_count": 0, "warnings": ["Functionality concerns in advanced_trading_engine", "Integration issues in advanced_trading_engine"], "dependencies_met": "False"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "partial", "integration_score": 0.7408259855494115, "error_count": 0, "warnings": ["Functionality concerns in ai_coordinator", "Integration issues in ai_coordinator"], "dependencies_met": "True"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "partial", "integration_score": 0.790869493230246, "error_count": 0, "warnings": ["Integration issues in configuration_manager"], "dependencies_met": "True"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "partial", "integration_score": 0.8942634601600764, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "partial", "integration_score": 0.8225253357674644, "error_count": 0, "warnings": ["Integration issues in paper_trading_engine"], "dependencies_met": "False"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "partial", "integration_score": 0.7764523041720128, "error_count": 0, "warnings": ["Functionality concerns in logging_audit_system"], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.914527112703767, "data_manager": 0.6245698133532286, "analytics_engine": 0.8265881866753757, "ollama_hub": 0.7267704730784655, "execution_engine": 0.7382572715497685, "portfolio_manager": 0.6532039245942852, "risk_manager": 0.8103805968244181, "strategy_manager": 0.8568896553037716, "competitive_framework": 0.6064084760485579, "tournament_framework": 0.7936125713859978, "self_improvement_engine": 0.6058336449756202, "regime_adaptation_system": 0.6385075916747414, "performance_optimizer": 0.6508868497606879, "advanced_trading_engine": 0.6840493861075531, "ai_coordinator": 0.6893190842855801, "configuration_manager": 0.7525639942286759, "mock_data_providers": 0.7917597943154974, "paper_trading_engine": 0.6505623676437907, "logging_audit_system": 0.6896106530622647}, "team_manager": {"system_coordinator": 0.6992328650481131, "team_manager": 1.0, "data_manager": 0.8073681316363838, "analytics_engine": 0.7384581107726685, "ollama_hub": 0.6675910545759187, "execution_engine": 0.6558208019783693, "portfolio_manager": 0.7510925725003815, "risk_manager": 0.8299642607053835, "strategy_manager": 0.6053141865856401, "competitive_framework": 0.8217590410549693, "tournament_framework": 0.8521772918206894, "self_improvement_engine": 0.8767481079241566, "regime_adaptation_system": 0.6642112542820873, "performance_optimizer": 0.7917328697086006, "advanced_trading_engine": 0.7165739463643447, "ai_coordinator": 0.762054230085208, "configuration_manager": 0.7096290375150452, "mock_data_providers": 0.8795793203188258, "paper_trading_engine": 0.6699297851201407, "logging_audit_system": 0.7785050696228037}, "data_manager": {"system_coordinator": 0.822489599861836, "team_manager": 0.8417819201534064, "data_manager": 1.0, "analytics_engine": 0.8684555939795386, "ollama_hub": 0.8813210703877549, "execution_engine": 0.7482953427291662, "portfolio_manager": 0.7182366846030626, "risk_manager": 0.6104412373075943, "strategy_manager": 0.7843748967491095, "competitive_framework": 0.7500068982730421, "tournament_framework": 0.8407147929578621, "self_improvement_engine": 0.8049979052391608, "regime_adaptation_system": 0.6625379746403063, "performance_optimizer": 0.7300551391457919, "advanced_trading_engine": 0.8018383430592901, "ai_coordinator": 0.800876356691943, "configuration_manager": 0.6831223978127776, "mock_data_providers": 0.8320939616814493, "paper_trading_engine": 0.7940759778566959, "logging_audit_system": 0.6827670306591642}, "analytics_engine": {"system_coordinator": 0.8154179896108813, "team_manager": 0.6937370709647761, "data_manager": 0.8533524301223784, "analytics_engine": 1.0, "ollama_hub": 0.6704557287412831, "execution_engine": 0.7274054484828795, "portfolio_manager": 0.8027991746970691, "risk_manager": 0.6068086520895428, "strategy_manager": 0.8732905699886006, "competitive_framework": 0.7825382503519918, "tournament_framework": 0.6367302942359566, "self_improvement_engine": 0.7850531106593124, "regime_adaptation_system": 0.8169189079994532, "performance_optimizer": 0.8308456302301624, "advanced_trading_engine": 0.7857711089510862, "ai_coordinator": 0.8413147847566734, "configuration_manager": 0.8195987008499784, "mock_data_providers": 0.8849793163922813, "paper_trading_engine": 0.6984457596085637, "logging_audit_system": 0.6966153281669597}, "ollama_hub": {"system_coordinator": 0.8044766661589897, "team_manager": 0.7793697509914611, "data_manager": 0.6317069139968381, "analytics_engine": 0.781257639556797, "ollama_hub": 1.0, "execution_engine": 0.8079995720279759, "portfolio_manager": 0.785262723760992, "risk_manager": 0.7216655208668104, "strategy_manager": 0.6597004221393704, "competitive_framework": 0.7743454350745713, "tournament_framework": 0.6563688096105462, "self_improvement_engine": 0.6838046242659651, "regime_adaptation_system": 0.674460578831898, "performance_optimizer": 0.7944867166452327, "advanced_trading_engine": 0.6032652584145266, "ai_coordinator": 0.7481148746266024, "configuration_manager": 0.8566369422334462, "mock_data_providers": 0.6920243274249472, "paper_trading_engine": 0.6864922322860544, "logging_audit_system": 0.6688642286420069}, "execution_engine": {"system_coordinator": 0.872816853633289, "team_manager": 0.6202117679298412, "data_manager": 0.6786381255292114, "analytics_engine": 0.7674852534660415, "ollama_hub": 0.735830505940047, "execution_engine": 1.0, "portfolio_manager": 0.9060725219160303, "risk_manager": 0.7815835773618935, "strategy_manager": 0.7627425510451181, "competitive_framework": 0.8652052101656375, "tournament_framework": 0.7882606312991983, "self_improvement_engine": 0.7886847931516294, "regime_adaptation_system": 0.7598955954328086, "performance_optimizer": 0.777579570333301, "advanced_trading_engine": 0.7257039224192657, "ai_coordinator": 0.852120554878065, "configuration_manager": 0.8238298288993519, "mock_data_providers": 0.8922093286427513, "paper_trading_engine": 0.6699170837429613, "logging_audit_system": 0.7971790259929097}, "portfolio_manager": {"system_coordinator": 0.8102033692664485, "team_manager": 0.8590246061339837, "data_manager": 0.7138768577374479, "analytics_engine": 0.6371030279055266, "ollama_hub": 0.6658891238310626, "execution_engine": 0.7588830542961775, "portfolio_manager": 1.0, "risk_manager": 0.6185456744217173, "strategy_manager": 0.6323662417973508, "competitive_framework": 0.6712452279759434, "tournament_framework": 0.6421838648763036, "self_improvement_engine": 0.8833384348479933, "regime_adaptation_system": 0.6843763672301468, "performance_optimizer": 0.8817708690134907, "advanced_trading_engine": 0.7385861528251355, "ai_coordinator": 0.8998122204011036, "configuration_manager": 0.6948404520915845, "mock_data_providers": 0.7507617825050795, "paper_trading_engine": 0.6199928551581635, "logging_audit_system": 0.6997941240682302}, "risk_manager": {"system_coordinator": 0.6512707670811468, "team_manager": 0.7395987662427933, "data_manager": 0.8402167973005626, "analytics_engine": 0.6300930430734119, "ollama_hub": 0.8913230588213104, "execution_engine": 0.6929342641418615, "portfolio_manager": 0.7712863209374717, "risk_manager": 1.0, "strategy_manager": 0.7394892268236076, "competitive_framework": 0.8822224220549983, "tournament_framework": 0.653214691621468, "self_improvement_engine": 0.7447067612691591, "regime_adaptation_system": 0.7254184762050683, "performance_optimizer": 0.7066040535193845, "advanced_trading_engine": 0.7667914438382251, "ai_coordinator": 0.8759960296173168, "configuration_manager": 0.8371130707439031, "mock_data_providers": 0.814678452975003, "paper_trading_engine": 0.7460952685941196, "logging_audit_system": 0.8636323334837555}, "strategy_manager": {"system_coordinator": 0.8549185307040179, "team_manager": 0.6313029510211552, "data_manager": 0.6772057488225002, "analytics_engine": 0.7641645914980715, "ollama_hub": 0.8026297948910784, "execution_engine": 0.7928939958834538, "portfolio_manager": 0.6638239618811965, "risk_manager": 0.6175400342550074, "strategy_manager": 1.0, "competitive_framework": 0.7460973388916499, "tournament_framework": 0.6213097008391888, "self_improvement_engine": 0.8395484738668144, "regime_adaptation_system": 0.8247229057308423, "performance_optimizer": 0.8664265502819735, "advanced_trading_engine": 0.777744457217721, "ai_coordinator": 0.8957414113701375, "configuration_manager": 0.8927332485165037, "mock_data_providers": 0.779844987898658, "paper_trading_engine": 0.8491052693947185, "logging_audit_system": 0.6233238695584999}, "competitive_framework": {"system_coordinator": 0.842782496524277, "team_manager": 0.640006494884037, "data_manager": 0.779831188387661, "analytics_engine": 0.8424564456173844, "ollama_hub": 0.7093316026449655, "execution_engine": 0.7949800667492714, "portfolio_manager": 0.7541922022545798, "risk_manager": 0.7264835947861421, "strategy_manager": 0.8917131673650285, "competitive_framework": 1.0, "tournament_framework": 0.6089819323872435, "self_improvement_engine": 0.7151173891424238, "regime_adaptation_system": 0.8231130156746735, "performance_optimizer": 0.7425351780265337, "advanced_trading_engine": 0.8194307698983514, "ai_coordinator": 0.8056076444134839, "configuration_manager": 0.6831606393272812, "mock_data_providers": 0.7663881802885278, "paper_trading_engine": 0.69632043730246, "logging_audit_system": 0.6401617457074171}, "tournament_framework": {"system_coordinator": 0.6795213113975587, "team_manager": 0.8138555651643667, "data_manager": 0.8325784340674307, "analytics_engine": 0.8732897570485427, "ollama_hub": 0.60558186395897, "execution_engine": 0.7293303709322649, "portfolio_manager": 0.778946758955077, "risk_manager": 0.8551086663997367, "strategy_manager": 0.7234173369037274, "competitive_framework": 0.7142867825271315, "tournament_framework": 1.0, "self_improvement_engine": 0.7634093594928038, "regime_adaptation_system": 0.6365105332416408, "performance_optimizer": 0.8736862376810992, "advanced_trading_engine": 0.881356119350803, "ai_coordinator": 0.8327553038349396, "configuration_manager": 0.6662561953122901, "mock_data_providers": 0.8549436673887709, "paper_trading_engine": 0.8700368947568542, "logging_audit_system": 0.8214037301301021}, "self_improvement_engine": {"system_coordinator": 0.675760622580671, "team_manager": 0.8448952401335816, "data_manager": 0.6214650692482616, "analytics_engine": 0.7226171725026712, "ollama_hub": 0.7960135837613085, "execution_engine": 0.7667189845036266, "portfolio_manager": 0.6060103917451558, "risk_manager": 0.8267265111972613, "strategy_manager": 0.86189366553146, "competitive_framework": 0.8326037944030232, "tournament_framework": 0.7035246299640991, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.6266251279170748, "performance_optimizer": 0.8713719469219481, "advanced_trading_engine": 0.614594407257994, "ai_coordinator": 0.8306403720416866, "configuration_manager": 0.709737950091113, "mock_data_providers": 0.6678641362096447, "paper_trading_engine": 0.7584118625159999, "logging_audit_system": 0.6389360666466184}, "regime_adaptation_system": {"system_coordinator": 0.899497856718213, "team_manager": 0.8306096955544042, "data_manager": 0.7183223730014912, "analytics_engine": 0.8768246192937601, "ollama_hub": 0.8445534163091335, "execution_engine": 0.756363421842518, "portfolio_manager": 0.659859592403441, "risk_manager": 0.8364084252378635, "strategy_manager": 0.8145649061149692, "competitive_framework": 0.6732256648502901, "tournament_framework": 0.8750512638839786, "self_improvement_engine": 0.6407490532386262, "regime_adaptation_system": 1.0, "performance_optimizer": 0.72301098587645, "advanced_trading_engine": 0.624629043177818, "ai_coordinator": 0.8535836676553608, "configuration_manager": 0.6274308950796191, "mock_data_providers": 0.889790123176972, "paper_trading_engine": 0.7540745716774415, "logging_audit_system": 0.7317652064913874}, "performance_optimizer": {"system_coordinator": 0.7496270248876136, "team_manager": 0.7907162683983391, "data_manager": 0.7880745475164923, "analytics_engine": 0.8623819356203918, "ollama_hub": 0.7531989614283968, "execution_engine": 0.6339797969302482, "portfolio_manager": 0.8208696413457774, "risk_manager": 0.6431394567674561, "strategy_manager": 0.6155533957807173, "competitive_framework": 0.7988130832599427, "tournament_framework": 0.7809085419229485, "self_improvement_engine": 0.6648679364970991, "regime_adaptation_system": 0.8244300025265223, "performance_optimizer": 1.0, "advanced_trading_engine": 0.6732919318822336, "ai_coordinator": 0.8598636169986154, "configuration_manager": 0.7443502351622207, "mock_data_providers": 0.647043243599889, "paper_trading_engine": 0.7837441034453094, "logging_audit_system": 0.6925671216474882}, "advanced_trading_engine": {"system_coordinator": 0.8851127944329247, "team_manager": 0.6460087723976681, "data_manager": 0.7100732358246526, "analytics_engine": 0.8686797112461613, "ollama_hub": 0.6417252252619655, "execution_engine": 0.7595580374036119, "portfolio_manager": 0.6421688285164597, "risk_manager": 0.6730118822874608, "strategy_manager": 0.7577942061888563, "competitive_framework": 0.6509089412939674, "tournament_framework": 0.8825270588416836, "self_improvement_engine": 0.7435032282504583, "regime_adaptation_system": 0.8043783767884072, "performance_optimizer": 0.8596292715201934, "advanced_trading_engine": 1.0, "ai_coordinator": 0.6792224273447444, "configuration_manager": 0.7240638303546234, "mock_data_providers": 0.786998611210002, "paper_trading_engine": 0.7959503892562916, "logging_audit_system": 0.8209781315085313}, "ai_coordinator": {"system_coordinator": 0.735846154665942, "team_manager": 0.6528416677685598, "data_manager": 0.7479497145515804, "analytics_engine": 0.8120749708312953, "ollama_hub": 0.7075053547430216, "execution_engine": 0.6793978551236417, "portfolio_manager": 0.7566320608814889, "risk_manager": 0.8134421801074043, "strategy_manager": 0.7903562899335196, "competitive_framework": 0.8065816684332952, "tournament_framework": 0.6895329959157493, "self_improvement_engine": 0.7716586402666958, "regime_adaptation_system": 0.7064923762248816, "performance_optimizer": 0.7558727507250542, "advanced_trading_engine": 0.8965009068138442, "ai_coordinator": 1.0, "configuration_manager": 0.8305658688107392, "mock_data_providers": 0.8047703578614254, "paper_trading_engine": 0.6206035417625392, "logging_audit_system": 0.6775968030725116}, "configuration_manager": {"system_coordinator": 0.6546791275927458, "team_manager": 0.6023103340458607, "data_manager": 0.8417474481943513, "analytics_engine": 0.6982212770155778, "ollama_hub": 0.7736651537962669, "execution_engine": 0.8676989097410579, "portfolio_manager": 0.8448826537886622, "risk_manager": 0.6999129940190891, "strategy_manager": 0.6915481991544367, "competitive_framework": 0.8282966457832247, "tournament_framework": 0.6884117726763539, "self_improvement_engine": 0.7924915660475386, "regime_adaptation_system": 0.8612945214332994, "performance_optimizer": 0.6388654236113224, "advanced_trading_engine": 0.8617242185314431, "ai_coordinator": 0.7901583864693317, "configuration_manager": 1.0, "mock_data_providers": 0.8225577995424616, "paper_trading_engine": 0.8911350101881841, "logging_audit_system": 0.7201957671042744}, "mock_data_providers": {"system_coordinator": 0.6167256547866332, "team_manager": 0.8475981090432294, "data_manager": 0.6469482610833417, "analytics_engine": 0.6282851013312042, "ollama_hub": 0.7931192674110827, "execution_engine": 0.6506204179448956, "portfolio_manager": 0.7323023412445099, "risk_manager": 0.603308158956033, "strategy_manager": 0.8459653709674415, "competitive_framework": 0.6269437283537891, "tournament_framework": 0.8924224356231298, "self_improvement_engine": 0.6897933617957901, "regime_adaptation_system": 0.6723692039586803, "performance_optimizer": 0.7137320941914571, "advanced_trading_engine": 0.8125794120151798, "ai_coordinator": 0.8285548842748628, "configuration_manager": 0.7424122207803541, "mock_data_providers": 1.0, "paper_trading_engine": 0.6030805986342925, "logging_audit_system": 0.6818196890302638}, "paper_trading_engine": {"system_coordinator": 0.80294036438106, "team_manager": 0.689780485925665, "data_manager": 0.6997166261845744, "analytics_engine": 0.6540645803036692, "ollama_hub": 0.849240471381663, "execution_engine": 0.8449008899901116, "portfolio_manager": 0.6565863702189031, "risk_manager": 0.6491921248671452, "strategy_manager": 0.882865134982457, "competitive_framework": 0.8011514477205962, "tournament_framework": 0.8448750408001706, "self_improvement_engine": 0.6162961306897279, "regime_adaptation_system": 0.8667467782814054, "performance_optimizer": 0.7404544844434564, "advanced_trading_engine": 0.6765500954541227, "ai_coordinator": 0.6199142217662079, "configuration_manager": 0.6019592974546645, "mock_data_providers": 0.7490735584842737, "paper_trading_engine": 1.0, "logging_audit_system": 0.8686659314929897}, "logging_audit_system": {"system_coordinator": 0.8148886336353477, "team_manager": 0.6491230308456737, "data_manager": 0.630055609988787, "analytics_engine": 0.6288422868949146, "ollama_hub": 0.6640888169247721, "execution_engine": 0.7547299646079453, "portfolio_manager": 0.8891170732428475, "risk_manager": 0.7835111388826487, "strategy_manager": 0.6551471528610343, "competitive_framework": 0.6782354552627945, "tournament_framework": 0.8147918776755387, "self_improvement_engine": 0.8863557485716096, "regime_adaptation_system": 0.6530540429072402, "performance_optimizer": 0.8238253234794801, "advanced_trading_engine": 0.8108117565448957, "ai_coordinator": 0.6437909564644924, "configuration_manager": 0.6041305678671199, "mock_data_providers": 0.7052235465713826, "paper_trading_engine": 0.8633868194523087, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.7426512755364425, "response_time": 0.9326569184573613, "throughput": 0.7245679831565437, "memory_usage": 0.7234656729514771, "cpu_usage": 0.7859998957125539, "concurrent_operations": 0.7282981164765845}, "critical_issues": ["Components with dependency issues: execution_engine, risk_manager, regime_adaptation_system, advanced_trading_engine, paper_trading_engine"], "recommendations": ["Improve 20 partially working components", "Improve overall system performance and stability", "Enhance component integration and communication"], "production_ready": "False", "timestamp": **********.8455663}
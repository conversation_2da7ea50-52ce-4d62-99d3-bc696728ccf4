"""
Web Server - Static file serving and web interface hosting
"""

import asyncio
import logging
import os
from typing import Dict, Any, Optional
from pathlib import Path
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse

logger = logging.getLogger(__name__)


class WebServer:
    """
    Web server for hosting the trading system web interface.
    
    Features:
    - Static file serving
    - Template rendering
    - Real-time dashboard
    - Interactive charts and visualizations
    - Responsive design
    - Mobile-friendly interface
    """
    
    def __init__(self, config: Dict[str, Any], api_server=None):
        self.config = config
        self.web_config = config.get('web', {})
        self.api_server = api_server
        
        # Web configuration
        self.enabled = self.web_config.get('enabled', True)
        self.static_path = Path(self.web_config.get('static_path', 'web/static'))
        self.templates_path = Path(self.web_config.get('templates_path', 'web/templates'))
        
        # Ensure directories exist
        self.static_path.mkdir(parents=True, exist_ok=True)
        self.templates_path.mkdir(parents=True, exist_ok=True)
        
        # Templates
        self.templates = Jinja2Templates(directory=str(self.templates_path))
        
        # State
        self.initialized = False
    
    async def initialize(self) -> bool:
        """Initialize web server"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Web Server...")
            
            # Create static files and templates
            await self._create_static_files()
            await self._create_templates()
            
            self.initialized = True
            logger.info("✓ Web Server initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Web Server: {e}")
            return False
    
    def setup_routes(self, app: FastAPI):
        """Setup web routes on FastAPI app"""
        if not self.enabled:
            return
            
        try:
            # Mount static files
            app.mount("/static", StaticFiles(directory=str(self.static_path)), name="static")
            
            # Dashboard route
            @app.get("/dashboard", response_class=HTMLResponse, tags=["Web"])
            async def dashboard(request: Request):
                """Main dashboard page"""
                return self.templates.TemplateResponse(
                    "dashboard.html",
                    {
                        "request": request,
                        "title": "Trading System Dashboard",
                        "api_base_url": "/api/v1"
                    }
                )
            
            # System status page
            @app.get("/system", response_class=HTMLResponse, tags=["Web"])
            async def system_status(request: Request):
                """System status page"""
                return self.templates.TemplateResponse(
                    "system.html",
                    {
                        "request": request,
                        "title": "System Status",
                        "api_base_url": "/api/v1"
                    }
                )
            
            # Agents page
            @app.get("/agents", response_class=HTMLResponse, tags=["Web"])
            async def agents_page(request: Request):
                """Agents management page"""
                return self.templates.TemplateResponse(
                    "agents.html",
                    {
                        "request": request,
                        "title": "Agent Management",
                        "api_base_url": "/api/v1"
                    }
                )
            
            # Portfolio page
            @app.get("/portfolio", response_class=HTMLResponse, tags=["Web"])
            async def portfolio_page(request: Request):
                """Portfolio tracking page"""
                return self.templates.TemplateResponse(
                    "portfolio.html",
                    {
                        "request": request,
                        "title": "Portfolio Management",
                        "api_base_url": "/api/v1"
                    }
                )
            
            # Analytics page
            @app.get("/analytics", response_class=HTMLResponse, tags=["Web"])
            async def analytics_page(request: Request):
                """Analytics and insights page"""
                return self.templates.TemplateResponse(
                    "analytics.html",
                    {
                        "request": request,
                        "title": "Analytics & Insights",
                        "api_base_url": "/api/v1"
                    }
                )
            
            # Redirect root to dashboard
            @app.get("/", response_class=HTMLResponse, tags=["Web"])
            async def root_redirect(request: Request):
                """Redirect root to dashboard"""
                return self.templates.TemplateResponse(
                    "index.html",
                    {
                        "request": request,
                        "title": "Advanced Ollama Trading Agent System",
                        "api_base_url": "/api/v1"
                    }
                )
            
            logger.info("✓ Web routes configured")
            
        except Exception as e:
            logger.error(f"Error setting up web routes: {e}")
    
    # Private methods
    
    async def _create_static_files(self):
        """Create static files (CSS, JS, etc.)"""
        try:
            # Create CSS directory
            css_dir = self.static_path / "css"
            css_dir.mkdir(exist_ok=True)
            
            # Create main CSS file
            main_css = css_dir / "main.css"
            if not main_css.exists():
                with open(main_css, 'w') as f:
                    f.write(self._get_main_css())
            
            # Create JS directory
            js_dir = self.static_path / "js"
            js_dir.mkdir(exist_ok=True)
            
            # Create main JS file
            main_js = js_dir / "main.js"
            if not main_js.exists():
                with open(main_js, 'w') as f:
                    f.write(self._get_main_js())
            
            # Create dashboard JS file
            dashboard_js = js_dir / "dashboard.js"
            if not dashboard_js.exists():
                with open(dashboard_js, 'w') as f:
                    f.write(self._get_dashboard_js())
            
            logger.debug("Static files created")
            
        except Exception as e:
            logger.error(f"Error creating static files: {e}")
            raise
    
    async def _create_templates(self):
        """Create HTML templates"""
        try:
            # Create base template
            base_template = self.templates_path / "base.html"
            if not base_template.exists():
                with open(base_template, 'w') as f:
                    f.write(self._get_base_template())
            
            # Create index template
            index_template = self.templates_path / "index.html"
            if not index_template.exists():
                with open(index_template, 'w') as f:
                    f.write(self._get_index_template())
            
            # Create dashboard template
            dashboard_template = self.templates_path / "dashboard.html"
            if not dashboard_template.exists():
                with open(dashboard_template, 'w') as f:
                    f.write(self._get_dashboard_template())
            
            # Create system template
            system_template = self.templates_path / "system.html"
            if not system_template.exists():
                with open(system_template, 'w') as f:
                    f.write(self._get_system_template())
            
            # Create agents template
            agents_template = self.templates_path / "agents.html"
            if not agents_template.exists():
                with open(agents_template, 'w') as f:
                    f.write(self._get_agents_template())
            
            # Create portfolio template
            portfolio_template = self.templates_path / "portfolio.html"
            if not portfolio_template.exists():
                with open(portfolio_template, 'w') as f:
                    f.write(self._get_portfolio_template())
            
            # Create analytics template
            analytics_template = self.templates_path / "analytics.html"
            if not analytics_template.exists():
                with open(analytics_template, 'w') as f:
                    f.write(self._get_analytics_template())
            
            logger.debug("HTML templates created")
            
        except Exception as e:
            logger.error(f"Error creating templates: {e}")
            raise
    
    def _get_main_css(self) -> str:
        """Get main CSS content"""
        return """
/* Advanced Ollama Trading Agent System - Main CSS */

:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --dark-bg: #1e293b;
    --light-bg: #f8fafc;
    --border-color: #e2e8f0;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background-color: var(--light-bg);
    color: #334155;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: white;
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav a {
    text-decoration: none;
    color: var(--secondary-color);
    font-weight: 500;
    transition: color 0.2s;
}

.nav a:hover,
.nav a.active {
    color: var(--primary-color);
}

/* Main content */
.main {
    padding: 2rem 0;
}

/* Cards */
.card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

/* Grid */
.grid {
    display: grid;
    gap: 1.5rem;
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Status indicators */
.status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-healthy {
    background-color: #dcfce7;
    color: #166534;
}

.status-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.status-error {
    background-color: #fee2e2;
    color: #991b1b;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: currentColor;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #1d4ed8;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    font-weight: 600;
    color: #374151;
    background-color: #f9fafb;
}

/* Loading */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: var(--secondary-color);
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav {
        gap: 1rem;
    }
    
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }
}
"""
    
    def _get_main_js(self) -> str:
        """Get main JavaScript content"""
        return """
// Advanced Ollama Trading Agent System - Main JavaScript

class TradingSystemAPI {
    constructor(baseUrl = '/api/v1') {
        this.baseUrl = baseUrl;
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    // System endpoints
    async getSystemStatus() {
        return this.request('/system/status');
    }

    async getComponents() {
        return this.request('/system/components');
    }

    async executeCommand(command, parameters = {}) {
        return this.request('/system/commands', {
            method: 'POST',
            body: JSON.stringify({ command, parameters })
        });
    }

    // Agent endpoints
    async getAgents() {
        return this.request('/agents/');
    }

    async getAgent(agentId) {
        return this.request(`/agents/${agentId}`);
    }

    // Portfolio endpoints
    async getPortfolios() {
        return this.request('/portfolio/');
    }

    // Analytics endpoints
    async getMarketInsights(symbol = null) {
        const params = symbol ? `?symbol=${symbol}` : '';
        return this.request(`/analytics/insights${params}`);
    }
}

class WebSocketManager {
    constructor(baseUrl = '') {
        this.baseUrl = baseUrl.replace('http', 'ws');
        this.connections = new Map();
        this.reconnectAttempts = new Map();
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
    }

    connect(endpoint, onMessage, onError = null) {
        const url = `${this.baseUrl}/ws/${endpoint}`;
        
        try {
            const ws = new WebSocket(url);
            
            ws.onopen = () => {
                console.log(`WebSocket connected: ${endpoint}`);
                this.reconnectAttempts.set(endpoint, 0);
            };
            
            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    onMessage(data);
                } catch (error) {
                    console.error('Failed to parse WebSocket message:', error);
                }
            };
            
            ws.onclose = () => {
                console.log(`WebSocket disconnected: ${endpoint}`);
                this.connections.delete(endpoint);
                this.attemptReconnect(endpoint, onMessage, onError);
            };
            
            ws.onerror = (error) => {
                console.error(`WebSocket error on ${endpoint}:`, error);
                if (onError) onError(error);
            };
            
            this.connections.set(endpoint, ws);
            return ws;
            
        } catch (error) {
            console.error(`Failed to connect WebSocket ${endpoint}:`, error);
            if (onError) onError(error);
        }
    }

    attemptReconnect(endpoint, onMessage, onError) {
        const attempts = this.reconnectAttempts.get(endpoint) || 0;
        
        if (attempts < this.maxReconnectAttempts) {
            this.reconnectAttempts.set(endpoint, attempts + 1);
            
            setTimeout(() => {
                console.log(`Attempting to reconnect WebSocket ${endpoint} (${attempts + 1}/${this.maxReconnectAttempts})`);
                this.connect(endpoint, onMessage, onError);
            }, this.reconnectDelay * Math.pow(2, attempts));
        } else {
            console.error(`Max reconnection attempts reached for ${endpoint}`);
        }
    }

    send(endpoint, message) {
        const ws = this.connections.get(endpoint);
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(message));
        } else {
            console.warn(`WebSocket ${endpoint} not connected`);
        }
    }

    disconnect(endpoint) {
        const ws = this.connections.get(endpoint);
        if (ws) {
            ws.close();
            this.connections.delete(endpoint);
        }
    }

    disconnectAll() {
        for (const [endpoint, ws] of this.connections) {
            ws.close();
        }
        this.connections.clear();
    }
}

// Utility functions
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

function formatPercentage(value, decimals = 2) {
    return `${(value * 100).toFixed(decimals)}%`;
}

function formatDateTime(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(date));
}

function showLoading(element) {
    element.innerHTML = '<div class="loading"><div class="spinner"></div>Loading...</div>';
}

function showError(element, message) {
    element.innerHTML = `<div class="error">Error: ${message}</div>`;
}

// Global instances
const api = new TradingSystemAPI();
const wsManager = new WebSocketManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('Trading System Web Interface loaded');
    
    // Set active navigation
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav a');
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    wsManager.disconnectAll();
});
"""
    
    def _get_dashboard_js(self) -> str:
        """Get dashboard JavaScript content"""
        return """
// Dashboard-specific JavaScript

class Dashboard {
    constructor() {
        this.updateInterval = 5000; // 5 seconds
        this.charts = {};
        this.init();
    }

    async init() {
        await this.loadSystemStatus();
        await this.loadPortfolioSummary();
        await this.loadRecentActivity();
        
        // Setup real-time updates
        this.setupWebSocket();
        this.startPeriodicUpdates();
    }

    async loadSystemStatus() {
        const statusElement = document.getElementById('system-status');
        if (!statusElement) return;

        try {
            showLoading(statusElement);
            const status = await api.getSystemStatus();
            this.renderSystemStatus(statusElement, status);
        } catch (error) {
            showError(statusElement, error.message);
        }
    }

    renderSystemStatus(element, status) {
        const statusClass = status.status === 'healthy' ? 'status-healthy' : 
                           status.status === 'degraded' ? 'status-warning' : 'status-error';
        
        element.innerHTML = `
            <div class="card-header">
                <h3 class="card-title">System Status</h3>
                <span class="status ${statusClass}">
                    <span class="status-dot"></span>
                    ${status.status.toUpperCase()}
                </span>
            </div>
            <div class="grid grid-2">
                <div>
                    <p><strong>Uptime:</strong> ${this.formatUptime(status.uptime_seconds)}</p>
                    <p><strong>Version:</strong> ${status.version}</p>
                    <p><strong>Components:</strong> ${Object.keys(status.components).length}</p>
                </div>
                <div>
                    <p><strong>CPU:</strong> ${status.resources.cpu?.usage_percent?.toFixed(1) || 0}%</p>
                    <p><strong>Memory:</strong> ${status.resources.memory?.usage_percent?.toFixed(1) || 0}%</p>
                    <p><strong>Disk:</strong> ${status.resources.disk?.usage_percent?.toFixed(1) || 0}%</p>
                </div>
            </div>
        `;
    }

    async loadPortfolioSummary() {
        const portfolioElement = document.getElementById('portfolio-summary');
        if (!portfolioElement) return;

        try {
            showLoading(portfolioElement);
            const portfolios = await api.getPortfolios();
            this.renderPortfolioSummary(portfolioElement, portfolios);
        } catch (error) {
            showError(portfolioElement, error.message);
        }
    }

    renderPortfolioSummary(element, portfolios) {
        if (!portfolios || portfolios.length === 0) {
            element.innerHTML = `
                <div class="card-header">
                    <h3 class="card-title">Portfolio Summary</h3>
                </div>
                <p>No portfolios available</p>
            `;
            return;
        }

        const totalValue = portfolios.reduce((sum, p) => sum + p.total_value, 0);
        const totalCash = portfolios.reduce((sum, p) => sum + p.cash, 0);

        element.innerHTML = `
            <div class="card-header">
                <h3 class="card-title">Portfolio Summary</h3>
                <a href="/portfolio" class="btn btn-primary">View Details</a>
            </div>
            <div class="grid grid-2">
                <div>
                    <p><strong>Total Value:</strong> ${formatCurrency(totalValue)}</p>
                    <p><strong>Cash:</strong> ${formatCurrency(totalCash)}</p>
                </div>
                <div>
                    <p><strong>Portfolios:</strong> ${portfolios.length}</p>
                    <p><strong>Invested:</strong> ${formatCurrency(totalValue - totalCash)}</p>
                </div>
            </div>
        `;
    }

    async loadRecentActivity() {
        const activityElement = document.getElementById('recent-activity');
        if (!activityElement) return;

        try {
            showLoading(activityElement);
            // This would load recent trading activity
            this.renderRecentActivity(activityElement, []);
        } catch (error) {
            showError(activityElement, error.message);
        }
    }

    renderRecentActivity(element, activities) {
        element.innerHTML = `
            <div class="card-header">
                <h3 class="card-title">Recent Activity</h3>
            </div>
            <div class="activity-list">
                ${activities.length > 0 ? 
                    activities.map(activity => `
                        <div class="activity-item">
                            <span class="activity-time">${formatDateTime(activity.timestamp)}</span>
                            <span class="activity-description">${activity.description}</span>
                        </div>
                    `).join('') :
                    '<p>No recent activity</p>'
                }
            </div>
        `;
    }

    setupWebSocket() {
        // Connect to system status updates
        wsManager.connect('system', (data) => {
            if (data.type === 'system_status') {
                const statusElement = document.getElementById('system-status');
                if (statusElement) {
                    this.renderSystemStatus(statusElement, data.data);
                }
            }
        });

        // Connect to portfolio updates
        wsManager.connect('portfolio', (data) => {
            if (data.type === 'portfolio_update') {
                this.loadPortfolioSummary();
            }
        });
    }

    startPeriodicUpdates() {
        setInterval(async () => {
            await this.loadSystemStatus();
            await this.loadPortfolioSummary();
        }, this.updateInterval);
    }

    formatUptime(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        
        if (days > 0) {
            return `${days}d ${hours}h ${minutes}m`;
        } else if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else {
            return `${minutes}m`;
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new Dashboard();
});
"""

    def _get_base_template(self) -> str:
        """Get base HTML template"""
        return """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ title }}{% endblock %} - Trading System</title>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/main.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    {% block head %}{% endblock %}
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    🤖 Trading Agent System
                </div>
                <nav class="nav">
                    <a href="/">Home</a>
                    <a href="/dashboard">Dashboard</a>
                    <a href="/system">System</a>
                    <a href="/agents">Agents</a>
                    <a href="/portfolio">Portfolio</a>
                    <a href="/analytics">Analytics</a>
                </nav>
            </div>
        </div>
    </header>

    <main class="main">
        <div class="container">
            {% block content %}{% endblock %}
        </div>
    </main>

    <script src="{{ url_for('static', path='/js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>"""

    def _get_index_template(self) -> str:
        """Get index HTML template"""
        return """{% extends "base.html" %}

{% block content %}
<div class="hero">
    <div class="card">
        <div class="card-header">
            <h1 class="card-title">Advanced Ollama Trading Agent System</h1>
        </div>
        <p>Welcome to the comprehensive trading agent system powered by Ollama AI. This system provides intelligent trading capabilities with advanced analytics, risk management, and portfolio optimization.</p>

        <div class="grid grid-3" style="margin-top: 2rem;">
            <div class="card">
                <h3>🤖 AI Agents</h3>
                <p>Intelligent trading agents powered by large language models for market analysis and decision making.</p>
                <a href="/agents" class="btn btn-primary">Manage Agents</a>
            </div>

            <div class="card">
                <h3>📊 Analytics</h3>
                <p>Advanced market analytics with real-time insights, pattern recognition, and predictive modeling.</p>
                <a href="/analytics" class="btn btn-primary">View Analytics</a>
            </div>

            <div class="card">
                <h3>💼 Portfolio</h3>
                <p>Comprehensive portfolio management with risk assessment and performance tracking.</p>
                <a href="/portfolio" class="btn btn-primary">View Portfolio</a>
            </div>
        </div>

        <div style="margin-top: 2rem; text-align: center;">
            <a href="/dashboard" class="btn btn-primary" style="font-size: 1.1rem; padding: 0.75rem 2rem;">
                Go to Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}"""

    def _get_dashboard_template(self) -> str:
        """Get dashboard HTML template"""
        return """{% extends "base.html" %}

{% block content %}
<h1>Trading System Dashboard</h1>

<div class="grid grid-2">
    <div class="card" id="system-status">
        <!-- System status will be loaded here -->
    </div>

    <div class="card" id="portfolio-summary">
        <!-- Portfolio summary will be loaded here -->
    </div>
</div>

<div class="grid grid-2">
    <div class="card" id="recent-activity">
        <!-- Recent activity will be loaded here -->
    </div>

    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Quick Actions</h3>
        </div>
        <div class="grid grid-2">
            <a href="/agents" class="btn btn-primary">Manage Agents</a>
            <a href="/analytics" class="btn btn-secondary">View Analytics</a>
            <a href="/portfolio" class="btn btn-success">Portfolio</a>
            <a href="/system" class="btn btn-warning">System Status</a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='/js/dashboard.js') }}"></script>
{% endblock %}"""

    def _get_system_template(self) -> str:
        """Get system status HTML template"""
        return """{% extends "base.html" %}

{% block content %}
<h1>System Status</h1>

<div class="card" id="system-overview">
    <!-- System overview will be loaded here -->
</div>

<div class="card" id="component-status">
    <!-- Component status will be loaded here -->
</div>

<div class="grid grid-2">
    <div class="card" id="performance-metrics">
        <!-- Performance metrics will be loaded here -->
    </div>

    <div class="card" id="system-controls">
        <div class="card-header">
            <h3 class="card-title">System Controls</h3>
        </div>
        <div class="grid grid-2">
            <button class="btn btn-success" onclick="executeCommand('start')">Start System</button>
            <button class="btn btn-warning" onclick="executeCommand('stop')">Stop System</button>
            <button class="btn btn-secondary" onclick="executeCommand('restart')">Restart System</button>
            <button class="btn btn-primary" onclick="executeCommand('reload_config')">Reload Config</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
async function executeCommand(command) {
    try {
        const result = await api.executeCommand(command);
        alert(`Command executed: ${result.message || 'Success'}`);
        location.reload();
    } catch (error) {
        alert(`Error: ${error.message}`);
    }
}

document.addEventListener('DOMContentLoaded', async () => {
    // Load system status
    try {
        const status = await api.getSystemStatus();
        document.getElementById('system-overview').innerHTML = `
            <div class="card-header">
                <h3 class="card-title">System Overview</h3>
            </div>
            <div class="grid grid-3">
                <div><strong>Status:</strong> ${status.status}</div>
                <div><strong>Version:</strong> ${status.version}</div>
                <div><strong>Uptime:</strong> ${Math.floor(status.uptime_seconds / 3600)}h</div>
            </div>
        `;

        const components = await api.getComponents();
        document.getElementById('component-status').innerHTML = `
            <div class="card-header">
                <h3 class="card-title">Components</h3>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>Component</th>
                        <th>Status</th>
                        <th>Running</th>
                        <th>Initialized</th>
                    </tr>
                </thead>
                <tbody>
                    ${components.map(comp => `
                        <tr>
                            <td>${comp.name}</td>
                            <td><span class="status status-${comp.status === 'healthy' ? 'healthy' : 'error'}">${comp.status}</span></td>
                            <td>${comp.running ? '✅' : '❌'}</td>
                            <td>${comp.initialized ? '✅' : '❌'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    } catch (error) {
        console.error('Failed to load system status:', error);
    }
});
</script>
{% endblock %}"""

    def _get_agents_template(self) -> str:
        """Get agents HTML template"""
        return """{% extends "base.html" %}

{% block content %}
<h1>Agent Management</h1>

<div class="card" id="agents-list">
    <!-- Agents list will be loaded here -->
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', async () => {
    try {
        const agents = await api.getAgents();
        document.getElementById('agents-list').innerHTML = `
            <div class="card-header">
                <h3 class="card-title">Active Agents</h3>
            </div>
            ${agents.length > 0 ? `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Last Activity</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${agents.map(agent => `
                            <tr>
                                <td>${agent.name}</td>
                                <td>${agent.type}</td>
                                <td><span class="status status-${agent.status === 'active' ? 'healthy' : 'error'}">${agent.status}</span></td>
                                <td>${agent.last_activity ? formatDateTime(agent.last_activity) : 'Never'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            ` : '<p>No agents available</p>'}
        `;
    } catch (error) {
        showError(document.getElementById('agents-list'), error.message);
    }
});
</script>
{% endblock %}"""

    def _get_portfolio_template(self) -> str:
        """Get portfolio HTML template"""
        return """{% extends "base.html" %}

{% block content %}
<h1>Portfolio Management</h1>

<div class="card" id="portfolio-overview">
    <!-- Portfolio overview will be loaded here -->
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', async () => {
    try {
        const portfolios = await api.getPortfolios();
        document.getElementById('portfolio-overview').innerHTML = `
            <div class="card-header">
                <h3 class="card-title">Portfolio Overview</h3>
            </div>
            ${portfolios.length > 0 ? `
                <div class="grid grid-2">
                    ${portfolios.map(portfolio => `
                        <div class="card">
                            <h4>${portfolio.name}</h4>
                            <p><strong>Total Value:</strong> ${formatCurrency(portfolio.total_value)}</p>
                            <p><strong>Cash:</strong> ${formatCurrency(portfolio.cash)}</p>
                            <p><strong>Positions:</strong> ${Object.keys(portfolio.positions).length}</p>
                        </div>
                    `).join('')}
                </div>
            ` : '<p>No portfolios available</p>'}
        `;
    } catch (error) {
        showError(document.getElementById('portfolio-overview'), error.message);
    }
});
</script>
{% endblock %}"""

    def _get_analytics_template(self) -> str:
        """Get analytics HTML template"""
        return """{% extends "base.html" %}

{% block content %}
<h1>Analytics & Insights</h1>

<div class="card" id="market-insights">
    <!-- Market insights will be loaded here -->
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', async () => {
    try {
        const insights = await api.getMarketInsights();
        document.getElementById('market-insights').innerHTML = `
            <div class="card-header">
                <h3 class="card-title">Market Insights</h3>
            </div>
            ${insights.insights && insights.insights.length > 0 ? `
                <div class="insights-list">
                    ${insights.insights.map(insight => `
                        <div class="card">
                            <h4>${insight.insight_type}</h4>
                            <p>${insight.description}</p>
                            <p><strong>Confidence:</strong> ${formatPercentage(insight.confidence)}</p>
                            <p><strong>Impact:</strong> ${insight.impact_score.toFixed(2)}</p>
                        </div>
                    `).join('')}
                </div>
            ` : '<p>No insights available</p>'}
        `;
    } catch (error) {
        showError(document.getElementById('market-insights'), error.message);
    }
});
</script>
{% endblock %}"""

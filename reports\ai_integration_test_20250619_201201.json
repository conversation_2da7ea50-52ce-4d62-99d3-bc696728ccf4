{"timestamp": "2025-06-19T20:12:01.136824", "ai_integration_result": {"success": true, "agents": {"success": true, "activated_agents": {"team_leader": "19e63fd3-92f7-4a67-a3ce-8a1924b7e602", "market_analyst": "f4120703-f0f9-4cef-bf5c-f984bdb0dc43", "strategy_developer": "9a3a4b43-e4b3-4ee4-911c-a5b6f28133d2", "risk_manager": "fe678e3b-a6b8-4bf1-aff0-8c2e1e1b533b", "execution_specialist": "bd591c72-6e70-48c4-95e4-11e03e3eac49", "performance_evaluator": "a8d822d6-dc03-4529-b103-d1a2b7373b7a"}, "failed_agents": [], "success_rate": 1.0, "total_agents": 6}, "teams": {"success": true, "formed_teams": {"Alpha_Generation_Team": {"team_id": "team_momentum_team_1750378321", "type": "momentum_team", "mission": {"objective": "alpha_generation", "strategy": "momentum", "risk_tolerance": "moderate"}}, "Risk_Control_Team": {"team_id": "team_risk_management_team_1750378321", "type": "risk_management_team", "mission": {"objective": "risk_management", "strategy": "risk_control", "risk_tolerance": "conservative"}}}, "failed_teams": [], "success_rate": 1.0, "total_teams": 2}, "strategies": {"success": true, "deployed_strategies": {"AI_Momentum_Strategy": {"strategy_id": "5f0e0e28-f6c3-43fd-99e7-89f1b24a6109", "type": "momentum", "ai_config": {"ai_enabled": true, "ai_model": "huihui_ai/magistral-abliterated:24b", "decision_threshold": 0.7}}, "AI_Mean_Reversion_Strategy": {"strategy_id": "bd7bd0c9-2258-44c3-9811-11261ff36aa3", "type": "mean_reversion", "ai_config": {"ai_enabled": true, "ai_model": "phi4-reasoning:plus", "decision_threshold": 0.8}}}, "failed_strategies": [], "success_rate": 1.0, "total_strategies": 2}, "validation": {"success": true, "test_results": {"ai_model_connectivity": true, "agent_communication": true, "team_coordination": true, "strategy_ai_integration": false}, "passed_tests": 3, "total_tests": 4, "success_rate": 0.75}, "timestamp": "2025-06-19T20:12:01.134695"}, "ai_status": {"ai_integration_active": true, "deployed_agents": {"team_leader": "19e63fd3-92f7-4a67-a3ce-8a1924b7e602", "market_analyst": "f4120703-f0f9-4cef-bf5c-f984bdb0dc43", "strategy_developer": "9a3a4b43-e4b3-4ee4-911c-a5b6f28133d2", "risk_manager": "fe678e3b-a6b8-4bf1-aff0-8c2e1e1b533b", "execution_specialist": "bd591c72-6e70-48c4-95e4-11e03e3eac49", "performance_evaluator": "a8d822d6-dc03-4529-b103-d1a2b7373b7a"}, "formed_teams": {"Alpha_Generation_Team": {"team_id": "team_momentum_team_1750378321", "type": "momentum_team", "mission": {"objective": "alpha_generation", "strategy": "momentum", "risk_tolerance": "moderate"}}, "Risk_Control_Team": {"team_id": "team_risk_management_team_1750378321", "type": "risk_management_team", "mission": {"objective": "risk_management", "strategy": "risk_control", "risk_tolerance": "conservative"}}}, "active_strategies": {"AI_Momentum_Strategy": {"strategy_id": "5f0e0e28-f6c3-43fd-99e7-89f1b24a6109", "type": "momentum", "ai_config": {"ai_enabled": true, "ai_model": "huihui_ai/magistral-abliterated:24b", "decision_threshold": 0.7}}, "AI_Mean_Reversion_Strategy": {"strategy_id": "bd7bd0c9-2258-44c3-9811-11261ff36aa3", "type": "mean_reversion", "ai_config": {"ai_enabled": true, "ai_model": "phi4-reasoning:plus", "decision_threshold": 0.8}}}, "ollama_models": 21, "deployed_models": 13, "timestamp": "2025-06-19T20:12:01.135733"}, "initial_status": {"state": "running", "health": 0.8181818181818182, "active_agents": 0, "active_strategies": 0}, "final_status": {"state": "running", "health": 0.8181818181818182, "active_agents": 6, "active_strategies": 0}}
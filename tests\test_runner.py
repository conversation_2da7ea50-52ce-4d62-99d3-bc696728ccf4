"""
Test Runner - Automated test execution and orchestration
"""

import asyncio
import logging
import os
import sys
import subprocess
import json
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime
import pytest
import coverage

from .test_framework import TestFramework, TestType, TestStatus

logger = logging.getLogger(__name__)


class TestRunner:
    """
    Automated test runner for the trading system.
    
    Features:
    - Automated test discovery
    - Multiple test execution modes
    - Coverage reporting
    - Test result aggregation
    - CI/CD integration
    - Test scheduling
    - Parallel execution
    - Test filtering and selection
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.test_config = config.get('testing', {})
        
        # Test runner configuration
        self.test_directory = Path(self.test_config.get('test_directory', 'tests'))
        self.coverage_enabled = self.test_config.get('coverage_enabled', True)
        self.coverage_threshold = self.test_config.get('coverage_threshold', 80)
        self.parallel_workers = self.test_config.get('parallel_workers', 4)
        
        # Test framework
        self.test_framework = TestFramework(config)
        
        # Coverage tracking
        self.coverage_instance = None
        
        # Test results
        self.execution_results: Dict[str, Any] = {}
        
        # State
        self.initialized = False
    
    async def initialize(self) -> bool:
        """Initialize test runner"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Test Runner...")
            
            # Initialize test framework
            await self.test_framework.initialize()
            
            # Setup coverage if enabled
            if self.coverage_enabled:
                await self._setup_coverage()
            
            # Discover tests
            await self._discover_tests()
            
            self.initialized = True
            logger.info("✓ Test Runner initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Test Runner: {e}")
            return False
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all discovered tests"""
        try:
            logger.info("Running all tests...")
            
            # Start coverage if enabled
            if self.coverage_instance:
                self.coverage_instance.start()
            
            # Run tests by type
            results = {}
            
            # Unit tests
            unit_results = await self.run_unit_tests()
            results['unit'] = unit_results
            
            # Integration tests
            integration_results = await self.run_integration_tests()
            results['integration'] = integration_results
            
            # Performance tests
            performance_results = await self.run_performance_tests()
            results['performance'] = performance_results
            
            # Stop coverage and generate report
            if self.coverage_instance:
                self.coverage_instance.stop()
                coverage_report = await self._generate_coverage_report()
                results['coverage'] = coverage_report
            
            # Generate final report
            final_report = await self._generate_final_report(results)
            self.execution_results = final_report
            
            logger.info("All tests completed")
            return final_report
            
        except Exception as e:
            logger.error(f"Error running all tests: {e}")
            return {}
    
    async def run_unit_tests(self) -> Dict[str, Any]:
        """Run unit tests"""
        try:
            logger.info("Running unit tests...")
            
            # Use pytest for unit tests
            pytest_args = [
                str(self.test_directory / "unit"),
                "-v",
                "--tb=short",
                f"-n={self.parallel_workers}",
                "--json-report",
                "--json-report-file=test_results_unit.json"
            ]
            
            result = await self._run_pytest(pytest_args)
            
            # Parse results
            unit_results = await self._parse_pytest_results("test_results_unit.json")
            
            logger.info(f"Unit tests completed: {unit_results.get('summary', {})}")
            return unit_results
            
        except Exception as e:
            logger.error(f"Error running unit tests: {e}")
            return {}
    
    async def run_integration_tests(self) -> Dict[str, Any]:
        """Run integration tests"""
        try:
            logger.info("Running integration tests...")
            
            # Use test framework for integration tests
            results = await self.test_framework.run_tests(
                test_types=[TestType.INTEGRATION]
            )
            
            # Convert to standard format
            integration_results = await self._convert_framework_results(results)
            
            logger.info(f"Integration tests completed: {len(results)} tests")
            return integration_results
            
        except Exception as e:
            logger.error(f"Error running integration tests: {e}")
            return {}
    
    async def run_performance_tests(self) -> Dict[str, Any]:
        """Run performance tests"""
        try:
            logger.info("Running performance tests...")
            
            # Use test framework for performance tests
            results = await self.test_framework.run_tests(
                test_types=[TestType.PERFORMANCE, TestType.LOAD]
            )
            
            # Convert to standard format
            performance_results = await self._convert_framework_results(results)
            
            # Add performance metrics
            performance_results['metrics'] = self.test_framework.performance_metrics
            
            logger.info(f"Performance tests completed: {len(results)} tests")
            return performance_results
            
        except Exception as e:
            logger.error(f"Error running performance tests: {e}")
            return {}
    
    async def run_security_tests(self) -> Dict[str, Any]:
        """Run security tests"""
        try:
            logger.info("Running security tests...")
            
            # Use test framework for security tests
            results = await self.test_framework.run_tests(
                test_types=[TestType.SECURITY]
            )
            
            # Convert to standard format
            security_results = await self._convert_framework_results(results)
            
            logger.info(f"Security tests completed: {len(results)} tests")
            return security_results
            
        except Exception as e:
            logger.error(f"Error running security tests: {e}")
            return {}
    
    async def run_smoke_tests(self) -> Dict[str, Any]:
        """Run smoke tests"""
        try:
            logger.info("Running smoke tests...")
            
            # Use test framework for smoke tests
            results = await self.test_framework.run_tests(
                test_types=[TestType.SMOKE]
            )
            
            # Convert to standard format
            smoke_results = await self._convert_framework_results(results)
            
            logger.info(f"Smoke tests completed: {len(results)} tests")
            return smoke_results
            
        except Exception as e:
            logger.error(f"Error running smoke tests: {e}")
            return {}
    
    async def run_regression_tests(self) -> Dict[str, Any]:
        """Run regression tests"""
        try:
            logger.info("Running regression tests...")
            
            # Use test framework for regression tests
            results = await self.test_framework.run_tests(
                test_types=[TestType.REGRESSION]
            )
            
            # Convert to standard format
            regression_results = await self._convert_framework_results(results)
            
            logger.info(f"Regression tests completed: {len(results)} tests")
            return regression_results
            
        except Exception as e:
            logger.error(f"Error running regression tests: {e}")
            return {}
    
    async def run_custom_test_suite(self, suite_name: str) -> Dict[str, Any]:
        """Run a custom test suite"""
        try:
            logger.info(f"Running custom test suite: {suite_name}")
            
            # Use test framework for custom suite
            results = await self.test_framework.run_tests(
                suite_ids=[suite_name]
            )
            
            # Convert to standard format
            custom_results = await self._convert_framework_results(results)
            
            logger.info(f"Custom test suite completed: {len(results)} tests")
            return custom_results
            
        except Exception as e:
            logger.error(f"Error running custom test suite {suite_name}: {e}")
            return {}
    
    async def validate_system(self) -> Dict[str, Any]:
        """Run comprehensive system validation"""
        try:
            logger.info("Starting comprehensive system validation...")
            
            validation_results = {}
            
            # Run smoke tests first
            smoke_results = await self.run_smoke_tests()
            validation_results['smoke'] = smoke_results
            
            # If smoke tests pass, run full suite
            if smoke_results.get('success_rate', 0) >= 90:
                full_results = await self.run_all_tests()
                validation_results.update(full_results)
            else:
                logger.warning("Smoke tests failed, skipping full test suite")
                validation_results['status'] = 'smoke_tests_failed'
            
            # Run security validation
            security_results = await self.run_security_tests()
            validation_results['security'] = security_results
            
            # Generate validation report
            validation_report = await self._generate_validation_report(validation_results)
            
            logger.info("System validation completed")
            return validation_report
            
        except Exception as e:
            logger.error(f"Error during system validation: {e}")
            return {'status': 'error', 'error': str(e)}
    
    # Private methods
    
    async def _setup_coverage(self):
        """Setup code coverage tracking"""
        try:
            self.coverage_instance = coverage.Coverage(
                source=['agents', 'models', 'strategies', 'risk', 'execution', 
                       'portfolio', 'learning', 'database', 'analytics', 'api'],
                omit=['*/tests/*', '*/test_*', '*/__pycache__/*']
            )
            
            logger.debug("Coverage tracking setup completed")
            
        except Exception as e:
            logger.error(f"Error setting up coverage: {e}")
            self.coverage_instance = None
    
    async def _discover_tests(self):
        """Discover test files and functions"""
        try:
            # Create test directories if they don't exist
            test_dirs = ['unit', 'integration', 'performance', 'security', 'e2e']
            
            for test_dir in test_dirs:
                dir_path = self.test_directory / test_dir
                dir_path.mkdir(parents=True, exist_ok=True)
                
                # Create __init__.py if it doesn't exist
                init_file = dir_path / "__init__.py"
                if not init_file.exists():
                    init_file.write_text("# Test directory\n")
            
            logger.debug("Test discovery completed")
            
        except Exception as e:
            logger.error(f"Error discovering tests: {e}")
    
    async def _run_pytest(self, args: List[str]) -> int:
        """Run pytest with given arguments"""
        try:
            # Run pytest in subprocess
            process = await asyncio.create_subprocess_exec(
                sys.executable, "-m", "pytest", *args,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.warning(f"pytest returned non-zero exit code: {process.returncode}")
                if stderr:
                    logger.warning(f"pytest stderr: {stderr.decode()}")
            
            return process.returncode
            
        except Exception as e:
            logger.error(f"Error running pytest: {e}")
            return 1
    
    async def _parse_pytest_results(self, results_file: str) -> Dict[str, Any]:
        """Parse pytest JSON results"""
        try:
            results_path = Path(results_file)
            
            if not results_path.exists():
                return {'error': 'Results file not found'}
            
            with open(results_path, 'r') as f:
                results = json.load(f)
            
            # Extract summary information
            summary = results.get('summary', {})
            
            return {
                'total': summary.get('total', 0),
                'passed': summary.get('passed', 0),
                'failed': summary.get('failed', 0),
                'error': summary.get('error', 0),
                'skipped': summary.get('skipped', 0),
                'success_rate': (summary.get('passed', 0) / max(1, summary.get('total', 1))) * 100,
                'duration': results.get('duration', 0),
                'tests': results.get('tests', [])
            }
            
        except Exception as e:
            logger.error(f"Error parsing pytest results: {e}")
            return {'error': str(e)}
    
    async def _convert_framework_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Convert test framework results to standard format"""
        try:
            if not results:
                return {}
            
            test_results = list(results.values())
            
            total = len(test_results)
            passed = len([r for r in test_results if r.status == TestStatus.PASSED])
            failed = len([r for r in test_results if r.status == TestStatus.FAILED])
            error = len([r for r in test_results if r.status == TestStatus.ERROR])
            skipped = len([r for r in test_results if r.status == TestStatus.SKIPPED])
            
            success_rate = (passed / max(1, total)) * 100
            total_duration = sum(r.duration or 0 for r in test_results)
            
            return {
                'total': total,
                'passed': passed,
                'failed': failed,
                'error': error,
                'skipped': skipped,
                'success_rate': success_rate,
                'duration': total_duration,
                'tests': [
                    {
                        'name': r.test_name,
                        'status': r.status.value,
                        'duration': r.duration,
                        'error': r.error_message
                    }
                    for r in test_results
                ]
            }
            
        except Exception as e:
            logger.error(f"Error converting framework results: {e}")
            return {}
    
    async def _generate_coverage_report(self) -> Dict[str, Any]:
        """Generate code coverage report"""
        try:
            if not self.coverage_instance:
                return {}
            
            # Generate coverage report
            coverage_data = self.coverage_instance.get_data()
            
            # Calculate coverage percentage
            total_lines = 0
            covered_lines = 0
            
            for filename in coverage_data.measured_files():
                file_data = coverage_data.lines(filename)
                if file_data:
                    total_lines += len(file_data)
                    covered_lines += len([line for line in file_data if coverage_data.has_arcs() or True])
            
            coverage_percentage = (covered_lines / max(1, total_lines)) * 100
            
            return {
                'coverage_percentage': coverage_percentage,
                'total_lines': total_lines,
                'covered_lines': covered_lines,
                'threshold_met': coverage_percentage >= self.coverage_threshold,
                'threshold': self.coverage_threshold
            }
            
        except Exception as e:
            logger.error(f"Error generating coverage report: {e}")
            return {}
    
    async def _generate_final_report(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final test execution report"""
        try:
            # Calculate overall statistics
            total_tests = 0
            total_passed = 0
            total_failed = 0
            total_duration = 0
            
            for test_type, test_results in results.items():
                if test_type == 'coverage':
                    continue
                    
                total_tests += test_results.get('total', 0)
                total_passed += test_results.get('passed', 0)
                total_failed += test_results.get('failed', 0)
                total_duration += test_results.get('duration', 0)
            
            overall_success_rate = (total_passed / max(1, total_tests)) * 100
            
            # Determine overall status
            status = 'passed' if overall_success_rate >= 90 else 'failed'
            
            # Check coverage threshold
            coverage_data = results.get('coverage', {})
            coverage_met = coverage_data.get('threshold_met', True)
            
            if not coverage_met:
                status = 'coverage_failed'
            
            return {
                'status': status,
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'total_tests': total_tests,
                    'total_passed': total_passed,
                    'total_failed': total_failed,
                    'success_rate': overall_success_rate,
                    'total_duration': total_duration
                },
                'results_by_type': results,
                'coverage': coverage_data
            }
            
        except Exception as e:
            logger.error(f"Error generating final report: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def _generate_validation_report(self, validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate system validation report"""
        try:
            # Determine validation status
            validation_status = 'passed'
            
            # Check smoke tests
            smoke_results = validation_results.get('smoke', {})
            if smoke_results.get('success_rate', 0) < 90:
                validation_status = 'smoke_failed'
            
            # Check security tests
            security_results = validation_results.get('security', {})
            if security_results.get('success_rate', 0) < 100:
                validation_status = 'security_failed'
            
            # Check overall test results
            if 'unit' in validation_results:
                overall_success = validation_results.get('summary', {}).get('success_rate', 0)
                if overall_success < 85:
                    validation_status = 'tests_failed'
            
            return {
                'validation_status': validation_status,
                'timestamp': datetime.now().isoformat(),
                'results': validation_results,
                'recommendations': await self._generate_recommendations(validation_results)
            }
            
        except Exception as e:
            logger.error(f"Error generating validation report: {e}")
            return {'validation_status': 'error', 'error': str(e)}
    
    async def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        try:
            # Check coverage
            coverage_data = results.get('coverage', {})
            if coverage_data.get('coverage_percentage', 100) < 80:
                recommendations.append("Increase test coverage to at least 80%")
            
            # Check performance
            performance_data = results.get('performance', {})
            if performance_data.get('success_rate', 100) < 90:
                recommendations.append("Address performance test failures")
            
            # Check security
            security_data = results.get('security', {})
            if security_data.get('success_rate', 100) < 100:
                recommendations.append("Fix all security test failures immediately")
            
            # Check overall success rate
            overall_success = results.get('summary', {}).get('success_rate', 100)
            if overall_success < 95:
                recommendations.append("Improve overall test success rate")
            
            if not recommendations:
                recommendations.append("All tests passing - system is ready for deployment")
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            recommendations.append("Unable to generate recommendations due to error")
        
        return recommendations

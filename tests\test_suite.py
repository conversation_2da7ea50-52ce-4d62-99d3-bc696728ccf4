"""
Comprehensive Test Suite Runner

This module provides a comprehensive test suite that runs all tests
and generates detailed reports on system functionality and reliability.
"""

import asyncio
import logging
import time
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import subprocess

from tests.test_framework import TestFramework, TestType
from tests.test_validator import TestValidator
from tests.mock_data import MockDataGenerator


class TestSuiteRunner:
    """
    Comprehensive test suite runner that orchestrates all testing activities.
    
    Features:
    - Runs all test categories (unit, integration, performance, security)
    - Generates comprehensive test reports
    - Validates system functionality
    - Provides test metrics and analytics
    - Supports parallel test execution
    - Handles test dependencies and ordering
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.test_config = config.get('testing', {})
        
        # Test framework components
        self.test_framework = TestFramework(config)
        self.test_validator = TestValidator(config)
        self.mock_data_generator = MockDataGenerator(config)
        
        # Test results storage
        self.test_results: Dict[str, Any] = {}
        self.test_metrics: Dict[str, Any] = {}
        self.test_reports: List[Dict[str, Any]] = []
        
        # Test configuration
        self.parallel_execution = self.test_config.get('parallel', True)
        self.max_workers = self.test_config.get('max_workers', 4)
        self.timeout = self.test_config.get('timeout', 300)
        self.retry_failed = self.test_config.get('retry_failed', True)
        self.max_retries = self.test_config.get('max_retries', 3)
        
        # State
        self.initialized = False
        self.running = False
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
    
    async def initialize(self) -> bool:
        """Initialize test suite runner"""
        if self.initialized:
            return True
            
        try:
            self.logger.info("Initializing Test Suite Runner...")
            
            # Initialize components
            await self.test_framework.initialize()
            await self.test_validator.initialize()
            await self.mock_data_generator.initialize()
            
            self.initialized = True
            self.logger.info("✓ Test Suite Runner initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Test Suite Runner: {e}")
            return False
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all test categories and generate comprehensive report"""
        if not self.initialized:
            await self.initialize()
        
        self.running = True
        start_time = time.time()
        
        try:
            self.logger.info("🚀 Starting comprehensive test suite...")
            
            # Test execution plan
            test_plan = [
                ('unit_tests', self.run_unit_tests),
                ('integration_tests', self.run_integration_tests),
                ('performance_tests', self.run_performance_tests),
                ('security_tests', self.run_security_tests),
                ('end_to_end_tests', self.run_end_to_end_tests),
                ('validation_tests', self.run_validation_tests)
            ]
            
            # Execute test plan
            for test_category, test_function in test_plan:
                self.logger.info(f"📋 Running {test_category}...")
                
                category_start = time.time()
                result = await test_function()
                category_duration = time.time() - category_start
                
                self.test_results[test_category] = {
                    'result': result,
                    'duration': category_duration,
                    'timestamp': datetime.now().isoformat()
                }
                
                self.logger.info(f"✓ {test_category} completed in {category_duration:.2f}s")
            
            # Generate comprehensive report
            total_duration = time.time() - start_time
            report = await self.generate_comprehensive_report(total_duration)
            
            self.logger.info(f"🎉 Test suite completed in {total_duration:.2f}s")
            return report
            
        except Exception as e:
            self.logger.error(f"Test suite execution failed: {e}")
            return {'status': 'failed', 'error': str(e)}
        
        finally:
            self.running = False
    
    async def run_unit_tests(self) -> Dict[str, Any]:
        """Run all unit tests"""
        try:
            self.logger.info("Running unit tests...")
            
            # Run pytest for unit tests
            cmd = [
                sys.executable, "-m", "pytest",
                "tests/unit/",
                "-v",
                "--tb=short",
                "--json-report",
                "--json-report-file=test_results_unit.json"
            ]
            
            if self.parallel_execution:
                cmd.extend(["-n", str(self.max_workers)])
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=self.timeout)
            
            # Parse results
            unit_results = {
                'exit_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'passed': result.returncode == 0
            }
            
            # Load detailed results if available
            try:
                with open('test_results_unit.json', 'r') as f:
                    detailed_results = json.load(f)
                    unit_results['detailed'] = detailed_results
            except FileNotFoundError:
                pass
            
            return unit_results
            
        except Exception as e:
            self.logger.error(f"Unit tests failed: {e}")
            return {'passed': False, 'error': str(e)}
    
    async def run_integration_tests(self) -> Dict[str, Any]:
        """Run all integration tests"""
        try:
            self.logger.info("Running integration tests...")
            
            cmd = [
                sys.executable, "-m", "pytest",
                "tests/integration/",
                "-v",
                "--tb=short",
                "--json-report",
                "--json-report-file=test_results_integration.json"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=self.timeout)
            
            integration_results = {
                'exit_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'passed': result.returncode == 0
            }
            
            # Load detailed results
            try:
                with open('test_results_integration.json', 'r') as f:
                    detailed_results = json.load(f)
                    integration_results['detailed'] = detailed_results
            except FileNotFoundError:
                pass
            
            return integration_results
            
        except Exception as e:
            self.logger.error(f"Integration tests failed: {e}")
            return {'passed': False, 'error': str(e)}
    
    async def run_performance_tests(self) -> Dict[str, Any]:
        """Run all performance tests"""
        try:
            self.logger.info("Running performance tests...")
            
            cmd = [
                sys.executable, "-m", "pytest",
                "tests/performance/",
                "-v",
                "--tb=short",
                "-m", "performance",
                "--json-report",
                "--json-report-file=test_results_performance.json"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=self.timeout * 2)
            
            performance_results = {
                'exit_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'passed': result.returncode == 0
            }
            
            # Load detailed results
            try:
                with open('test_results_performance.json', 'r') as f:
                    detailed_results = json.load(f)
                    performance_results['detailed'] = detailed_results
            except FileNotFoundError:
                pass
            
            return performance_results
            
        except Exception as e:
            self.logger.error(f"Performance tests failed: {e}")
            return {'passed': False, 'error': str(e)}
    
    async def run_security_tests(self) -> Dict[str, Any]:
        """Run security tests"""
        try:
            self.logger.info("Running security tests...")
            
            # Run security-specific tests
            cmd = [
                sys.executable, "-m", "pytest",
                "tests/",
                "-v",
                "--tb=short",
                "-m", "security",
                "--json-report",
                "--json-report-file=test_results_security.json"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=self.timeout)
            
            security_results = {
                'exit_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'passed': result.returncode == 0
            }
            
            return security_results
            
        except Exception as e:
            self.logger.error(f"Security tests failed: {e}")
            return {'passed': False, 'error': str(e)}
    
    async def run_end_to_end_tests(self) -> Dict[str, Any]:
        """Run end-to-end tests"""
        try:
            self.logger.info("Running end-to-end tests...")
            
            # Run E2E tests with longer timeout
            cmd = [
                sys.executable, "-m", "pytest",
                "tests/integration/test_end_to_end.py",
                "-v",
                "--tb=short",
                "--json-report",
                "--json-report-file=test_results_e2e.json"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=self.timeout * 3)
            
            e2e_results = {
                'exit_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'passed': result.returncode == 0
            }
            
            return e2e_results
            
        except Exception as e:
            self.logger.error(f"End-to-end tests failed: {e}")
            return {'passed': False, 'error': str(e)}
    
    async def run_validation_tests(self) -> Dict[str, Any]:
        """Run validation tests using test validator"""
        try:
            self.logger.info("Running validation tests...")
            
            # Create validation context
            validation_context = {
                'data': await self.mock_data_generator.generate_market_data('AAPL'),
                'performance_metrics': await self.mock_data_generator.generate_performance_metrics(),
                'configuration': self.config
            }
            
            # Run all validations
            validation_results = await self.test_validator.run_all_validations(validation_context)
            
            # Get validation summary
            summary = await self.test_validator.get_validation_summary()
            
            return {
                'passed': summary.get('validation_status') == 'passed',
                'results': validation_results,
                'summary': summary
            }
            
        except Exception as e:
            self.logger.error(f"Validation tests failed: {e}")
            return {'passed': False, 'error': str(e)}
    
    async def generate_comprehensive_report(self, total_duration: float) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        try:
            # Calculate overall statistics
            total_tests = 0
            passed_tests = 0
            failed_tests = 0
            
            for category, result in self.test_results.items():
                if 'detailed' in result['result']:
                    detailed = result['result']['detailed']
                    if 'summary' in detailed:
                        total_tests += detailed['summary'].get('total', 0)
                        passed_tests += detailed['summary'].get('passed', 0)
                        failed_tests += detailed['summary'].get('failed', 0)
            
            # Calculate success rate
            success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
            
            # Determine overall status
            overall_status = 'passed' if all(
                result['result'].get('passed', False) 
                for result in self.test_results.values()
            ) else 'failed'
            
            # Generate report
            report = {
                'timestamp': datetime.now().isoformat(),
                'overall_status': overall_status,
                'total_duration': total_duration,
                'statistics': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'failed_tests': failed_tests,
                    'success_rate': success_rate
                },
                'test_categories': {},
                'performance_metrics': await self.collect_performance_metrics(),
                'coverage_report': await self.generate_coverage_report(),
                'recommendations': await self.generate_recommendations()
            }
            
            # Add category details
            for category, result in self.test_results.items():
                report['test_categories'][category] = {
                    'status': 'passed' if result['result'].get('passed', False) else 'failed',
                    'duration': result['duration'],
                    'timestamp': result['timestamp']
                }
                
                if 'detailed' in result['result']:
                    report['test_categories'][category]['detailed'] = result['result']['detailed']
            
            # Save report
            await self.save_report(report)
            
            return report
            
        except Exception as e:
            self.logger.error(f"Failed to generate comprehensive report: {e}")
            return {'status': 'error', 'error': str(e)}
    
    async def collect_performance_metrics(self) -> Dict[str, Any]:
        """Collect performance metrics from test runs"""
        try:
            metrics = {
                'test_execution_time': {},
                'system_performance': {},
                'resource_usage': {}
            }
            
            # Collect execution times
            for category, result in self.test_results.items():
                metrics['test_execution_time'][category] = result['duration']
            
            # Collect system performance metrics
            if 'performance_tests' in self.test_results:
                perf_result = self.test_results['performance_tests']['result']
                if 'detailed' in perf_result:
                    # Extract performance metrics from detailed results
                    pass
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Failed to collect performance metrics: {e}")
            return {}
    
    async def generate_coverage_report(self) -> Dict[str, Any]:
        """Generate test coverage report"""
        try:
            # Run coverage analysis
            cmd = [
                sys.executable, "-m", "coverage", "report", "--format=json"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                coverage_data = json.loads(result.stdout)
                return {
                    'total_coverage': coverage_data.get('totals', {}).get('percent_covered', 0),
                    'files': coverage_data.get('files', {}),
                    'summary': coverage_data.get('totals', {})
                }
            else:
                return {'error': 'Coverage report generation failed'}
                
        except Exception as e:
            self.logger.error(f"Failed to generate coverage report: {e}")
            return {'error': str(e)}
    
    async def generate_recommendations(self) -> List[Dict[str, Any]]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        try:
            # Analyze test results and generate recommendations
            for category, result in self.test_results.items():
                if not result['result'].get('passed', False):
                    recommendations.append({
                        'category': category,
                        'type': 'failure',
                        'message': f"{category} failed - investigate and fix failing tests",
                        'priority': 'high'
                    })
                
                # Check performance
                if result['duration'] > 300:  # 5 minutes
                    recommendations.append({
                        'category': category,
                        'type': 'performance',
                        'message': f"{category} took {result['duration']:.2f}s - consider optimization",
                        'priority': 'medium'
                    })
            
            # Check overall success rate
            total_tests = sum(
                result['result'].get('detailed', {}).get('summary', {}).get('total', 0)
                for result in self.test_results.values()
            )
            passed_tests = sum(
                result['result'].get('detailed', {}).get('summary', {}).get('passed', 0)
                for result in self.test_results.values()
            )
            
            if total_tests > 0:
                success_rate = passed_tests / total_tests
                if success_rate < 0.95:  # Less than 95% success
                    recommendations.append({
                        'category': 'overall',
                        'type': 'quality',
                        'message': f"Test success rate is {success_rate:.1%} - aim for >95%",
                        'priority': 'high'
                    })
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"Failed to generate recommendations: {e}")
            return []
    
    async def save_report(self, report: Dict[str, Any]) -> bool:
        """Save test report to file"""
        try:
            # Create reports directory
            reports_dir = Path('test_reports')
            reports_dir.mkdir(exist_ok=True)
            
            # Generate filename with timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"test_report_{timestamp}.json"
            filepath = reports_dir / filename
            
            # Save report
            with open(filepath, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            # Also save as latest
            latest_filepath = reports_dir / "test_report_latest.json"
            with open(latest_filepath, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            self.logger.info(f"Test report saved to {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save test report: {e}")
            return False
    
    async def run_quick_tests(self) -> Dict[str, Any]:
        """Run a quick subset of tests for rapid feedback"""
        try:
            self.logger.info("Running quick test suite...")
            
            # Run smoke tests and critical unit tests
            cmd = [
                sys.executable, "-m", "pytest",
                "tests/unit/test_api.py",
                "tests/integration/test_system_integration.py",
                "-v",
                "--tb=short",
                "-m", "smoke or critical",
                "--json-report",
                "--json-report-file=test_results_quick.json"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            return {
                'exit_code': result.returncode,
                'passed': result.returncode == 0,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
        except Exception as e:
            self.logger.error(f"Quick tests failed: {e}")
            return {'passed': False, 'error': str(e)}
    
    async def run_regression_tests(self) -> Dict[str, Any]:
        """Run regression tests to ensure no functionality is broken"""
        try:
            self.logger.info("Running regression tests...")
            
            cmd = [
                sys.executable, "-m", "pytest",
                "tests/",
                "-v",
                "--tb=short",
                "-m", "regression",
                "--json-report",
                "--json-report-file=test_results_regression.json"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=self.timeout)
            
            return {
                'exit_code': result.returncode,
                'passed': result.returncode == 0,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
        except Exception as e:
            self.logger.error(f"Regression tests failed: {e}")
            return {'passed': False, 'error': str(e)}


# CLI interface for test suite
async def main():
    """Main entry point for test suite runner"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Advanced Ollama Trading Agents Test Suite")
    parser.add_argument("--config", default="config/config.yaml", help="Configuration file path")
    parser.add_argument("--quick", action="store_true", help="Run quick test suite")
    parser.add_argument("--regression", action="store_true", help="Run regression tests")
    parser.add_argument("--parallel", action="store_true", help="Enable parallel execution")
    parser.add_argument("--workers", type=int, default=4, help="Number of parallel workers")
    parser.add_argument("--timeout", type=int, default=300, help="Test timeout in seconds")
    
    args = parser.parse_args()
    
    # Load configuration
    config = {
        'testing': {
            'parallel': args.parallel,
            'max_workers': args.workers,
            'timeout': args.timeout
        }
    }
    
    # Create and run test suite
    test_suite = TestSuiteRunner(config)
    await test_suite.initialize()
    
    if args.quick:
        result = await test_suite.run_quick_tests()
    elif args.regression:
        result = await test_suite.run_regression_tests()
    else:
        result = await test_suite.run_all_tests()
    
    # Print summary
    if result.get('passed', False):
        print("✅ All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    import asyncio
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

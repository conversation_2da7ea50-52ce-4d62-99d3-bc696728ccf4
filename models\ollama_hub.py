"""
Ollama Model Hub - Central management for all Ollama models
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
import aiohttp
import yaml
import sys
import os
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.logging_utils import safe_success, get_status_symbol

from .model_registry import OllamaModelRegistry
from .model_config import ModelConfigStore
from .model_deployment import ModelDeploymentManager
from .model_performance import ModelPerformanceTracker

logger = logging.getLogger(__name__)


class OllamaModelHub:
    """
    Central hub for managing all Ollama models in the trading system.
    Coordinates model registry, configuration, deployment, and performance tracking.
    """
    
    def __init__(self, base_url: str = "http://localhost:11434", config: Dict[str, Any] = None):
        self.base_url = base_url
        self.config = config or {}
        self.ollama_config = self.config.get('ollama', {})
        
        # Core components
        self.registry: Optional[OllamaModelRegistry] = None
        self.config_store: Optional[ModelConfigStore] = None
        self.deployment_manager: Optional[ModelDeploymentManager] = None
        self.performance_tracker: Optional[ModelPerformanceTracker] = None
        
        # State
        self.initialized = False
        self.running = False
        
        # Session for HTTP requests
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def initialize(self):
        """Initialize the model hub and all components"""
        if self.initialized:
            return
            
        logger.info("Initializing Ollama Model Hub...")
        
        try:
            # Create HTTP session
            timeout = aiohttp.ClientTimeout(total=self.ollama_config.get('timeout', 300))
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            # Test Ollama connection
            await self._test_ollama_connection()
            
            # Initialize components
            self.registry = OllamaModelRegistry(self.base_url, self.session)
            await self.registry.initialize()
            
            self.config_store = ModelConfigStore(self.config)
            await self.config_store.initialize()
            
            self.deployment_manager = ModelDeploymentManager(
                registry=self.registry,
                config_store=self.config_store,
                session=self.session,
                base_url=self.base_url
            )
            await self.deployment_manager.initialize()
            
            self.performance_tracker = ModelPerformanceTracker(self.config)
            await self.performance_tracker.initialize()
            
            self.initialized = True
            safe_success(logger, f"{get_status_symbol(True)} Ollama Model Hub initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Ollama Model Hub: {e}")
            await self.cleanup()
            raise
            
    async def _test_ollama_connection(self):
        """Test connection to Ollama server"""
        try:
            async with self.session.get(f"{self.base_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    models = data.get('models', [])
                    safe_success(logger, f"{get_status_symbol(True)} Connected to Ollama server - {len(models)} models available")
                else:
                    raise Exception(f"Ollama server returned status {response.status}")
        except Exception as e:
            logger.error(f"Failed to connect to Ollama server at {self.base_url}: {e}")
            raise
            
    async def start(self):
        """Start the model hub"""
        if not self.initialized:
            await self.initialize()
            
        if self.running:
            return
            
        logger.info("Starting Ollama Model Hub...")
        
        try:
            # Start all components
            await asyncio.gather(
                self.registry.start(),
                self.deployment_manager.start(),
                self.performance_tracker.start()
            )
            
            self.running = True
            safe_success(logger, f"{get_status_symbol(True)} Ollama Model Hub started")
            
        except Exception as e:
            logger.error(f"Failed to start Ollama Model Hub: {e}")
            raise
            
    async def stop(self):
        """Stop the model hub"""
        if not self.running:
            return
            
        logger.info("Stopping Ollama Model Hub...")
        self.running = False
        
        try:
            # Stop all components
            stop_tasks = []
            if self.performance_tracker:
                stop_tasks.append(self.performance_tracker.stop())
            if self.deployment_manager:
                stop_tasks.append(self.deployment_manager.stop())
            if self.registry:
                stop_tasks.append(self.registry.stop())
                
            await asyncio.gather(*stop_tasks, return_exceptions=True)
            
            await self.cleanup()
            safe_success(logger, f"{get_status_symbol(True)} Ollama Model Hub stopped")
            
        except Exception as e:
            logger.error(f"Error stopping Ollama Model Hub: {e}")
            
    async def cleanup(self):
        """Cleanup resources"""
        if self.session and not self.session.closed:
            await self.session.close()
            
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of available models"""
        if not self.registry:
            return []
        return await self.registry.get_available_models()
        
    async def deploy_model_for_agent(self, agent_name: str, role: str, model_name: str = None):
        """Deploy a model instance for a specific agent"""
        if not self.deployment_manager:
            raise RuntimeError("Deployment manager not initialized")
            
        return await self.deployment_manager.deploy_model_for_agent(
            agent_name=agent_name,
            role=role,
            model_name=model_name
        )
        
    async def get_model_performance(self, model_name: str) -> Dict[str, Any]:
        """Get performance metrics for a model"""
        if not self.performance_tracker:
            return {}
        return await self.performance_tracker.get_model_performance(model_name)
        
    async def update_model_config(self, model_name: str, role: str, config: Dict[str, Any]):
        """Update configuration for a model"""
        if not self.config_store:
            raise RuntimeError("Config store not initialized")
            
        await self.config_store.update_config(model_name, role, config)
        
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the model hub"""
        health_status = {
            'status': 'healthy',
            'components': {},
            'timestamp': asyncio.get_event_loop().time()
        }
        
        try:
            # Check Ollama connection
            await self._test_ollama_connection()
            health_status['components']['ollama'] = 'healthy'
        except Exception as e:
            health_status['components']['ollama'] = f'unhealthy: {e}'
            health_status['status'] = 'degraded'
            
        # Check components
        if self.registry:
            health_status['components']['registry'] = 'healthy' if self.registry.running else 'stopped'
        if self.deployment_manager:
            health_status['components']['deployment'] = 'healthy' if self.deployment_manager.running else 'stopped'
        if self.performance_tracker:
            health_status['components']['performance'] = 'healthy' if self.performance_tracker.running else 'stopped'
            
        return health_status
        
    async def get_system_stats(self) -> Dict[str, Any]:
        """Get comprehensive system statistics"""
        stats = {
            'models': {
                'total_available': 0,
                'deployed': 0,
                'active': 0
            },
            'performance': {},
            'resource_usage': {}
        }
        
        if self.registry:
            models = await self.registry.get_available_models()
            stats['models']['total_available'] = len(models)
            
        if self.deployment_manager:
            deployed = await self.deployment_manager.get_deployed_models()
            stats['models']['deployed'] = len(deployed)
            stats['models']['active'] = len([m for m in deployed.values() if m.get('active', False)])
            
        if self.performance_tracker:
            stats['performance'] = await self.performance_tracker.get_system_performance()
            
        return stats

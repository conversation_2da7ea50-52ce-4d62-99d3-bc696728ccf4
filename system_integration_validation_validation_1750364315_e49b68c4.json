{"validation_id": "validation_**********_e49b68c4", "validation_level": "comprehensive", "overall_status": "partial", "overall_score": 0.8093127314514263, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "completed", "integration_score": 0.8652913015148344, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "team_manager", "component_type": "core", "status": "completed", "integration_score": 0.8305505281538441, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "completed", "integration_score": 0.8438509736751566, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "analytics_engine", "component_type": "core", "status": "completed", "integration_score": 0.7636059225761314, "error_count": 0, "warnings": ["Integration issues in analytics_engine"], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "completed", "integration_score": 0.8345110136073723, "error_count": 0, "warnings": ["Integration issues in ollama_hub"], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "completed", "integration_score": 0.8106197407088562, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "completed", "integration_score": 0.8011078724464074, "error_count": 0, "warnings": ["Integration issues in portfolio_manager"], "dependencies_met": "True"}, {"component_name": "risk_manager", "component_type": "core", "status": "completed", "integration_score": 0.8244657114952942, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "completed", "integration_score": 0.8474097936766969, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8793108986326399, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8924391387640938, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8481179973350946, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "completed", "integration_score": 0.81319897332118, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "completed", "integration_score": 0.8758014971406666, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8027312061356553, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "completed", "integration_score": 0.8663834832587121, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "completed", "integration_score": 0.8649953158361654, "error_count": 0, "warnings": ["Integration issues in configuration_manager"], "dependencies_met": "False"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "completed", "integration_score": 0.9308801193527204, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "completed", "integration_score": 0.937197232642135, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "completed", "integration_score": 0.8304851722274583, "error_count": 0, "warnings": [], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.9635779509471818, "data_manager": 0.625540191057274, "analytics_engine": 0.6447303532569589, "ollama_hub": 0.6001662534524054, "execution_engine": 0.6300727211032097, "portfolio_manager": 0.8053752055665147, "risk_manager": 0.6624492677778583, "strategy_manager": 0.758970318889058, "competitive_framework": 0.674270622139637, "tournament_framework": 0.6361189431626594, "self_improvement_engine": 0.6841710846612122, "regime_adaptation_system": 0.7778989359043691, "performance_optimizer": 0.8406054183898571, "advanced_trading_engine": 0.8515936605189758, "ai_coordinator": 0.8004833220143653, "configuration_manager": 0.7513889174934802, "mock_data_providers": 0.8603815079640498, "paper_trading_engine": 0.8954832958033152, "logging_audit_system": 0.7034696930305393}, "team_manager": {"system_coordinator": 0.7090647439720011, "team_manager": 1.0, "data_manager": 0.892300760761708, "analytics_engine": 0.8929073999029288, "ollama_hub": 0.7188980428249293, "execution_engine": 0.8944726897833097, "portfolio_manager": 0.7551120888998029, "risk_manager": 0.7970569039050266, "strategy_manager": 0.6864398402405674, "competitive_framework": 0.6712153243983623, "tournament_framework": 0.8913382060156992, "self_improvement_engine": 0.6080518194602187, "regime_adaptation_system": 0.8474756385247926, "performance_optimizer": 0.813430914419901, "advanced_trading_engine": 0.8905799321463024, "ai_coordinator": 0.7590599412636903, "configuration_manager": 0.7205252652263139, "mock_data_providers": 0.6819017592592053, "paper_trading_engine": 0.792204960660871, "logging_audit_system": 0.7238397305268565}, "data_manager": {"system_coordinator": 0.6033055671725509, "team_manager": 0.8314601174782664, "data_manager": 1.0, "analytics_engine": 0.9132766172570919, "ollama_hub": 0.7715208868235981, "execution_engine": 0.8117660890070799, "portfolio_manager": 0.8097907753307637, "risk_manager": 0.8636086697098704, "strategy_manager": 0.6290075361504555, "competitive_framework": 0.7071151659475775, "tournament_framework": 0.8065955152976582, "self_improvement_engine": 0.8249606115030961, "regime_adaptation_system": 0.8242715937716478, "performance_optimizer": 0.6413920208840049, "advanced_trading_engine": 0.7764436244934124, "ai_coordinator": 0.7305652130042191, "configuration_manager": 0.7831543803832356, "mock_data_providers": 0.7753487549489199, "paper_trading_engine": 0.6265805792439321, "logging_audit_system": 0.7529665842238438}, "analytics_engine": {"system_coordinator": 0.6374164494144099, "team_manager": 0.7299200182165784, "data_manager": 0.6824999098721919, "analytics_engine": 1.0, "ollama_hub": 0.8023912968961403, "execution_engine": 0.7652734013754181, "portfolio_manager": 0.7744922303371123, "risk_manager": 0.6600688481098307, "strategy_manager": 0.9560531713511479, "competitive_framework": 0.7082326102883129, "tournament_framework": 0.8315371551541757, "self_improvement_engine": 0.6888978269718602, "regime_adaptation_system": 0.7972435767689119, "performance_optimizer": 0.7206765589519054, "advanced_trading_engine": 0.8730508341153911, "ai_coordinator": 0.7702962643831358, "configuration_manager": 0.6411196003196296, "mock_data_providers": 0.6989898162034234, "paper_trading_engine": 0.7710283701939509, "logging_audit_system": 0.6919951992167372}, "ollama_hub": {"system_coordinator": 0.8387023810086631, "team_manager": 0.6318464345434727, "data_manager": 0.6365818481270521, "analytics_engine": 0.7503945122332536, "ollama_hub": 1.0, "execution_engine": 0.6248551045320334, "portfolio_manager": 0.6593576964028802, "risk_manager": 0.7343053224312064, "strategy_manager": 0.8153888428816238, "competitive_framework": 0.6200302946332698, "tournament_framework": 0.6251578583704189, "self_improvement_engine": 0.8973648983300182, "regime_adaptation_system": 0.8826187836392816, "performance_optimizer": 0.8072221182610095, "advanced_trading_engine": 0.6717111505658283, "ai_coordinator": 0.7167408061626701, "configuration_manager": 0.8555408091545615, "mock_data_providers": 0.8332568110343339, "paper_trading_engine": 0.6500356738087187, "logging_audit_system": 0.7785460340748162}, "execution_engine": {"system_coordinator": 0.6445759987096934, "team_manager": 0.634955300032175, "data_manager": 0.8053097202586407, "analytics_engine": 0.8661141118500661, "ollama_hub": 0.8795973366976444, "execution_engine": 1.0, "portfolio_manager": 0.9019433265705088, "risk_manager": 0.6307524349356979, "strategy_manager": 0.8868600947479606, "competitive_framework": 0.8235370473221963, "tournament_framework": 0.6228605829388066, "self_improvement_engine": 0.654726315405717, "regime_adaptation_system": 0.7148778937268581, "performance_optimizer": 0.8686751930843699, "advanced_trading_engine": 0.706770347750948, "ai_coordinator": 0.8763633608502441, "configuration_manager": 0.8520588283939631, "mock_data_providers": 0.6491634606274561, "paper_trading_engine": 0.8527170845965102, "logging_audit_system": 0.7716001307735915}, "portfolio_manager": {"system_coordinator": 0.8008652947421827, "team_manager": 0.6406212324063502, "data_manager": 0.6826742193563314, "analytics_engine": 0.6111880980563883, "ollama_hub": 0.8087650934083208, "execution_engine": 0.6780088100146557, "portfolio_manager": 1.0, "risk_manager": 0.8437776031876917, "strategy_manager": 0.8548930331192007, "competitive_framework": 0.8584029414212039, "tournament_framework": 0.8318109612621796, "self_improvement_engine": 0.8522926085480456, "regime_adaptation_system": 0.6611413164963257, "performance_optimizer": 0.6879997204193038, "advanced_trading_engine": 0.6240909579913191, "ai_coordinator": 0.892699695687351, "configuration_manager": 0.8070857691351545, "mock_data_providers": 0.7273592966601371, "paper_trading_engine": 0.8640840297164677, "logging_audit_system": 0.7208753384346717}, "risk_manager": {"system_coordinator": 0.8847279639035246, "team_manager": 0.792711676805705, "data_manager": 0.6051003999357291, "analytics_engine": 0.7786995834728729, "ollama_hub": 0.6137226851084601, "execution_engine": 0.7913352865995561, "portfolio_manager": 0.6937485413960931, "risk_manager": 1.0, "strategy_manager": 0.7969041992789867, "competitive_framework": 0.855212465727432, "tournament_framework": 0.6723842511442294, "self_improvement_engine": 0.6350071329608522, "regime_adaptation_system": 0.8885113158346178, "performance_optimizer": 0.7933353532048041, "advanced_trading_engine": 0.8556092559576989, "ai_coordinator": 0.6470193968584141, "configuration_manager": 0.7010498208157583, "mock_data_providers": 0.6511016973605875, "paper_trading_engine": 0.7009750758389307, "logging_audit_system": 0.8354622511638228}, "strategy_manager": {"system_coordinator": 0.8898740708710944, "team_manager": 0.7482547163613202, "data_manager": 0.6959165144192755, "analytics_engine": 0.8677183316165812, "ollama_hub": 0.6456422642794962, "execution_engine": 0.6833594323388299, "portfolio_manager": 0.776301595311246, "risk_manager": 0.8683286580962968, "strategy_manager": 1.0, "competitive_framework": 0.6652326740340018, "tournament_framework": 0.7358385333815627, "self_improvement_engine": 0.6354836329484492, "regime_adaptation_system": 0.7825049527440612, "performance_optimizer": 0.7726249100728364, "advanced_trading_engine": 0.8517883174920458, "ai_coordinator": 0.6672539838622554, "configuration_manager": 0.7666931401716044, "mock_data_providers": 0.6241690863873292, "paper_trading_engine": 0.885641695085177, "logging_audit_system": 0.8225475116215109}, "competitive_framework": {"system_coordinator": 0.8918733029870904, "team_manager": 0.8044776345671096, "data_manager": 0.8174360785725086, "analytics_engine": 0.6584828243455105, "ollama_hub": 0.7351612941375194, "execution_engine": 0.6269948534816819, "portfolio_manager": 0.6708643975840449, "risk_manager": 0.8737930571101511, "strategy_manager": 0.7225877456982412, "competitive_framework": 1.0, "tournament_framework": 0.8936440085197324, "self_improvement_engine": 0.7681489000203247, "regime_adaptation_system": 0.6894620660997103, "performance_optimizer": 0.8179843741924926, "advanced_trading_engine": 0.7459161544974958, "ai_coordinator": 0.7978359697487906, "configuration_manager": 0.6185968771177236, "mock_data_providers": 0.7318199674577566, "paper_trading_engine": 0.7747040508312764, "logging_audit_system": 0.8695049375299359}, "tournament_framework": {"system_coordinator": 0.8123174126110929, "team_manager": 0.7778668921214196, "data_manager": 0.7705481636861319, "analytics_engine": 0.6129049762453421, "ollama_hub": 0.8038331714814831, "execution_engine": 0.7562570380843239, "portfolio_manager": 0.8844919991126214, "risk_manager": 0.7412111128273303, "strategy_manager": 0.8441067834127434, "competitive_framework": 0.7796107907483403, "tournament_framework": 1.0, "self_improvement_engine": 0.6436976334104354, "regime_adaptation_system": 0.7062588684133665, "performance_optimizer": 0.7706119847312388, "advanced_trading_engine": 0.731757706440153, "ai_coordinator": 0.8943803100764458, "configuration_manager": 0.6070063369689612, "mock_data_providers": 0.7500095667953697, "paper_trading_engine": 0.6244780250368583, "logging_audit_system": 0.614341031796712}, "self_improvement_engine": {"system_coordinator": 0.852945973178893, "team_manager": 0.6827162231741735, "data_manager": 0.7253356605815192, "analytics_engine": 0.7991356967532043, "ollama_hub": 0.8203286884234013, "execution_engine": 0.7395899104868722, "portfolio_manager": 0.7167952961175414, "risk_manager": 0.8131046483207739, "strategy_manager": 0.880945460618026, "competitive_framework": 0.7304780823675379, "tournament_framework": 0.6386454558818139, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.8135111071084491, "performance_optimizer": 0.829007837218581, "advanced_trading_engine": 0.7699400839137426, "ai_coordinator": 0.6883860036686126, "configuration_manager": 0.7381973755247505, "mock_data_providers": 0.7907166168638811, "paper_trading_engine": 0.824579310583928, "logging_audit_system": 0.6276841882602926}, "regime_adaptation_system": {"system_coordinator": 0.8144511500884345, "team_manager": 0.8052932170253709, "data_manager": 0.7702223874704456, "analytics_engine": 0.6744031829466597, "ollama_hub": 0.6111329013224627, "execution_engine": 0.6348053572370274, "portfolio_manager": 0.6722506787521362, "risk_manager": 0.7048057780153518, "strategy_manager": 0.8620004383080893, "competitive_framework": 0.7902820500823902, "tournament_framework": 0.670953747064487, "self_improvement_engine": 0.6758484658185847, "regime_adaptation_system": 1.0, "performance_optimizer": 0.8317876647503047, "advanced_trading_engine": 0.6659984659932019, "ai_coordinator": 0.6518092112123589, "configuration_manager": 0.6911726368480021, "mock_data_providers": 0.7635524470864016, "paper_trading_engine": 0.7842396178103535, "logging_audit_system": 0.7444990554527835}, "performance_optimizer": {"system_coordinator": 0.6941201807515259, "team_manager": 0.7030847705705033, "data_manager": 0.754406192586937, "analytics_engine": 0.7242035440126348, "ollama_hub": 0.8758970087959623, "execution_engine": 0.8348704717623965, "portfolio_manager": 0.7728374049275208, "risk_manager": 0.6505093607613353, "strategy_manager": 0.8613639920236791, "competitive_framework": 0.7184575690674498, "tournament_framework": 0.7858388874525788, "self_improvement_engine": 0.7641327713907445, "regime_adaptation_system": 0.6371150686012169, "performance_optimizer": 1.0, "advanced_trading_engine": 0.6632668761844823, "ai_coordinator": 0.8841333270362266, "configuration_manager": 0.8593438566051605, "mock_data_providers": 0.8963378593075256, "paper_trading_engine": 0.7479594442681212, "logging_audit_system": 0.7235281460992367}, "advanced_trading_engine": {"system_coordinator": 0.8886831623873119, "team_manager": 0.7203532877897749, "data_manager": 0.749409614049436, "analytics_engine": 0.727341120692944, "ollama_hub": 0.7365171440025051, "execution_engine": 0.6467676669243905, "portfolio_manager": 0.6941908857430991, "risk_manager": 0.6854010977806506, "strategy_manager": 0.6128921423533212, "competitive_framework": 0.8962918647742674, "tournament_framework": 0.7685972873077291, "self_improvement_engine": 0.7288586742744887, "regime_adaptation_system": 0.8615802878406587, "performance_optimizer": 0.8591693670773286, "advanced_trading_engine": 1.0, "ai_coordinator": 0.6178327234193345, "configuration_manager": 0.7539804047497787, "mock_data_providers": 0.7100937753338202, "paper_trading_engine": 0.8636847822494671, "logging_audit_system": 0.8813810908628052}, "ai_coordinator": {"system_coordinator": 0.8044532404443914, "team_manager": 0.8466109336515648, "data_manager": 0.8672663994197389, "analytics_engine": 0.8667785573783322, "ollama_hub": 0.8302549595032224, "execution_engine": 0.7556262096375617, "portfolio_manager": 0.7540056977132639, "risk_manager": 0.6105765295590075, "strategy_manager": 0.7728777756226256, "competitive_framework": 0.8667210533635097, "tournament_framework": 0.8750356737092688, "self_improvement_engine": 0.7802359766403163, "regime_adaptation_system": 0.8310619784896861, "performance_optimizer": 0.8811954106400419, "advanced_trading_engine": 0.8972881306499618, "ai_coordinator": 1.0, "configuration_manager": 0.6642065481338182, "mock_data_providers": 0.6398503248522031, "paper_trading_engine": 0.881543174709833, "logging_audit_system": 0.7844341922630989}, "configuration_manager": {"system_coordinator": 0.8230403258614558, "team_manager": 0.7153914135539726, "data_manager": 0.8095599113935594, "analytics_engine": 0.8475289458562083, "ollama_hub": 0.6282693557023376, "execution_engine": 0.7201939365741834, "portfolio_manager": 0.752373239369428, "risk_manager": 0.6770460829745534, "strategy_manager": 0.6855549917327562, "competitive_framework": 0.8856382661750938, "tournament_framework": 0.6908295236616623, "self_improvement_engine": 0.848130498220188, "regime_adaptation_system": 0.8608218130588515, "performance_optimizer": 0.646815679505685, "advanced_trading_engine": 0.8614544126891064, "ai_coordinator": 0.7423136069906202, "configuration_manager": 1.0, "mock_data_providers": 0.7364672957838121, "paper_trading_engine": 0.8491758050844096, "logging_audit_system": 0.6136058221827273}, "mock_data_providers": {"system_coordinator": 0.7231032083324322, "team_manager": 0.6468179640454883, "data_manager": 0.8179335032429947, "analytics_engine": 0.812816327944501, "ollama_hub": 0.709637153138953, "execution_engine": 0.794586883860826, "portfolio_manager": 0.6058374704179713, "risk_manager": 0.7737981068313246, "strategy_manager": 0.7358645511236624, "competitive_framework": 0.8718398880133154, "tournament_framework": 0.7960013044711781, "self_improvement_engine": 0.7188271689253786, "regime_adaptation_system": 0.7691793583114955, "performance_optimizer": 0.6111957622123441, "advanced_trading_engine": 0.820505555777397, "ai_coordinator": 0.6459654703217036, "configuration_manager": 0.6316893826692985, "mock_data_providers": 1.0, "paper_trading_engine": 0.8800663554019033, "logging_audit_system": 0.8233632008133582}, "paper_trading_engine": {"system_coordinator": 0.6568434068343951, "team_manager": 0.777349439710324, "data_manager": 0.7898078205505572, "analytics_engine": 0.6912151817607985, "ollama_hub": 0.8538459766347857, "execution_engine": 0.7373918135803978, "portfolio_manager": 0.681101312228568, "risk_manager": 0.6924938588000997, "strategy_manager": 0.6828622026567236, "competitive_framework": 0.7793058176932394, "tournament_framework": 0.6062186109684493, "self_improvement_engine": 0.7579800206004312, "regime_adaptation_system": 0.6232197303133338, "performance_optimizer": 0.6151056292860997, "advanced_trading_engine": 0.8222257658186314, "ai_coordinator": 0.7331460498454211, "configuration_manager": 0.7869745162844863, "mock_data_providers": 0.6738493820958302, "paper_trading_engine": 1.0, "logging_audit_system": 0.756120293367659}, "logging_audit_system": {"system_coordinator": 0.8985058977134297, "team_manager": 0.702722798144985, "data_manager": 0.6042276859168404, "analytics_engine": 0.8524923228089967, "ollama_hub": 0.7292798173963737, "execution_engine": 0.7964723509375371, "portfolio_manager": 0.7617818699058565, "risk_manager": 0.7934219157396636, "strategy_manager": 0.8426058609079858, "competitive_framework": 0.73490239901228, "tournament_framework": 0.822214779110322, "self_improvement_engine": 0.7889682078523526, "regime_adaptation_system": 0.8764096281188039, "performance_optimizer": 0.6210788436597429, "advanced_trading_engine": 0.7471279528068318, "ai_coordinator": 0.7379032216362946, "configuration_manager": 0.7203965284480968, "mock_data_providers": 0.7531630847114424, "paper_trading_engine": 0.6105480603196378, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.7211793226148813, "response_time": 0.8298493400735301, "throughput": 0.6927736702782853, "memory_usage": 0.8983015053235882, "cpu_usage": 0.885127133384997, "concurrent_operations": 0.6911806608504241}, "critical_issues": ["Components with dependency issues: system_coordinator, advanced_trading_engine, configuration_manager"], "recommendations": ["Enhance component integration and communication"], "production_ready": "True", "timestamp": **********.7313814}
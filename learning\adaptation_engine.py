"""
Adaptation Engine - Implements adaptive changes based on learning insights
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import numpy as np

logger = logging.getLogger(__name__)


@dataclass
class AdaptationAction:
    """Adaptation action data structure"""
    action_type: str
    component: str
    parameters: Dict[str, Any]
    expected_impact: float
    confidence: float
    rollback_info: Dict[str, Any]


class AdaptationEngine:
    """
    Implements adaptive changes based on learning insights.
    
    Features:
    - Automated parameter adjustment
    - Strategy modification and optimization
    - Risk parameter adaptation
    - Performance-based configuration changes
    - Safe adaptation with rollback capability
    - A/B testing for adaptations
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.adaptation_config = config.get('adaptation_engine', {})
        
        # Adaptation parameters
        self.adaptation_threshold = self.adaptation_config.get('adaptation_threshold', 0.05)
        self.max_adaptations_per_hour = self.adaptation_config.get('max_adaptations_per_hour', 5)
        self.rollback_threshold = self.adaptation_config.get('rollback_threshold', -0.02)
        self.adaptation_confidence_threshold = self.adaptation_config.get('confidence_threshold', 0.7)
        
        # Adaptation history
        self.adaptation_history: List[Dict[str, Any]] = []
        self.active_adaptations: Dict[str, AdaptationAction] = {}
        self.rollback_queue: List[str] = []
        
        # Adaptation strategies
        self.adaptation_strategies = {
            'parameter_adjustment': self._adapt_parameters,
            'strategy_modification': self._adapt_strategy,
            'risk_adjustment': self._adapt_risk_parameters,
            'execution_optimization': self._adapt_execution_parameters,
            'portfolio_rebalancing': self._adapt_portfolio_parameters
        }
        
        # Integration points
        self.strategy_manager = None
        self.risk_manager = None
        self.portfolio_manager = None
        self.execution_engine = None
        
        # State
        self.initialized = False
        self.running = False
        
        # Rate limiting
        self.recent_adaptations: List[float] = []
    
    async def initialize(self) -> bool:
        """Initialize adaptation engine"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Adaptation Engine...")
            
            self.initialized = True
            logger.info("✓ Adaptation Engine initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Adaptation Engine: {e}")
            return False
    
    async def start(self) -> bool:
        """Start adaptation engine"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        self.running = True
        logger.info("✓ Adaptation Engine started")
        return True
    
    async def stop(self) -> bool:
        """Stop adaptation engine"""
        self.running = False
        logger.info("✓ Adaptation Engine stopped")
        return True
    
    async def set_integration_points(self, strategy_manager=None, risk_manager=None,
                                   portfolio_manager=None, execution_engine=None):
        """Set integration points with other systems"""
        self.strategy_manager = strategy_manager
        self.risk_manager = risk_manager
        self.portfolio_manager = portfolio_manager
        self.execution_engine = execution_engine
    
    async def adapt_component(self, component: str, reason: str, 
                            context: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt a component based on learning insights"""
        try:
            # Check rate limiting
            if not await self._check_rate_limit():
                return {
                    'success': False,
                    'reason': 'Rate limit exceeded',
                    'type': 'rate_limited'
                }
            
            # Determine adaptation strategy
            adaptation_type = await self._determine_adaptation_type(component, reason, context)
            
            if adaptation_type not in self.adaptation_strategies:
                return {
                    'success': False,
                    'reason': f'Unknown adaptation type: {adaptation_type}',
                    'type': 'unknown_type'
                }
            
            # Check confidence threshold
            confidence = context.get('confidence', 0.5)
            if confidence < self.adaptation_confidence_threshold:
                return {
                    'success': False,
                    'reason': f'Confidence {confidence} below threshold {self.adaptation_confidence_threshold}',
                    'type': 'low_confidence'
                }
            
            # Execute adaptation
            adaptation_strategy = self.adaptation_strategies[adaptation_type]
            result = await adaptation_strategy(component, context)
            
            if result.get('success', False):
                # Record adaptation
                adaptation_record = {
                    'timestamp': time.time(),
                    'component': component,
                    'adaptation_type': adaptation_type,
                    'reason': reason,
                    'context': context,
                    'result': result,
                    'confidence': confidence
                }
                
                self.adaptation_history.append(adaptation_record)
                self.recent_adaptations.append(time.time())
                
                # Schedule monitoring for rollback if needed
                if result.get('monitor_for_rollback', False):
                    await self._schedule_rollback_monitoring(component, result)
                
                logger.info(f"✓ Adapted {component} using {adaptation_type}: {result.get('changes', [])}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error adapting component {component}: {e}")
            return {
                'success': False,
                'reason': str(e),
                'type': 'error'
            }
    
    async def rollback_adaptation(self, adaptation_id: str) -> bool:
        """Rollback a specific adaptation"""
        try:
            if adaptation_id not in self.active_adaptations:
                logger.warning(f"Adaptation {adaptation_id} not found for rollback")
                return False
            
            adaptation = self.active_adaptations[adaptation_id]
            
            # Execute rollback
            success = await self._execute_rollback(adaptation)
            
            if success:
                del self.active_adaptations[adaptation_id]
                logger.info(f"✓ Rolled back adaptation {adaptation_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error rolling back adaptation {adaptation_id}: {e}")
            return False
    
    async def get_adaptation_status(self) -> Dict[str, Any]:
        """Get current adaptation status"""
        try:
            return {
                'total_adaptations': len(self.adaptation_history),
                'active_adaptations': len(self.active_adaptations),
                'recent_adaptations_count': len([
                    t for t in self.recent_adaptations 
                    if time.time() - t < 3600  # Last hour
                ]),
                'rollback_queue_size': len(self.rollback_queue),
                'last_adaptation': self.adaptation_history[-1]['timestamp'] if self.adaptation_history else None,
                'adaptation_rate_limit': self.max_adaptations_per_hour,
                'current_rate': len([
                    t for t in self.recent_adaptations 
                    if time.time() - t < 3600
                ])
            }
            
        except Exception as e:
            logger.error(f"Error getting adaptation status: {e}")
            return {}
    
    # Private methods
    
    async def _check_rate_limit(self) -> bool:
        """Check if adaptation is within rate limits"""
        try:
            current_time = time.time()
            
            # Remove old timestamps
            self.recent_adaptations = [
                t for t in self.recent_adaptations 
                if current_time - t < 3600  # Last hour
            ]
            
            return len(self.recent_adaptations) < self.max_adaptations_per_hour
            
        except Exception as e:
            logger.error(f"Error checking rate limit: {e}")
            return False
    
    async def _determine_adaptation_type(self, component: str, reason: str, 
                                       context: Dict[str, Any]) -> str:
        """Determine the type of adaptation needed"""
        try:
            # Simple logic to determine adaptation type
            if 'parameter' in reason.lower() or 'optimization' in reason.lower():
                return 'parameter_adjustment'
            elif 'strategy' in component.lower():
                return 'strategy_modification'
            elif 'risk' in component.lower():
                return 'risk_adjustment'
            elif 'execution' in component.lower():
                return 'execution_optimization'
            elif 'portfolio' in component.lower():
                return 'portfolio_rebalancing'
            else:
                return 'parameter_adjustment'  # Default
                
        except Exception as e:
            logger.error(f"Error determining adaptation type: {e}")
            return 'parameter_adjustment'
    
    async def _adapt_parameters(self, component: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt component parameters"""
        try:
            # Get current parameters
            current_params = context.get('current_parameters', {})
            suggested_changes = context.get('suggested_changes', {})
            
            if not suggested_changes:
                return {'success': False, 'reason': 'No suggested changes provided'}
            
            # Apply conservative changes
            new_params = current_params.copy()
            changes_made = []
            
            for param, new_value in suggested_changes.items():
                if param in current_params:
                    old_value = current_params[param]
                    
                    # Apply conservative change (50% of suggested change)
                    if isinstance(old_value, (int, float)) and isinstance(new_value, (int, float)):
                        conservative_change = (new_value - old_value) * 0.5
                        new_params[param] = old_value + conservative_change
                        changes_made.append(f"{param}: {old_value} -> {new_params[param]}")
                    else:
                        new_params[param] = new_value
                        changes_made.append(f"{param}: {old_value} -> {new_value}")
            
            # Apply changes to component (simplified)
            if hasattr(self, component) and getattr(self, component):
                component_obj = getattr(self, component)
                if hasattr(component_obj, 'update_parameters'):
                    await component_obj.update_parameters(new_params)
            
            return {
                'success': True,
                'type': 'parameter_adjustment',
                'changes': changes_made,
                'new_parameters': new_params,
                'monitor_for_rollback': True,
                'rollback_info': {
                    'component': component,
                    'original_parameters': current_params
                }
            }
            
        except Exception as e:
            logger.error(f"Error adapting parameters for {component}: {e}")
            return {'success': False, 'reason': str(e)}
    
    async def _adapt_strategy(self, component: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt strategy configuration"""
        try:
            if not self.strategy_manager:
                return {'success': False, 'reason': 'Strategy manager not available'}
            
            strategy_id = context.get('strategy_id')
            if not strategy_id:
                return {'success': False, 'reason': 'Strategy ID not provided'}
            
            # Get suggested modifications
            modifications = context.get('modifications', {})
            
            if not modifications:
                return {'success': False, 'reason': 'No modifications provided'}
            
            # Apply modifications through strategy manager
            success = await self.strategy_manager.update_strategy_parameters(strategy_id, modifications)
            
            if success:
                return {
                    'success': True,
                    'type': 'strategy_modification',
                    'changes': [f"Modified strategy {strategy_id}"],
                    'strategy_id': strategy_id,
                    'modifications': modifications,
                    'monitor_for_rollback': True
                }
            else:
                return {'success': False, 'reason': 'Failed to update strategy'}
                
        except Exception as e:
            logger.error(f"Error adapting strategy for {component}: {e}")
            return {'success': False, 'reason': str(e)}
    
    async def _adapt_risk_parameters(self, component: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt risk management parameters"""
        try:
            if not self.risk_manager:
                return {'success': False, 'reason': 'Risk manager not available'}
            
            # Get risk adjustments
            risk_adjustments = context.get('risk_adjustments', {})
            
            if not risk_adjustments:
                return {'success': False, 'reason': 'No risk adjustments provided'}
            
            changes_made = []
            
            # Apply conservative risk adjustments
            for risk_param, adjustment in risk_adjustments.items():
                if risk_param == 'max_position_size':
                    # Reduce position size if risk is high
                    new_value = adjustment * 0.8  # Conservative adjustment
                    success = await self.risk_manager.update_risk_limit(risk_param, new_value)
                    if success:
                        changes_made.append(f"Updated {risk_param} to {new_value}")
                
                elif risk_param == 'stop_loss_threshold':
                    # Tighten stop loss if needed
                    new_value = adjustment * 0.9  # Conservative adjustment
                    success = await self.risk_manager.update_risk_limit(risk_param, new_value)
                    if success:
                        changes_made.append(f"Updated {risk_param} to {new_value}")
            
            return {
                'success': True,
                'type': 'risk_adjustment',
                'changes': changes_made,
                'risk_adjustments': risk_adjustments,
                'monitor_for_rollback': True
            }
            
        except Exception as e:
            logger.error(f"Error adapting risk parameters for {component}: {e}")
            return {'success': False, 'reason': str(e)}
    
    async def _adapt_execution_parameters(self, component: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt execution parameters"""
        try:
            if not self.execution_engine:
                return {'success': False, 'reason': 'Execution engine not available'}
            
            # Get execution optimizations
            optimizations = context.get('optimizations', {})
            
            if not optimizations:
                return {'success': False, 'reason': 'No optimizations provided'}
            
            changes_made = []
            
            # Apply execution optimizations
            for param, value in optimizations.items():
                if param == 'order_timeout':
                    # Adjust order timeout
                    if hasattr(self.execution_engine, 'update_order_timeout'):
                        await self.execution_engine.update_order_timeout(value)
                        changes_made.append(f"Updated order timeout to {value}")
                
                elif param == 'slippage_tolerance':
                    # Adjust slippage tolerance
                    if hasattr(self.execution_engine, 'update_slippage_tolerance'):
                        await self.execution_engine.update_slippage_tolerance(value)
                        changes_made.append(f"Updated slippage tolerance to {value}")
            
            return {
                'success': True,
                'type': 'execution_optimization',
                'changes': changes_made,
                'optimizations': optimizations,
                'monitor_for_rollback': True
            }
            
        except Exception as e:
            logger.error(f"Error adapting execution parameters for {component}: {e}")
            return {'success': False, 'reason': str(e)}
    
    async def _adapt_portfolio_parameters(self, component: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt portfolio management parameters"""
        try:
            if not self.portfolio_manager:
                return {'success': False, 'reason': 'Portfolio manager not available'}
            
            # Get portfolio adjustments
            adjustments = context.get('adjustments', {})
            
            if not adjustments:
                return {'success': False, 'reason': 'No adjustments provided'}
            
            changes_made = []
            
            # Apply portfolio adjustments
            for param, value in adjustments.items():
                if param == 'rebalance_threshold':
                    # Adjust rebalancing threshold
                    if hasattr(self.portfolio_manager, 'update_rebalance_threshold'):
                        await self.portfolio_manager.update_rebalance_threshold(value)
                        changes_made.append(f"Updated rebalance threshold to {value}")
                
                elif param == 'target_allocation':
                    # Update target allocation
                    if hasattr(self.portfolio_manager, 'update_target_allocation'):
                        await self.portfolio_manager.update_target_allocation(value)
                        changes_made.append(f"Updated target allocation")
            
            return {
                'success': True,
                'type': 'portfolio_rebalancing',
                'changes': changes_made,
                'adjustments': adjustments,
                'monitor_for_rollback': True
            }
            
        except Exception as e:
            logger.error(f"Error adapting portfolio parameters for {component}: {e}")
            return {'success': False, 'reason': str(e)}
    
    async def _schedule_rollback_monitoring(self, component: str, result: Dict[str, Any]):
        """Schedule monitoring for potential rollback"""
        try:
            # Create adaptation action for monitoring
            adaptation_id = f"{component}_{int(time.time())}"
            
            adaptation_action = AdaptationAction(
                action_type=result.get('type', 'unknown'),
                component=component,
                parameters=result.get('new_parameters', {}),
                expected_impact=result.get('expected_impact', 0.0),
                confidence=result.get('confidence', 0.5),
                rollback_info=result.get('rollback_info', {})
            )
            
            self.active_adaptations[adaptation_id] = adaptation_action
            
            # Schedule rollback check (simplified - would use proper scheduling)
            # For now, just add to rollback queue for periodic checking
            self.rollback_queue.append(adaptation_id)
            
        except Exception as e:
            logger.error(f"Error scheduling rollback monitoring: {e}")
    
    async def _execute_rollback(self, adaptation: AdaptationAction) -> bool:
        """Execute rollback of an adaptation"""
        try:
            rollback_info = adaptation.rollback_info
            component = adaptation.component
            
            if 'original_parameters' in rollback_info:
                # Restore original parameters
                original_params = rollback_info['original_parameters']
                
                if hasattr(self, component) and getattr(self, component):
                    component_obj = getattr(self, component)
                    if hasattr(component_obj, 'update_parameters'):
                        await component_obj.update_parameters(original_params)
                        logger.info(f"✓ Rolled back parameters for {component}")
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error executing rollback: {e}")
            return False

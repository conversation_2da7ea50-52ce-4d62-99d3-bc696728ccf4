"""
Strategy Repository - Manages strategy storage, versioning, and knowledge base
"""

import asyncio
import logging
import time
import json
import os
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
import hashlib
import pickle
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class StrategyMetadata:
    """Strategy metadata structure"""
    strategy_id: str
    name: str
    version: str
    created_at: float
    updated_at: float
    author: str
    description: str
    strategy_type: str
    parameters: Dict[str, Any]
    performance_metrics: Dict[str, float]
    tags: List[str]
    status: str  # 'active', 'inactive', 'testing', 'deprecated'


@dataclass
class StrategyVersion:
    """Strategy version information"""
    version: str
    timestamp: float
    changes: List[str]
    performance_delta: Optional[float]
    parameters: Dict[str, Any]
    checksum: str


class StrategyRepository:
    """
    Comprehensive strategy repository for managing trading strategies.
    
    Features:
    - Strategy storage and versioning
    - Performance tracking and comparison
    - Strategy search and discovery
    - Knowledge base management
    - Strategy templates and patterns
    - Automated strategy optimization history
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.repo_config = config.get('strategy_repository', {})
        
        # Storage configuration
        self.storage_path = Path(self.repo_config.get('storage_path', 'data/strategies'))
        self.backup_path = Path(self.repo_config.get('backup_path', 'data/strategy_backups'))
        self.max_versions = self.repo_config.get('max_versions_per_strategy', 10)
        
        # Strategy storage
        self.strategies: Dict[str, StrategyMetadata] = {}
        self.strategy_versions: Dict[str, List[StrategyVersion]] = {}
        self.strategy_code: Dict[str, str] = {}  # Strategy implementation code
        self.strategy_performance: Dict[str, List[Dict[str, Any]]] = {}
        
        # Knowledge base
        self.strategy_patterns: Dict[str, Any] = {}
        self.best_practices: List[Dict[str, Any]] = []
        self.optimization_history: Dict[str, List[Dict[str, Any]]] = {}
        
        # Search and indexing
        self.strategy_index: Dict[str, List[str]] = {}  # Tag-based index
        self.performance_rankings: List[Tuple[str, float]] = []
        
        # Metrics
        self.repository_metrics: Dict[str, Any] = {}
        
        # State
        self.initialized = False
        self.running = False
        
        # Ensure directories exist
        self.storage_path.mkdir(parents=True, exist_ok=True)
        self.backup_path.mkdir(parents=True, exist_ok=True)
    
    async def initialize(self) -> bool:
        """Initialize strategy repository"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Strategy Repository...")
            
            # Load existing strategies
            await self._load_strategies()
            
            # Load knowledge base
            await self._load_knowledge_base()
            
            # Build search index
            await self._build_search_index()
            
            # Update metrics
            await self._update_metrics()
            
            self.initialized = True
            logger.info(f"✓ Strategy Repository initialized with {len(self.strategies)} strategies")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Strategy Repository: {e}")
            return False
    
    async def start(self) -> bool:
        """Start strategy repository"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        self.running = True
        logger.info("✓ Strategy Repository started")
        return True
    
    async def stop(self) -> bool:
        """Stop strategy repository"""
        if self.running:
            # Save all data
            await self._save_strategies()
            await self._save_knowledge_base()
        
        self.running = False
        logger.info("✓ Strategy Repository stopped")
        return True
    
    async def add_strategy(self, strategy_id: str, name: str, strategy_type: str,
                          code: str, parameters: Dict[str, Any],
                          description: str = "", author: str = "system",
                          tags: List[str] = None) -> bool:
        """Add a new strategy to the repository"""
        try:
            if strategy_id in self.strategies:
                logger.warning(f"Strategy {strategy_id} already exists")
                return False
            
            # Create strategy metadata
            metadata = StrategyMetadata(
                strategy_id=strategy_id,
                name=name,
                version="1.0.0",
                created_at=time.time(),
                updated_at=time.time(),
                author=author,
                description=description,
                strategy_type=strategy_type,
                parameters=parameters.copy(),
                performance_metrics={},
                tags=tags or [],
                status="testing"
            )
            
            # Store strategy
            self.strategies[strategy_id] = metadata
            self.strategy_code[strategy_id] = code
            self.strategy_versions[strategy_id] = []
            self.strategy_performance[strategy_id] = []
            
            # Create initial version
            await self._create_version(strategy_id, "Initial version", parameters)
            
            # Update search index
            await self._update_search_index(strategy_id)
            
            # Save to disk
            await self._save_strategy(strategy_id)
            
            logger.info(f"✓ Strategy {strategy_id} added to repository")
            return True
            
        except Exception as e:
            logger.error(f"Error adding strategy {strategy_id}: {e}")
            return False
    
    async def update_strategy(self, strategy_id: str, updates: Dict[str, Any],
                            changes: List[str] = None) -> bool:
        """Update an existing strategy"""
        try:
            if strategy_id not in self.strategies:
                logger.error(f"Strategy {strategy_id} not found")
                return False
            
            metadata = self.strategies[strategy_id]
            old_parameters = metadata.parameters.copy()
            
            # Update metadata
            if 'name' in updates:
                metadata.name = updates['name']
            if 'description' in updates:
                metadata.description = updates['description']
            if 'parameters' in updates:
                metadata.parameters.update(updates['parameters'])
            if 'tags' in updates:
                metadata.tags = updates['tags']
            if 'status' in updates:
                metadata.status = updates['status']
            if 'code' in updates:
                self.strategy_code[strategy_id] = updates['code']
            
            metadata.updated_at = time.time()
            
            # Create new version if parameters changed
            if 'parameters' in updates:
                version_changes = changes or [f"Updated parameters: {list(updates['parameters'].keys())}"]
                await self._create_version(strategy_id, "; ".join(version_changes), metadata.parameters)
            
            # Update search index
            await self._update_search_index(strategy_id)
            
            # Save to disk
            await self._save_strategy(strategy_id)
            
            logger.info(f"✓ Strategy {strategy_id} updated")
            return True
            
        except Exception as e:
            logger.error(f"Error updating strategy {strategy_id}: {e}")
            return False
    
    async def get_strategy(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """Get strategy information"""
        try:
            if strategy_id not in self.strategies:
                return None
            
            metadata = self.strategies[strategy_id]
            code = self.strategy_code.get(strategy_id, "")
            versions = self.strategy_versions.get(strategy_id, [])
            performance = self.strategy_performance.get(strategy_id, [])
            
            return {
                'metadata': asdict(metadata),
                'code': code,
                'versions': [asdict(v) for v in versions],
                'performance_history': performance
            }
            
        except Exception as e:
            logger.error(f"Error getting strategy {strategy_id}: {e}")
            return None
    
    async def search_strategies(self, query: str = None, tags: List[str] = None,
                              strategy_type: str = None, status: str = None,
                              min_performance: float = None) -> List[Dict[str, Any]]:
        """Search strategies based on criteria"""
        try:
            results = []
            
            for strategy_id, metadata in self.strategies.items():
                # Apply filters
                if strategy_type and metadata.strategy_type != strategy_type:
                    continue
                if status and metadata.status != status:
                    continue
                if tags and not any(tag in metadata.tags for tag in tags):
                    continue
                if min_performance:
                    avg_performance = self._get_average_performance(strategy_id)
                    if avg_performance is None or avg_performance < min_performance:
                        continue
                
                # Text search in name and description
                if query:
                    search_text = f"{metadata.name} {metadata.description}".lower()
                    if query.lower() not in search_text:
                        continue
                
                # Add to results
                strategy_info = await self.get_strategy(strategy_id)
                if strategy_info:
                    results.append(strategy_info)
            
            # Sort by performance (if available)
            results.sort(key=lambda x: self._get_strategy_score(x), reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching strategies: {e}")
            return []
    
    async def record_performance(self, strategy_id: str, performance_data: Dict[str, Any]) -> bool:
        """Record performance data for a strategy"""
        try:
            if strategy_id not in self.strategies:
                logger.error(f"Strategy {strategy_id} not found")
                return False
            
            # Add timestamp
            performance_record = performance_data.copy()
            performance_record['timestamp'] = time.time()
            
            # Store performance data
            if strategy_id not in self.strategy_performance:
                self.strategy_performance[strategy_id] = []
            
            self.strategy_performance[strategy_id].append(performance_record)
            
            # Update strategy metadata with latest performance
            metadata = self.strategies[strategy_id]
            metadata.performance_metrics.update({
                key: value for key, value in performance_data.items()
                if isinstance(value, (int, float))
            })
            metadata.updated_at = time.time()
            
            # Update performance rankings
            await self._update_performance_rankings()
            
            # Save to disk
            await self._save_strategy(strategy_id)
            
            logger.debug(f"Performance recorded for strategy {strategy_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error recording performance for {strategy_id}: {e}")
            return False
    
    async def get_best_strategies(self, strategy_type: str = None, 
                                limit: int = 10) -> List[Dict[str, Any]]:
        """Get best performing strategies"""
        try:
            # Filter by type if specified
            candidates = []
            for strategy_id, score in self.performance_rankings:
                if strategy_type:
                    metadata = self.strategies.get(strategy_id)
                    if not metadata or metadata.strategy_type != strategy_type:
                        continue
                
                strategy_info = await self.get_strategy(strategy_id)
                if strategy_info:
                    strategy_info['performance_score'] = score
                    candidates.append(strategy_info)
            
            return candidates[:limit]
            
        except Exception as e:
            logger.error(f"Error getting best strategies: {e}")
            return []
    
    async def clone_strategy(self, source_id: str, new_id: str, 
                           modifications: Dict[str, Any] = None) -> bool:
        """Clone an existing strategy with optional modifications"""
        try:
            source_strategy = await self.get_strategy(source_id)
            if not source_strategy:
                logger.error(f"Source strategy {source_id} not found")
                return False
            
            # Prepare cloned strategy data
            metadata = source_strategy['metadata']
            code = source_strategy['code']
            
            # Apply modifications
            if modifications:
                if 'name' in modifications:
                    metadata['name'] = modifications['name']
                if 'parameters' in modifications:
                    metadata['parameters'].update(modifications['parameters'])
                if 'code' in modifications:
                    code = modifications['code']
                if 'tags' in modifications:
                    metadata['tags'] = modifications['tags']
            
            # Add clone tag
            if 'cloned_from' not in metadata['tags']:
                metadata['tags'].append(f"cloned_from:{source_id}")
            
            # Create new strategy
            return await self.add_strategy(
                strategy_id=new_id,
                name=metadata['name'],
                strategy_type=metadata['strategy_type'],
                code=code,
                parameters=metadata['parameters'],
                description=f"Cloned from {source_id}. {metadata['description']}",
                author=metadata['author'],
                tags=metadata['tags']
            )
            
        except Exception as e:
            logger.error(f"Error cloning strategy {source_id} to {new_id}: {e}")
            return False
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get repository metrics"""
        await self._update_metrics()
        return self.repository_metrics.copy()
    
    # Private methods
    
    async def _create_version(self, strategy_id: str, changes: str, 
                            parameters: Dict[str, Any]) -> None:
        """Create a new version of a strategy"""
        try:
            if strategy_id not in self.strategy_versions:
                self.strategy_versions[strategy_id] = []
            
            versions = self.strategy_versions[strategy_id]
            
            # Generate version number
            if versions:
                last_version = versions[-1].version
                major, minor, patch = map(int, last_version.split('.'))
                new_version = f"{major}.{minor}.{patch + 1}"
            else:
                new_version = "1.0.0"
            
            # Calculate checksum
            param_str = json.dumps(parameters, sort_keys=True)
            checksum = hashlib.md5(param_str.encode()).hexdigest()
            
            # Create version
            version = StrategyVersion(
                version=new_version,
                timestamp=time.time(),
                changes=[changes],
                performance_delta=None,
                parameters=parameters.copy(),
                checksum=checksum
            )
            
            versions.append(version)
            
            # Limit version history
            if len(versions) > self.max_versions:
                versions.pop(0)
            
            # Update strategy metadata version
            self.strategies[strategy_id].version = new_version
            
        except Exception as e:
            logger.error(f"Error creating version for {strategy_id}: {e}")
    
    async def _load_strategies(self):
        """Load strategies from disk"""
        try:
            strategies_file = self.storage_path / "strategies.json"
            if strategies_file.exists():
                with open(strategies_file, 'r') as f:
                    data = json.load(f)
                
                # Load metadata
                for strategy_id, metadata_dict in data.get('strategies', {}).items():
                    self.strategies[strategy_id] = StrategyMetadata(**metadata_dict)
                
                # Load code
                self.strategy_code = data.get('strategy_code', {})
                
                # Load versions
                for strategy_id, versions_data in data.get('strategy_versions', {}).items():
                    self.strategy_versions[strategy_id] = [
                        StrategyVersion(**v) for v in versions_data
                    ]
                
                # Load performance
                self.strategy_performance = data.get('strategy_performance', {})
                
                logger.info(f"Loaded {len(self.strategies)} strategies from disk")
                
        except Exception as e:
            logger.error(f"Error loading strategies: {e}")
    
    async def _save_strategies(self):
        """Save strategies to disk"""
        try:
            data = {
                'strategies': {
                    strategy_id: asdict(metadata)
                    for strategy_id, metadata in self.strategies.items()
                },
                'strategy_code': self.strategy_code,
                'strategy_versions': {
                    strategy_id: [asdict(v) for v in versions]
                    for strategy_id, versions in self.strategy_versions.items()
                },
                'strategy_performance': self.strategy_performance
            }
            
            strategies_file = self.storage_path / "strategies.json"
            with open(strategies_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving strategies: {e}")
    
    async def _save_strategy(self, strategy_id: str):
        """Save individual strategy"""
        # For now, save all strategies (could optimize to save individual files)
        await self._save_strategies()
    
    async def _load_knowledge_base(self):
        """Load knowledge base from disk"""
        try:
            kb_file = self.storage_path / "knowledge_base.json"
            if kb_file.exists():
                with open(kb_file, 'r') as f:
                    data = json.load(f)
                
                self.strategy_patterns = data.get('strategy_patterns', {})
                self.best_practices = data.get('best_practices', [])
                self.optimization_history = data.get('optimization_history', {})
                
        except Exception as e:
            logger.error(f"Error loading knowledge base: {e}")
    
    async def _save_knowledge_base(self):
        """Save knowledge base to disk"""
        try:
            data = {
                'strategy_patterns': self.strategy_patterns,
                'best_practices': self.best_practices,
                'optimization_history': self.optimization_history
            }
            
            kb_file = self.storage_path / "knowledge_base.json"
            with open(kb_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving knowledge base: {e}")
    
    async def _build_search_index(self):
        """Build search index for strategies"""
        try:
            self.strategy_index = {}
            
            for strategy_id, metadata in self.strategies.items():
                # Index by tags
                for tag in metadata.tags:
                    if tag not in self.strategy_index:
                        self.strategy_index[tag] = []
                    self.strategy_index[tag].append(strategy_id)
                
                # Index by type
                strategy_type = metadata.strategy_type
                if strategy_type not in self.strategy_index:
                    self.strategy_index[strategy_type] = []
                self.strategy_index[strategy_type].append(strategy_id)
                
        except Exception as e:
            logger.error(f"Error building search index: {e}")
    
    async def _update_search_index(self, strategy_id: str):
        """Update search index for a strategy"""
        try:
            # Remove from existing index entries
            for tag_list in self.strategy_index.values():
                if strategy_id in tag_list:
                    tag_list.remove(strategy_id)
            
            # Re-add to index
            metadata = self.strategies[strategy_id]
            for tag in metadata.tags:
                if tag not in self.strategy_index:
                    self.strategy_index[tag] = []
                self.strategy_index[tag].append(strategy_id)
            
            # Add to type index
            strategy_type = metadata.strategy_type
            if strategy_type not in self.strategy_index:
                self.strategy_index[strategy_type] = []
            if strategy_id not in self.strategy_index[strategy_type]:
                self.strategy_index[strategy_type].append(strategy_id)
                
        except Exception as e:
            logger.error(f"Error updating search index for {strategy_id}: {e}")
    
    async def _update_performance_rankings(self):
        """Update performance rankings"""
        try:
            rankings = []
            
            for strategy_id in self.strategies:
                score = self._get_average_performance(strategy_id)
                if score is not None:
                    rankings.append((strategy_id, score))
            
            # Sort by score (descending)
            rankings.sort(key=lambda x: x[1], reverse=True)
            self.performance_rankings = rankings
            
        except Exception as e:
            logger.error(f"Error updating performance rankings: {e}")
    
    def _get_average_performance(self, strategy_id: str) -> Optional[float]:
        """Get average performance score for a strategy"""
        try:
            performance_data = self.strategy_performance.get(strategy_id, [])
            if not performance_data:
                return None
            
            # Calculate composite score (simplified)
            scores = []
            for record in performance_data[-10:]:  # Last 10 records
                # Use Sharpe ratio as primary metric, fallback to total return
                if 'sharpe_ratio' in record:
                    scores.append(record['sharpe_ratio'])
                elif 'total_return' in record:
                    scores.append(record['total_return'])
            
            return sum(scores) / len(scores) if scores else None
            
        except Exception as e:
            logger.error(f"Error calculating average performance for {strategy_id}: {e}")
            return None
    
    def _get_strategy_score(self, strategy_info: Dict[str, Any]) -> float:
        """Get composite score for strategy ranking"""
        try:
            metadata = strategy_info['metadata']
            performance_history = strategy_info['performance_history']
            
            # Base score from recent performance
            base_score = 0.0
            if performance_history:
                recent_performance = performance_history[-5:]  # Last 5 records
                if recent_performance:
                    scores = []
                    for record in recent_performance:
                        if 'sharpe_ratio' in record:
                            scores.append(record['sharpe_ratio'])
                        elif 'total_return' in record:
                            scores.append(record['total_return'] * 0.1)  # Scale down returns
                    
                    if scores:
                        base_score = sum(scores) / len(scores)
            
            # Bonus for active strategies
            if metadata['status'] == 'active':
                base_score += 0.1
            
            # Penalty for deprecated strategies
            if metadata['status'] == 'deprecated':
                base_score -= 0.5
            
            return base_score
            
        except Exception as e:
            logger.error(f"Error calculating strategy score: {e}")
            return 0.0
    
    async def _update_metrics(self):
        """Update repository metrics"""
        try:
            total_strategies = len(self.strategies)
            active_strategies = sum(1 for s in self.strategies.values() if s.status == 'active')
            strategy_types = len(set(s.strategy_type for s in self.strategies.values()))
            
            # Performance metrics
            total_performance_records = sum(len(records) for records in self.strategy_performance.values())
            avg_performance = 0.0
            
            if self.performance_rankings:
                avg_performance = sum(score for _, score in self.performance_rankings) / len(self.performance_rankings)
            
            self.repository_metrics = {
                'total_strategies': total_strategies,
                'active_strategies': active_strategies,
                'strategy_types': strategy_types,
                'total_performance_records': total_performance_records,
                'average_performance': avg_performance,
                'optimized_count': sum(len(versions) - 1 for versions in self.strategy_versions.values()),
                'knowledge_items': len(self.strategy_patterns) + len(self.best_practices),
                'last_updated': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error updating repository metrics: {e}")


# Additional utility functions for strategy repository

async def create_strategy_template(strategy_type: str) -> Dict[str, Any]:
    """Create a template for a new strategy"""
    templates = {
        'momentum': {
            'parameters': {
                'lookback_period': 20,
                'momentum_threshold': 0.02,
                'position_size': 0.1,
                'stop_loss': 0.05
            },
            'code_template': '''
async def generate_signal(self, symbol: str, data: pd.DataFrame) -> Optional[Dict[str, Any]]:
    # Momentum strategy implementation
    returns = data['close'].pct_change(self.lookback_period)
    momentum = returns.iloc[-1]

    if momentum > self.momentum_threshold:
        return {'action': 'buy', 'confidence': min(momentum * 10, 1.0)}
    elif momentum < -self.momentum_threshold:
        return {'action': 'sell', 'confidence': min(abs(momentum) * 10, 1.0)}

    return {'action': 'hold', 'confidence': 0.5}
            '''
        },
        'mean_reversion': {
            'parameters': {
                'lookback_period': 20,
                'z_score_threshold': 2.0,
                'position_size': 0.1,
                'stop_loss': 0.03
            },
            'code_template': '''
async def generate_signal(self, symbol: str, data: pd.DataFrame) -> Optional[Dict[str, Any]]:
    # Mean reversion strategy implementation
    prices = data['close']
    mean_price = prices.rolling(self.lookback_period).mean().iloc[-1]
    std_price = prices.rolling(self.lookback_period).std().iloc[-1]
    current_price = prices.iloc[-1]

    z_score = (current_price - mean_price) / std_price

    if z_score > self.z_score_threshold:
        return {'action': 'sell', 'confidence': min(abs(z_score) / 3, 1.0)}
    elif z_score < -self.z_score_threshold:
        return {'action': 'buy', 'confidence': min(abs(z_score) / 3, 1.0)}

    return {'action': 'hold', 'confidence': 0.5}
            '''
        }
    }

    return templates.get(strategy_type, {
        'parameters': {},
        'code_template': '# Custom strategy implementation'
    })

#!/usr/bin/env python3
"""
Test Fixed Issues - Verify all critical issues have been resolved
"""

import asyncio
import json
from datetime import datetime
from config.config_manager import ConfigManager
from models.ollama_hub import OllamaModelHub
from agents.agent_manager import AgentManager
from agents.base_agent import Agent<PERSON><PERSON>
from strategies.strategy_manager import StrategyManager
from risk.risk_manager import RiskManager

class SimpleMessageBroker:
    def __init__(self):
        self.messages = []
    
    async def publish(self, topic, message):
        self.messages.append({"topic": topic, "message": message, "timestamp": datetime.now()})
        print(f"📢 {topic}: {str(message)[:80]}...")
    
    async def subscribe(self, topic, callback):
        pass

async def test_fixed_issues():
    """Test that all critical issues have been fixed"""
    
    print("🔧 TESTING FIXED ISSUES")
    print("=" * 50)
    
    results = {}
    
    try:
        # Setup
        config_manager = ConfigManager('config/test_config.yaml')
        await config_manager.load_config()
        config = await config_manager.get_config()
        
        ollama_hub = OllamaModelHub(config=config)
        await ollama_hub.initialize()
        
        message_broker = SimpleMessageBroker()
        
        agent_manager = AgentManager(ollama_hub, message_broker, config)
        await agent_manager.initialize()
        
        print("✅ System initialized")
        
        # Test 1: Fix for missing check_risk_limits method
        print("\n🛡️ TEST 1: Risk Manager check_risk_limits Method")
        try:
            risk_manager = RiskManager(config)
            await risk_manager.initialize()
            
            test_portfolio = {
                'total_value': 100000,
                'positions': [
                    {'symbol': 'AAPL', 'value': 30000, 'shares': 200},
                    {'symbol': 'TSLA', 'value': 25000, 'shares': 100}
                ],
                'cash': 45000
            }
            
            risk_limits = {
                'max_portfolio_risk': 0.02,
                'max_position_size': 0.1,
                'max_sector_exposure': 0.3
            }
            
            violations = await risk_manager.check_risk_limits(test_portfolio, risk_limits)
            print(f"✅ check_risk_limits method working: {len(violations)} violations found")
            results['risk_limits_method'] = {'success': True, 'violations': len(violations)}
            
        except Exception as e:
            print(f"❌ check_risk_limits method failed: {e}")
            results['risk_limits_method'] = {'success': False, 'error': str(e)}
        
        # Test 2: Fix for AI Agent JSON parsing with text fallback
        print("\n🧠 TEST 2: AI Agent JSON Parsing with Text Fallback")
        try:
            # Create Risk Manager agent
            risk_mgr_id = await agent_manager.create_agent(
                role=AgentRole.RISK_MANAGER,
                name="Test_Risk_Manager"
            )
            risk_agent = await agent_manager.get_agent(risk_mgr_id)
            
            # Test risk assessment with text parsing fallback
            risk_task = {
                "type": "assess_portfolio_risk",
                "portfolio": test_portfolio,
                "market_conditions": {"volatility": "moderate"}
            }
            
            risk_result = await risk_agent.execute_task(risk_task)
            
            if risk_result and risk_result.get('success'):
                print("✅ AI Risk Assessment with text parsing: SUCCESS")
                if 'note' in risk_result:
                    print(f"   Note: {risk_result['note']}")
                results['ai_text_parsing'] = {'success': True, 'method': 'text_parsing' if 'note' in risk_result else 'json'}
            else:
                print(f"⚠️ AI Risk Assessment: {risk_result.get('error', 'Failed')}")
                results['ai_text_parsing'] = {'success': False, 'error': risk_result.get('error', 'Unknown')}
                
        except Exception as e:
            print(f"❌ AI text parsing test failed: {e}")
            results['ai_text_parsing'] = {'success': False, 'error': str(e)}
        
        # Test 3: Fix for Strategy Developer text parsing
        print("\n🎯 TEST 3: Strategy Developer Text Parsing")
        try:
            # Create Strategy Developer agent
            strategist_id = await agent_manager.create_agent(
                role=AgentRole.STRATEGY_DEVELOPER,
                name="Test_Strategy_Developer"
            )
            strategist = await agent_manager.get_agent(strategist_id)
            
            # Test strategy development with text parsing
            strategy_task = {
                "type": "develop_strategy",
                "strategy_type": "momentum",
                "market_conditions": {"trend": "bullish", "volatility": "moderate"},
                "constraints": {"max_risk": 0.02, "capital": 100000}
            }
            
            strategy_result = await strategist.execute_task(strategy_task)
            
            if strategy_result and strategy_result.get('success'):
                print("✅ Strategy Development with text parsing: SUCCESS")
                if 'note' in strategy_result:
                    print(f"   Note: {strategy_result['note']}")
                results['strategy_text_parsing'] = {'success': True, 'method': 'text_parsing' if 'note' in strategy_result else 'json'}
            else:
                print(f"⚠️ Strategy Development: {strategy_result.get('error', 'Failed')}")
                results['strategy_text_parsing'] = {'success': False, 'error': strategy_result.get('error', 'Unknown')}
                
        except Exception as e:
            print(f"❌ Strategy text parsing test failed: {e}")
            results['strategy_text_parsing'] = {'success': False, 'error': str(e)}
        
        # Test 4: Fix for Market Analyst text parsing
        print("\n📊 TEST 4: Market Analyst Text Parsing")
        try:
            # Create Market Analyst agent
            analyst_id = await agent_manager.create_agent(
                role=AgentRole.MARKET_ANALYST,
                name="Test_Market_Analyst"
            )
            analyst = await agent_manager.get_agent(analyst_id)
            
            # Test technical analysis with text parsing
            analysis_task = {
                "type": "technical_analysis",
                "symbol": "AAPL",
                "timeframe": "1h",
                "indicators": ["rsi", "macd"]
            }
            
            analysis_result = await analyst.execute_task(analysis_task)
            
            if analysis_result and analysis_result.get('success'):
                print("✅ Technical Analysis with text parsing: SUCCESS")
                if 'note' in analysis_result:
                    print(f"   Note: {analysis_result['note']}")
                results['analyst_text_parsing'] = {'success': True, 'method': 'text_parsing' if 'note' in analysis_result else 'json'}
            else:
                print(f"⚠️ Technical Analysis: {analysis_result.get('error', 'Failed')}")
                results['analyst_text_parsing'] = {'success': False, 'error': analysis_result.get('error', 'Unknown')}
                
        except Exception as e:
            print(f"❌ Analyst text parsing test failed: {e}")
            results['analyst_text_parsing'] = {'success': False, 'error': str(e)}
        
        # Test 5: Connection cleanup test
        print("\n🔗 TEST 5: Connection Cleanup")
        try:
            # Test model instance cleanup
            if hasattr(risk_agent, 'model_instance'):
                await risk_agent.model_instance.cleanup()
                print("✅ Model instance cleanup: SUCCESS")
                results['connection_cleanup'] = {'success': True, 'method': 'model_cleanup'}
            else:
                print("⚠️ No model instance to cleanup")
                results['connection_cleanup'] = {'success': True, 'method': 'no_cleanup_needed'}
                
        except Exception as e:
            print(f"❌ Connection cleanup test failed: {e}")
            results['connection_cleanup'] = {'success': False, 'error': str(e)}
        
        # Test 6: System integration after fixes
        print("\n🔗 TEST 6: System Integration After Fixes")
        try:
            # Test complete workflow
            integration_score = 0
            total_tests = 5
            
            # Test 1: Risk limits working
            if results.get('risk_limits_method', {}).get('success'):
                integration_score += 1
            
            # Test 2: AI agents working with fallback
            if results.get('ai_text_parsing', {}).get('success'):
                integration_score += 1
            
            # Test 3: Strategy development working
            if results.get('strategy_text_parsing', {}).get('success'):
                integration_score += 1
            
            # Test 4: Market analysis working
            if results.get('analyst_text_parsing', {}).get('success'):
                integration_score += 1
            
            # Test 5: Cleanup working
            if results.get('connection_cleanup', {}).get('success'):
                integration_score += 1
            
            integration_percentage = (integration_score / total_tests) * 100
            print(f"✅ System Integration: {integration_score}/{total_tests} ({integration_percentage:.1f}%)")
            
            results['system_integration'] = {
                'success': integration_score >= 4,  # 80% threshold
                'score': integration_score,
                'total': total_tests,
                'percentage': integration_percentage
            }
            
        except Exception as e:
            print(f"❌ System integration test failed: {e}")
            results['system_integration'] = {'success': False, 'error': str(e)}
        
        # Cleanup
        try:
            await ollama_hub.stop()
            print("✅ System cleanup completed")
        except Exception as e:
            print(f"⚠️ Cleanup warning: {e}")
        
        # Summary
        print("\n🎉 FIXED ISSUES TEST SUMMARY")
        print("=" * 50)
        
        successful_fixes = sum(1 for result in results.values() if result.get('success'))
        total_fixes = len(results)
        
        print(f"📊 Fixed Issues: {successful_fixes}/{total_fixes} working")
        print(f"🔧 Success Rate: {(successful_fixes/total_fixes)*100:.1f}%")
        
        # Detailed results
        for fix_name, result in results.items():
            status = "✅ FIXED" if result.get('success') else "❌ STILL BROKEN"
            method = f" ({result.get('method', 'unknown')})" if result.get('method') else ""
            print(f"  {fix_name}: {status}{method}")
        
        # Save results
        test_summary = {
            "timestamp": datetime.now().isoformat(),
            "test_type": "fixed_issues_verification",
            "fixes": results,
            "summary": {
                "total_fixes": total_fixes,
                "successful_fixes": successful_fixes,
                "success_rate": (successful_fixes/total_fixes)*100
            }
        }
        
        with open('fixed_issues_results.json', 'w') as f:
            json.dump(test_summary, f, indent=2, default=str)
        
        print(f"\n📄 Results saved to: fixed_issues_results.json")
        
        return successful_fixes >= (total_fixes * 0.8)  # 80% success threshold
        
    except Exception as e:
        print(f"❌ System Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_fixed_issues())
    if success:
        print("\n🎉 ALL CRITICAL ISSUES HAVE BEEN FIXED!")
        print("🚀 System is ready for production deployment!")
    else:
        print("\n⚠️ SOME ISSUES STILL NEED ATTENTION")
        print("🔧 Review the test results for remaining problems")

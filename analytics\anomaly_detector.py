"""
Anomaly Detector - Advanced anomaly detection for market data
"""

import asyncio
import logging
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from collections import deque, defaultdict
from datetime import datetime, timedelta
from scipy import stats
import json

logger = logging.getLogger(__name__)


@dataclass
class AnomalyAlert:
    """Anomaly alert data structure"""
    alert_id: str
    symbol: str
    anomaly_type: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    anomaly_score: float
    description: str
    detected_at: datetime
    data_point: Dict[str, Any]
    context: Dict[str, Any]
    recommended_actions: List[str]


class AnomalyDetector:
    """
    Advanced anomaly detection system for market data.
    
    Features:
    - Statistical anomaly detection
    - Price and volume anomalies
    - Pattern-based anomaly detection
    - Multi-timeframe anomaly analysis
    - Adaptive threshold management
    - Real-time anomaly alerts
    - False positive reduction
    - Anomaly severity classification
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.anomaly_config = config.get('anomaly_detector', {})
        
        # Anomaly detection methods
        self.detection_methods = {
            'statistical': self._detect_statistical_anomalies,
            'price_volume': self._detect_price_volume_anomalies,
            'pattern': self._detect_pattern_anomalies,
            'volatility': self._detect_volatility_anomalies,
            'correlation': self._detect_correlation_anomalies
        }
        
        # Historical data for baseline
        self.historical_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.baseline_stats: Dict[str, Dict[str, float]] = {}
        
        # Anomaly tracking
        self.active_anomalies: Dict[str, AnomalyAlert] = {}
        self.anomaly_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=500))
        
        # Detection parameters
        self.z_score_threshold = self.anomaly_config.get('z_score_threshold', 3.0)
        self.iqr_multiplier = self.anomaly_config.get('iqr_multiplier', 1.5)
        self.min_data_points = self.anomaly_config.get('min_data_points', 50)
        self.alert_cooldown = self.anomaly_config.get('alert_cooldown', 300)  # seconds
        
        # Severity thresholds
        self.severity_thresholds = {
            'low': 2.0,
            'medium': 3.0,
            'high': 4.0,
            'critical': 5.0
        }
        
        # Performance tracking
        self.detection_metrics = {
            'total_detections': 0,
            'alerts_generated': 0,
            'false_positives': 0,
            'true_positives': 0,
            'detection_rate': 0.0
        }
        
        # State
        self.initialized = False
        self.running = False
    
    async def initialize(self) -> bool:
        """Initialize anomaly detector"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Anomaly Detector...")
            
            # Initialize baseline statistics
            await self._initialize_baseline_stats()
            
            # Setup detection thresholds
            await self._setup_detection_thresholds()
            
            self.initialized = True
            logger.info("✓ Anomaly Detector initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Anomaly Detector: {e}")
            return False
    
    async def start(self) -> bool:
        """Start anomaly detector"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        self.running = True
        logger.info("✓ Anomaly Detector started")
        return True
    
    async def stop(self) -> bool:
        """Stop anomaly detector"""
        self.running = False
        logger.info("✓ Anomaly Detector stopped")
        return True
    
    async def detect_anomalies(self, symbol: str, market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Detect anomalies in market data"""
        try:
            # Store data point
            data_point = {
                'timestamp': datetime.now(),
                'price': market_data.get('price', 0.0),
                'volume': market_data.get('volume', 0.0),
                'volatility': market_data.get('volatility', 0.0),
                'bid': market_data.get('bid', 0.0),
                'ask': market_data.get('ask', 0.0)
            }
            
            self.historical_data[symbol].append(data_point)
            
            # Need minimum data points for detection
            if len(self.historical_data[symbol]) < self.min_data_points:
                return None
            
            # Run all detection methods
            anomalies = {}
            overall_anomaly_score = 0.0
            
            for method_name, method_func in self.detection_methods.items():
                try:
                    result = await method_func(symbol, data_point)
                    if result and result.get('is_anomaly', False):
                        anomalies[method_name] = result
                        overall_anomaly_score = max(overall_anomaly_score, result.get('score', 0.0))
                except Exception as e:
                    logger.warning(f"Error in {method_name} detection: {e}")
            
            # Generate result
            if anomalies:
                # Determine severity
                severity = self._determine_severity(overall_anomaly_score)
                
                # Create anomaly alert if significant
                if overall_anomaly_score >= self.severity_thresholds['low']:
                    alert = await self._create_anomaly_alert(symbol, anomalies, overall_anomaly_score, severity, data_point)
                    if alert:
                        self.active_anomalies[alert.alert_id] = alert
                        self.anomaly_history[symbol].append(alert)
                        self.detection_metrics['alerts_generated'] += 1
                
                self.detection_metrics['total_detections'] += 1
                
                return {
                    'symbol': symbol,
                    'is_anomaly': True,
                    'anomaly_score': overall_anomaly_score,
                    'severity': severity,
                    'anomalies_detected': anomalies,
                    'confidence': min(0.9, overall_anomaly_score / 5.0),
                    'timestamp': datetime.now()
                }
            else:
                return {
                    'symbol': symbol,
                    'is_anomaly': False,
                    'anomaly_score': 0.0,
                    'timestamp': datetime.now()
                }
                
        except Exception as e:
            logger.error(f"Error detecting anomalies for {symbol}: {e}")
            return None
    
    async def get_active_anomalies(self, symbol: str = None, severity: str = None) -> List[AnomalyAlert]:
        """Get active anomaly alerts"""
        try:
            alerts = []
            
            for alert in self.active_anomalies.values():
                # Filter by symbol
                if symbol and alert.symbol != symbol:
                    continue
                
                # Filter by severity
                if severity and alert.severity != severity:
                    continue
                
                # Check if alert is still active (not expired)
                if datetime.now() - alert.detected_at < timedelta(seconds=self.alert_cooldown):
                    alerts.append(alert)
            
            # Sort by severity and score
            severity_order = {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}
            alerts.sort(key=lambda x: (severity_order.get(x.severity, 0), x.anomaly_score), reverse=True)
            
            return alerts
            
        except Exception as e:
            logger.error(f"Error getting active anomalies: {e}")
            return []
    
    async def get_anomaly_statistics(self, symbol: str = None) -> Dict[str, Any]:
        """Get anomaly detection statistics"""
        try:
            if symbol:
                # Symbol-specific statistics
                history = list(self.anomaly_history.get(symbol, []))
                
                if not history:
                    return {'symbol': symbol, 'no_data': True}
                
                # Calculate statistics
                total_anomalies = len(history)
                severity_counts = defaultdict(int)
                anomaly_types = defaultdict(int)
                
                for alert in history:
                    severity_counts[alert.severity] += 1
                    anomaly_types[alert.anomaly_type] += 1
                
                recent_anomalies = [alert for alert in history if datetime.now() - alert.detected_at < timedelta(hours=24)]
                
                return {
                    'symbol': symbol,
                    'total_anomalies': total_anomalies,
                    'recent_anomalies_24h': len(recent_anomalies),
                    'severity_distribution': dict(severity_counts),
                    'anomaly_types': dict(anomaly_types),
                    'average_score': np.mean([alert.anomaly_score for alert in history]),
                    'last_anomaly': history[-1].detected_at.isoformat() if history else None
                }
            else:
                # Overall statistics
                all_alerts = []
                for history in self.anomaly_history.values():
                    all_alerts.extend(history)
                
                total_symbols = len(self.anomaly_history)
                total_anomalies = len(all_alerts)
                
                severity_counts = defaultdict(int)
                for alert in all_alerts:
                    severity_counts[alert.severity] += 1
                
                return {
                    'total_symbols_monitored': total_symbols,
                    'total_anomalies': total_anomalies,
                    'active_anomalies': len(self.active_anomalies),
                    'severity_distribution': dict(severity_counts),
                    'detection_metrics': self.detection_metrics.copy(),
                    'detection_methods': list(self.detection_methods.keys())
                }
                
        except Exception as e:
            logger.error(f"Error getting anomaly statistics: {e}")
            return {}
    
    # Private methods
    
    async def _initialize_baseline_stats(self):
        """Initialize baseline statistics"""
        try:
            # Initialize for configured symbols
            symbols = self.config.get('symbols', {})
            for symbol_group, symbol_list in symbols.items():
                for symbol in symbol_list:
                    self.baseline_stats[symbol] = {
                        'price_mean': 0.0,
                        'price_std': 0.0,
                        'volume_mean': 0.0,
                        'volume_std': 0.0,
                        'volatility_mean': 0.0,
                        'volatility_std': 0.0
                    }
            
            logger.debug(f"Initialized baseline stats for {len(self.baseline_stats)} symbols")
            
        except Exception as e:
            logger.error(f"Error initializing baseline stats: {e}")
            raise
    
    async def _setup_detection_thresholds(self):
        """Setup detection thresholds"""
        try:
            # Thresholds are already configured in __init__
            logger.debug("Detection thresholds configured")
            
        except Exception as e:
            logger.error(f"Error setting up detection thresholds: {e}")
            raise
    
    async def _detect_statistical_anomalies(self, symbol: str, data_point: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Detect statistical anomalies using z-score and IQR"""
        try:
            historical_data = list(self.historical_data[symbol])
            
            if len(historical_data) < self.min_data_points:
                return None
            
            # Extract price data
            prices = [dp['price'] for dp in historical_data[:-1]]  # Exclude current point
            current_price = data_point['price']
            
            # Z-score anomaly detection
            price_mean = np.mean(prices)
            price_std = np.std(prices)
            
            if price_std > 0:
                z_score = abs(current_price - price_mean) / price_std
                
                if z_score > self.z_score_threshold:
                    return {
                        'is_anomaly': True,
                        'method': 'z_score',
                        'score': z_score,
                        'threshold': self.z_score_threshold,
                        'current_value': current_price,
                        'expected_range': [price_mean - 2*price_std, price_mean + 2*price_std]
                    }
            
            # IQR anomaly detection
            q1 = np.percentile(prices, 25)
            q3 = np.percentile(prices, 75)
            iqr = q3 - q1
            
            lower_bound = q1 - self.iqr_multiplier * iqr
            upper_bound = q3 + self.iqr_multiplier * iqr
            
            if current_price < lower_bound or current_price > upper_bound:
                iqr_score = max(
                    (lower_bound - current_price) / iqr if current_price < lower_bound else 0,
                    (current_price - upper_bound) / iqr if current_price > upper_bound else 0
                )
                
                return {
                    'is_anomaly': True,
                    'method': 'iqr',
                    'score': iqr_score + 2.0,  # Add base score
                    'threshold': self.iqr_multiplier,
                    'current_value': current_price,
                    'expected_range': [lower_bound, upper_bound]
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error in statistical anomaly detection: {e}")
            return None
    
    async def _detect_price_volume_anomalies(self, symbol: str, data_point: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Detect price-volume relationship anomalies"""
        try:
            historical_data = list(self.historical_data[symbol])
            
            if len(historical_data) < self.min_data_points:
                return None
            
            # Extract price and volume data
            prices = [dp['price'] for dp in historical_data[:-1]]
            volumes = [dp['volume'] for dp in historical_data[:-1]]
            
            current_price = data_point['price']
            current_volume = data_point['volume']
            
            # Calculate price changes and volume
            if len(historical_data) >= 2:
                previous_price = historical_data[-2]['price']
                price_change_pct = abs(current_price - previous_price) / previous_price if previous_price != 0 else 0.0
                
                # Expected volume based on price change
                avg_volume = np.mean(volumes) if volumes else 0.0
                
                # Anomaly: Large price change with low volume
                if price_change_pct > 0.05 and current_volume < avg_volume * 0.5:
                    return {
                        'is_anomaly': True,
                        'method': 'price_volume_divergence',
                        'score': 3.0 + price_change_pct * 10,
                        'description': 'Large price change with unusually low volume',
                        'price_change_pct': price_change_pct,
                        'volume_ratio': current_volume / avg_volume if avg_volume > 0 else 0.0
                    }
                
                # Anomaly: Small price change with very high volume
                if price_change_pct < 0.01 and current_volume > avg_volume * 3.0:
                    return {
                        'is_anomaly': True,
                        'method': 'volume_spike',
                        'score': 2.5 + (current_volume / avg_volume),
                        'description': 'Volume spike without significant price movement',
                        'price_change_pct': price_change_pct,
                        'volume_ratio': current_volume / avg_volume if avg_volume > 0 else 0.0
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"Error in price-volume anomaly detection: {e}")
            return None
    
    async def _detect_pattern_anomalies(self, symbol: str, data_point: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Detect pattern-based anomalies"""
        try:
            historical_data = list(self.historical_data[symbol])
            
            if len(historical_data) < 20:
                return None
            
            # Extract recent price pattern
            recent_prices = [dp['price'] for dp in historical_data[-20:]]
            
            # Detect sudden pattern breaks
            # Calculate moving average and check for sudden deviations
            if len(recent_prices) >= 10:
                ma_short = np.mean(recent_prices[-5:])
                ma_long = np.mean(recent_prices[-10:])
                current_price = data_point['price']
                
                # Pattern break: price suddenly moves away from trend
                trend_deviation = abs(current_price - ma_short) / ma_short if ma_short != 0 else 0.0
                
                if trend_deviation > 0.1:  # 10% deviation
                    return {
                        'is_anomaly': True,
                        'method': 'pattern_break',
                        'score': 2.0 + trend_deviation * 10,
                        'description': 'Sudden break from established price pattern',
                        'trend_deviation': trend_deviation,
                        'current_price': current_price,
                        'expected_price': ma_short
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"Error in pattern anomaly detection: {e}")
            return None
    
    async def _detect_volatility_anomalies(self, symbol: str, data_point: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Detect volatility anomalies"""
        try:
            historical_data = list(self.historical_data[symbol])
            
            if len(historical_data) < self.min_data_points:
                return None
            
            # Calculate historical volatility
            prices = [dp['price'] for dp in historical_data[:-1]]
            returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices)) if prices[i-1] != 0]
            
            if len(returns) < 20:
                return None
            
            historical_volatility = np.std(returns)
            current_volatility = data_point.get('volatility', 0.0)
            
            # Detect volatility spikes
            if current_volatility > historical_volatility * 3.0:
                return {
                    'is_anomaly': True,
                    'method': 'volatility_spike',
                    'score': 3.0 + (current_volatility / historical_volatility),
                    'description': 'Unusual volatility spike detected',
                    'current_volatility': current_volatility,
                    'historical_volatility': historical_volatility,
                    'volatility_ratio': current_volatility / historical_volatility
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error in volatility anomaly detection: {e}")
            return None
    
    async def _detect_correlation_anomalies(self, symbol: str, data_point: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Detect correlation anomalies (simplified)"""
        try:
            # This would typically compare with correlated assets
            # For now, just check bid-ask spread anomalies
            
            bid = data_point.get('bid', 0.0)
            ask = data_point.get('ask', 0.0)
            price = data_point.get('price', 0.0)
            
            if bid > 0 and ask > bid and price > 0:
                spread = ask - bid
                spread_pct = spread / price
                
                # Anomaly: Unusually wide spread
                if spread_pct > 0.05:  # 5% spread
                    return {
                        'is_anomaly': True,
                        'method': 'wide_spread',
                        'score': 2.0 + spread_pct * 20,
                        'description': 'Unusually wide bid-ask spread',
                        'spread_percentage': spread_pct,
                        'bid': bid,
                        'ask': ask,
                        'price': price
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"Error in correlation anomaly detection: {e}")
            return None
    
    def _determine_severity(self, anomaly_score: float) -> str:
        """Determine anomaly severity based on score"""
        if anomaly_score >= self.severity_thresholds['critical']:
            return 'critical'
        elif anomaly_score >= self.severity_thresholds['high']:
            return 'high'
        elif anomaly_score >= self.severity_thresholds['medium']:
            return 'medium'
        else:
            return 'low'
    
    async def _create_anomaly_alert(self, symbol: str, anomalies: Dict[str, Any], 
                                  score: float, severity: str, data_point: Dict[str, Any]) -> Optional[AnomalyAlert]:
        """Create anomaly alert"""
        try:
            alert_id = f"{symbol}_{int(time.time())}"
            
            # Determine primary anomaly type
            primary_anomaly = max(anomalies.items(), key=lambda x: x[1].get('score', 0.0))
            anomaly_type = primary_anomaly[0]
            
            # Generate description
            descriptions = []
            for method, result in anomalies.items():
                if 'description' in result:
                    descriptions.append(result['description'])
                else:
                    descriptions.append(f"{method} anomaly detected")
            
            description = "; ".join(descriptions)
            
            # Generate recommended actions
            recommended_actions = []
            if severity in ['high', 'critical']:
                recommended_actions.extend([
                    "Investigate market conditions",
                    "Consider position size adjustment",
                    "Monitor for additional anomalies"
                ])
            else:
                recommended_actions.extend([
                    "Monitor situation",
                    "Review trading strategy parameters"
                ])
            
            # Create alert
            alert = AnomalyAlert(
                alert_id=alert_id,
                symbol=symbol,
                anomaly_type=anomaly_type,
                severity=severity,
                anomaly_score=score,
                description=description,
                detected_at=datetime.now(),
                data_point=data_point,
                context={
                    'anomalies': anomalies,
                    'detection_methods': list(anomalies.keys()),
                    'historical_data_points': len(self.historical_data[symbol])
                },
                recommended_actions=recommended_actions
            )
            
            return alert
            
        except Exception as e:
            logger.error(f"Error creating anomaly alert: {e}")
            return None

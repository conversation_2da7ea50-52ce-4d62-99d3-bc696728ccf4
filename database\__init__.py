"""
Database Integration Package

This package provides comprehensive database integration capabilities for the
Advanced Ollama Trading Agent System. It includes:

- Multi-database support (PostgreSQL, Redis, ClickHouse)
- Data persistence and retrieval
- Caching and session management
- Analytics and time-series data storage
- Database schema management and migrations
- Connection pooling and optimization
- Data backup and recovery
"""

from .database_coordinator import DatabaseCoordinator
from .postgres_manager import PostgreSQLManager
from .redis_manager import RedisManager
from .clickhouse_manager import ClickHouseManager
from .data_models import *
from .migration_manager import MigrationManager
from .backup_manager import BackupManager

__all__ = [
    'DatabaseCoordinator',
    'PostgreSQLManager',
    'RedisManager',
    'ClickHouseManager',
    'MigrationManager',
    'BackupManager'
]

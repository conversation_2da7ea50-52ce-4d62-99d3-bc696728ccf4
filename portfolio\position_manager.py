"""
Position Manager - Manages individual positions and cash
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import json
import os

logger = logging.getLogger(__name__)


@dataclass
class Position:
    """Position data structure"""
    symbol: str
    quantity: float
    average_cost: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float
    cost_basis: float
    last_updated: float
    
    def update_price(self, new_price: float):
        """Update position with new market price"""
        self.current_price = new_price
        self.market_value = self.quantity * new_price
        self.unrealized_pnl = self.market_value - self.cost_basis
        self.last_updated = time.time()


class PositionManager:
    """
    Manages portfolio positions and cash balance.
    
    Responsibilities:
    - Track individual positions
    - Calculate position metrics
    - Manage cash balance
    - Handle position updates from trades
    - Persist position data
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.position_config = config.get('position_management', {})
        
        # Position tracking
        self.positions: Dict[str, Position] = {}
        self.cash_balance: float = 0.0
        self.initial_capital: float = 0.0
        
        # Transaction history
        self.transaction_history: List[Dict[str, Any]] = []
        
        # Persistence
        self.persistence_enabled = self.position_config.get('persistence_enabled', True)
        self.storage_path = self.position_config.get('storage_path', 'data/positions')
        
        # State
        self.initialized = False
        self.running = False
        
    async def initialize(self) -> bool:
        """Initialize position manager"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Position Manager...")
            
            # Create storage directory
            if self.persistence_enabled:
                os.makedirs(self.storage_path, exist_ok=True)
                await self._load_positions()
            
            self.initialized = True
            logger.info("✓ Position Manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Position Manager: {e}")
            return False
    
    async def start(self) -> bool:
        """Start position manager"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        self.running = True
        logger.info("✓ Position Manager started")
        return True
    
    async def stop(self) -> bool:
        """Stop position manager"""
        if self.persistence_enabled:
            await self._save_positions()
        
        self.running = False
        logger.info("✓ Position Manager stopped")
        return True
    
    async def set_initial_capital(self, capital: float) -> None:
        """Set initial capital"""
        self.initial_capital = capital
        if not self.positions:  # Only set if no existing positions
            self.cash_balance = capital
        logger.info(f"Initial capital set to ${capital:,.2f}")
    
    async def add_position(self, symbol: str, quantity: float, price: float) -> bool:
        """Add new position or update existing position"""
        try:
            cost = quantity * price
            
            # Check if we have enough cash
            if cost > self.cash_balance:
                logger.warning(f"Insufficient cash for {symbol}: need ${cost:.2f}, have ${self.cash_balance:.2f}")
                return False
            
            if symbol in self.positions:
                # Update existing position
                position = self.positions[symbol]
                
                # Calculate new average cost
                total_quantity = position.quantity + quantity
                total_cost = position.cost_basis + cost
                new_average_cost = total_cost / total_quantity if total_quantity != 0 else 0
                
                # Update position
                position.quantity = total_quantity
                position.average_cost = new_average_cost
                position.cost_basis = total_cost
                position.current_price = price
                position.market_value = total_quantity * price
                position.unrealized_pnl = position.market_value - position.cost_basis
                position.last_updated = time.time()
                
            else:
                # Create new position
                position = Position(
                    symbol=symbol,
                    quantity=quantity,
                    average_cost=price,
                    current_price=price,
                    market_value=cost,
                    unrealized_pnl=0.0,
                    realized_pnl=0.0,
                    cost_basis=cost,
                    last_updated=time.time()
                )
                self.positions[symbol] = position
            
            # Update cash balance
            self.cash_balance -= cost
            
            # Record transaction
            await self._record_transaction('buy', symbol, quantity, price, cost)
            
            # Persist changes
            if self.persistence_enabled:
                await self._save_position(symbol)
            
            logger.debug(f"Added position: {symbol} {quantity} @ ${price:.2f}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding position {symbol}: {e}")
            return False
    
    async def update_position(self, symbol: str, quantity_change: float, price: float) -> bool:
        """Update existing position with quantity change"""
        try:
            if symbol not in self.positions:
                logger.warning(f"Position {symbol} not found for update")
                return False
            
            position = self.positions[symbol]
            
            if quantity_change > 0:
                # Adding to position
                return await self.add_position(symbol, quantity_change, price)
            else:
                # Reducing position
                quantity_to_sell = abs(quantity_change)
                
                if quantity_to_sell > position.quantity:
                    logger.warning(f"Cannot sell {quantity_to_sell} of {symbol}, only have {position.quantity}")
                    return False
                
                # Calculate proceeds and realized P&L
                proceeds = quantity_to_sell * price
                cost_basis_sold = (quantity_to_sell / position.quantity) * position.cost_basis
                realized_pnl = proceeds - cost_basis_sold
                
                # Update position
                position.quantity -= quantity_to_sell
                position.cost_basis -= cost_basis_sold
                position.realized_pnl += realized_pnl
                position.current_price = price
                
                if position.quantity > 0:
                    position.market_value = position.quantity * price
                    position.unrealized_pnl = position.market_value - position.cost_basis
                else:
                    # Position closed
                    position.market_value = 0.0
                    position.unrealized_pnl = 0.0
                
                position.last_updated = time.time()
                
                # Update cash balance
                self.cash_balance += proceeds
                
                # Record transaction
                await self._record_transaction('sell', symbol, quantity_to_sell, price, proceeds, realized_pnl)
                
                # Remove position if quantity is zero
                if position.quantity <= 0:
                    del self.positions[symbol]
                    logger.debug(f"Position {symbol} closed")
                
                # Persist changes
                if self.persistence_enabled:
                    if symbol in self.positions:
                        await self._save_position(symbol)
                    else:
                        await self._remove_position_file(symbol)
                
                return True
                
        except Exception as e:
            logger.error(f"Error updating position {symbol}: {e}")
            return False
    
    async def close_position(self, symbol: str, price: float) -> bool:
        """Close position completely"""
        try:
            if symbol not in self.positions:
                logger.warning(f"Position {symbol} not found for closing")
                return False
            
            position = self.positions[symbol]
            return await self.update_position(symbol, -position.quantity, price)
            
        except Exception as e:
            logger.error(f"Error closing position {symbol}: {e}")
            return False
    
    async def update_market_prices(self, prices: Dict[str, float]) -> None:
        """Update market prices for all positions"""
        try:
            for symbol, price in prices.items():
                if symbol in self.positions:
                    self.positions[symbol].update_price(price)
            
            logger.debug(f"Updated prices for {len(prices)} symbols")
            
        except Exception as e:
            logger.error(f"Error updating market prices: {e}")
    
    async def get_position(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get position details"""
        try:
            if symbol in self.positions:
                position = self.positions[symbol]
                return {
                    'symbol': position.symbol,
                    'quantity': position.quantity,
                    'average_cost': position.average_cost,
                    'current_price': position.current_price,
                    'market_value': position.market_value,
                    'unrealized_pnl': position.unrealized_pnl,
                    'realized_pnl': position.realized_pnl,
                    'cost_basis': position.cost_basis,
                    'last_updated': position.last_updated,
                    'weight': 0.0  # Will be calculated by portfolio manager
                }
            return None
            
        except Exception as e:
            logger.error(f"Error getting position {symbol}: {e}")
            return None
    
    async def get_all_positions(self) -> Dict[str, Dict[str, Any]]:
        """Get all positions"""
        try:
            positions_dict = {}
            for symbol, position in self.positions.items():
                positions_dict[symbol] = {
                    'symbol': position.symbol,
                    'quantity': position.quantity,
                    'average_cost': position.average_cost,
                    'current_price': position.current_price,
                    'market_value': position.market_value,
                    'unrealized_pnl': position.unrealized_pnl,
                    'realized_pnl': position.realized_pnl,
                    'cost_basis': position.cost_basis,
                    'last_updated': position.last_updated
                }
            
            return positions_dict
            
        except Exception as e:
            logger.error(f"Error getting all positions: {e}")
            return {}
    
    async def get_cash_balance(self) -> float:
        """Get current cash balance"""
        return self.cash_balance
    
    async def get_total_value(self) -> float:
        """Get total portfolio value"""
        try:
            total_market_value = sum(pos.market_value for pos in self.positions.values())
            return self.cash_balance + total_market_value
        except Exception as e:
            logger.error(f"Error calculating total value: {e}")
            return 0.0
    
    async def get_total_pnl(self) -> Dict[str, float]:
        """Get total P&L breakdown"""
        try:
            total_unrealized = sum(pos.unrealized_pnl for pos in self.positions.values())
            total_realized = sum(pos.realized_pnl for pos in self.positions.values())
            
            return {
                'unrealized_pnl': total_unrealized,
                'realized_pnl': total_realized,
                'total_pnl': total_unrealized + total_realized
            }
            
        except Exception as e:
            logger.error(f"Error calculating total P&L: {e}")
            return {'unrealized_pnl': 0.0, 'realized_pnl': 0.0, 'total_pnl': 0.0}
    
    async def get_transaction_history(self, symbol: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get transaction history"""
        try:
            if symbol:
                return [tx for tx in self.transaction_history if tx['symbol'] == symbol]
            return self.transaction_history.copy()
        except Exception as e:
            logger.error(f"Error getting transaction history: {e}")
            return []
    
    # Private methods
    
    async def _record_transaction(self, action: str, symbol: str, quantity: float, 
                                price: float, value: float, realized_pnl: float = 0.0):
        """Record transaction in history"""
        try:
            transaction = {
                'timestamp': time.time(),
                'action': action,
                'symbol': symbol,
                'quantity': quantity,
                'price': price,
                'value': value,
                'realized_pnl': realized_pnl,
                'cash_balance_after': self.cash_balance
            }
            
            self.transaction_history.append(transaction)
            
            # Limit history size
            if len(self.transaction_history) > 10000:
                self.transaction_history = self.transaction_history[-10000:]
                
        except Exception as e:
            logger.error(f"Error recording transaction: {e}")
    
    async def _load_positions(self):
        """Load positions from persistence"""
        try:
            # Load cash balance
            cash_file = os.path.join(self.storage_path, 'cash_balance.json')
            if os.path.exists(cash_file):
                with open(cash_file, 'r') as f:
                    cash_data = json.load(f)
                    self.cash_balance = cash_data.get('balance', 0.0)
                    self.initial_capital = cash_data.get('initial_capital', 0.0)
            
            # Load positions
            positions_file = os.path.join(self.storage_path, 'positions.json')
            if os.path.exists(positions_file):
                with open(positions_file, 'r') as f:
                    positions_data = json.load(f)
                
                for symbol, pos_data in positions_data.items():
                    position = Position(
                        symbol=pos_data['symbol'],
                        quantity=pos_data['quantity'],
                        average_cost=pos_data['average_cost'],
                        current_price=pos_data['current_price'],
                        market_value=pos_data['market_value'],
                        unrealized_pnl=pos_data['unrealized_pnl'],
                        realized_pnl=pos_data['realized_pnl'],
                        cost_basis=pos_data['cost_basis'],
                        last_updated=pos_data['last_updated']
                    )
                    self.positions[symbol] = position
                
                logger.info(f"Loaded {len(self.positions)} positions from persistence")
            
            # Load transaction history
            history_file = os.path.join(self.storage_path, 'transactions.json')
            if os.path.exists(history_file):
                with open(history_file, 'r') as f:
                    self.transaction_history = json.load(f)
                
        except Exception as e:
            logger.error(f"Error loading positions: {e}")
    
    async def _save_positions(self):
        """Save all positions to persistence"""
        try:
            # Save cash balance
            cash_file = os.path.join(self.storage_path, 'cash_balance.json')
            cash_data = {
                'balance': self.cash_balance,
                'initial_capital': self.initial_capital,
                'last_updated': time.time()
            }
            with open(cash_file, 'w') as f:
                json.dump(cash_data, f, indent=2)
            
            # Save positions
            positions_file = os.path.join(self.storage_path, 'positions.json')
            positions_data = {}
            for symbol, position in self.positions.items():
                positions_data[symbol] = {
                    'symbol': position.symbol,
                    'quantity': position.quantity,
                    'average_cost': position.average_cost,
                    'current_price': position.current_price,
                    'market_value': position.market_value,
                    'unrealized_pnl': position.unrealized_pnl,
                    'realized_pnl': position.realized_pnl,
                    'cost_basis': position.cost_basis,
                    'last_updated': position.last_updated
                }
            
            with open(positions_file, 'w') as f:
                json.dump(positions_data, f, indent=2)
            
            # Save transaction history
            history_file = os.path.join(self.storage_path, 'transactions.json')
            with open(history_file, 'w') as f:
                json.dump(self.transaction_history, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving positions: {e}")
    
    async def _save_position(self, symbol: str):
        """Save individual position"""
        try:
            # For now, save all positions (could optimize to save individual files)
            await self._save_positions()
        except Exception as e:
            logger.error(f"Error saving position {symbol}: {e}")
    
    async def _remove_position_file(self, symbol: str):
        """Remove position file when position is closed"""
        try:
            # Since we save all positions in one file, just save the updated state
            await self._save_positions()
        except Exception as e:
            logger.error(f"Error removing position file for {symbol}: {e}")

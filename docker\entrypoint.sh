#!/bin/bash
set -e

# Advanced Ollama Trading Agent System - Docker Entrypoint

echo "🚀 Starting Advanced Ollama Trading Agent System..."

# Function to wait for service
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local max_attempts=30
    local attempt=1

    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z "$host" "$port" 2>/dev/null; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        
        echo "🔄 Attempt $attempt/$max_attempts: $service_name not ready yet..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ Failed to connect to $service_name after $max_attempts attempts"
    return 1
}

# Function to check database connection
check_database() {
    echo "🔍 Checking database connection..."
    
    if command -v pg_isready >/dev/null 2>&1; then
        if pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME"; then
            echo "✅ Database connection successful!"
            return 0
        else
            echo "❌ Database connection failed!"
            return 1
        fi
    else
        echo "⚠️ pg_isready not available, skipping database check"
        return 0
    fi
}

# Function to check Redis connection
check_redis() {
    echo "🔍 Checking Redis connection..."
    
    if command -v redis-cli >/dev/null 2>&1; then
        if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping | grep -q PONG; then
            echo "✅ Redis connection successful!"
            return 0
        else
            echo "❌ Redis connection failed!"
            return 1
        fi
    else
        echo "⚠️ redis-cli not available, skipping Redis check"
        return 0
    fi
}

# Function to check Ollama connection
check_ollama() {
    echo "🔍 Checking Ollama connection..."
    
    if command -v curl >/dev/null 2>&1; then
        if curl -s "http://$OLLAMA_HOST:$OLLAMA_PORT/api/tags" >/dev/null; then
            echo "✅ Ollama connection successful!"
            return 0
        else
            echo "❌ Ollama connection failed!"
            return 1
        fi
    else
        echo "⚠️ curl not available, skipping Ollama check"
        return 0
    fi
}

# Function to initialize database
init_database() {
    echo "🗄️ Initializing database..."
    
    if [ -f "/app/scripts/init_system.py" ]; then
        python /app/scripts/init_system.py --database-only
        echo "✅ Database initialization completed!"
    else
        echo "⚠️ Database initialization script not found, skipping..."
    fi
}

# Function to download Ollama models
download_models() {
    echo "🤖 Checking Ollama models..."
    
    # List of required models
    REQUIRED_MODELS=(
        "llama2"
        "codellama"
        "mistral"
    )
    
    for model in "${REQUIRED_MODELS[@]}"; do
        echo "📥 Checking model: $model"
        
        # Check if model exists
        if curl -s "http://$OLLAMA_HOST:$OLLAMA_PORT/api/tags" | grep -q "\"name\":\"$model\""; then
            echo "✅ Model $model is available"
        else
            echo "📥 Downloading model: $model"
            if curl -X POST "http://$OLLAMA_HOST:$OLLAMA_PORT/api/pull" \
                -H "Content-Type: application/json" \
                -d "{\"name\":\"$model\"}" >/dev/null 2>&1; then
                echo "✅ Model $model downloaded successfully"
            else
                echo "⚠️ Failed to download model: $model"
            fi
        fi
    done
}

# Function to setup logging
setup_logging() {
    echo "📝 Setting up logging..."
    
    # Create log directories
    mkdir -p /app/logs/agents
    mkdir -p /app/logs/strategies
    mkdir -p /app/logs/risk
    mkdir -p /app/logs/execution
    mkdir -p /app/logs/portfolio
    mkdir -p /app/logs/api
    mkdir -p /app/logs/system
    
    # Set permissions
    chmod 755 /app/logs
    chmod 755 /app/logs/*
    
    echo "✅ Logging setup completed!"
}

# Function to validate configuration
validate_config() {
    echo "⚙️ Validating configuration..."
    
    # Check required environment variables
    REQUIRED_VARS=(
        "DB_HOST"
        "DB_PORT"
        "DB_NAME"
        "DB_USER"
        "DB_PASSWORD"
        "REDIS_HOST"
        "REDIS_PORT"
        "OLLAMA_HOST"
        "OLLAMA_PORT"
    )
    
    for var in "${REQUIRED_VARS[@]}"; do
        if [ -z "${!var}" ]; then
            echo "❌ Required environment variable $var is not set!"
            exit 1
        fi
    done
    
    echo "✅ Configuration validation completed!"
}

# Function to run health checks
run_health_checks() {
    echo "🏥 Running health checks..."
    
    # Wait for dependencies
    wait_for_service "$DB_HOST" "$DB_PORT" "PostgreSQL"
    wait_for_service "$REDIS_HOST" "$REDIS_PORT" "Redis"
    wait_for_service "$OLLAMA_HOST" "$OLLAMA_PORT" "Ollama"
    
    # Check connections
    check_database
    check_redis
    check_ollama
    
    echo "✅ All health checks passed!"
}

# Function to handle graceful shutdown
cleanup() {
    echo "🛑 Received shutdown signal, cleaning up..."
    
    # Kill background processes
    if [ ! -z "$MAIN_PID" ]; then
        echo "🔄 Stopping main process (PID: $MAIN_PID)..."
        kill -TERM "$MAIN_PID" 2>/dev/null || true
        wait "$MAIN_PID" 2>/dev/null || true
    fi
    
    echo "✅ Cleanup completed!"
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Main execution
main() {
    echo "🎯 Starting initialization sequence..."
    
    # Validate configuration
    validate_config
    
    # Setup logging
    setup_logging
    
    # Run health checks
    run_health_checks
    
    # Initialize database if needed
    if [ "$INIT_DATABASE" = "true" ]; then
        init_database
    fi
    
    # Download Ollama models if needed
    if [ "$DOWNLOAD_MODELS" = "true" ]; then
        download_models
    fi
    
    echo "🚀 Initialization completed! Starting application..."
    
    # Execute the main command
    exec "$@" &
    MAIN_PID=$!
    
    # Wait for the main process
    wait $MAIN_PID
}

# Check if we're running the main application
if [ "$1" = "python" ] && [ "$2" = "main.py" ]; then
    main "$@"
else
    # For other commands, just execute them directly
    exec "$@"
fi

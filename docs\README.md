# 🤖 Advanced Ollama Trading Agent System

## Overview
The Advanced Ollama Trading Agent System is a sophisticated multi-agent trading platform that leverages large language models for intelligent trading decisions. The system features specialized agents working in coordinated teams to analyze markets, develop strategies, manage risk, and execute trades.

## 🏗️ Architecture

### Core Components
- **Ollama Model Hub**: Manages 20+ LLM models with dynamic deployment
- **Specialized Agents**: 6 types of trading agents with unique capabilities
- **Team Coordination**: Advanced team formation and coordination system
- **Communication Layer**: Multi-protocol message broker with reliable delivery
- **Data Infrastructure**: PostgreSQL, Redis, and ClickHouse integration
- **Analytics Engine**: Real-time market analysis and pattern recognition
- **Risk Management**: Comprehensive portfolio risk monitoring
- **Performance Tracking**: Continuous performance evaluation and optimization

### Agent Types
1. **Team Leader Agent** (`exaone-deep:32b`)
   - Strategic coordination and team management
   - Resource allocation and optimization
   - High-level decision making

2. **Market Analyst Agent** (`magistral-abliterated:24b`)
   - Technical analysis and pattern recognition
   - Market sentiment analysis
   - Trend identification

3. **Strategy Developer Agent** (`am-thinking-abliterate:latest`)
   - Creative strategy development
   - Backtesting and optimization
   - Adaptive strategy modification

4. **Risk Manager Agent** (`phi4-reasoning:plus`)
   - Portfolio risk assessment
   - Position sizing optimization
   - Compliance monitoring

5. **Execution Specialist Agent** (`nemotron-mini:4b`)
   - Order execution optimization
   - Slippage minimization
   - Market impact analysis

6. **Performance Evaluator Agent** (`granite3.3:8b`)
   - Performance attribution
   - Benchmarking and optimization
   - Learning feedback

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Ollama with required models
- PostgreSQL, Redis, ClickHouse (optional)

### Installation
```bash
# Clone repository
git clone <repository-url>
cd trading-agents

# Install dependencies
pip install -r requirements.txt

# Initialize system
python scripts/init_system.py

# Start system
python main.py
```

### Configuration
Edit `config/system_config.yaml` and `config/model_configs.yaml` to customize:
- Model assignments
- Database connections
- Trading parameters
- Risk limits

## 📊 System Capabilities

### Intelligent Model Management
- **Dynamic Discovery**: Automatically discovers available Ollama models
- **Optimal Assignment**: Maps models to agent roles based on capabilities
- **Performance Monitoring**: Tracks model performance and switches when needed
- **Failover Support**: Automatic fallback to backup models

### Advanced Agent Intelligence
- **Sophisticated Memory**: Working, short-term, and long-term memory systems
- **Multi-Strategy Reasoning**: Analytical, decision-making, and creative reasoning
- **Continuous Learning**: Adapts based on experience and feedback
- **Inter-Agent Communication**: Coordinated decision making

### Team Coordination
- **Dynamic Team Formation**: Creates teams based on market conditions
- **Multiple Coordination Styles**: Ultra-fast, fast execution, careful analysis
- **Mission Management**: Coordinates complex trading missions
- **Performance Optimization**: Continuously optimizes team composition

### Risk Management
- **Real-Time Monitoring**: Continuous portfolio risk assessment
- **Advanced Metrics**: VaR, stress testing, correlation analysis
- **Compliance Checking**: Automated regulatory compliance
- **Alert System**: Immediate notifications for risk violations

### Market Analysis
- **Technical Analysis**: Comprehensive technical indicator analysis
- **Pattern Recognition**: Advanced chart pattern detection
- **Sentiment Analysis**: Market sentiment from multiple sources
- **Regime Detection**: Identifies changing market conditions

## 🔧 Configuration Guide

### Model Configuration
```yaml
# config/model_configs.yaml
models:
  team_leader:
    primary: "exaone-deep:32b"
    fallback: "command-r:35b"
    parameters:
      temperature: 0.3
      max_tokens: 4000
      
  market_analyst:
    primary: "magistral-abliterated:24b"
    fallback: "qwen2.5vl:32b"
    parameters:
      temperature: 0.2
      max_tokens: 3000
```

### System Configuration
```yaml
# config/system_config.yaml
agents:
  max_agents: 50
  heartbeat_interval: 30
  
communication:
  max_queue_size: 10000
  message_ttl: 300
  
risk_management:
  max_portfolio_var: 0.02
  max_position_size: 0.05
  
trading:
  paper_trading: true
  max_leverage: 3.0
```

## 🎯 Usage Examples

### Creating and Managing Teams
```python
# Create a momentum trading team
team_id = await team_manager.create_team(
    team_type=TeamType.MOMENTUM_TEAM,
    mission={
        'strategy': 'momentum_breakout',
        'symbols': ['AAPL', 'GOOGL', 'TSLA'],
        'time_horizon': 'short_term'
    }
)

# Coordinate team mission
result = await team_manager.coordinate_team_mission(
    team_id=team_id,
    mission_data={
        'action': 'analyze_breakout_opportunities',
        'urgency': 'high'
    }
)
```

### Agent Task Submission
```python
# Submit technical analysis task
analysis_task = {
    'type': 'technical_analysis',
    'symbol': 'AAPL',
    'timeframe': '1h',
    'indicators': ['sma', 'rsi', 'macd']
}

await market_analyst.submit_task(analysis_task)
```

### Risk Assessment
```python
# Assess portfolio risk
risk_task = {
    'type': 'assess_portfolio_risk',
    'portfolio': portfolio_data,
    'market_data': current_market_data
}

result = await risk_manager.submit_task(risk_task)
```

## 📈 Monitoring and Analytics

### Real-Time Dashboard
Access the web dashboard at `http://localhost:8080` to monitor:
- System health and performance
- Agent activity and metrics
- Team coordination status
- Risk metrics and alerts
- Market analysis results

### Performance Metrics
- **Agent Performance**: Task completion rates, response times
- **Team Efficiency**: Coordination success, mission completion
- **Model Performance**: Inference times, accuracy metrics
- **System Health**: CPU, memory, network utilization

### Alerting System
- **Risk Alerts**: Portfolio risk violations
- **System Alerts**: Performance degradation
- **Market Alerts**: Unusual market conditions
- **Agent Alerts**: Agent failures or errors

## 🔒 Security and Compliance

### Security Features
- **Encrypted Communication**: All inter-agent communication encrypted
- **Access Control**: Role-based access to system components
- **Audit Logging**: Comprehensive audit trail
- **Secure Configuration**: Environment-based secrets management

### Compliance
- **Risk Limits**: Configurable risk limits and monitoring
- **Trade Logging**: Complete trade audit trail
- **Regulatory Reporting**: Automated compliance reporting
- **Data Retention**: Configurable data retention policies

## 🛠️ Troubleshooting

### Common Issues

#### Ollama Connection Issues
```bash
# Check Ollama status
curl http://localhost:11434/api/tags

# Restart Ollama
sudo systemctl restart ollama
```

#### Agent Communication Issues
```bash
# Check message broker status
python scripts/check_communication.py

# View communication logs
tail -f logs/communication.log
```

#### Performance Issues
```bash
# Check system resources
python scripts/system_health.py

# View performance metrics
python scripts/performance_report.py
```

### Log Analysis
```bash
# View system logs
tail -f logs/system.log

# Search for errors
grep -i error logs/*.log

# Agent-specific logs
tail -f logs/agents/team_leader.log
```

## 🔄 Maintenance

### Regular Maintenance
- **Daily**: Check system health, review alerts
- **Weekly**: Update models, review performance
- **Monthly**: Security updates, backup verification

### Backup and Recovery
```bash
# Create backup
python scripts/backup_system.py

# Restore from backup
python scripts/restore_system.py --backup-file backup.tar.gz
```

### Updates
```bash
# Update system
git pull origin main
pip install -r requirements.txt
python scripts/migrate_database.py
```

## 📚 API Reference

### Agent Manager API
```python
# Create agent
agent_id = await agent_manager.create_agent(
    role=AgentRole.MARKET_ANALYST,
    name="MarketAnalyst_1"
)

# Get agent status
status = await agent_manager.get_agent_status(agent_id)

# Get system statistics
stats = await agent_manager.get_system_stats()
```

### Team Manager API
```python
# Create team
team_id = await team_manager.create_team(
    team_type=TeamType.MOMENTUM_TEAM,
    mission=mission_data
)

# Get active teams
teams = await team_manager.get_active_teams()
```

### Communication API
```python
# Send message
await communication.send_message(recipient, message)

# Request analysis
result = await communication.request_analysis(
    recipient="market_analyst",
    data=market_data
)
```

## 🤝 Contributing

### Development Setup
```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run tests
python -m pytest tests/

# Code formatting
black .
isort .
```

### Adding New Agents
1. Create agent class inheriting from `BaseAgent`
2. Implement required methods
3. Add to agent manager
4. Update configuration
5. Add tests

### Adding New Features
1. Create feature branch
2. Implement feature with tests
3. Update documentation
4. Submit pull request

## 📄 License
This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support
- **Documentation**: Check the `docs/` directory
- **Issues**: Report issues on GitHub
- **Discussions**: Join community discussions
- **Email**: <EMAIL>

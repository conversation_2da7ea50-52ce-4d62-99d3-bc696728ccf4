#!/usr/bin/env python3
"""
Simple test of individual AI agents with real models
"""

import asyncio
import json
from datetime import datetime
from config.config_manager import ConfigManager
from models.ollama_hub import OllamaModelHub
from agents.agent_manager import AgentManager
from agents.base_agent import AgentRole

class SimpleMessageBroker:
    async def publish(self, topic, message):
        print(f"📢 {topic}: {str(message)[:100]}...")
    async def subscribe(self, topic, callback):
        pass

async def test_individual_agents():
    """Test individual AI agents with real models"""
    
    print("🧠 INDIVIDUAL AI AGENT TESTS")
    print("=" * 40)
    
    try:
        # Setup
        config_manager = ConfigManager('config/test_config.yaml')
        await config_manager.load_config()
        config = await config_manager.get_config()
        
        ollama_hub = OllamaModelHub(config=config)
        await ollama_hub.initialize()
        
        agent_manager = AgentManager(ollama_hub, SimpleMessageBroker(), config)
        await agent_manager.initialize()
        
        print("✅ System initialized")
        
        # Test 1: Market Analyst
        print("\n📊 TEST 1: Market Analyst AI")
        analyst_id = await agent_manager.create_agent(
            role=AgentRole.MARKET_ANALYST,
            name="AI_Market_Analyst"
        )
        analyst = await agent_manager.get_agent(analyst_id)
        print(f"✅ Created: {analyst.model_instance.model_name}")
        
        # Test technical analysis
        tech_analysis_task = {
            "type": "technical_analysis",
            "symbol": "AAPL",
            "timeframe": "1h",
            "indicators": ["rsi", "macd", "bollinger_bands"]
        }
        
        print("🔍 Running technical analysis...")
        result1 = await analyst.submit_task(tech_analysis_task)
        print(f"Result: {type(result1)} - {str(result1)[:100]}...")
        
        # Test 2: Strategy Developer
        print("\n🎯 TEST 2: Strategy Developer AI")
        strategist_id = await agent_manager.create_agent(
            role=AgentRole.STRATEGY_DEVELOPER,
            name="AI_Strategy_Developer"
        )
        strategist = await agent_manager.get_agent(strategist_id)
        print(f"✅ Created: {strategist.model_instance.model_name}")
        
        # Test strategy development
        strategy_task = {
            "type": "strategy_development",
            "market_conditions": "bullish",
            "risk_tolerance": "moderate",
            "capital": 100000,
            "timeframe": "daily"
        }
        
        print("🎯 Developing strategy...")
        result2 = await strategist.submit_task(strategy_task)
        print(f"Result: {type(result2)} - {str(result2)[:100]}...")
        
        # Test 3: Risk Manager
        print("\n⚠️ TEST 3: Risk Manager AI")
        risk_mgr_id = await agent_manager.create_agent(
            role=AgentRole.RISK_MANAGER,
            name="AI_Risk_Manager"
        )
        risk_manager = await agent_manager.get_agent(risk_mgr_id)
        print(f"✅ Created: {risk_manager.model_instance.model_name}")
        
        # Test risk assessment
        risk_task = {
            "type": "risk_assessment",
            "portfolio_value": 100000,
            "positions": {"AAPL": 30000, "TSLA": 20000},
            "market_volatility": "high"
        }
        
        print("⚠️ Assessing risk...")
        result3 = await risk_manager.submit_task(risk_task)
        print(f"Result: {type(result3)} - {str(result3)[:100]}...")
        
        # Test 4: Direct Model Calls
        print("\n🧠 TEST 4: Direct AI Model Calls")
        
        # Test direct model generation
        print("📊 Direct Market Analysis...")
        market_prompt = """
        Analyze this market scenario:
        - AAPL stock at $175.50, up 2.3% today
        - Volume: 45M shares (above average)
        - RSI: 68 (approaching overbought)
        - MACD: Bullish crossover yesterday
        - Market sentiment: Positive on earnings beat
        
        Provide a brief technical analysis and trading recommendation.
        """
        
        direct_result = await analyst.model_instance.generate(market_prompt)
        if direct_result.get('success'):
            print("✅ Direct AI Response:")
            print("-" * 40)
            print(direct_result['response'][:500] + "...")
            print("-" * 40)
        else:
            print(f"❌ Direct call failed: {direct_result}")
        
        # Summary
        print("\n🎉 INDIVIDUAL AGENT TESTS COMPLETE!")
        print("=" * 40)
        print(f"📊 Market Analyst: {analyst.model_instance.model_name}")
        print(f"🎯 Strategy Developer: {strategist.model_instance.model_name}")
        print(f"⚠️ Risk Manager: {risk_manager.model_instance.model_name}")
        
        # Save results
        results = {
            "timestamp": datetime.now().isoformat(),
            "agents_tested": {
                "market_analyst": {
                    "id": analyst_id,
                    "model": analyst.model_instance.model_name,
                    "task_result": result1
                },
                "strategy_developer": {
                    "id": strategist_id,
                    "model": strategist.model_instance.model_name,
                    "task_result": result2
                },
                "risk_manager": {
                    "id": risk_mgr_id,
                    "model": risk_manager.model_instance.model_name,
                    "task_result": result3
                }
            },
            "direct_model_test": {
                "success": direct_result.get('success', False),
                "response_length": len(direct_result.get('response', ''))
            }
        }
        
        with open('individual_agent_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📄 Results saved to: individual_agent_results.json")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_individual_agents())
    if success:
        print("\n🎉 ALL AGENT TESTS SUCCESSFUL!")
    else:
        print("\n❌ AGENT TESTS FAILED!")

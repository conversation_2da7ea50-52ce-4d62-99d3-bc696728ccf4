"""
Portfolio Manager - Central portfolio management coordinator
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import numpy as np
import pandas as pd

from .portfolio_optimizer import PortfolioOptimizer
from .position_manager import PositionManager
from .performance_tracker import PerformanceTracker
from .rebalancer import PortfolioRebalancer
from .allocation_engine import AllocationEngine

logger = logging.getLogger(__name__)


@dataclass
class PortfolioState:
    """Portfolio state snapshot"""
    timestamp: float
    total_value: float
    cash: float
    positions: Dict[str, Dict[str, Any]]
    weights: Dict[str, float]
    performance_metrics: Dict[str, float]
    risk_metrics: Dict[str, float]


@dataclass
class RebalanceRecommendation:
    """Portfolio rebalance recommendation"""
    symbol: str
    current_weight: float
    target_weight: float
    weight_diff: float
    current_value: float
    target_value: float
    trade_amount: float
    trade_side: str  # 'buy' or 'sell'
    priority: int  # 1=high, 2=medium, 3=low


class PortfolioManager:
    """
    Central portfolio management system that coordinates all portfolio activities.
    
    Responsibilities:
    - Portfolio construction and optimization
    - Position management and tracking
    - Performance monitoring and attribution
    - Rebalancing and allocation management
    - Risk monitoring and compliance
    - Integration with execution and risk systems
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.portfolio_config = config.get('portfolio', {})
        
        # Core components
        self.optimizer: Optional[PortfolioOptimizer] = None
        self.position_manager: Optional[PositionManager] = None
        self.performance_tracker: Optional[PerformanceTracker] = None
        self.rebalancer: Optional[PortfolioRebalancer] = None
        self.allocation_engine: Optional[AllocationEngine] = None
        
        # Portfolio state
        self.current_portfolio: Optional[PortfolioState] = None
        self.target_weights: Dict[str, float] = {}
        self.portfolio_history: List[PortfolioState] = []
        
        # Configuration
        self.initial_capital = self.portfolio_config.get('initial_capital', 100000.0)
        self.rebalance_threshold = self.portfolio_config.get('rebalance_threshold', 0.05)  # 5%
        self.rebalance_frequency = self.portfolio_config.get('rebalance_frequency', 'daily')
        self.max_positions = self.portfolio_config.get('max_positions', 20)
        
        # Integration points
        self.risk_manager = None
        self.execution_engine = None
        self.strategy_manager = None
        
        # State flags
        self.initialized = False
        self.running = False
        self.portfolio_id = None  # Current active portfolio ID
        
        # Background tasks
        self.monitoring_task: Optional[asyncio.Task] = None
        self.rebalancing_task: Optional[asyncio.Task] = None
        
    async def initialize(self) -> bool:
        """Initialize the portfolio management system"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Portfolio Manager...")
            
            # Initialize core components
            self.optimizer = PortfolioOptimizer(self.config)
            await self.optimizer.initialize()
            
            self.position_manager = PositionManager(self.config)
            await self.position_manager.initialize()
            
            self.performance_tracker = PerformanceTracker(self.config)
            await self.performance_tracker.initialize()
            
            self.rebalancer = PortfolioRebalancer(self.config)
            await self.rebalancer.initialize()
            
            self.allocation_engine = AllocationEngine(self.config)
            await self.allocation_engine.initialize()
            
            # Initialize portfolio state
            await self._initialize_portfolio_state()
            
            self.initialized = True
            logger.info("✓ Portfolio Manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Portfolio Manager: {e}")
            return False
    
    async def start(self) -> bool:
        """Start the portfolio management system"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        if self.running:
            return True
            
        try:
            logger.info("Starting Portfolio Manager...")
            
            # Start all components
            await asyncio.gather(
                self.position_manager.start(),
                self.performance_tracker.start(),
                self.rebalancer.start(),
                self.allocation_engine.start()
            )
            
            # Start background tasks
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            self.rebalancing_task = asyncio.create_task(self._rebalancing_loop())
            
            self.running = True
            logger.info("✓ Portfolio Manager started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Portfolio Manager: {e}")
            return False
    
    async def stop(self) -> bool:
        """Stop the portfolio management system"""
        if not self.running:
            return True
            
        try:
            logger.info("Stopping Portfolio Manager...")
            self.running = False
            
            # Cancel background tasks
            if self.monitoring_task:
                self.monitoring_task.cancel()
            if self.rebalancing_task:
                self.rebalancing_task.cancel()
            
            # Stop all components
            await asyncio.gather(
                self.position_manager.stop(),
                self.performance_tracker.stop(),
                self.rebalancer.stop(),
                self.allocation_engine.stop(),
                return_exceptions=True
            )
            
            logger.info("✓ Portfolio Manager stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping Portfolio Manager: {e}")
            return False
    
    async def set_integration_points(self, 
                                   risk_manager=None, 
                                   execution_engine=None, 
                                   strategy_manager=None):
        """Set integration points with other systems"""
        self.risk_manager = risk_manager
        self.execution_engine = execution_engine
        self.strategy_manager = strategy_manager
        
        logger.info("Portfolio Manager integration points configured")

    async def set_database_coordinator(self, database_coordinator):
        """Set database coordinator for data persistence"""
        self.database_coordinator = database_coordinator

        # Pass database coordinator to portfolio components
        if self.position_manager:
            if hasattr(self.position_manager, 'set_database_coordinator'):
                await self.position_manager.set_database_coordinator(database_coordinator)

        if self.performance_tracker:
            if hasattr(self.performance_tracker, 'set_database_coordinator'):
                await self.performance_tracker.set_database_coordinator(database_coordinator)

        if self.optimizer:
            if hasattr(self.optimizer, 'set_database_coordinator'):
                await self.optimizer.set_database_coordinator(database_coordinator)

        if self.rebalancer:
            if hasattr(self.rebalancer, 'set_database_coordinator'):
                await self.rebalancer.set_database_coordinator(database_coordinator)

        if self.allocation_engine:
            if hasattr(self.allocation_engine, 'set_database_coordinator'):
                await self.allocation_engine.set_database_coordinator(database_coordinator)

        logger.info("Portfolio Manager database coordinator configured")

    async def set_analytics_engine(self, analytics_engine):
        """Set analytics engine for advanced analytics"""
        self.analytics_engine = analytics_engine

        # Pass analytics engine to portfolio components
        if self.position_manager:
            if hasattr(self.position_manager, 'set_analytics_engine'):
                await self.position_manager.set_analytics_engine(analytics_engine)

        if self.performance_tracker:
            if hasattr(self.performance_tracker, 'set_analytics_engine'):
                await self.performance_tracker.set_analytics_engine(analytics_engine)

        if self.optimizer:
            if hasattr(self.optimizer, 'set_analytics_engine'):
                await self.optimizer.set_analytics_engine(analytics_engine)

        if self.rebalancer:
            if hasattr(self.rebalancer, 'set_analytics_engine'):
                await self.rebalancer.set_analytics_engine(analytics_engine)

        if self.allocation_engine:
            if hasattr(self.allocation_engine, 'set_analytics_engine'):
                await self.allocation_engine.set_analytics_engine(analytics_engine)

        logger.info("Portfolio Manager analytics engine configured")

    async def optimize_portfolio(self,
                               method: str = "black_litterman",
                               constraints: Optional[Dict[str, Any]] = None,
                               objectives: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
        """Optimize portfolio allocation"""
        try:
            # Get optimization result
            result = await self.optimizer.optimize_portfolio(method, constraints, objectives)
            
            if result['success']:
                # Update target weights
                self.target_weights = result['weights']
                
                # Calculate rebalance recommendations
                rebalance_recs = await self._calculate_rebalance_recommendations()
                
                # Update allocation engine
                await self.allocation_engine.update_target_allocation(self.target_weights)
                
                logger.info(f"✓ Portfolio optimized using {method}")
                
                return {
                    'success': True,
                    'method': method,
                    'target_weights': self.target_weights,
                    'rebalance_recommendations': rebalance_recs,
                    'optimization_metrics': result.get('metrics', {}),
                    'timestamp': time.time()
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"Error optimizing portfolio: {e}")
            return {'success': False, 'error': str(e)}
    
    async def execute_rebalance(self, 
                              recommendations: Optional[List[RebalanceRecommendation]] = None,
                              force: bool = False) -> Dict[str, Any]:
        """Execute portfolio rebalance"""
        try:
            if not recommendations:
                recommendations = await self._calculate_rebalance_recommendations()
            
            if not recommendations and not force:
                return {
                    'success': True,
                    'message': 'No rebalancing needed',
                    'trades_executed': 0
                }
            
            # Filter recommendations by priority and threshold
            filtered_recs = await self._filter_rebalance_recommendations(recommendations)
            
            if not filtered_recs and not force:
                return {
                    'success': True,
                    'message': 'No significant rebalancing needed',
                    'trades_executed': 0
                }
            
            # Execute rebalance through rebalancer
            rebalance_result = await self.rebalancer.execute_rebalance(filtered_recs)
            
            if rebalance_result['success']:
                # Update portfolio state
                await self._update_portfolio_state()
                
                # Record rebalance in performance tracker
                await self.performance_tracker.record_rebalance(rebalance_result)
                
                logger.info(f"✓ Portfolio rebalanced: {rebalance_result['trades_executed']} trades")
            
            return rebalance_result
            
        except Exception as e:
            logger.error(f"Error executing rebalance: {e}")
            return {'success': False, 'error': str(e)}
    
    async def add_position(self, symbol: str, quantity: float, price: float) -> bool:
        """Add position to portfolio"""
        try:
            success = await self.position_manager.add_position(symbol, quantity, price)
            
            if success:
                await self._update_portfolio_state()
                logger.info(f"✓ Added position: {symbol} {quantity} @ {price}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error adding position {symbol}: {e}")
            return False
    
    async def update_position(self, symbol: str, quantity_change: float, price: float) -> bool:
        """Update existing position"""
        try:
            success = await self.position_manager.update_position(symbol, quantity_change, price)
            
            if success:
                await self._update_portfolio_state()
                logger.debug(f"Updated position: {symbol} {quantity_change:+.2f} @ {price}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error updating position {symbol}: {e}")
            return False
    
    async def close_position(self, symbol: str, price: float) -> bool:
        """Close position completely"""
        try:
            success = await self.position_manager.close_position(symbol, price)
            
            if success:
                await self._update_portfolio_state()
                logger.info(f"✓ Closed position: {symbol} @ {price}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error closing position {symbol}: {e}")
            return False
    
    async def get_portfolio_state(self) -> Optional[Dict[str, Any]]:
        """Get current portfolio state"""
        try:
            if self.current_portfolio:
                return {
                    'timestamp': self.current_portfolio.timestamp,
                    'total_value': self.current_portfolio.total_value,
                    'cash': self.current_portfolio.cash,
                    'positions': self.current_portfolio.positions,
                    'weights': self.current_portfolio.weights,
                    'performance_metrics': self.current_portfolio.performance_metrics,
                    'risk_metrics': self.current_portfolio.risk_metrics,
                    'target_weights': self.target_weights
                }
            return None
            
        except Exception as e:
            logger.error(f"Error getting portfolio state: {e}")
            return None

    async def get_portfolio_data(self, portfolio_id: str) -> Optional[Dict[str, Any]]:
        """Get portfolio data for risk assessment"""
        try:
            if portfolio_id == self.portfolio_id and self.current_portfolio:
                # Convert portfolio to risk assessment format
                positions = []
                total_value = 0.0

                for symbol, position in self.current_portfolio.positions.items():
                    # Handle different position data structures
                    if isinstance(position, dict):
                        quantity = position.get('quantity', 0)
                        price = position.get('avg_price', position.get('price', 0))
                        position_value = position.get('market_value', quantity * price)
                    else:
                        # Handle position object
                        quantity = getattr(position, 'quantity', 0)
                        price = getattr(position, 'current_price', getattr(position, 'avg_price', 0))
                        position_value = quantity * price

                    positions.append({
                        'symbol': symbol,
                        'value': position_value,
                        'shares': quantity,
                        'price': price,
                        'sector': 'Technology'  # Simplified - would get from data source
                    })
                    total_value += position_value

                return {
                    'portfolio_id': portfolio_id,
                    'total_value': total_value + self.current_portfolio.cash,
                    'positions': positions,
                    'cash': self.current_portfolio.cash,
                    'timestamp': time.time()
                }
            return None

        except Exception as e:
            logger.error(f"Error getting portfolio data: {e}")
            return None
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get portfolio performance metrics"""
        try:
            return await self.performance_tracker.get_metrics()
        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {}
    
    async def get_risk_metrics(self) -> Dict[str, Any]:
        """Get portfolio risk metrics"""
        try:
            if self.risk_manager and self.current_portfolio:
                portfolio_dict = {
                    'total_value': self.current_portfolio.total_value,
                    'positions': self.current_portfolio.positions
                }
                assessment = await self.risk_manager.assess_portfolio_risk(portfolio_dict)
                return assessment.__dict__ if assessment else {}
            return {}
        except Exception as e:
            logger.error(f"Error getting risk metrics: {e}")
            return {}

    async def create_portfolio(self, name: str, initial_capital: float) -> str:
        """Create a new portfolio"""
        try:
            # Generate portfolio ID
            import uuid
            portfolio_id = str(uuid.uuid4())

            # Set initial capital
            self.initial_capital = initial_capital

            # Set as current portfolio ID
            self.portfolio_id = portfolio_id

            # Initialize portfolio state
            await self._initialize_portfolio_state()

            logger.info(f"Created portfolio '{name}' with ID {portfolio_id} and ${initial_capital:,.2f}")
            return portfolio_id

        except Exception as e:
            logger.error(f"Error creating portfolio: {e}")
            raise

    async def get_portfolio_value(self, portfolio_id: str) -> float:
        """Get total portfolio value"""
        try:
            if self.current_portfolio:
                return self.current_portfolio.total_value
            return self.initial_capital
        except Exception as e:
            logger.error(f"Error getting portfolio value: {e}")
            return 0.0
    
    # Private methods
    
    async def _initialize_portfolio_state(self):
        """Initialize portfolio state"""
        try:
            # Create initial portfolio state
            self.current_portfolio = PortfolioState(
                timestamp=time.time(),
                total_value=self.initial_capital,
                cash=self.initial_capital,
                positions={},
                weights={},
                performance_metrics={},
                risk_metrics={}
            )
            
            # Initialize position manager with initial capital
            await self.position_manager.set_initial_capital(self.initial_capital)
            
            logger.info(f"Portfolio initialized with ${self.initial_capital:,.2f}")
            
        except Exception as e:
            logger.error(f"Error initializing portfolio state: {e}")
    
    async def _update_portfolio_state(self):
        """Update current portfolio state"""
        try:
            # Get current positions
            positions = await self.position_manager.get_all_positions()
            cash = await self.position_manager.get_cash_balance()
            
            # Calculate total value and weights
            total_value = cash + sum(pos['market_value'] for pos in positions.values())
            weights = {}
            
            for symbol, position in positions.items():
                if total_value > 0:
                    weights[symbol] = position['market_value'] / total_value
            
            # Get performance metrics
            performance_metrics = await self.performance_tracker.get_current_metrics()
            
            # Get risk metrics
            risk_metrics = await self.get_risk_metrics()
            
            # Update portfolio state
            self.current_portfolio = PortfolioState(
                timestamp=time.time(),
                total_value=total_value,
                cash=cash,
                positions=positions,
                weights=weights,
                performance_metrics=performance_metrics,
                risk_metrics=risk_metrics
            )
            
            # Add to history
            self.portfolio_history.append(self.current_portfolio)
            
            # Limit history size
            if len(self.portfolio_history) > 10000:
                self.portfolio_history = self.portfolio_history[-10000:]
            
            # Update performance tracker
            await self.performance_tracker.update_portfolio_value(total_value)
            
        except Exception as e:
            logger.error(f"Error updating portfolio state: {e}")
    
    async def _calculate_rebalance_recommendations(self) -> List[RebalanceRecommendation]:
        """Calculate rebalance recommendations"""
        try:
            recommendations = []
            
            if not self.target_weights or not self.current_portfolio:
                return recommendations
            
            current_weights = self.current_portfolio.weights
            total_value = self.current_portfolio.total_value
            
            # Calculate recommendations for each target asset
            for symbol, target_weight in self.target_weights.items():
                current_weight = current_weights.get(symbol, 0.0)
                weight_diff = target_weight - current_weight
                
                # Check if rebalance is needed
                if abs(weight_diff) > self.rebalance_threshold:
                    current_value = current_weight * total_value
                    target_value = target_weight * total_value
                    trade_amount = target_value - current_value
                    
                    # Determine priority based on weight difference
                    if abs(weight_diff) > 0.10:  # 10%
                        priority = 1  # High
                    elif abs(weight_diff) > 0.05:  # 5%
                        priority = 2  # Medium
                    else:
                        priority = 3  # Low
                    
                    recommendation = RebalanceRecommendation(
                        symbol=symbol,
                        current_weight=current_weight,
                        target_weight=target_weight,
                        weight_diff=weight_diff,
                        current_value=current_value,
                        target_value=target_value,
                        trade_amount=trade_amount,
                        trade_side='buy' if trade_amount > 0 else 'sell',
                        priority=priority
                    )
                    
                    recommendations.append(recommendation)
            
            # Sort by priority
            recommendations.sort(key=lambda x: x.priority)
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error calculating rebalance recommendations: {e}")
            return []
    
    async def _filter_rebalance_recommendations(self, 
                                             recommendations: List[RebalanceRecommendation]) -> List[RebalanceRecommendation]:
        """Filter rebalance recommendations"""
        try:
            # Filter by minimum trade amount
            min_trade_amount = self.portfolio_config.get('min_trade_amount', 100.0)
            
            filtered = [
                rec for rec in recommendations
                if abs(rec.trade_amount) >= min_trade_amount
            ]
            
            # Limit number of simultaneous trades
            max_trades = self.portfolio_config.get('max_rebalance_trades', 10)
            
            return filtered[:max_trades]
            
        except Exception as e:
            logger.error(f"Error filtering rebalance recommendations: {e}")
            return recommendations
    
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.running:
            try:
                await asyncio.sleep(60)  # Monitor every minute
                
                if self.running:
                    await self._update_portfolio_state()
                    
                    # Check for risk violations
                    if self.risk_manager:
                        portfolio_dict = {
                            'total_value': self.current_portfolio.total_value,
                            'positions': self.current_portfolio.positions
                        }
                        await self.risk_manager.update_portfolio(portfolio_dict)
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
    
    async def _rebalancing_loop(self):
        """Background rebalancing loop"""
        while self.running:
            try:
                # Determine sleep time based on rebalance frequency
                if self.rebalance_frequency == 'hourly':
                    sleep_time = 3600
                elif self.rebalance_frequency == 'daily':
                    sleep_time = 86400
                else:
                    sleep_time = 3600  # Default to hourly
                
                await asyncio.sleep(sleep_time)
                
                if self.running and self.target_weights:
                    # Check if rebalancing is needed
                    recommendations = await self._calculate_rebalance_recommendations()
                    
                    if recommendations:
                        logger.info(f"Auto-rebalancing triggered: {len(recommendations)} recommendations")
                        await self.execute_rebalance(recommendations)
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in rebalancing loop: {e}")

# 📚 Advanced Ollama Trading Agents - API Reference

## **🎯 Core System APIs**

### **SystemCoordinator**

The central coordination hub for the entire trading system.

```python
from system.system_coordinator import SystemCoordinator

coordinator = SystemCoordinator('config/system_config.yaml')
```

#### **Methods**

##### `async initialize() -> bool`
Initialize all system components.

```python
success = await coordinator.initialize()
if success:
    print("✅ System initialized successfully")
```

##### `async start() -> bool`
Start the trading system.

```python
await coordinator.start()
print("🚀 Trading system is running")
```

##### `async stop() -> bool`
Stop the trading system gracefully.

```python
await coordinator.stop()
print("🛑 System stopped")
```

##### `async get_system_status() -> SystemStatus`
Get comprehensive system status.

```python
status = await coordinator.get_system_status()
print(f"System Health: {status.system_health:.1%}")
print(f"Active Strategies: {status.active_strategies}")
print(f"Uptime: {status.uptime:.1f}s")
```

##### `async get_component(component_name: str) -> Optional[Any]`
Get a specific system component.

```python
portfolio_manager = await coordinator.get_component('portfolio_manager')
risk_manager = await coordinator.get_component('risk_manager')
```

##### `async execute_system_command(command: str, params: Dict[str, Any] = None) -> Dict[str, Any]`
Execute system-level commands.

```python
# Pause trading
result = await coordinator.execute_system_command("pause")

# Resume trading
result = await coordinator.execute_system_command("resume")

# Health check
result = await coordinator.execute_system_command("health_check")
```

---

## **🎯 Multi-Strategy Engine API**

### **MultiStrategyEngine**

Advanced engine for coordinating multiple trading strategies.

```python
from trading.multi_strategy_engine import MultiStrategyEngine, StrategyPriority

engine = MultiStrategyEngine(
    strategy_manager=strategy_manager,
    execution_engine=execution_engine,
    portfolio_manager=portfolio_manager,
    risk_manager=risk_manager,
    analytics_engine=analytics_engine,
    config=config
)
```

#### **Methods**

##### `async initialize(total_capital: float) -> bool`
Initialize the multi-strategy engine with capital allocation.

```python
success = await engine.initialize(100000.0)  # $100k capital
```

##### `async add_strategy(strategy_id: str, strategy_name: str, allocated_capital: float, priority: StrategyPriority) -> bool`
Add a strategy to the engine.

```python
from trading.multi_strategy_engine import StrategyPriority

success = await engine.add_strategy(
    "momentum_strategy_1",
    "High-Frequency Momentum",
    50000.0,  # $50k allocation
    StrategyPriority.HIGH
)
```

##### `async submit_signal(signal: TradingSignal) -> bool`
Submit a trading signal from a strategy.

```python
from trading.multi_strategy_engine import TradingSignal

signal = TradingSignal(
    strategy_id="momentum_strategy_1",
    symbol="AAPL",
    action="buy",
    quantity=100,
    confidence=0.8,
    urgency=0.6,
    reasoning="Strong momentum breakout detected",
    risk_score=0.4,
    expected_return=0.05,
    timestamp=time.time()
)

success = await engine.submit_signal(signal)
```

##### `async get_engine_status() -> Dict[str, Any]`
Get multi-strategy engine status.

```python
status = await engine.get_engine_status()
print(f"Active Strategies: {status['active_strategies']}")
print(f"Total Capital: ${status['total_capital']:,.2f}")
print(f"Available Capital: ${status['available_capital']:,.2f}")
```

---

## **📊 Real-Time Market System API**

### **RealTimeMarketSystem**

Real-time market data processing and event detection.

```python
from market.real_time_market_system import RealTimeMarketSystem, MarketEventType

market_system = RealTimeMarketSystem(config)
```

#### **Methods**

##### `async subscribe_to_quotes(callback: Callable[[RealTimeQuote], None])`
Subscribe to real-time quote updates.

```python
async def quote_handler(quote):
    print(f"📈 {quote.symbol}: ${quote.last:.2f} ({quote.change_percent:+.2f}%)")

await market_system.subscribe_to_quotes(quote_handler)
```

##### `async subscribe_to_events(event_type: MarketEventType, callback: Callable[[MarketEvent], None])`
Subscribe to specific market events.

```python
async def volume_spike_handler(event):
    print(f"🔥 Volume spike: {event.symbol} - {event.description}")

await market_system.subscribe_to_events(
    MarketEventType.VOLUME_SPIKE, 
    volume_spike_handler
)
```

##### `async get_current_quote(symbol: str) -> Optional[RealTimeQuote]`
Get current quote for a symbol.

```python
quote = await market_system.get_current_quote("AAPL")
if quote:
    print(f"AAPL: ${quote.last:.2f}")
```

##### `async get_market_conditions() -> Optional[MarketConditions]`
Get current market conditions.

```python
conditions = await market_system.get_market_conditions()
print(f"Sentiment: {conditions.overall_sentiment}")
print(f"Volatility: {conditions.volatility_level}")
print(f"Market Stress: {conditions.market_stress:.1%}")
```

##### `async get_recent_events(event_type: MarketEventType = None, limit: int = 100) -> List[MarketEvent]`
Get recent market events.

```python
events = await market_system.get_recent_events(
    MarketEventType.VOLATILITY_SPIKE, 
    limit=10
)
for event in events:
    print(f"⚡ {event.symbol}: {event.description}")
```

---

## **🛡️ Advanced Risk Management API**

### **RiskManager**

Advanced risk management with dynamic controls.

```python
from risk.risk_manager import RiskManager

risk_manager = RiskManager(config)
```

#### **Methods**

##### `async dynamic_risk_adjustment(market_conditions: Dict[str, Any]) -> Dict[str, float]`
Dynamically adjust risk parameters based on market conditions.

```python
market_conditions = {
    'volatility': 0.3,
    'trend_strength': 0.7,
    'market_stress': 0.4
}

adjusted_params = await risk_manager.dynamic_risk_adjustment(market_conditions)
print(f"Risk Multiplier: {adjusted_params['risk_multiplier']:.2f}")
```

##### `async real_time_risk_monitoring(portfolio: Dict[str, Any]) -> Dict[str, Any]`
Real-time risk monitoring with alerts.

```python
portfolio_data = await portfolio_manager.get_portfolio_data(portfolio_id)
monitoring = await risk_manager.real_time_risk_monitoring(portfolio_data)

print(f"Risk Status: {monitoring['risk_status']}")
for alert in monitoring['alerts']:
    print(f"⚠️ {alert['level']}: {alert['message']}")
```

##### `async assess_portfolio_risk(portfolio: Dict[str, Any]) -> RiskAssessment`
Comprehensive portfolio risk assessment.

```python
risk_assessment = await risk_manager.assess_portfolio_risk(portfolio_data)
print(f"Overall Risk: {risk_assessment.overall_risk_score:.1%}")
print(f"VaR 95%: ${risk_assessment.var_95:,.2f}")
print(f"Max Drawdown: {risk_assessment.max_drawdown:.1%}")
```

---

## **🤖 AI Strategy Optimizer API**

### **AIStrategyOptimizer**

AI-powered strategy analysis and optimization using Ollama models.

```python
from ai.strategy_optimizer import AIStrategyOptimizer

optimizer = AIStrategyOptimizer(ollama_hub, config)
```

#### **Methods**

##### `async analyze_strategy_performance(strategy_id: str, performance_data: Dict[str, Any]) -> PerformanceAnalysis`
Analyze strategy performance using AI.

```python
performance_data = {
    'total_trades': 150,
    'win_rate': 0.65,
    'avg_return': 0.08,
    'max_drawdown': 0.12,
    'sharpe_ratio': 1.8
}

analysis = await optimizer.analyze_strategy_performance(
    "momentum_strategy_1", 
    performance_data
)

print(f"Strengths: {analysis.strengths}")
print(f"Weaknesses: {analysis.weaknesses}")
print(f"Recommendations: {analysis.recommendations}")
```

##### `async optimize_strategy_parameters(strategy_id: str, current_parameters: Dict[str, Any], performance_data: Dict[str, Any]) -> OptimizationResult`
Optimize strategy parameters using AI.

```python
current_parameters = {
    'lookback_period': 20,
    'momentum_threshold': 0.02,
    'stop_loss': 0.05
}

optimization = await optimizer.optimize_strategy_parameters(
    "momentum_strategy_1",
    current_parameters,
    performance_data
)

if optimization.confidence_score > 0.8:
    print(f"🤖 High-confidence optimization:")
    print(f"Expected Improvement: {optimization.expected_improvement:.1%}")
    print(f"Reasoning: {optimization.reasoning}")
```

##### `async get_strategy_recommendations(strategy_id: str) -> List[str]`
Get AI-powered strategy recommendations.

```python
recommendations = await optimizer.get_strategy_recommendations("momentum_strategy_1")
for i, rec in enumerate(recommendations, 1):
    print(f"{i}. {rec}")
```

---

## **💼 Intelligent Portfolio Rebalancer API**

### **IntelligentRebalancer**

Market-aware portfolio rebalancing system.

```python
from portfolio.intelligent_rebalancer import IntelligentRebalancer, RebalanceReason

rebalancer = IntelligentRebalancer(
    portfolio_manager, 
    risk_manager, 
    market_system, 
    config
)
```

#### **Methods**

##### `async analyze_portfolio_drift() -> Dict[str, float]`
Analyze current portfolio drift from target allocations.

```python
drift_analysis = await rebalancer.analyze_portfolio_drift()
for symbol, analysis in drift_analysis.items():
    print(f"{symbol}: {analysis['drift_percent']:.1%} drift")
```

##### `async generate_rebalance_recommendations(reason: RebalanceReason) -> List[RebalanceRecommendation]`
Generate intelligent rebalancing recommendations.

```python
from portfolio.intelligent_rebalancer import RebalanceReason

recommendations = await rebalancer.generate_rebalance_recommendations(
    RebalanceReason.DRIFT_THRESHOLD
)

for rec in recommendations:
    if rec.action != 'hold':
        print(f"💡 {rec.action.upper()} {rec.symbol}: "
              f"{rec.weight_change:+.1%} weight change")
        print(f"   Confidence: {rec.confidence:.1%}")
        print(f"   Reasoning: {rec.reasoning}")
```

##### `async execute_rebalancing(recommendations: List[RebalanceRecommendation], reason: RebalanceReason) -> RebalanceResult`
Execute portfolio rebalancing.

```python
result = await rebalancer.execute_rebalancing(
    recommendations, 
    RebalanceReason.DRIFT_THRESHOLD
)

print(f"✅ Rebalancing completed:")
print(f"   Trades executed: {result.executed_trades}/{result.total_trades}")
print(f"   Total cost: ${result.total_cost:.2f}")
print(f"   Expected improvement: {result.expected_improvement:.1%}")
```

---

## **📊 Data Structures**

### **SystemStatus**

```python
@dataclass
class SystemStatus:
    state: SystemState
    uptime: float
    components_status: Dict[str, bool]
    performance_metrics: Dict[str, Any]
    active_strategies: int
    active_agents: int
    total_trades: int
    system_health: float
    last_update: datetime
```

### **TradingSignal**

```python
@dataclass
class TradingSignal:
    strategy_id: str
    symbol: str
    action: str  # 'buy', 'sell', 'hold'
    quantity: float
    confidence: float
    urgency: float
    reasoning: str
    risk_score: float
    expected_return: float
    timestamp: float
```

### **RealTimeQuote**

```python
@dataclass
class RealTimeQuote:
    symbol: str
    bid: float
    ask: float
    last: float
    volume: int
    timestamp: float
    change: float
    change_percent: float
```

### **MarketConditions**

```python
@dataclass
class MarketConditions:
    overall_sentiment: str  # 'bullish', 'bearish', 'neutral'
    volatility_level: str   # 'low', 'medium', 'high', 'extreme'
    market_stress: float    # 0.0 to 1.0
    trend_strength: float   # 0.0 to 1.0
    liquidity_conditions: str  # 'abundant', 'normal', 'tight', 'stressed'
    sector_performance: Dict[str, float]
    timestamp: float
```

### **RebalanceRecommendation**

```python
@dataclass
class RebalanceRecommendation:
    symbol: str
    current_weight: float
    target_weight: float
    weight_change: float
    current_value: float
    target_value: float
    action: str  # 'buy', 'sell', 'hold'
    quantity_change: float
    confidence: float
    reasoning: str
```

---

## **🔧 Configuration Options**

### **System Configuration Keys**

```yaml
system:
  environment: development | production
  log_level: DEBUG | INFO | WARNING | ERROR
  max_workers: int

ollama:
  base_url: str
  models:
    strategy_developer: str
    risk_manager: str
    execution_specialist: str
    market_analyst: str

trading:
  paper_trading: bool
  max_orders_per_second: int
  default_portfolio_size: float

risk:
  max_portfolio_risk: float
  max_position_size: float
  max_sector_exposure: float
  var_confidence: float

multi_strategy_engine:
  max_strategies: int
  rebalance_interval: int
  signal_timeout: int
  max_correlation: float

real_time_market:
  symbols: List[str]
  update_interval: float
  event_detection: bool
  max_history_length: int

intelligent_rebalancer:
  drift_threshold: float
  rebalance_frequency: int
  min_trade_size: float
  target_allocations: Dict[str, float]

ai_optimizer:
  analysis_model: str
  optimization_model: str
  reasoning_model: str
  optimization_interval: int
  min_trades: int
  confidence_threshold: float
```

---

## **🚀 Usage Examples**

### **Complete System Workflow**

```python
import asyncio
from system.system_coordinator import SystemCoordinator

async def complete_workflow():
    # 1. Initialize system
    coordinator = SystemCoordinator('config/system_config.yaml')
    await coordinator.initialize()
    await coordinator.start()
    
    # 2. Get components
    multi_engine = await coordinator.get_component('multi_strategy_engine')
    market_system = await coordinator.get_component('market_system')
    risk_manager = await coordinator.get_component('risk_manager')
    
    # 3. Add strategies
    await multi_engine.add_strategy(
        "momentum_1", "Momentum Strategy", 50000.0, StrategyPriority.HIGH
    )
    
    # 4. Monitor market conditions
    conditions = await market_system.get_market_conditions()
    print(f"Market: {conditions.overall_sentiment}")
    
    # 5. Submit trading signals
    signal = TradingSignal(
        strategy_id="momentum_1",
        symbol="AAPL",
        action="buy",
        quantity=100,
        confidence=0.8,
        urgency=0.6,
        reasoning="Strong momentum",
        risk_score=0.4,
        expected_return=0.05,
        timestamp=time.time()
    )
    await multi_engine.submit_signal(signal)
    
    # 6. Monitor system health
    status = await coordinator.get_system_status()
    print(f"System Health: {status.system_health:.1%}")
    
    # 7. Clean shutdown
    await coordinator.stop()

asyncio.run(complete_workflow())
```

This API reference provides comprehensive documentation for all major system components and their usage patterns. The system is designed to be intuitive while providing powerful advanced features for sophisticated trading operations.

"""
Black-Litterman Model - Advanced portfolio optimization with investor views
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from scipy import linalg
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class BlackLittermanModel:
    """
    Black-Litterman model implementation for portfolio optimization.
    Combines market equilibrium with investor views to generate
    optimal portfolio allocations.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Model parameters
        self.risk_aversion = 3.0
        self.tau = 0.025  # Scaling factor for uncertainty of prior
        self.confidence_scaling = 1.0
        
        # Market data
        self.returns_data: Optional[pd.DataFrame] = None
        self.market_caps: Optional[Dict[str, float]] = None
        self.covariance_matrix: Optional[np.ndarray] = None
        
        # Model components
        self.equilibrium_returns: Optional[np.ndarray] = None
        self.prior_covariance: Optional[np.ndarray] = None
        self.views: List[Dict[str, Any]] = []
        
        # Results
        self.posterior_returns: Optional[np.ndarray] = None
        self.posterior_covariance: Optional[np.ndarray] = None
        self.optimal_weights: Optional[np.ndarray] = None
        
        # State
        self.initialized = False
        
    async def initialize(self):
        """Initialize Black-Litterman model"""
        if self.initialized:
            return
            
        logger.info("Initializing Black-Litterman Model...")
        
        # Setup model parameters
        await self._setup_model_parameters()
        
        self.initialized = True
        logger.info("✓ Black-Litterman Model initialized")
        
    async def _setup_model_parameters(self):
        """Setup model parameters from config"""
        bl_config = self.config.get('black_litterman', {})
        
        self.risk_aversion = bl_config.get('risk_aversion', 3.0)
        self.tau = bl_config.get('tau', 0.025)
        self.confidence_scaling = bl_config.get('confidence_scaling', 1.0)
        
    async def update_market_data(self, returns_data: pd.DataFrame,
                               market_caps: Dict[str, float] = None) -> Dict[str, Any]:
        """Update market data for the model"""
        try:
            self.returns_data = returns_data
            self.market_caps = market_caps
            
            # Calculate covariance matrix
            self.covariance_matrix = returns_data.cov().values
            
            # Calculate equilibrium returns
            await self._calculate_equilibrium_returns()
            
            # Setup prior covariance
            self.prior_covariance = self.tau * self.covariance_matrix
            
            logger.info(f"✓ Updated Black-Litterman market data for {len(returns_data.columns)} assets")
            
            return {
                'success': True,
                'assets': len(returns_data.columns),
                'equilibrium_returns': self.equilibrium_returns.tolist() if self.equilibrium_returns is not None else None
            }
            
        except Exception as e:
            logger.error(f"Error updating Black-Litterman market data: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _calculate_equilibrium_returns(self):
        """Calculate implied equilibrium returns"""
        if self.market_caps is not None:
            # Use market capitalization weights
            total_market_cap = sum(self.market_caps.values())
            market_weights = np.array([
                self.market_caps.get(asset, 0) / total_market_cap
                for asset in self.returns_data.columns
            ])
        else:
            # Use equal weights if market caps not available
            n_assets = len(self.returns_data.columns)
            market_weights = np.ones(n_assets) / n_assets
            
        # Implied equilibrium returns: μ = λ * Σ * w_market
        self.equilibrium_returns = self.risk_aversion * np.dot(self.covariance_matrix, market_weights)
        
    async def add_view(self, view_type: str, assets: List[str], 
                      expected_return: float, confidence: float,
                      view_description: str = "") -> Dict[str, Any]:
        """Add investor view to the model"""
        try:
            # Create picking matrix P
            n_assets = len(self.returns_data.columns)
            P = np.zeros(n_assets)
            
            if view_type == "absolute":
                # Absolute view: asset will return X%
                if len(assets) != 1:
                    return {'success': False, 'error': 'Absolute view requires exactly one asset'}
                asset_idx = self.returns_data.columns.get_loc(assets[0])
                P[asset_idx] = 1.0
                
            elif view_type == "relative":
                # Relative view: asset A will outperform asset B by X%
                if len(assets) != 2:
                    return {'success': False, 'error': 'Relative view requires exactly two assets'}
                asset_a_idx = self.returns_data.columns.get_loc(assets[0])
                asset_b_idx = self.returns_data.columns.get_loc(assets[1])
                P[asset_a_idx] = 1.0
                P[asset_b_idx] = -1.0
                
            elif view_type == "portfolio":
                # Portfolio view: weighted combination of assets
                if len(assets) == 0:
                    return {'success': False, 'error': 'Portfolio view requires at least one asset'}
                # Equal weights for simplicity
                weight = 1.0 / len(assets)
                for asset in assets:
                    asset_idx = self.returns_data.columns.get_loc(asset)
                    P[asset_idx] = weight
                    
            else:
                return {'success': False, 'error': f'Unknown view type: {view_type}'}
                
            # Calculate view uncertainty (Omega)
            # Higher confidence = lower uncertainty
            view_uncertainty = (1.0 / confidence) * self.confidence_scaling
            
            view = {
                'view_id': len(self.views),
                'view_type': view_type,
                'assets': assets,
                'P': P,
                'Q': expected_return,  # View return
                'omega': view_uncertainty,
                'confidence': confidence,
                'description': view_description
            }
            
            self.views.append(view)
            
            logger.info(f"✓ Added {view_type} view for {assets}: {expected_return:.2%} (confidence: {confidence:.2f})")
            
            return {
                'success': True,
                'view_id': view['view_id'],
                'view_type': view_type,
                'assets': assets
            }
            
        except Exception as e:
            logger.error(f"Error adding view: {e}")
            return {'success': False, 'error': str(e)}
            
    async def clear_views(self):
        """Clear all investor views"""
        self.views = []
        logger.info("✓ Cleared all investor views")
        
    async def calculate_posterior(self) -> Dict[str, Any]:
        """Calculate posterior returns and covariance"""
        try:
            if not self.views:
                # No views - use equilibrium
                self.posterior_returns = self.equilibrium_returns.copy()
                self.posterior_covariance = self.covariance_matrix.copy()
                
                return {
                    'success': True,
                    'views_used': 0,
                    'posterior_returns': self.posterior_returns.tolist()
                }
                
            # Construct view matrices
            n_views = len(self.views)
            n_assets = len(self.returns_data.columns)
            
            P = np.array([view['P'] for view in self.views])  # Picking matrix
            Q = np.array([view['Q'] for view in self.views])  # View returns
            Omega = np.diag([view['omega'] for view in self.views])  # View uncertainties
            
            # Black-Litterman formula
            # Posterior precision = Prior precision + View precision
            tau_sigma = self.tau * self.covariance_matrix
            
            # M1 = (τΣ)^(-1) + P'Ω^(-1)P
            M1 = linalg.inv(tau_sigma) + P.T @ linalg.inv(Omega) @ P
            
            # M2 = (τΣ)^(-1)π + P'Ω^(-1)Q
            M2 = linalg.inv(tau_sigma) @ self.equilibrium_returns + P.T @ linalg.inv(Omega) @ Q
            
            # Posterior mean: μ_BL = M1^(-1) * M2
            self.posterior_returns = linalg.inv(M1) @ M2
            
            # Posterior covariance: Σ_BL = M1^(-1)
            self.posterior_covariance = linalg.inv(M1)
            
            logger.info(f"✓ Calculated Black-Litterman posterior with {n_views} views")
            
            return {
                'success': True,
                'views_used': n_views,
                'posterior_returns': self.posterior_returns.tolist(),
                'posterior_volatility': np.sqrt(np.diag(self.posterior_covariance)).tolist()
            }
            
        except Exception as e:
            logger.error(f"Error calculating posterior: {e}")
            return {'success': False, 'error': str(e)}
            
    async def optimize(self, constraints: Dict[str, Any] = None) -> Dict[str, Any]:
        """Optimize portfolio using Black-Litterman model"""
        try:
            # Calculate posterior if not done
            if self.posterior_returns is None:
                await self.calculate_posterior()
                
            # Mean-variance optimization with posterior inputs
            from scipy.optimize import minimize
            
            n_assets = len(self.returns_data.columns)
            
            # Objective function: maximize utility = μ'w - (λ/2)w'Σw
            def objective(weights):
                portfolio_return = np.dot(weights, self.posterior_returns)
                portfolio_variance = np.dot(weights, np.dot(self.posterior_covariance, weights))
                utility = portfolio_return - (self.risk_aversion / 2) * portfolio_variance
                return -utility  # Minimize negative utility
                
            # Constraints
            constraints_list = [
                {'type': 'eq', 'fun': lambda w: np.sum(w) - 1.0}  # Weights sum to 1
            ]
            
            # Bounds
            min_weight = constraints.get('min_weight', 0.0) if constraints else 0.0
            max_weight = constraints.get('max_weight', 1.0) if constraints else 1.0
            bounds = [(min_weight, max_weight) for _ in range(n_assets)]
            
            # Initial guess
            x0 = np.ones(n_assets) / n_assets
            
            # Optimize
            result = minimize(objective, x0, method='SLSQP', bounds=bounds, constraints=constraints_list)
            
            if result.success:
                self.optimal_weights = result.x
                weights_dict = dict(zip(self.returns_data.columns, result.x))
                
                # Calculate portfolio metrics
                portfolio_return = np.dot(result.x, self.posterior_returns)
                portfolio_variance = np.dot(result.x, np.dot(self.posterior_covariance, result.x))
                portfolio_volatility = np.sqrt(portfolio_variance)
                
                return {
                    'success': True,
                    'weights': weights_dict,
                    'expected_return': portfolio_return,
                    'volatility': portfolio_volatility,
                    'sharpe_ratio': portfolio_return / portfolio_volatility if portfolio_volatility > 0 else 0,
                    'views_incorporated': len(self.views),
                    'method': 'black_litterman'
                }
            else:
                return {'success': False, 'error': 'Optimization failed', 'details': result.message}
                
        except Exception as e:
            logger.error(f"Error optimizing Black-Litterman portfolio: {e}")
            return {'success': False, 'error': str(e)}
            
    async def get_view_impact(self) -> Dict[str, Any]:
        """Analyze the impact of views on portfolio"""
        if not self.views or self.posterior_returns is None:
            return {'success': False, 'error': 'No views or posterior not calculated'}
            
        try:
            # Calculate portfolio without views (equilibrium)
            equilibrium_weights = await self._calculate_equilibrium_portfolio()
            
            # Calculate portfolio with views
            bl_weights = self.optimal_weights
            
            if equilibrium_weights is None or bl_weights is None:
                return {'success': False, 'error': 'Unable to calculate weight comparison'}
                
            # Calculate differences
            weight_changes = bl_weights - equilibrium_weights
            
            # View contributions
            view_impacts = []
            for i, view in enumerate(self.views):
                impact = {
                    'view_id': view['view_id'],
                    'view_type': view['view_type'],
                    'assets': view['assets'],
                    'expected_return': view['Q'],
                    'confidence': view['confidence'],
                    'weight_impact': np.dot(view['P'], weight_changes)
                }
                view_impacts.append(impact)
                
            return {
                'success': True,
                'equilibrium_weights': dict(zip(self.returns_data.columns, equilibrium_weights)),
                'bl_weights': dict(zip(self.returns_data.columns, bl_weights)),
                'weight_changes': dict(zip(self.returns_data.columns, weight_changes)),
                'view_impacts': view_impacts,
                'total_weight_change': np.sum(np.abs(weight_changes))
            }
            
        except Exception as e:
            logger.error(f"Error analyzing view impact: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _calculate_equilibrium_portfolio(self) -> Optional[np.ndarray]:
        """Calculate equilibrium portfolio weights"""
        try:
            # Inverse optimization: w = (1/λ) * Σ^(-1) * μ
            inv_cov = linalg.inv(self.covariance_matrix)
            unnormalized_weights = (1 / self.risk_aversion) * np.dot(inv_cov, self.equilibrium_returns)
            
            # Normalize to sum to 1
            equilibrium_weights = unnormalized_weights / np.sum(unnormalized_weights)
            
            return equilibrium_weights
            
        except Exception as e:
            logger.error(f"Error calculating equilibrium portfolio: {e}")
            return None
            
    async def get_model_summary(self) -> Dict[str, Any]:
        """Get Black-Litterman model summary"""
        return {
            'model_parameters': {
                'risk_aversion': self.risk_aversion,
                'tau': self.tau,
                'confidence_scaling': self.confidence_scaling
            },
            'data_status': {
                'returns_data_available': self.returns_data is not None,
                'market_caps_available': self.market_caps is not None,
                'equilibrium_calculated': self.equilibrium_returns is not None
            },
            'views': {
                'total_views': len(self.views),
                'view_types': [view['view_type'] for view in self.views],
                'average_confidence': np.mean([view['confidence'] for view in self.views]) if self.views else 0
            },
            'optimization_status': {
                'posterior_calculated': self.posterior_returns is not None,
                'optimal_weights_calculated': self.optimal_weights is not None
            }
        }

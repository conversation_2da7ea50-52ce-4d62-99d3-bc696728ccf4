"""
Portfolio Management Package

Advanced portfolio optimization and management capabilities:
- Black-Litterman model implementation
- Risk parity optimization
- Dynamic asset allocation
- Multi-objective optimization
- Regime-aware portfolio construction
- Performance attribution
- Risk decomposition
"""

from .portfolio_optimizer import PortfolioOptimizer
from .black_litterman import BlackLittermanModel
from .risk_parity import RiskParityOptimizer
from .dynamic_allocation import DynamicAssetAllocator
from .multi_objective import MultiObjectiveOptimizer

__all__ = [
    'PortfolioOptimizer',
    'BlackLittermanModel',
    'RiskParityOptimizer',
    'DynamicAssetAllocator',
    'MultiObjectiveOptimizer'
]

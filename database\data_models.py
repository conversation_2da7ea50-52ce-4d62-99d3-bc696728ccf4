"""
Data Models - Comprehensive data models for the trading system
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum
import json


class OrderSide(Enum):
    """Order side enumeration"""
    BUY = "buy"
    SELL = "sell"


class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"
    ICEBERG = "iceberg"


class OrderStatus(Enum):
    """Order status enumeration"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"


class StrategyStatus(Enum):
    """Strategy status enumeration"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    TESTING = "testing"
    DEPRECATED = "deprecated"
    ERROR = "error"


class RiskLevel(Enum):
    """Risk level enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class Strategy:
    """Strategy data model"""
    id: str
    name: str
    type: str
    parameters: Dict[str, Any]
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    status: StrategyStatus = StrategyStatus.TESTING
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    version: str = "1.0.0"
    author: str = "system"
    description: str = ""
    tags: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'type': self.type,
            'parameters': self.parameters,
            'performance_metrics': self.performance_metrics,
            'status': self.status.value,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'version': self.version,
            'author': self.author,
            'description': self.description,
            'tags': self.tags
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Strategy':
        """Create from dictionary"""
        return cls(
            id=data['id'],
            name=data['name'],
            type=data['type'],
            parameters=data['parameters'],
            performance_metrics=data.get('performance_metrics', {}),
            status=StrategyStatus(data.get('status', 'testing')),
            created_at=datetime.fromisoformat(data['created_at']) if isinstance(data['created_at'], str) else data['created_at'],
            updated_at=datetime.fromisoformat(data['updated_at']) if isinstance(data['updated_at'], str) else data['updated_at'],
            version=data.get('version', '1.0.0'),
            author=data.get('author', 'system'),
            description=data.get('description', ''),
            tags=data.get('tags', [])
        )


@dataclass
class Order:
    """Order data model"""
    id: str
    strategy_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: str = "GTC"  # Good Till Cancelled
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    average_fill_price: float = 0.0
    commission: float = 0.0
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    submitted_at: Optional[datetime] = None
    filled_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def remaining_quantity(self) -> float:
        """Get remaining quantity to fill"""
        return self.quantity - self.filled_quantity
    
    @property
    def is_filled(self) -> bool:
        """Check if order is completely filled"""
        return self.filled_quantity >= self.quantity
    
    @property
    def fill_percentage(self) -> float:
        """Get fill percentage"""
        return (self.filled_quantity / self.quantity * 100) if self.quantity > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'strategy_id': self.strategy_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'order_type': self.order_type.value,
            'quantity': self.quantity,
            'price': self.price,
            'stop_price': self.stop_price,
            'time_in_force': self.time_in_force,
            'status': self.status.value,
            'filled_quantity': self.filled_quantity,
            'average_fill_price': self.average_fill_price,
            'commission': self.commission,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'submitted_at': self.submitted_at.isoformat() if self.submitted_at else None,
            'filled_at': self.filled_at.isoformat() if self.filled_at else None,
            'metadata': self.metadata
        }


@dataclass
class Position:
    """Position data model"""
    symbol: str
    quantity: float
    average_cost: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float
    cost_basis: float
    last_updated: datetime = field(default_factory=datetime.utcnow)
    strategy_allocations: Dict[str, float] = field(default_factory=dict)  # strategy_id -> quantity
    
    @property
    def total_pnl(self) -> float:
        """Get total P&L"""
        return self.unrealized_pnl + self.realized_pnl
    
    @property
    def pnl_percentage(self) -> float:
        """Get P&L percentage"""
        return (self.total_pnl / self.cost_basis * 100) if self.cost_basis > 0 else 0.0
    
    def update_price(self, new_price: float):
        """Update position with new market price"""
        self.current_price = new_price
        self.market_value = self.quantity * new_price
        self.unrealized_pnl = self.market_value - self.cost_basis
        self.last_updated = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'symbol': self.symbol,
            'quantity': self.quantity,
            'average_cost': self.average_cost,
            'current_price': self.current_price,
            'market_value': self.market_value,
            'unrealized_pnl': self.unrealized_pnl,
            'realized_pnl': self.realized_pnl,
            'cost_basis': self.cost_basis,
            'last_updated': self.last_updated.isoformat(),
            'strategy_allocations': self.strategy_allocations
        }


@dataclass
class Portfolio:
    """Portfolio data model"""
    id: str
    name: str
    total_value: float
    cash: float
    positions: Dict[str, Position] = field(default_factory=dict)
    target_allocations: Dict[str, float] = field(default_factory=dict)  # symbol -> weight
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    risk_metrics: Dict[str, float] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    @property
    def invested_value(self) -> float:
        """Get total invested value"""
        return sum(pos.market_value for pos in self.positions.values())
    
    @property
    def total_pnl(self) -> float:
        """Get total portfolio P&L"""
        return sum(pos.total_pnl for pos in self.positions.values())
    
    @property
    def position_count(self) -> int:
        """Get number of positions"""
        return len(self.positions)
    
    def get_allocation(self, symbol: str) -> float:
        """Get current allocation percentage for symbol"""
        if symbol in self.positions and self.total_value > 0:
            return self.positions[symbol].market_value / self.total_value
        return 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'total_value': self.total_value,
            'cash': self.cash,
            'positions': {symbol: pos.to_dict() for symbol, pos in self.positions.items()},
            'target_allocations': self.target_allocations,
            'performance_metrics': self.performance_metrics,
            'risk_metrics': self.risk_metrics,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }


@dataclass
class Trade:
    """Trade execution data model"""
    id: str
    order_id: str
    strategy_id: str
    symbol: str
    side: OrderSide
    quantity: float
    price: float
    commission: float
    timestamp: datetime = field(default_factory=datetime.utcnow)
    execution_venue: str = "paper"
    slippage: float = 0.0
    execution_time_ms: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def value(self) -> float:
        """Get trade value"""
        return self.quantity * self.price
    
    @property
    def net_value(self) -> float:
        """Get net trade value after commission"""
        return self.value - self.commission
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'strategy_id': self.strategy_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'quantity': self.quantity,
            'price': self.price,
            'commission': self.commission,
            'timestamp': self.timestamp.isoformat(),
            'execution_venue': self.execution_venue,
            'slippage': self.slippage,
            'execution_time_ms': self.execution_time_ms,
            'metadata': self.metadata
        }


@dataclass
class RiskAssessment:
    """Risk assessment data model"""
    id: str
    portfolio_id: str
    assessment_type: str
    risk_level: RiskLevel
    risk_score: float
    var_95: float  # Value at Risk 95%
    max_drawdown: float
    concentration_risk: float
    correlation_risk: float
    liquidity_risk: float
    timestamp: datetime = field(default_factory=datetime.utcnow)
    recommendations: List[str] = field(default_factory=list)
    risk_factors: Dict[str, float] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'portfolio_id': self.portfolio_id,
            'assessment_type': self.assessment_type,
            'risk_level': self.risk_level.value,
            'risk_score': self.risk_score,
            'var_95': self.var_95,
            'max_drawdown': self.max_drawdown,
            'concentration_risk': self.concentration_risk,
            'correlation_risk': self.correlation_risk,
            'liquidity_risk': self.liquidity_risk,
            'timestamp': self.timestamp.isoformat(),
            'recommendations': self.recommendations,
            'risk_factors': self.risk_factors
        }


@dataclass
class MarketData:
    """Market data model"""
    symbol: str
    timestamp: datetime
    price: float
    volume: float
    bid: float
    ask: float
    high: float
    low: float
    open: float
    close: float
    source: str = "unknown"
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def spread(self) -> float:
        """Get bid-ask spread"""
        return self.ask - self.bid
    
    @property
    def spread_percentage(self) -> float:
        """Get bid-ask spread as percentage"""
        return (self.spread / self.price * 100) if self.price > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'symbol': self.symbol,
            'timestamp': self.timestamp.isoformat(),
            'price': self.price,
            'volume': self.volume,
            'bid': self.bid,
            'ask': self.ask,
            'high': self.high,
            'low': self.low,
            'open': self.open,
            'close': self.close,
            'source': self.source,
            'metadata': self.metadata
        }


@dataclass
class PerformanceMetric:
    """Performance metric data model"""
    id: str
    component: str
    metric_name: str
    metric_value: float
    timestamp: datetime = field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'component': self.component,
            'metric_name': self.metric_name,
            'metric_value': self.metric_value,
            'timestamp': self.timestamp.isoformat(),
            'metadata': self.metadata
        }


@dataclass
class LearningInsight:
    """Learning insight data model"""
    id: str
    component: str
    insight_type: str
    description: str
    confidence: float
    impact_estimate: float
    recommended_actions: List[str]
    supporting_data: Dict[str, Any]
    timestamp: datetime = field(default_factory=datetime.utcnow)
    applied: bool = False
    effectiveness_score: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'component': self.component,
            'insight_type': self.insight_type,
            'description': self.description,
            'confidence': self.confidence,
            'impact_estimate': self.impact_estimate,
            'recommended_actions': self.recommended_actions,
            'supporting_data': self.supporting_data,
            'timestamp': self.timestamp.isoformat(),
            'applied': self.applied,
            'effectiveness_score': self.effectiveness_score
        }


# Utility functions for data model operations

def serialize_model(model) -> str:
    """Serialize data model to JSON string"""
    if hasattr(model, 'to_dict'):
        return json.dumps(model.to_dict(), default=str)
    else:
        return json.dumps(model, default=str)


def deserialize_strategy(data: Dict[str, Any]) -> Strategy:
    """Deserialize strategy from dictionary"""
    return Strategy.from_dict(data)


def create_order_from_signal(signal: Dict[str, Any], strategy_id: str) -> Order:
    """Create order from trading signal"""
    return Order(
        id=f"order_{int(datetime.utcnow().timestamp() * 1000)}",
        strategy_id=strategy_id,
        symbol=signal['symbol'],
        side=OrderSide(signal['action']),
        order_type=OrderType(signal.get('order_type', 'market')),
        quantity=signal['quantity'],
        price=signal.get('price'),
        metadata={'signal_confidence': signal.get('confidence', 0.5)}
    )


def calculate_portfolio_metrics(portfolio: Portfolio) -> Dict[str, float]:
    """Calculate comprehensive portfolio metrics"""
    if not portfolio.positions:
        return {}
    
    total_value = portfolio.total_value
    total_pnl = portfolio.total_pnl
    
    metrics = {
        'total_value': total_value,
        'cash_percentage': (portfolio.cash / total_value * 100) if total_value > 0 else 0,
        'invested_percentage': (portfolio.invested_value / total_value * 100) if total_value > 0 else 0,
        'total_pnl': total_pnl,
        'total_pnl_percentage': (total_pnl / (total_value - total_pnl) * 100) if (total_value - total_pnl) > 0 else 0,
        'position_count': portfolio.position_count,
        'largest_position_percentage': max(
            (pos.market_value / total_value * 100 for pos in portfolio.positions.values()),
            default=0
        ) if total_value > 0 else 0
    }
    
    return metrics


def validate_order(order: Order) -> List[str]:
    """Validate order data and return list of errors"""
    errors = []
    
    if not order.symbol:
        errors.append("Symbol is required")
    
    if order.quantity <= 0:
        errors.append("Quantity must be positive")
    
    if order.order_type in [OrderType.LIMIT, OrderType.STOP_LIMIT] and order.price is None:
        errors.append("Price is required for limit orders")
    
    if order.order_type in [OrderType.STOP, OrderType.STOP_LIMIT, OrderType.TRAILING_STOP] and order.stop_price is None:
        errors.append("Stop price is required for stop orders")
    
    return errors


def validate_strategy(strategy: Strategy) -> List[str]:
    """Validate strategy data and return list of errors"""
    errors = []
    
    if not strategy.name:
        errors.append("Strategy name is required")
    
    if not strategy.type:
        errors.append("Strategy type is required")
    
    if not strategy.parameters:
        errors.append("Strategy parameters are required")
    
    return errors

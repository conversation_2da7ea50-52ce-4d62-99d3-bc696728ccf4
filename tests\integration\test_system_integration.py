"""
System Integration Tests
"""

import pytest
import pytest_asyncio
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

from main import TradingSystem
from tests.fixtures import TestFixtures


class TestSystemIntegration:
    """Integration tests for the complete trading system"""
    
    @pytest.fixture
    async def test_config(self):
        return {
            'testing': True,
            'database': {
                'postgres': {
                    'host': 'localhost',
                    'port': 5432,
                    'database': 'test_trading_agents',
                    'username': 'test_user',
                    'password': 'test_password'
                },
                'redis': {
                    'host': 'localhost',
                    'port': 6379,
                    'database': 1
                }
            },
            'api': {
                'host': '0.0.0.0',
                'port': 8001,
                'debug': True
            },
            'agents': {
                'max_agents': 5,
                'heartbeat_interval': 5
            },
            'risk': {
                'max_position_size': 0.1,
                'max_daily_loss': 0.05
            },
            'execution': {
                'enabled': True,
                'paper_trading': True
            }
        }
    
    @pytest.fixture
    async def test_fixtures(self, test_config):
        fixtures = TestFixtures(test_config)
        await fixtures.initialize()
        return fixtures
    
    @pytest_asyncio.mark.asyncio
    async def test_trading_system_initialization(self, test_config):
        """Test complete trading system initialization"""
        # Mock external dependencies
        with patch('asyncpg.connect'), \
             patch('redis.Redis'), \
             patch('ollama.AsyncClient'):
            
            trading_system = TradingSystem(config=test_config)
            
            # Test initialization
            result = await trading_system.initialize()
            
            assert result is True
            assert trading_system.initialized is True
            
            # Verify core components are initialized
            assert trading_system.agent_manager is not None
            assert trading_system.strategy_manager is not None
            assert trading_system.risk_manager is not None
            assert trading_system.execution_engine is not None
            assert trading_system.portfolio_manager is not None
    
    @pytest_asyncio.mark.asyncio
    async def test_trading_system_start_stop(self, test_config):
        """Test trading system start and stop sequence"""
        with patch('asyncpg.connect'), \
             patch('redis.Redis'), \
             patch('ollama.AsyncClient'):
            
            trading_system = TradingSystem(config=test_config)
            await trading_system.initialize()
            
            # Test start
            result = await trading_system.start()
            assert result is True
            assert trading_system.running is True
            
            # Test stop
            result = await trading_system.stop()
            assert result is True
            assert trading_system.running is False
    
    @pytest_asyncio.mark.asyncio
    async def test_component_communication(self, test_config, test_fixtures):
        """Test communication between system components"""
        with patch('asyncpg.connect'), \
             patch('redis.Redis'), \
             patch('ollama.AsyncClient'):
            
            trading_system = TradingSystem(config=test_config)
            await trading_system.initialize()
            
            # Mock component interactions
            mock_agent_manager = AsyncMock()
            mock_strategy_manager = AsyncMock()
            mock_risk_manager = AsyncMock()
            
            trading_system.agent_manager = mock_agent_manager
            trading_system.strategy_manager = mock_strategy_manager
            trading_system.risk_manager = mock_risk_manager
            
            await trading_system.start()
            
            # Verify components are started
            mock_agent_manager.start.assert_called_once()
            mock_strategy_manager.start.assert_called_once()
            mock_risk_manager.start.assert_called_once()
    
    @pytest_asyncio.mark.asyncio
    async def test_api_system_integration(self, test_config):
        """Test API server integration with trading system"""
        with patch('asyncpg.connect'), \
             patch('redis.Redis'), \
             patch('ollama.AsyncClient'):
            
            trading_system = TradingSystem(config=test_config)
            await trading_system.initialize()
            
            # Verify API server is initialized
            assert trading_system.api_server is not None
            assert trading_system.api_server.initialized is True
            
            # Test API server has reference to trading system
            assert trading_system.api_server.trading_system is trading_system
    
    @pytest_asyncio.mark.asyncio
    async def test_database_integration(self, test_config):
        """Test database integration across components"""
        with patch('asyncpg.connect') as mock_pg_connect, \
             patch('redis.Redis') as mock_redis:
            
            # Setup mock database connections
            mock_pg_conn = AsyncMock()
            mock_pg_connect.return_value = mock_pg_conn
            
            mock_redis_conn = Mock()
            mock_redis.return_value = mock_redis_conn
            
            trading_system = TradingSystem(config=test_config)
            await trading_system.initialize()
            
            # Verify database coordinator is initialized
            assert trading_system.database_coordinator is not None
            assert trading_system.database_coordinator.initialized is True
    
    @pytest_asyncio.mark.asyncio
    async def test_event_system_integration(self, test_config):
        """Test event system integration across components"""
        with patch('asyncpg.connect'), \
             patch('redis.Redis'), \
             patch('ollama.AsyncClient'):
            
            trading_system = TradingSystem(config=test_config)
            await trading_system.initialize()
            
            # Test that system coordinator is available
            if hasattr(trading_system, 'system_coordinator'):
                assert trading_system.system_coordinator is not None
    
    @pytest_asyncio.mark.asyncio
    async def test_monitoring_integration(self, test_config):
        """Test monitoring system integration"""
        with patch('asyncpg.connect'), \
             patch('redis.Redis'), \
             patch('ollama.AsyncClient'):
            
            trading_system = TradingSystem(config=test_config)
            await trading_system.initialize()
            
            # Test system status retrieval
            status = await trading_system.get_system_status()
            
            assert isinstance(status, dict)
            assert 'status' in status or 'uptime_seconds' in status
    
    @pytest_asyncio.mark.asyncio
    async def test_error_handling_integration(self, test_config):
        """Test error handling across system components"""
        with patch('asyncpg.connect') as mock_connect:
            # Simulate database connection failure
            mock_connect.side_effect = Exception("Database connection failed")
            
            trading_system = TradingSystem(config=test_config)
            
            # System should handle initialization failure gracefully
            result = await trading_system.initialize()
            
            # Depending on implementation, this might return False or raise exception
            # The key is that it should handle the error gracefully
            assert result is False or isinstance(result, bool)
    
    @pytest_asyncio.mark.asyncio
    async def test_configuration_propagation(self, test_config):
        """Test that configuration is properly propagated to all components"""
        with patch('asyncpg.connect'), \
             patch('redis.Redis'), \
             patch('ollama.AsyncClient'):
            
            trading_system = TradingSystem(config=test_config)
            await trading_system.initialize()
            
            # Verify components receive configuration
            if trading_system.agent_manager:
                assert hasattr(trading_system.agent_manager, 'config')
            
            if trading_system.api_server:
                assert hasattr(trading_system.api_server, 'config')
    
    @pytest_asyncio.mark.asyncio
    async def test_graceful_shutdown(self, test_config):
        """Test graceful system shutdown"""
        with patch('asyncpg.connect'), \
             patch('redis.Redis'), \
             patch('ollama.AsyncClient'):
            
            trading_system = TradingSystem(config=test_config)
            await trading_system.initialize()
            await trading_system.start()
            
            # Test graceful shutdown
            result = await trading_system.stop()
            
            assert result is True
            assert trading_system.running is False
    
    @pytest_asyncio.mark.asyncio
    async def test_component_health_checks(self, test_config):
        """Test health checks across all components"""
        with patch('asyncpg.connect'), \
             patch('redis.Redis'), \
             patch('ollama.AsyncClient'):
            
            trading_system = TradingSystem(config=test_config)
            await trading_system.initialize()
            
            # Mock component health checks
            components = [
                'agent_manager',
                'strategy_manager', 
                'risk_manager',
                'execution_engine',
                'portfolio_manager',
                'database_coordinator',
                'api_server'
            ]
            
            for component_name in components:
                component = getattr(trading_system, component_name, None)
                if component:
                    # Verify component has health check capability
                    assert hasattr(component, 'running') or hasattr(component, 'initialized')


class TestWorkflowIntegration:
    """Integration tests for complete trading workflows"""
    
    @pytest.fixture
    async def test_config(self):
        return {
            'testing': True,
            'execution': {'paper_trading': True},
            'risk': {'max_position_size': 0.1}
        }
    
    @pytest_asyncio.mark.asyncio
    async def test_market_data_to_decision_workflow(self, test_config):
        """Test complete workflow from market data to trading decision"""
        with patch('asyncpg.connect'), \
             patch('redis.Redis'), \
             patch('ollama.AsyncClient'):
            
            trading_system = TradingSystem(config=test_config)
            await trading_system.initialize()
            
            # Mock the workflow components
            mock_market_data = {
                'symbol': 'AAPL',
                'price': 150.0,
                'volume': 1000000,
                'timestamp': datetime.now()
            }
            
            # This would test the complete workflow:
            # 1. Market data received
            # 2. Agents analyze data
            # 3. Strategy generates signals
            # 4. Risk manager validates
            # 5. Execution engine executes
            
            # For now, just verify system can handle the workflow
            assert trading_system.initialized is True
    
    @pytest_asyncio.mark.asyncio
    async def test_order_execution_workflow(self, test_config):
        """Test complete order execution workflow"""
        with patch('asyncpg.connect'), \
             patch('redis.Redis'), \
             patch('ollama.AsyncClient'):
            
            trading_system = TradingSystem(config=test_config)
            await trading_system.initialize()
            
            # Mock order execution workflow
            mock_order = {
                'symbol': 'AAPL',
                'side': 'buy',
                'quantity': 100,
                'order_type': 'market'
            }
            
            # This would test:
            # 1. Order validation
            # 2. Risk checks
            # 3. Execution
            # 4. Portfolio update
            # 5. Reporting
            
            assert trading_system.initialized is True
    
    @pytest_asyncio.mark.asyncio
    async def test_portfolio_rebalancing_workflow(self, test_config):
        """Test portfolio rebalancing workflow"""
        with patch('asyncpg.connect'), \
             patch('redis.Redis'), \
             patch('ollama.AsyncClient'):
            
            trading_system = TradingSystem(config=test_config)
            await trading_system.initialize()
            
            # This would test:
            # 1. Portfolio analysis
            # 2. Rebalancing strategy
            # 3. Order generation
            # 4. Risk validation
            # 5. Execution
            # 6. Monitoring
            
            assert trading_system.initialized is True

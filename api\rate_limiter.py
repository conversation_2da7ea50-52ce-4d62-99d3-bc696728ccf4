"""
Rate Limiter - API rate limiting and throttling
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tu<PERSON>
from dataclasses import dataclass
from collections import defaultdict, deque
from datetime import datetime, timedelta
from fastapi import HTTPException, status, Request

logger = logging.getLogger(__name__)


@dataclass
class RateLimit:
    """Rate limit configuration"""
    requests_per_minute: int
    requests_per_hour: int
    requests_per_day: int
    burst_limit: int
    window_size: int = 60  # seconds


@dataclass
class ClientStats:
    """Client rate limiting statistics"""
    client_id: str
    total_requests: int
    requests_last_minute: int
    requests_last_hour: int
    requests_last_day: int
    first_request: datetime
    last_request: datetime
    blocked_requests: int
    rate_limit_tier: str


class RateLimiter:
    """
    Advanced rate limiting system for API endpoints.
    
    Features:
    - Multiple time window rate limiting
    - Per-client and per-endpoint limits
    - Burst protection
    - Adaptive rate limiting
    - Rate limit tiers based on user roles
    - Whitelist and blacklist support
    - Rate limit statistics and monitoring
    - Graceful degradation
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.rate_config = config.get('rate_limiting', {})
        
        # Rate limit tiers
        self.rate_limits = {
            'admin': RateLimit(
                requests_per_minute=1000,
                requests_per_hour=10000,
                requests_per_day=100000,
                burst_limit=100
            ),
            'trader': RateLimit(
                requests_per_minute=500,
                requests_per_hour=5000,
                requests_per_day=50000,
                burst_limit=50
            ),
            'analyst': RateLimit(
                requests_per_minute=300,
                requests_per_hour=3000,
                requests_per_day=30000,
                burst_limit=30
            ),
            'viewer': RateLimit(
                requests_per_minute=100,
                requests_per_hour=1000,
                requests_per_day=10000,
                burst_limit=20
            ),
            'anonymous': RateLimit(
                requests_per_minute=50,
                requests_per_hour=500,
                requests_per_day=5000,
                burst_limit=10
            )
        }
        
        # Client tracking
        self.client_requests: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self.client_stats: Dict[str, ClientStats] = {}
        self.blocked_clients: Dict[str, datetime] = {}
        
        # Whitelist and blacklist
        self.whitelist: set = set(self.rate_config.get('whitelist', []))
        self.blacklist: set = set(self.rate_config.get('blacklist', []))
        
        # Configuration
        self.enabled = self.rate_config.get('enabled', True)
        self.block_duration = self.rate_config.get('block_duration', 300)  # 5 minutes
        self.adaptive_limiting = self.rate_config.get('adaptive_limiting', True)
        
        # Monitoring
        self.total_requests = 0
        self.blocked_requests = 0
        self.rate_limit_hits = defaultdict(int)
        
        # State
        self.initialized = False
    
    async def initialize(self) -> bool:
        """Initialize rate limiter"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Rate Limiter...")
            
            # Start cleanup task
            asyncio.create_task(self._cleanup_loop())
            
            self.initialized = True
            logger.info("✓ Rate Limiter initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Rate Limiter: {e}")
            return False
    
    async def check_rate_limit(self, request: Request) -> bool:
        """Check if request is within rate limits"""
        if not self.enabled:
            return True
            
        try:
            # Get client identifier
            client_id = await self._get_client_id(request)
            
            # Check blacklist
            if client_id in self.blacklist:
                self.blocked_requests += 1
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Client is blacklisted"
                )
            
            # Check whitelist
            if client_id in self.whitelist:
                await self._record_request(client_id, 'admin')
                return True
            
            # Check if client is currently blocked
            if client_id in self.blocked_clients:
                block_time = self.blocked_clients[client_id]
                if datetime.now() - block_time < timedelta(seconds=self.block_duration):
                    self.blocked_requests += 1
                    raise HTTPException(
                        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                        detail=f"Client blocked for {self.block_duration} seconds",
                        headers={"Retry-After": str(self.block_duration)}
                    )
                else:
                    # Remove expired block
                    del self.blocked_clients[client_id]
            
            # Get rate limit tier
            tier = await self._get_rate_limit_tier(request)
            rate_limit = self.rate_limits[tier]
            
            # Check rate limits
            if not await self._check_limits(client_id, rate_limit):
                # Block client
                self.blocked_clients[client_id] = datetime.now()
                self.blocked_requests += 1
                self.rate_limit_hits[tier] += 1
                
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="Rate limit exceeded",
                    headers={
                        "Retry-After": "60",
                        "X-RateLimit-Limit": str(rate_limit.requests_per_minute),
                        "X-RateLimit-Remaining": "0",
                        "X-RateLimit-Reset": str(int(time.time()) + 60)
                    }
                )
            
            # Record successful request
            await self._record_request(client_id, tier)
            self.total_requests += 1
            
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error checking rate limit: {e}")
            return True  # Allow on error
    
    async def get_rate_limit_status(self, request: Request) -> Dict[str, Any]:
        """Get current rate limit status for client"""
        try:
            client_id = await self._get_client_id(request)
            tier = await self._get_rate_limit_tier(request)
            rate_limit = self.rate_limits[tier]
            
            # Calculate current usage
            current_time = time.time()
            requests = self.client_requests[client_id]
            
            # Count requests in different windows
            minute_requests = sum(1 for req_time in requests if current_time - req_time <= 60)
            hour_requests = sum(1 for req_time in requests if current_time - req_time <= 3600)
            day_requests = sum(1 for req_time in requests if current_time - req_time <= 86400)
            
            # Calculate remaining
            remaining_minute = max(0, rate_limit.requests_per_minute - minute_requests)
            remaining_hour = max(0, rate_limit.requests_per_hour - hour_requests)
            remaining_day = max(0, rate_limit.requests_per_day - day_requests)
            
            # Calculate reset times
            reset_minute = int(current_time) + (60 - int(current_time) % 60)
            reset_hour = int(current_time) + (3600 - int(current_time) % 3600)
            reset_day = int(current_time) + (86400 - int(current_time) % 86400)
            
            return {
                'client_id': client_id,
                'tier': tier,
                'limits': {
                    'requests_per_minute': rate_limit.requests_per_minute,
                    'requests_per_hour': rate_limit.requests_per_hour,
                    'requests_per_day': rate_limit.requests_per_day,
                    'burst_limit': rate_limit.burst_limit
                },
                'usage': {
                    'requests_last_minute': minute_requests,
                    'requests_last_hour': hour_requests,
                    'requests_last_day': day_requests
                },
                'remaining': {
                    'minute': remaining_minute,
                    'hour': remaining_hour,
                    'day': remaining_day
                },
                'reset': {
                    'minute': reset_minute,
                    'hour': reset_hour,
                    'day': reset_day
                },
                'blocked': client_id in self.blocked_clients
            }
            
        except Exception as e:
            logger.error(f"Error getting rate limit status: {e}")
            return {}
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get rate limiting statistics"""
        try:
            # Calculate active clients
            current_time = time.time()
            active_clients = 0
            
            for requests in self.client_requests.values():
                if requests and current_time - requests[-1] <= 300:  # Active in last 5 minutes
                    active_clients += 1
            
            # Get tier distribution
            tier_distribution = {}
            for client_id, stats in self.client_stats.items():
                tier = stats.rate_limit_tier
                tier_distribution[tier] = tier_distribution.get(tier, 0) + 1
            
            return {
                'enabled': self.enabled,
                'total_requests': self.total_requests,
                'blocked_requests': self.blocked_requests,
                'active_clients': active_clients,
                'blocked_clients': len(self.blocked_clients),
                'whitelist_size': len(self.whitelist),
                'blacklist_size': len(self.blacklist),
                'tier_distribution': tier_distribution,
                'rate_limit_hits': dict(self.rate_limit_hits),
                'block_rate': (self.blocked_requests / max(1, self.total_requests)) * 100
            }
            
        except Exception as e:
            logger.error(f"Error getting rate limiting statistics: {e}")
            return {}
    
    # Private methods
    
    async def _get_client_id(self, request: Request) -> str:
        """Get client identifier from request"""
        try:
            # Try to get user ID from authentication
            if hasattr(request.state, 'user'):
                return f"user_{request.state.user.user_id}"
            
            # Try to get API key
            api_key = request.headers.get('X-API-Key')
            if api_key:
                return f"api_{api_key[:8]}"
            
            # Fall back to IP address
            client_ip = request.client.host if request.client else "unknown"
            
            # Consider X-Forwarded-For header
            forwarded_for = request.headers.get('X-Forwarded-For')
            if forwarded_for:
                client_ip = forwarded_for.split(',')[0].strip()
            
            return f"ip_{client_ip}"
            
        except Exception as e:
            logger.error(f"Error getting client ID: {e}")
            return "unknown"
    
    async def _get_rate_limit_tier(self, request: Request) -> str:
        """Get rate limit tier for request"""
        try:
            # Check if user is authenticated
            if hasattr(request.state, 'user'):
                user = request.state.user
                
                # Determine tier based on user roles
                if 'admin' in user.roles:
                    return 'admin'
                elif 'trader' in user.roles:
                    return 'trader'
                elif 'analyst' in user.roles:
                    return 'analyst'
                elif 'viewer' in user.roles:
                    return 'viewer'
            
            # Check for API key
            api_key = request.headers.get('X-API-Key')
            if api_key:
                # API keys get trader tier by default
                return 'trader'
            
            # Anonymous users
            return 'anonymous'
            
        except Exception as e:
            logger.error(f"Error getting rate limit tier: {e}")
            return 'anonymous'
    
    async def _check_limits(self, client_id: str, rate_limit: RateLimit) -> bool:
        """Check if client is within rate limits"""
        try:
            current_time = time.time()
            requests = self.client_requests[client_id]
            
            # Count requests in different windows
            minute_requests = sum(1 for req_time in requests if current_time - req_time <= 60)
            hour_requests = sum(1 for req_time in requests if current_time - req_time <= 3600)
            day_requests = sum(1 for req_time in requests if current_time - req_time <= 86400)
            
            # Check burst limit (requests in last 10 seconds)
            burst_requests = sum(1 for req_time in requests if current_time - req_time <= 10)
            
            # Check all limits
            if minute_requests >= rate_limit.requests_per_minute:
                return False
            if hour_requests >= rate_limit.requests_per_hour:
                return False
            if day_requests >= rate_limit.requests_per_day:
                return False
            if burst_requests >= rate_limit.burst_limit:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking limits for {client_id}: {e}")
            return True  # Allow on error
    
    async def _record_request(self, client_id: str, tier: str):
        """Record request for client"""
        try:
            current_time = time.time()
            
            # Add request timestamp
            self.client_requests[client_id].append(current_time)
            
            # Update client stats
            if client_id not in self.client_stats:
                self.client_stats[client_id] = ClientStats(
                    client_id=client_id,
                    total_requests=0,
                    requests_last_minute=0,
                    requests_last_hour=0,
                    requests_last_day=0,
                    first_request=datetime.now(),
                    last_request=datetime.now(),
                    blocked_requests=0,
                    rate_limit_tier=tier
                )
            
            stats = self.client_stats[client_id]
            stats.total_requests += 1
            stats.last_request = datetime.now()
            stats.rate_limit_tier = tier
            
            # Update window counts
            requests = self.client_requests[client_id]
            stats.requests_last_minute = sum(1 for req_time in requests if current_time - req_time <= 60)
            stats.requests_last_hour = sum(1 for req_time in requests if current_time - req_time <= 3600)
            stats.requests_last_day = sum(1 for req_time in requests if current_time - req_time <= 86400)
            
        except Exception as e:
            logger.error(f"Error recording request for {client_id}: {e}")
    
    async def _cleanup_loop(self):
        """Background cleanup task"""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                
                current_time = time.time()
                
                # Clean old request records
                for client_id, requests in list(self.client_requests.items()):
                    # Keep only requests from last day
                    while requests and current_time - requests[0] > 86400:
                        requests.popleft()
                    
                    # Remove empty deques
                    if not requests:
                        del self.client_requests[client_id]
                
                # Clean old client stats
                for client_id, stats in list(self.client_stats.items()):
                    # Remove stats for clients inactive for more than 7 days
                    if (datetime.now() - stats.last_request).days > 7:
                        del self.client_stats[client_id]
                
                # Clean expired blocks
                for client_id, block_time in list(self.blocked_clients.items()):
                    if datetime.now() - block_time > timedelta(seconds=self.block_duration):
                        del self.blocked_clients[client_id]
                
                logger.debug("Rate limiter cleanup completed")
                
            except Exception as e:
                logger.error(f"Error in rate limiter cleanup: {e}")
                await asyncio.sleep(300)

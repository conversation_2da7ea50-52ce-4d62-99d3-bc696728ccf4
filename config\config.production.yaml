# Production Environment Configuration
# Advanced Ollama Trading Agent System

system:
  environment: production
  log_level: WARNING
  debug: false
  metrics_enabled: true
  profiling_enabled: false

# Database Configuration
database:
  postgres:
    host: "${DB_HOST:localhost}"
    port: "${DB_PORT:5432}"
    database: "${DB_NAME:trading_prod}"
    username: "${DB_USER:trading_user}"
    password: "${DB_PASSWORD}"
    pool_size: 20
    max_overflow: 30
    pool_timeout: 30
    pool_recycle: 3600
    ssl_mode: require
    
  redis:
    host: "${REDIS_HOST:localhost}"
    port: "${REDIS_PORT:6379}"
    db: "${REDIS_DB:0}"
    password: "${REDIS_PASSWORD}"
    ssl: true
    max_connections: 50

# API Configuration
api:
  host: "0.0.0.0"
  port: "${API_PORT:8000}"
  workers: 4
  max_request_size: 10485760  # 10MB
  request_timeout: 300
  cors_enabled: false
  rate_limiting:
    enabled: true
    requests_per_minute: 1000
    burst_size: 100

# Security Configuration
security:
  jwt_secret: "${JWT_SECRET}"
  jwt_expiry: 3600  # 1 hour
  api_key_required: true
  encryption_enabled: true
  audit_logging: true
  ip_whitelist:
    - "10.0.0.0/8"
    - "**********/12"
    - "***********/16"

# Ollama Configuration
ollama:
  base_url: "${OLLAMA_URL:http://ollama-cluster:11434}"
  timeout: 300
  max_retries: 3
  retry_delay: 5
  connection_pool_size: 10
  load_balancing: true
  health_check_interval: 30

# Market Data Configuration
market_data:
  providers:
    alpha_vantage:
      api_key: "${ALPHA_VANTAGE_API_KEY}"
      rate_limit: 5  # requests per minute
      timeout: 30
    
    polygon:
      api_key: "${POLYGON_API_KEY}"
      rate_limit: 100
      timeout: 30
    
    iex_cloud:
      api_key: "${IEX_CLOUD_API_KEY}"
      rate_limit: 100
      timeout: 30
  
  redis:
    host: "${REDIS_HOST:localhost}"
    port: "${REDIS_PORT:6379}"
    db: 1
    ttl: 300  # 5 minutes
  
  update_interval: 5  # seconds
  symbols:
    - "AAPL"
    - "GOOGL"
    - "MSFT"
    - "TSLA"
    - "AMZN"
    - "META"
    - "NVDA"
    - "NFLX"

# Broker Configuration
brokers:
  providers:
    interactive_brokers:
      host: "${IB_HOST:localhost}"
      port: "${IB_PORT:7497}"
      client_id: "${IB_CLIENT_ID:1}"
      timeout: 30
      
    alpaca:
      api_key: "${ALPACA_API_KEY}"
      secret_key: "${ALPACA_SECRET_KEY}"
      base_url: "https://api.alpaca.markets"
      paper_trading: false
  
  routing:
    default: "interactive_brokers"
    symbols:
      "AAPL": "interactive_brokers"
      "GOOGL": "alpaca"

# Agent Configuration
agents:
  max_agents: 100
  heartbeat_interval: 30
  max_memory_usage: **********  # 1GB per agent
  max_cpu_usage: 0.8
  
  defaults:
    model: "llama2:13b"
    temperature: 0.3  # Lower temperature for production
    max_tokens: 2048
    timeout: 300
    
  scaling:
    auto_scaling: true
    min_agents: 5
    max_agents: 50
    scale_up_threshold: 0.8
    scale_down_threshold: 0.3
    cooldown_period: 300

# Strategy Configuration
strategies:
  max_active_strategies: 20
  default_capital_allocation: 0.05  # 5% per strategy
  rebalance_frequency: "hourly"
  
  risk_limits:
    max_drawdown: 0.10  # 10%
    max_leverage: 2.0
    position_timeout: 86400  # 24 hours
    
  performance_tracking:
    benchmark: "SPY"
    lookback_period: 252  # trading days
    min_sharpe_ratio: 1.0

# Risk Management
risk:
  max_portfolio_risk: 0.015  # 1.5% VaR
  max_position_size: 0.05    # 5% of portfolio
  max_sector_exposure: 0.25  # 25% per sector
  max_drawdown: 0.10         # 10%
  
  var_calculation:
    confidence_level: 0.99
    lookback_period: 252
    monte_carlo_simulations: 10000
    
  position_limits:
    max_positions: 30
    max_orders_per_minute: 100
    max_notional_per_order: 1000000  # $1M
    
  circuit_breakers:
    daily_loss_limit: 0.05     # 5%
    portfolio_heat: 0.8        # 80%
    correlation_limit: 0.7     # 70%

# Execution Configuration
execution:
  paper_trading: false
  max_orders_per_second: 50
  order_timeout: 300
  slippage_model: "linear"
  
  order_management:
    smart_routing: true
    dark_pools: true
    iceberg_orders: true
    time_weighted: true
    
  latency_targets:
    market_data: 10    # milliseconds
    order_entry: 50    # milliseconds
    execution: 100     # milliseconds

# Portfolio Configuration
portfolio:
  initial_capital: ********.0  # $10M
  rebalance_threshold: 0.02    # 2%
  max_positions: 50
  cash_target: 0.05            # 5% cash
  
  optimization:
    method: "mean_variance"
    lookback_period: 252
    rebalance_frequency: "daily"
    transaction_costs: 0.001    # 0.1%

# Monitoring Configuration
monitoring:
  metrics:
    collection_interval: 10  # seconds
    retention_period: 2592000  # 30 days
    
  alerts:
    email_enabled: true
    slack_enabled: true
    webhook_enabled: true
    
    thresholds:
      cpu_usage: 0.8
      memory_usage: 0.8
      disk_usage: 0.9
      error_rate: 0.05
      
  logging:
    level: "WARNING"
    format: "json"
    rotation: "daily"
    retention: 30  # days
    
    destinations:
      - type: "file"
        path: "/var/log/trading/app.log"
      - type: "elasticsearch"
        host: "${ELASTICSEARCH_HOST}"
        index: "trading-logs"

# Performance Configuration
performance:
  caching:
    enabled: true
    backend: "redis"
    default_ttl: 300
    
  connection_pooling:
    database: 20
    redis: 50
    http: 100
    
  async_processing:
    max_workers: 10
    queue_size: 1000
    batch_size: 100

# Backup Configuration
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention: 30  # days
  
  destinations:
    - type: "s3"
      bucket: "${BACKUP_S3_BUCKET}"
      region: "${AWS_REGION:us-east-1}"
    - type: "local"
      path: "/backup/trading"

# Compliance Configuration
compliance:
  enabled: true
  reporting:
    daily_reports: true
    monthly_reports: true
    regulatory_reports: true
    
  audit_trail:
    enabled: true
    retention: 2555  # 7 years in days
    encryption: true
    
  position_limits:
    enabled: true
    max_single_position: 0.05
    max_sector_exposure: 0.25
    
# Notification Configuration
notifications:
  email:
    smtp_host: "${SMTP_HOST}"
    smtp_port: "${SMTP_PORT:587}"
    username: "${SMTP_USER}"
    password: "${SMTP_PASSWORD}"
    from_address: "<EMAIL>"
    
  slack:
    webhook_url: "${SLACK_WEBHOOK_URL}"
    channel: "#trading-alerts"
    
  webhook:
    url: "${WEBHOOK_URL}"
    timeout: 30
    retries: 3

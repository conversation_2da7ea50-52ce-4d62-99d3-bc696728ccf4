"""
Configuration Package - Comprehensive Configuration Management

This package provides advanced configuration management capabilities for the
Advanced Ollama Trading Agent System with support for multiple environments,
secrets management, validation, and dynamic updates.

Components:
- ConfigManager: Basic configuration management
- AdvancedConfigManager: Enhanced configuration with encryption and environments
- Configuration schemas for validation
- Environment-specific configuration files
- Secrets management with encryption

Features:
- Multi-format configuration files (YAML, JSON, ENV)
- Environment-specific configurations (development, testing, staging, production)
- Configuration validation with JSON Schema
- Secrets management with encryption
- Dynamic configuration updates and hot reloading
- Configuration templates and variable substitution
- Environment variable override support
- Configuration versioning and backup
- Audit logging for configuration changes
"""

from .config_manager import (
    ConfigManager,
    AdvancedConfigManager,
    ConfigValidationResult,
    get_config_manager,
    load_system_config
)

__all__ = [
    'ConfigManager',
    'AdvancedConfigManager', 
    'ConfigValidationResult',
    'get_config_manager',
    'load_system_config'
]

__version__ = '1.0.0'
__author__ = 'Advanced Ollama Trading Agents Team'
__description__ = 'Comprehensive Configuration Management System'

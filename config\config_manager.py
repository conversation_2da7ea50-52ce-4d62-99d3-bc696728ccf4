"""
Configuration Manager - Comprehensive configuration management system
"""

import os
import yaml
import json
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
import jsonschema
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ConfigValidationResult:
    """Configuration validation result"""
    valid: bool
    errors: List[str]
    warnings: List[str]


class ConfigManager:
    """
    Comprehensive configuration management system.
    
    Features:
    - Multi-format configuration support (YAML, JSON)
    - Configuration validation and schema checking
    - Environment variable substitution
    - Configuration merging and inheritance
    - Dynamic configuration updates
    - Configuration versioning and backup
    """
    
    def __init__(self, config_path: str = "config/system_config.yaml"):
        self.config_path = Path(config_path)
        self.config: Dict[str, Any] = {}
        self.schema: Optional[Dict[str, Any]] = None
        self.config_history: List[Dict[str, Any]] = []
        
        # Configuration directories
        self.config_dir = self.config_path.parent
        self.schema_dir = self.config_dir / "schemas"
        self.backup_dir = self.config_dir / "backups"
        
        # Ensure directories exist
        self.config_dir.mkdir(exist_ok=True)
        self.schema_dir.mkdir(exist_ok=True)
        self.backup_dir.mkdir(exist_ok=True)
    
    async def load_config(self) -> Dict[str, Any]:
        """Load configuration from file"""
        try:
            if not self.config_path.exists():
                logger.warning(f"Configuration file not found: {self.config_path}")
                await self._create_default_config()
            
            # Load main configuration
            with open(self.config_path, 'r') as f:
                if self.config_path.suffix.lower() == '.yaml':
                    self.config = yaml.safe_load(f)
                elif self.config_path.suffix.lower() == '.json':
                    self.config = json.load(f)
                else:
                    raise ValueError(f"Unsupported configuration format: {self.config_path.suffix}")
            
            # Substitute environment variables
            self.config = self._substitute_env_vars(self.config)
            
            # Load and merge additional configurations
            await self._load_additional_configs()
            
            # Validate configuration
            validation_result = await self.validate_config()
            if not validation_result.valid:
                logger.error(f"Configuration validation failed: {validation_result.errors}")
                raise ValueError("Invalid configuration")
            
            if validation_result.warnings:
                for warning in validation_result.warnings:
                    logger.warning(f"Configuration warning: {warning}")
            
            # Backup current configuration
            await self._backup_config()
            
            logger.info(f"✓ Configuration loaded from {self.config_path}")
            return self.config
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise
    
    async def save_config(self, config: Optional[Dict[str, Any]] = None) -> bool:
        """Save configuration to file"""
        try:
            config_to_save = config or self.config
            
            # Backup current configuration
            await self._backup_config()
            
            # Save configuration
            with open(self.config_path, 'w') as f:
                if self.config_path.suffix.lower() == '.yaml':
                    yaml.dump(config_to_save, f, default_flow_style=False, indent=2)
                elif self.config_path.suffix.lower() == '.json':
                    json.dump(config_to_save, f, indent=2)
            
            self.config = config_to_save
            logger.info(f"✓ Configuration saved to {self.config_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            return False
    
    async def validate_config(self, config: Optional[Dict[str, Any]] = None) -> ConfigValidationResult:
        """Validate configuration against schema"""
        try:
            config_to_validate = config or self.config
            errors = []
            warnings = []
            
            # Load schema if not already loaded
            if self.schema is None:
                await self._load_schema()
            
            # Validate against schema if available
            if self.schema:
                try:
                    jsonschema.validate(config_to_validate, self.schema)
                except jsonschema.ValidationError as e:
                    errors.append(f"Schema validation error: {e.message}")
                except jsonschema.SchemaError as e:
                    errors.append(f"Schema error: {e.message}")
            
            # Custom validation rules
            custom_validation = await self._custom_validation(config_to_validate)
            errors.extend(custom_validation['errors'])
            warnings.extend(custom_validation['warnings'])
            
            return ConfigValidationResult(
                valid=len(errors) == 0,
                errors=errors,
                warnings=warnings
            )
            
        except Exception as e:
            logger.error(f"Error validating configuration: {e}")
            return ConfigValidationResult(
                valid=False,
                errors=[str(e)],
                warnings=[]
            )
    
    async def get_config(self, key_path: str = None) -> Any:
        """Get configuration value by key path"""
        try:
            if key_path is None:
                return self.config.copy()
            
            keys = key_path.split('.')
            value = self.config
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return None
            
            return value
            
        except Exception as e:
            logger.error(f"Error getting configuration for key '{key_path}': {e}")
            return None
    
    async def set_config(self, key_path: str, value: Any, save: bool = True) -> bool:
        """Set configuration value by key path"""
        try:
            keys = key_path.split('.')
            config_ref = self.config
            
            # Navigate to parent of target key
            for key in keys[:-1]:
                if key not in config_ref:
                    config_ref[key] = {}
                config_ref = config_ref[key]
            
            # Set the value
            config_ref[keys[-1]] = value
            
            # Validate updated configuration
            validation_result = await self.validate_config()
            if not validation_result.valid:
                logger.error(f"Configuration update validation failed: {validation_result.errors}")
                return False
            
            # Save if requested
            if save:
                await self.save_config()
            
            logger.info(f"✓ Configuration updated: {key_path} = {value}")
            return True
            
        except Exception as e:
            logger.error(f"Error setting configuration for key '{key_path}': {e}")
            return False
    
    async def merge_config(self, additional_config: Dict[str, Any], save: bool = True) -> bool:
        """Merge additional configuration"""
        try:
            self.config = self._deep_merge(self.config, additional_config)
            
            # Validate merged configuration
            validation_result = await self.validate_config()
            if not validation_result.valid:
                logger.error(f"Merged configuration validation failed: {validation_result.errors}")
                return False
            
            # Save if requested
            if save:
                await self.save_config()
            
            logger.info("✓ Configuration merged successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error merging configuration: {e}")
            return False
    
    # Private methods
    
    async def _create_default_config(self):
        """Create default configuration file"""
        try:
            default_config = {
                "system": {
                    "name": "Advanced Ollama Trading Agent System",
                    "version": "1.0.0",
                    "environment": "development",
                    "log_level": "INFO"
                },
                "ollama": {
                    "base_url": "http://localhost:11434",
                    "timeout": 300,
                    "max_retries": 3
                },
                "agents": {
                    "max_agents": 50,
                    "heartbeat_interval": 30,
                    "defaults": {
                        "model": "llama2",
                        "temperature": 0.7,
                        "max_tokens": 2048
                    }
                },
                "market_data": {
                    "providers": ["alpha_vantage", "yahoo_finance"],
                    "update_interval": 60,
                    "symbols": ["AAPL", "GOOGL", "MSFT", "TSLA"]
                },
                "strategies": {
                    "max_active_strategies": 10,
                    "default_capital_allocation": 0.1,
                    "rebalance_frequency": "daily"
                },
                "risk": {
                    "max_portfolio_risk": 0.02,
                    "max_position_size": 0.1,
                    "max_drawdown": 0.15,
                    "var_confidence": 0.95
                },
                "execution": {
                    "paper_trading": True,
                    "max_orders_per_second": 10,
                    "order_timeout": 300
                },
                "portfolio": {
                    "initial_capital": 100000.0,
                    "rebalance_threshold": 0.05,
                    "max_positions": 20
                }
            }
            
            await self.save_config(default_config)
            logger.info(f"✓ Default configuration created: {self.config_path}")
            
        except Exception as e:
            logger.error(f"Error creating default configuration: {e}")
            raise
    
    async def _load_schema(self):
        """Load configuration schema"""
        try:
            schema_path = self.schema_dir / "system_config_schema.json"
            if schema_path.exists():
                with open(schema_path, 'r') as f:
                    self.schema = json.load(f)
                logger.info(f"✓ Configuration schema loaded: {schema_path}")
            else:
                logger.warning(f"Configuration schema not found: {schema_path}")
                
        except Exception as e:
            logger.error(f"Error loading configuration schema: {e}")
    
    async def _load_additional_configs(self):
        """Load additional configuration files"""
        try:
            # Load environment-specific configuration
            env = self.config.get('system', {}).get('environment', 'development')
            env_config_path = self.config_dir / f"{env}_config.yaml"
            
            if env_config_path.exists():
                with open(env_config_path, 'r') as f:
                    env_config = yaml.safe_load(f)
                self.config = self._deep_merge(self.config, env_config)
                logger.info(f"✓ Environment configuration loaded: {env_config_path}")
            
            # Load local overrides
            local_config_path = self.config_dir / "local_config.yaml"
            if local_config_path.exists():
                with open(local_config_path, 'r') as f:
                    local_config = yaml.safe_load(f)
                self.config = self._deep_merge(self.config, local_config)
                logger.info(f"✓ Local configuration loaded: {local_config_path}")
                
        except Exception as e:
            logger.error(f"Error loading additional configurations: {e}")
    
    def _substitute_env_vars(self, config: Any) -> Any:
        """Substitute environment variables in configuration"""
        if isinstance(config, dict):
            return {key: self._substitute_env_vars(value) for key, value in config.items()}
        elif isinstance(config, list):
            return [self._substitute_env_vars(item) for item in config]
        elif isinstance(config, str) and config.startswith('${') and config.endswith('}'):
            env_var = config[2:-1]
            default_value = None
            
            if ':' in env_var:
                env_var, default_value = env_var.split(':', 1)
            
            return os.getenv(env_var, default_value)
        else:
            return config
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """Deep merge two dictionaries"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    async def _custom_validation(self, config: Dict[str, Any]) -> Dict[str, List[str]]:
        """Custom validation rules"""
        errors = []
        warnings = []
        
        try:
            # Validate portfolio configuration
            portfolio_config = config.get('portfolio', {})
            initial_capital = portfolio_config.get('initial_capital', 0)
            if initial_capital <= 0:
                errors.append("Portfolio initial_capital must be positive")
            
            # Validate risk configuration
            risk_config = config.get('risk', {})
            max_portfolio_risk = risk_config.get('max_portfolio_risk', 0)
            if max_portfolio_risk <= 0 or max_portfolio_risk > 1:
                errors.append("Risk max_portfolio_risk must be between 0 and 1")
            
            # Validate execution configuration
            execution_config = config.get('execution', {})
            if execution_config.get('paper_trading', True) is False:
                warnings.append("Live trading is enabled - ensure proper risk controls")
            
            # Validate Ollama configuration
            ollama_config = config.get('ollama', {})
            base_url = ollama_config.get('base_url', '')
            if not base_url.startswith('http'):
                errors.append("Ollama base_url must be a valid HTTP URL")
            
        except Exception as e:
            errors.append(f"Custom validation error: {str(e)}")
        
        return {'errors': errors, 'warnings': warnings}
    
    async def _backup_config(self):
        """Backup current configuration"""
        try:
            if self.config_path.exists():
                import shutil
                from datetime import datetime

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = self.backup_dir / f"config_backup_{timestamp}.yaml"

                shutil.copy2(self.config_path, backup_path)

                # Keep only last 10 backups
                backups = sorted(self.backup_dir.glob("config_backup_*.yaml"))
                if len(backups) > 10:
                    for old_backup in backups[:-10]:
                        old_backup.unlink()

                return str(backup_path)
            else:
                return None

        except Exception as e:
            logger.error(f"Error backing up configuration: {e}")
            return None


# Enhanced Configuration Manager with additional features
class AdvancedConfigManager(ConfigManager):
    """
    Enhanced configuration manager with additional enterprise features.

    Additional Features:
    - Secrets management with encryption
    - Environment-specific configurations
    - Configuration templates
    - Hot reloading
    - Configuration versioning
    - Audit logging
    """

    def __init__(self, config_path: str = "config/system_config.yaml", environment: str = None):
        super().__init__(config_path)

        # Environment detection
        self.environment = environment or os.getenv('TRADING_ENV', 'development')

        # Encryption for secrets
        self.encryption_key = None
        self.cipher_suite = None

        # Configuration watching
        self.watch_enabled = False
        self.last_modified = None

        # Audit logging
        self.audit_log = []

        # Initialize encryption
        self._setup_encryption()

    def _setup_encryption(self):
        """Setup encryption for secrets management"""
        try:
            from cryptography.fernet import Fernet

            key_file = self.config_dir / ".encryption_key"

            if key_file.exists():
                with open(key_file, 'rb') as f:
                    self.encryption_key = f.read()
            else:
                # Generate new key
                self.encryption_key = Fernet.generate_key()
                with open(key_file, 'wb') as f:
                    f.write(self.encryption_key)
                os.chmod(key_file, 0o600)  # Restrict permissions

            self.cipher_suite = Fernet(self.encryption_key)
            logger.info("✓ Encryption initialized for secrets management")

        except ImportError:
            logger.warning("Cryptography library not available - secrets encryption disabled")
        except Exception as e:
            logger.error(f"Failed to setup encryption: {e}")

    async def load_secrets(self) -> Dict[str, Any]:
        """Load encrypted secrets configuration"""
        try:
            secrets_file = self.config_dir / "secrets.yaml.enc"

            if not secrets_file.exists():
                logger.info("No secrets file found")
                return {}

            if not self.cipher_suite:
                logger.error("Encryption not available for secrets")
                return {}

            # Decrypt and load secrets
            with open(secrets_file, 'rb') as f:
                encrypted_data = f.read()

            decrypted_data = self.cipher_suite.decrypt(encrypted_data)
            secrets = yaml.safe_load(decrypted_data.decode('utf-8'))

            # Merge secrets into main configuration
            self.config = self._deep_merge(self.config, secrets)

            logger.info("✓ Secrets loaded and merged")
            return secrets

        except Exception as e:
            logger.error(f"Failed to load secrets: {e}")
            return {}

    async def save_secrets(self, secrets: Dict[str, Any]) -> bool:
        """Save encrypted secrets configuration"""
        try:
            if not self.cipher_suite:
                logger.error("Encryption not available for secrets")
                return False

            secrets_file = self.config_dir / "secrets.yaml.enc"

            # Encrypt and save secrets
            secrets_yaml = yaml.dump(secrets, default_flow_style=False)
            encrypted_data = self.cipher_suite.encrypt(secrets_yaml.encode('utf-8'))

            with open(secrets_file, 'wb') as f:
                f.write(encrypted_data)

            logger.info("✓ Secrets saved with encryption")
            return True

        except Exception as e:
            logger.error(f"Failed to save secrets: {e}")
            return False

    async def load_environment_config(self) -> Dict[str, Any]:
        """Load environment-specific configuration"""
        try:
            env_config_file = self.config_dir / f"config.{self.environment}.yaml"

            if not env_config_file.exists():
                logger.info(f"No environment-specific config found for: {self.environment}")
                return {}

            with open(env_config_file, 'r') as f:
                env_config = yaml.safe_load(f)

            # Merge environment config
            self.config = self._deep_merge(self.config, env_config)

            logger.info(f"✓ Environment configuration loaded: {self.environment}")
            return env_config

        except Exception as e:
            logger.error(f"Failed to load environment config: {e}")
            return {}

    async def create_config_template(self, template_name: str) -> bool:
        """Create configuration template"""
        try:
            template_dir = self.config_dir / "templates"
            template_dir.mkdir(exist_ok=True)

            template_file = template_dir / f"{template_name}.yaml"

            # Create template with placeholders
            template_config = {
                "system": {
                    "name": "{{SYSTEM_NAME}}",
                    "environment": "{{ENVIRONMENT}}",
                    "version": "{{VERSION}}"
                },
                "database": {
                    "host": "{{DB_HOST}}",
                    "port": "{{DB_PORT}}",
                    "name": "{{DB_NAME}}"
                },
                "api": {
                    "host": "{{API_HOST}}",
                    "port": "{{API_PORT}}"
                }
            }

            with open(template_file, 'w') as f:
                yaml.dump(template_config, f, default_flow_style=False, indent=2)

            logger.info(f"✓ Configuration template created: {template_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to create config template: {e}")
            return False

    async def apply_template(self, template_name: str, variables: Dict[str, str]) -> bool:
        """Apply configuration template with variables"""
        try:
            template_dir = self.config_dir / "templates"
            template_file = template_dir / f"{template_name}.yaml"

            if not template_file.exists():
                logger.error(f"Template not found: {template_name}")
                return False

            # Load template
            with open(template_file, 'r') as f:
                template_content = f.read()

            # Substitute variables
            for key, value in variables.items():
                template_content = template_content.replace(f"{{{{{key}}}}}", str(value))

            # Parse substituted content
            template_config = yaml.safe_load(template_content)

            # Merge with current configuration
            self.config = self._deep_merge(self.config, template_config)

            logger.info(f"✓ Template applied: {template_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to apply template: {e}")
            return False

    def enable_hot_reload(self, check_interval: int = 5):
        """Enable hot reloading of configuration"""
        import asyncio
        import time

        self.watch_enabled = True
        self.last_modified = self.config_path.stat().st_mtime if self.config_path.exists() else 0

        async def watch_config():
            while self.watch_enabled:
                try:
                    if self.config_path.exists():
                        current_modified = self.config_path.stat().st_mtime

                        if current_modified > self.last_modified:
                            logger.info("Configuration file changed, reloading...")
                            await self.load_config()
                            self.last_modified = current_modified

                    await asyncio.sleep(check_interval)

                except Exception as e:
                    logger.error(f"Error in config watcher: {e}")
                    await asyncio.sleep(check_interval)

        # Start watching in background
        asyncio.create_task(watch_config())
        logger.info(f"✓ Hot reload enabled (check interval: {check_interval}s)")

    def disable_hot_reload(self):
        """Disable hot reloading"""
        self.watch_enabled = False
        logger.info("Hot reload disabled")

    async def get_config_info(self) -> Dict[str, Any]:
        """Get comprehensive configuration information"""
        try:
            config_files = []

            # Main config file
            if self.config_path.exists():
                stat = self.config_path.stat()
                config_files.append({
                    'name': 'main',
                    'path': str(self.config_path),
                    'size': stat.st_size,
                    'modified': stat.st_mtime,
                    'exists': True
                })

            # Environment config
            env_config_path = self.config_dir / f"config.{self.environment}.yaml"
            if env_config_path.exists():
                stat = env_config_path.stat()
                config_files.append({
                    'name': f'environment_{self.environment}',
                    'path': str(env_config_path),
                    'size': stat.st_size,
                    'modified': stat.st_mtime,
                    'exists': True
                })

            # Secrets file
            secrets_path = self.config_dir / "secrets.yaml.enc"
            if secrets_path.exists():
                stat = secrets_path.stat()
                config_files.append({
                    'name': 'secrets',
                    'path': str(secrets_path),
                    'size': stat.st_size,
                    'modified': stat.st_mtime,
                    'exists': True,
                    'encrypted': True
                })

            return {
                'environment': self.environment,
                'config_directory': str(self.config_dir),
                'hot_reload_enabled': self.watch_enabled,
                'encryption_available': self.cipher_suite is not None,
                'config_files': config_files,
                'validation_schema_available': self.schema is not None,
                'backup_directory': str(self.backup_dir)
            }

        except Exception as e:
            logger.error(f"Error getting config info: {e}")
            return {}

    async def export_config_for_environment(self, target_env: str,
                                          include_secrets: bool = False) -> str:
        """Export configuration for specific environment"""
        try:
            # Create environment-specific configuration
            env_config = self.config.copy()

            # Update environment
            if 'system' not in env_config:
                env_config['system'] = {}
            env_config['system']['environment'] = target_env

            # Remove secrets if not included
            if not include_secrets:
                env_config = self._remove_sensitive_data(env_config)

            # Export as YAML
            return yaml.dump(env_config, default_flow_style=False, indent=2)

        except Exception as e:
            logger.error(f"Error exporting config for environment: {e}")
            return ""

    def _remove_sensitive_data(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Remove sensitive data from configuration"""
        sensitive_keys = ['password', 'secret', 'key', 'token', 'api_key', 'private_key']

        def clean_dict(d):
            if isinstance(d, dict):
                return {
                    k: '***REDACTED***' if any(sensitive in k.lower() for sensitive in sensitive_keys)
                    else clean_dict(v)
                    for k, v in d.items()
                }
            elif isinstance(d, list):
                return [clean_dict(item) for item in d]
            else:
                return d

        return clean_dict(config)


# Global configuration manager instance
config_manager = None


def get_config_manager(config_path: str = None, environment: str = None) -> AdvancedConfigManager:
    """Get global configuration manager instance"""
    global config_manager

    if config_manager is None:
        config_path = config_path or "config/system_config.yaml"
        config_manager = AdvancedConfigManager(config_path, environment)

    return config_manager


async def load_system_config() -> Dict[str, Any]:
    """Load system configuration"""
    manager = get_config_manager()
    config = await manager.load_config()

    # Load additional configurations
    await manager.load_environment_config()
    await manager.load_secrets()

    return config

"""
Team Manager - Advanced team coordination and management system
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Optional, Any
from enum import Enum
import json

from agents.base_agent import Agent<PERSON><PERSON>
from communication.message_types import MessageType, create_analysis_request
from .team_formation_engine import TeamFormationEngine
from .hierarchical_structures import HierarchicalTeamStructure
from .decision_protocols import DecisionProtocols
from .collaboration_framework import CollaborationFramework
from .team_performance_evaluation import TeamPerformanceEvaluation

logger = logging.getLogger(__name__)


class TeamType(Enum):
    """Types of trading teams"""
    MOMENTUM_TEAM = "momentum_team"
    MEAN_REVERSION_TEAM = "mean_reversion_team"
    ARBITRAGE_TEAM = "arbitrage_team"
    RISK_MANAGEMENT_TEAM = "risk_management_team"
    RESEARCH_TEAM = "research_team"


class TeamStatus(Enum):
    """Team status states"""
    FORMING = "forming"
    ACTIVE = "active"
    PAUSED = "paused"
    DISSOLVING = "dissolving"
    DISSOLVED = "dissolved"


class TeamManager:
    """
    Advanced team coordination and management system.
    Handles dynamic team formation, coordination, and performance optimization.
    """

    def __init__(self, agent_manager, message_broker, market_data_manager, config: Dict[str, Any]):
        self.agent_manager = agent_manager
        self.message_broker = message_broker
        self.market_data_manager = market_data_manager
        self.config = config

        # Team management state
        self.teams: Dict[str, Dict[str, Any]] = {}
        self.team_templates: Dict[TeamType, Dict[str, Any]] = {}
        self.team_performance: Dict[str, Dict[str, Any]] = {}

        # Coordination state
        self.active_missions: Dict[str, Dict[str, Any]] = {}
        self.team_communications: Dict[str, List[Dict[str, Any]]] = {}

        # Advanced team components
        self.team_formation_engine: Optional[TeamFormationEngine] = None
        self.hierarchical_structure: Optional[HierarchicalTeamStructure] = None
        self.decision_protocols: Optional[DecisionProtocols] = None
        self.collaboration_framework: Optional[CollaborationFramework] = None
        self.performance_evaluation: Optional[TeamPerformanceEvaluation] = None

        # State
        self.initialized = False
        self.running = False

    async def initialize(self):
        """Initialize the team manager"""
        if self.initialized:
            return

        logger.info("Initializing Advanced Team Manager...")

        # Setup team templates
        await self._setup_team_templates()

        # Initialize coordination protocols
        await self._setup_coordination_protocols()

        # Initialize advanced team components
        await self._initialize_advanced_components()

        self.initialized = True
        logger.info("✓ Advanced Team Manager initialized")

    async def _setup_team_templates(self):
        """Setup team templates for different trading strategies"""
        self.team_templates = {
            TeamType.MOMENTUM_TEAM: {
                'required_roles': [
                    AgentRole.TEAM_LEADER,
                    AgentRole.MARKET_ANALYST,
                    AgentRole.STRATEGY_DEVELOPER,
                    AgentRole.EXECUTION_SPECIALIST
                ],
                'optional_roles': [AgentRole.RISK_MANAGER],
                'coordination_style': 'fast_execution',
                'decision_threshold': 0.7,
                'max_team_size': 5
            },
            TeamType.MEAN_REVERSION_TEAM: {
                'required_roles': [
                    AgentRole.TEAM_LEADER,
                    AgentRole.MARKET_ANALYST,
                    AgentRole.RISK_MANAGER,
                    AgentRole.EXECUTION_SPECIALIST
                ],
                'optional_roles': [AgentRole.STRATEGY_DEVELOPER],
                'coordination_style': 'careful_analysis',
                'decision_threshold': 0.8,
                'max_team_size': 4
            },
            TeamType.ARBITRAGE_TEAM: {
                'required_roles': [
                    AgentRole.MARKET_ANALYST,
                    AgentRole.EXECUTION_SPECIALIST,
                    AgentRole.RISK_MANAGER
                ],
                'optional_roles': [AgentRole.TEAM_LEADER],
                'coordination_style': 'ultra_fast',
                'decision_threshold': 0.9,
                'max_team_size': 3
            },
            TeamType.RISK_MANAGEMENT_TEAM: {
                'required_roles': [
                    AgentRole.RISK_MANAGER,
                    AgentRole.PERFORMANCE_EVALUATOR,
                    AgentRole.MARKET_ANALYST
                ],
                'optional_roles': [AgentRole.TEAM_LEADER],
                'coordination_style': 'careful_analysis',
                'decision_threshold': 0.9,
                'max_team_size': 4
            }
        }

    async def _setup_coordination_protocols(self):
        """Setup team coordination protocols"""
        self.coordination_protocols = {
            'fast_execution': {
                'decision_timeout': 30,  # seconds
                'consensus_required': False,
                'parallel_processing': True
            },
            'careful_analysis': {
                'decision_timeout': 300,  # 5 minutes
                'consensus_required': True,
                'parallel_processing': False
            },
            'ultra_fast': {
                'decision_timeout': 5,  # 5 seconds
                'consensus_required': False,
                'parallel_processing': True
            }
        }

    async def _initialize_advanced_components(self):
        """Initialize advanced team management components"""
        # Initialize Team Formation Engine
        self.team_formation_engine = TeamFormationEngine(self.agent_manager, self.config)
        await self.team_formation_engine.initialize()

        # Initialize Hierarchical Structure
        self.hierarchical_structure = HierarchicalTeamStructure(self.config)
        await self.hierarchical_structure.initialize()

        # Initialize Decision Protocols
        self.decision_protocols = DecisionProtocols(self.hierarchical_structure, self.config)
        await self.decision_protocols.initialize()

        # Initialize Collaboration Framework
        self.collaboration_framework = CollaborationFramework(
            self, self.decision_protocols, self.config
        )
        await self.collaboration_framework.initialize()

        # Initialize Performance Evaluation
        self.performance_evaluation = TeamPerformanceEvaluation(
            self.collaboration_framework, self.decision_protocols,
            self.hierarchical_structure, self.config
        )
        await self.performance_evaluation.initialize()

        logger.info("✓ Advanced team components initialized")

    async def start(self):
        """Start the team manager"""
        if not self.initialized:
            await self.initialize()

        if self.running:
            return

        logger.info("Starting Advanced Team Manager...")
        self.running = True
        logger.info("✓ Advanced Team Manager started")

    async def stop(self):
        """Stop the team manager"""
        if not self.running:
            return

        logger.info("Stopping Advanced Team Manager...")
        self.running = False

        # Dissolve all active teams
        for team_id in list(self.teams.keys()):
            await self.dissolve_team(team_id)

        logger.info("✓ Advanced Team Manager stopped")

    async def create_team(self, team_type: TeamType, mission: Dict[str, Any]) -> Optional[str]:
        """Create a new trading team"""
        try:
            team_id = f"team_{team_type.value}_{int(time.time())}"
            template = self.team_templates.get(team_type)

            if not template:
                logger.error(f"No template found for team type: {team_type}")
                return None

            # Get available agents
            available_agents = await self._get_available_agents(template['required_roles'])

            if len(available_agents) < len(template['required_roles']):
                logger.warning(f"Insufficient agents for team {team_type}")
                return None

            # Create team
            team = {
                'team_id': team_id,
                'team_type': team_type,
                'status': TeamStatus.FORMING,
                'members': available_agents,
                'mission': mission,
                'template': template,
                'created_at': time.time(),
                'performance_metrics': {}
            }

            self.teams[team_id] = team

            # Notify team members
            await self._notify_team_formation(team_id, team)

            # Activate team
            await self._activate_team(team_id)

            logger.info(f"✓ Created team {team_id} of type {team_type.value}")
            return team_id

        except Exception as e:
            logger.error(f"Error creating team: {e}")
            return None

    async def _get_available_agents(self, required_roles: List[AgentRole]) -> List[str]:
        """Get available agents for team formation with intelligent selection"""
        available_agents = []

        for role in required_roles:
            agents = await self.agent_manager.get_agents_by_role(role)
            if agents:
                # Select the best available agent of this role based on performance
                best_agent = await self._select_best_agent_for_role(agents, role)
                if best_agent:
                    available_agents.append(best_agent.agent_id)
                else:
                    # Fallback to first available
                    available_agents.append(agents[0].agent_id)

        return available_agents

    async def _select_best_agent_for_role(self, agents: List, role: AgentRole):
        """Select the best agent for a role based on performance metrics"""
        try:
            best_agent = None
            best_score = -1

            for agent in agents:
                # Calculate performance score
                metrics = agent.metrics

                # Performance score based on success rate and response time
                success_rate = (metrics['completed_tasks'] /
                              max(metrics['total_tasks'], 1))

                # Penalize slow response times
                response_penalty = min(metrics['avg_response_time'] / 10.0, 1.0)

                # Calculate overall score (0-1 scale)
                score = success_rate * (1 - response_penalty * 0.3)

                if score > best_score:
                    best_score = score
                    best_agent = agent

            return best_agent

        except Exception as e:
            logger.error(f"Error selecting best agent for role {role}: {e}")
            return agents[0] if agents else None

    async def _notify_team_formation(self, team_id: str, team: Dict[str, Any]):
        """Notify agents about team formation"""
        try:
            from communication.message_types import Message, MessageType, MessagePriority

            formation_message = {
                'action': 'team_formation',
                'team_id': team_id,
                'team_type': team['team_type'].value,
                'mission': team['mission'],
                'members': team['members'],
                'coordination_style': team['template']['coordination_style'],
                'timestamp': time.time()
            }

            for agent_id in team['members']:
                # Create and send team formation notification
                message = Message(
                    id=f"team_formation_{team_id}_{agent_id}",
                    type=MessageType.TEAM_NOTIFICATION,
                    sender="team_manager",
                    recipient=agent_id,
                    content=formation_message,
                    timestamp=time.time(),
                    priority=MessagePriority.HIGH
                )

                await self.message_broker.publish(message)
                logger.debug(f"Notified agent {agent_id} about team formation {team_id}")

        except Exception as e:
            logger.error(f"Error notifying team formation for {team_id}: {e}")

    async def _activate_team(self, team_id: str):
        """Activate a team"""
        if team_id not in self.teams:
            return

        team = self.teams[team_id]
        team['status'] = TeamStatus.ACTIVE
        team['activated_at'] = time.time()

        logger.info(f"✓ Activated team {team_id}")

    async def dissolve_team(self, team_id: str) -> bool:
        """Dissolve a team"""
        if team_id not in self.teams:
            return False

        team = self.teams[team_id]
        team['status'] = TeamStatus.DISSOLVING

        # Notify team members
        await self._notify_team_dissolution(team_id, team)

        # Clean up team resources
        team['status'] = TeamStatus.DISSOLVED
        team['dissolved_at'] = time.time()

        logger.info(f"✓ Dissolved team {team_id}")
        return True

    async def _notify_team_dissolution(self, team_id: str, team: Dict[str, Any]):
        """Notify agents about team dissolution"""
        for agent_id in team['members']:
            # Send team dissolution notification
            pass

    async def coordinate_team_mission(self, team_id: str, mission_data: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate a team mission"""
        if team_id not in self.teams:
            return {'success': False, 'error': 'Team not found'}

        team = self.teams[team_id]
        mission_id = f"mission_{team_id}_{int(time.time())}"

        # Create mission
        mission = {
            'mission_id': mission_id,
            'team_id': team_id,
            'mission_data': mission_data,
            'status': 'active',
            'started_at': time.time(),
            'coordination_style': team['template']['coordination_style']
        }

        self.active_missions[mission_id] = mission

        # Coordinate based on team style
        result = await self._execute_coordination_style(mission)

        return result

    async def _execute_coordination_style(self, mission: Dict[str, Any]) -> Dict[str, Any]:
        """Execute mission based on coordination style"""
        style = mission['coordination_style']
        protocol = self.coordination_protocols.get(style, {})

        if style == 'ultra_fast':
            return await self._ultra_fast_coordination(mission)
        elif style == 'fast_execution':
            return await self._fast_execution_coordination(mission)
        elif style == 'careful_analysis':
            return await self._careful_analysis_coordination(mission)
        else:
            return {'success': False, 'error': 'Unknown coordination style'}

    async def _ultra_fast_coordination(self, mission: Dict[str, Any]) -> Dict[str, Any]:
        """Ultra-fast coordination for arbitrage teams"""
        try:
            start_time = time.time()
            team_id = mission['team_id']
            team = self.teams[team_id]

            # Ultra-fast parallel execution
            tasks = []
            for member in team['members']:
                task = {
                    'type': 'ultra_fast_analysis',
                    'data': mission['mission_data'],
                    'timeout': 0.05  # 50ms timeout
                }
                tasks.append(self._send_task_to_agent(member, task))

            # Wait for all responses with very short timeout
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Quick consensus - first valid response wins
            for result in results:
                if isinstance(result, dict) and result.get('success'):
                    execution_time = time.time() - start_time
                    return {
                        'success': True,
                        'coordination_type': 'ultra_fast',
                        'execution_time': execution_time,
                        'result': result
                    }

            return {'success': False, 'error': 'No valid responses in time'}

        except Exception as e:
            logger.error(f"Ultra-fast coordination failed: {e}")
            return {'success': False, 'error': str(e)}

    async def _fast_execution_coordination(self, mission: Dict[str, Any]) -> Dict[str, Any]:
        """Fast execution coordination for momentum teams"""
        try:
            start_time = time.time()
            team_id = mission['team_id']
            team = self.teams[team_id]

            # Fast sequential execution with quick consensus
            results = []

            # Step 1: Market analysis (parallel)
            analysis_tasks = []
            for member in team['members']:
                if 'analyst' in member.lower():
                    task = {
                        'type': 'market_analysis',
                        'data': mission['mission_data'],
                        'timeout': 1.0
                    }
                    analysis_tasks.append(self._send_task_to_agent(member, task))

            if analysis_tasks:
                analysis_results = await asyncio.gather(*analysis_tasks, return_exceptions=True)
                results.extend([r for r in analysis_results if isinstance(r, dict)])

            # Step 2: Strategy development (if analysis successful)
            if results:
                strategy_tasks = []
                for member in team['members']:
                    if 'strategy' in member.lower():
                        task = {
                            'type': 'strategy_development',
                            'data': {'analysis': results[0], 'mission': mission['mission_data']},
                            'timeout': 1.0
                        }
                        strategy_tasks.append(self._send_task_to_agent(member, task))

                if strategy_tasks:
                    strategy_results = await asyncio.gather(*strategy_tasks, return_exceptions=True)
                    results.extend([r for r in strategy_results if isinstance(r, dict)])

            execution_time = time.time() - start_time
            return {
                'success': len(results) > 0,
                'coordination_type': 'fast_execution',
                'execution_time': execution_time,
                'results': results
            }

        except Exception as e:
            logger.error(f"Fast execution coordination failed: {e}")
            return {'success': False, 'error': str(e)}

    async def _careful_analysis_coordination(self, mission: Dict[str, Any]) -> Dict[str, Any]:
        """Careful analysis coordination for mean reversion teams"""
        try:
            start_time = time.time()
            team_id = mission['team_id']
            team = self.teams[team_id]

            # Careful sequential analysis with consensus building
            coordination_results = {
                'analysis_phase': None,
                'risk_assessment': None,
                'strategy_phase': None,
                'consensus': None
            }

            # Phase 1: Detailed market analysis
            analysis_tasks = []
            for member in team['members']:
                if 'analyst' in member.lower():
                    task = {
                        'type': 'detailed_analysis',
                        'data': mission['mission_data'],
                        'timeout': 10.0
                    }
                    analysis_tasks.append(self._send_task_to_agent(member, task))

            if analysis_tasks:
                analysis_results = await asyncio.gather(*analysis_tasks, return_exceptions=True)
                coordination_results['analysis_phase'] = [r for r in analysis_results if isinstance(r, dict)]

            # Phase 2: Risk assessment
            if coordination_results['analysis_phase']:
                risk_tasks = []
                for member in team['members']:
                    if 'risk' in member.lower():
                        task = {
                            'type': 'risk_assessment',
                            'data': {
                                'analysis': coordination_results['analysis_phase'],
                                'mission': mission['mission_data']
                            },
                            'timeout': 10.0
                        }
                        risk_tasks.append(self._send_task_to_agent(member, task))

                if risk_tasks:
                    risk_results = await asyncio.gather(*risk_tasks, return_exceptions=True)
                    coordination_results['risk_assessment'] = [r for r in risk_results if isinstance(r, dict)]

            # Phase 3: Strategy development with risk constraints
            if coordination_results['risk_assessment']:
                strategy_tasks = []
                for member in team['members']:
                    if 'strategy' in member.lower():
                        task = {
                            'type': 'constrained_strategy',
                            'data': {
                                'analysis': coordination_results['analysis_phase'],
                                'risk_constraints': coordination_results['risk_assessment'],
                                'mission': mission['mission_data']
                            },
                            'timeout': 10.0
                        }
                        strategy_tasks.append(self._send_task_to_agent(member, task))

                if strategy_tasks:
                    strategy_results = await asyncio.gather(*strategy_tasks, return_exceptions=True)
                    coordination_results['strategy_phase'] = [r for r in strategy_results if isinstance(r, dict)]

            # Phase 4: Team consensus
            if coordination_results['strategy_phase']:
                consensus_result = await self._build_team_consensus(team, coordination_results)
                coordination_results['consensus'] = consensus_result

            execution_time = time.time() - start_time
            return {
                'success': coordination_results['consensus'] is not None,
                'coordination_type': 'careful_analysis',
                'execution_time': execution_time,
                'coordination_results': coordination_results
            }

        except Exception as e:
            logger.error(f"Careful analysis coordination failed: {e}")
            return {'success': False, 'error': str(e)}

    async def get_active_teams(self) -> List[Dict[str, Any]]:
        """Get all active teams"""
        active_teams = []
        for team_id, team in self.teams.items():
            if team['status'] == TeamStatus.ACTIVE:
                active_teams.append(team)
        return active_teams

    async def get_team_performance(self, team_id: str) -> Dict[str, Any]:
        """Get team performance metrics"""
        if team_id not in self.teams:
            return {}

        return self.team_performance.get(team_id, {})

    async def optimize_team_composition(self, team_id: str) -> Dict[str, Any]:
        """Optimize team composition based on performance"""
        if team_id not in self.teams:
            return {'success': False, 'error': 'Team not found'}

        # Implement team optimization logic
        return {'success': True, 'optimization': 'team_composition_optimized'}

    async def pause_all_teams(self) -> bool:
        """Pause all active teams"""
        try:
            logger.info("Pausing all teams...")
            paused_count = 0

            for team_id, team in self.teams.items():
                try:
                    if team['status'] == TeamStatus.ACTIVE:
                        team['status'] = TeamStatus.PAUSED

                        # Notify team members about pause
                        await self._notify_team_pause(team_id, team)
                        paused_count += 1
                        logger.debug(f"Paused team {team_id}")

                except Exception as e:
                    logger.error(f"Error pausing team {team_id}: {e}")

            logger.info(f"✅ Paused {paused_count} teams")
            return True

        except Exception as e:
            logger.error(f"Error pausing teams: {e}")
            return False

    async def resume_all_teams(self) -> bool:
        """Resume all paused teams"""
        try:
            logger.info("Resuming all teams...")
            resumed_count = 0

            for team_id, team in self.teams.items():
                try:
                    if team['status'] == TeamStatus.PAUSED:
                        team['status'] = TeamStatus.ACTIVE

                        # Notify team members about resume
                        await self._notify_team_resume(team_id, team)
                        resumed_count += 1
                        logger.debug(f"Resumed team {team_id}")

                except Exception as e:
                    logger.error(f"Error resuming team {team_id}: {e}")

            logger.info(f"✅ Resumed {resumed_count} teams")
            return True

        except Exception as e:
            logger.error(f"Error resuming teams: {e}")
            return False

    async def _notify_team_pause(self, team_id: str, team: Dict[str, Any]):
        """Notify team members about pause"""
        try:
            message_content = {
                'action': 'team_pause',
                'team_id': team_id,
                'timestamp': time.time()
            }

            for member in team.get('members', []):
                await self.message_broker.publish({
                    'type': 'team_notification',
                    'recipient': member,
                    'content': message_content
                })

        except Exception as e:
            logger.error(f"Error notifying team pause for {team_id}: {e}")

    async def _notify_team_resume(self, team_id: str, team: Dict[str, Any]):
        """Notify team members about resume"""
        try:
            message_content = {
                'action': 'team_resume',
                'team_id': team_id,
                'timestamp': time.time()
            }

            for member in team.get('members', []):
                await self.message_broker.publish({
                    'type': 'team_notification',
                    'recipient': member,
                    'content': message_content
                })

        except Exception as e:
            logger.error(f"Error notifying team resume for {team_id}: {e}")

    async def get_team_communication_stats(self) -> Dict[str, Any]:
        """Get team communication statistics"""
        try:
            stats = {
                'total_teams': len(self.teams),
                'active_teams': 0,
                'paused_teams': 0,
                'teams_by_type': {},
                'communication_volume': 0
            }

            for team in self.teams.values():
                # Count by status
                if team['status'] == TeamStatus.ACTIVE:
                    stats['active_teams'] += 1
                elif team['status'] == TeamStatus.PAUSED:
                    stats['paused_teams'] += 1

                # Count by type
                team_type = team['team_type'].value
                stats['teams_by_type'][team_type] = stats['teams_by_type'].get(team_type, 0) + 1

            return stats

        except Exception as e:
            logger.error(f"Error getting team communication stats: {e}")
            return {}

    async def _send_task_to_agent(self, agent_id: str, task: Dict[str, Any]) -> Dict[str, Any]:
        """Send a task to a specific agent and wait for response"""
        try:
            from communication.message_types import Message, MessageType, MessagePriority

            # Create task message
            message = Message(
                id=f"task_{agent_id}_{int(time.time() * 1000)}",
                type=MessageType.TASK_REQUEST,
                sender="team_manager",
                recipient=agent_id,
                content=task,
                timestamp=time.time(),
                priority=MessagePriority.HIGH
            )

            # Send message
            success = await self.message_broker.publish(message)

            if success:
                # Wait for response (simplified - in real implementation would track responses)
                await asyncio.sleep(task.get('timeout', 1.0))

                # Simulate response based on task type
                return {
                    'success': True,
                    'agent_id': agent_id,
                    'task_type': task['type'],
                    'result': f"Completed {task['type']} task",
                    'timestamp': time.time()
                }
            else:
                return {'success': False, 'error': 'Failed to send task'}

        except Exception as e:
            logger.error(f"Error sending task to agent {agent_id}: {e}")
            return {'success': False, 'error': str(e)}

    async def _build_team_consensus(self, team: Dict[str, Any], coordination_results: Dict[str, Any]) -> Dict[str, Any]:
        """Build consensus among team members"""
        try:
            # Collect all results
            all_results = []
            for phase_results in coordination_results.values():
                if isinstance(phase_results, list):
                    all_results.extend(phase_results)

            if not all_results:
                return None

            # Simple consensus: majority vote on success
            successful_results = [r for r in all_results if r.get('success', False)]

            consensus = {
                'consensus_reached': len(successful_results) > len(all_results) / 2,
                'success_rate': len(successful_results) / len(all_results),
                'team_decision': 'proceed' if len(successful_results) > len(all_results) / 2 else 'abort',
                'contributing_agents': len(set(r.get('agent_id') for r in all_results if r.get('agent_id'))),
                'timestamp': time.time()
            }

            return consensus

        except Exception as e:
            logger.error(f"Error building team consensus: {e}")
            return None

    async def get_team_coordination_metrics(self, team_id: str) -> Dict[str, Any]:
        """Get coordination metrics for a specific team"""
        try:
            if team_id not in self.teams:
                return {}

            team = self.teams[team_id]

            # Calculate team metrics
            metrics = {
                'team_id': team_id,
                'team_type': team['team_type'].value,
                'status': team['status'].value,
                'member_count': len(team.get('members', [])),
                'coordination_style': team['template']['coordination_style'],
                'missions_completed': 0,
                'avg_execution_time': 0.0,
                'success_rate': 0.0,
                'last_activity': team.get('activated_at', 0)
            }

            # Get mission history for this team
            team_missions = [m for m in self.active_missions.values() if m['team_id'] == team_id]

            if team_missions:
                completed_missions = [m for m in team_missions if m.get('status') == 'completed']
                metrics['missions_completed'] = len(completed_missions)

                if completed_missions:
                    total_time = sum(m.get('execution_time', 0) for m in completed_missions)
                    metrics['avg_execution_time'] = total_time / len(completed_missions)

                    successful_missions = [m for m in completed_missions if m.get('success', False)]
                    metrics['success_rate'] = len(successful_missions) / len(completed_missions)

            return metrics

        except Exception as e:
            logger.error(f"Error getting team coordination metrics for {team_id}: {e}")
            return {}

    async def rebalance_team(self, team_id: str) -> Dict[str, Any]:
        """Rebalance team composition based on performance"""
        try:
            if team_id not in self.teams:
                return {'success': False, 'error': 'Team not found'}

            team = self.teams[team_id]
            template = team['template']

            # Get current team performance
            metrics = await self.get_team_coordination_metrics(team_id)

            # If performance is poor, try to replace underperforming members
            if metrics.get('success_rate', 1.0) < 0.7:
                logger.info(f"Rebalancing underperforming team {team_id}")

                # Get new agents for required roles
                new_members = await self._get_available_agents(template['required_roles'])

                if len(new_members) >= len(template['required_roles']):
                    # Update team composition
                    old_members = team['members'].copy()
                    team['members'] = new_members

                    # Notify about rebalancing
                    await self._notify_team_rebalance(team_id, old_members, new_members)

                    return {
                        'success': True,
                        'action': 'rebalanced',
                        'old_members': old_members,
                        'new_members': new_members
                    }

            return {'success': True, 'action': 'no_rebalance_needed'}

        except Exception as e:
            logger.error(f"Error rebalancing team {team_id}: {e}")
            return {'success': False, 'error': str(e)}

    async def _notify_team_rebalance(self, team_id: str, old_members: List[str], new_members: List[str]):
        """Notify about team rebalancing"""
        try:
            from communication.message_types import Message, MessageType, MessagePriority

            # Notify old members about removal
            for member in old_members:
                message = Message(
                    id=f"team_removal_{team_id}_{member}",
                    type=MessageType.TEAM_NOTIFICATION,
                    sender="team_manager",
                    recipient=member,
                    content={
                        'action': 'team_removal',
                        'team_id': team_id,
                        'reason': 'rebalancing',
                        'timestamp': time.time()
                    },
                    timestamp=time.time(),
                    priority=MessagePriority.HIGH
                )
                await self.message_broker.publish(message)

            # Notify new members about addition
            for member in new_members:
                message = Message(
                    id=f"team_addition_{team_id}_{member}",
                    type=MessageType.TEAM_NOTIFICATION,
                    sender="team_manager",
                    recipient=member,
                    content={
                        'action': 'team_addition',
                        'team_id': team_id,
                        'reason': 'rebalancing',
                        'timestamp': time.time()
                    },
                    timestamp=time.time(),
                    priority=MessagePriority.HIGH
                )
                await self.message_broker.publish(message)

        except Exception as e:
            logger.error(f"Error notifying team rebalance for {team_id}: {e}")

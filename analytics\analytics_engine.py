"""
Advanced Analytics Engine - Central coordination for all analytics capabilities
"""

import asyncio
import logging
import time
import random
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
import json

# Analytics component imports
from .market_analytics import MarketAnalytics
from .pattern_detector import PatternDetector
from .real_time_analyzer import RealTimeAnalyzer
from .predictive_engine import PredictiveEngine
from .anomaly_detector import AnomalyDetector
from .sentiment_analyzer import SentimentAnalyzer

logger = logging.getLogger(__name__)


@dataclass
class AnalyticsResult:
    """Analytics result data structure"""
    analysis_type: str
    symbol: str
    timestamp: datetime
    confidence: float
    result_data: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MarketInsight:
    """Market insight data structure"""
    insight_id: str
    insight_type: str
    description: str
    confidence: float
    impact_score: float
    supporting_evidence: List[str]
    recommended_actions: List[str]
    expiry_time: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class AdvancedAnalyticsEngine:
    """
    Advanced Analytics Engine - Central coordination for all analytics capabilities.
    
    Features:
    - Real-time market analysis and pattern recognition
    - Multi-timeframe predictive modeling
    - Anomaly detection and alert generation
    - Sentiment analysis and market psychology
    - Cross-asset correlation analysis
    - Market regime detection and adaptation
    - Performance attribution and risk analytics
    - Ensemble decision making and confidence scoring
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.analytics_config = config.get('analytics_engine', {})
        
        # Core analytics components (simplified for testing)
        self.market_analytics = None
        self.pattern_detector = None
        self.real_time_analyzer = None
        self.predictive_engine = None
        self.anomaly_detector = None
        self.sentiment_analyzer = None
        
        # Analytics state
        self.active_symbols: set = set()
        self.analysis_results: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.market_insights: Dict[str, MarketInsight] = {}
        self.correlation_matrix: Dict[str, Dict[str, float]] = {}
        self.market_regime: Dict[str, str] = {}
        
        # Real-time data streams
        self.price_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.volume_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.volatility_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Analytics configuration
        self.analysis_intervals = self.analytics_config.get('analysis_intervals', {
            'real_time': 1,      # seconds
            'short_term': 60,    # seconds
            'medium_term': 300,  # seconds
            'long_term': 3600    # seconds
        })
        
        self.confidence_threshold = self.analytics_config.get('confidence_threshold', 0.7)
        self.max_insights = self.analytics_config.get('max_insights', 100)
        
        # Performance tracking
        self.analytics_metrics = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'insights_generated': 0,
            'average_confidence': 0.0,
            'processing_time_ms': 0.0
        }
        
        # Background tasks
        self.analysis_tasks: List[asyncio.Task] = []
        
        # State
        self.initialized = False
        self.running = False
    
    async def initialize(self) -> bool:
        """Initialize analytics engine"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Advanced Analytics Engine...")
            
            # Initialize core analytics components
            await self._initialize_analytics_components()
            
            # Setup analysis pipelines
            await self._setup_analysis_pipelines()
            
            # Initialize market data streams
            await self._initialize_data_streams()
            
            self.initialized = True
            logger.info("✓ Advanced Analytics Engine initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Advanced Analytics Engine: {e}")
            return False
    
    async def start(self) -> bool:
        """Start analytics engine"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        if self.running:
            return True
            
        try:
            logger.info("Starting Advanced Analytics Engine...")
            
            # Start all analytics components
            start_tasks = []
            if self.market_analytics:
                start_tasks.append(self.market_analytics.start())
            if self.pattern_detector:
                start_tasks.append(self.pattern_detector.start())
            if self.real_time_analyzer:
                start_tasks.append(self.real_time_analyzer.start())
            if self.predictive_engine:
                start_tasks.append(self.predictive_engine.start())
            if self.anomaly_detector:
                start_tasks.append(self.anomaly_detector.start())
            if self.sentiment_analyzer:
                start_tasks.append(self.sentiment_analyzer.start())
            
            await asyncio.gather(*start_tasks, return_exceptions=True)
            
            # Start background analysis tasks
            await self._start_analysis_tasks()
            
            self.running = True
            logger.info("✓ Advanced Analytics Engine started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Advanced Analytics Engine: {e}")
            return False
    
    async def stop(self) -> bool:
        """Stop analytics engine"""
        if not self.running:
            return True
            
        try:
            logger.info("Stopping Advanced Analytics Engine...")
            self.running = False
            
            # Cancel background tasks
            for task in self.analysis_tasks:
                task.cancel()
            
            # Stop all analytics components
            stop_tasks = []
            if self.sentiment_analyzer:
                stop_tasks.append(self.sentiment_analyzer.stop())
            if self.anomaly_detector:
                stop_tasks.append(self.anomaly_detector.stop())
            if self.predictive_engine:
                stop_tasks.append(self.predictive_engine.stop())
            if self.real_time_analyzer:
                stop_tasks.append(self.real_time_analyzer.stop())
            if self.pattern_detector:
                stop_tasks.append(self.pattern_detector.stop())
            if self.market_analytics:
                stop_tasks.append(self.market_analytics.stop())
            
            await asyncio.gather(*stop_tasks, return_exceptions=True)
            
            logger.info("✓ Advanced Analytics Engine stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping Advanced Analytics Engine: {e}")
            return False
    
    # Market Data Processing
    
    async def process_market_data(self, symbol: str, data: Dict[str, Any]) -> bool:
        """Process incoming market data"""
        try:
            # Store data in streams
            self.price_data[symbol].append(data.get('price', 0.0))
            self.volume_data[symbol].append(data.get('volume', 0.0))
            
            # Calculate volatility
            if len(self.price_data[symbol]) >= 2:
                prices = list(self.price_data[symbol])
                returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices)) if prices[i-1] != 0]
                if returns:
                    volatility = np.std(returns[-20:]) if len(returns) >= 20 else np.std(returns)
                    self.volatility_data[symbol].append(volatility)
            
            # Add symbol to active symbols
            self.active_symbols.add(symbol)
            
            # Trigger real-time analysis
            if self.real_time_analyzer:
                await self.real_time_analyzer.analyze_tick(symbol, data)
            
            return True
            
        except Exception as e:
            logger.error(f"Error processing market data for {symbol}: {e}")
            return False
    
    # Analysis Methods
    
    async def analyze_symbol(self, symbol: str, analysis_types: List[str] = None) -> Dict[str, AnalyticsResult]:
        """Perform comprehensive analysis for a symbol"""
        try:
            if analysis_types is None:
                analysis_types = ['technical', 'pattern', 'predictive', 'anomaly', 'sentiment']
            
            results = {}
            
            # Get market data
            market_data = await self._get_market_data(symbol)
            if not market_data:
                return results
            
            # Perform each type of analysis
            for analysis_type in analysis_types:
                try:
                    if analysis_type == 'technical':
                        result = await self._perform_technical_analysis(symbol, market_data)
                    elif analysis_type == 'pattern':
                        result = await self._perform_pattern_analysis(symbol, market_data)
                    elif analysis_type == 'predictive':
                        result = await self._perform_predictive_analysis(symbol, market_data)
                    elif analysis_type == 'anomaly':
                        result = await self._perform_anomaly_analysis(symbol, market_data)
                    elif analysis_type == 'sentiment':
                        result = await self._perform_sentiment_analysis(symbol, market_data)
                    else:
                        continue
                    
                    if result:
                        results[analysis_type] = result
                        
                        # Store result
                        self.analysis_results[symbol].append(result)
                        
                        # Generate insights if confidence is high
                        if result.confidence >= self.confidence_threshold:
                            await self._generate_insights(result)
                    
                except Exception as e:
                    logger.error(f"Error in {analysis_type} analysis for {symbol}: {e}")
            
            # Update metrics
            self.analytics_metrics['total_analyses'] += len(analysis_types)
            self.analytics_metrics['successful_analyses'] += len(results)
            
            return results
            
        except Exception as e:
            logger.error(f"Error analyzing symbol {symbol}: {e}")
            return {}
    
    async def get_market_insights(self, symbol: str = None, 
                                insight_type: str = None) -> List[MarketInsight]:
        """Get market insights"""
        try:
            insights = []
            
            for insight_id, insight in self.market_insights.items():
                # Filter by symbol
                if symbol and insight.metadata.get('symbol') != symbol:
                    continue
                
                # Filter by type
                if insight_type and insight.insight_type != insight_type:
                    continue
                
                # Check if insight is still valid
                if insight.expiry_time and datetime.now() > insight.expiry_time:
                    continue
                
                insights.append(insight)
            
            # Sort by impact score and confidence
            insights.sort(key=lambda x: (x.impact_score, x.confidence), reverse=True)
            
            return insights
            
        except Exception as e:
            logger.error(f"Error getting market insights: {e}")
            return []
    
    async def get_correlation_analysis(self, symbols: List[str], 
                                     timeframe: str = '1h') -> Dict[str, Any]:
        """Get correlation analysis between symbols"""
        try:
            if len(symbols) < 2:
                return {'error': 'At least 2 symbols required for correlation analysis'}
            
            # Get price data for all symbols
            price_data = {}
            for symbol in symbols:
                if symbol in self.price_data and len(self.price_data[symbol]) > 0:
                    price_data[symbol] = list(self.price_data[symbol])
            
            if len(price_data) < 2:
                return {'error': 'Insufficient data for correlation analysis'}
            
            # Calculate correlations
            correlations = {}
            for i, symbol1 in enumerate(symbols):
                if symbol1 not in price_data:
                    continue
                    
                correlations[symbol1] = {}
                
                for j, symbol2 in enumerate(symbols):
                    if symbol2 not in price_data:
                        continue
                    
                    if i == j:
                        correlations[symbol1][symbol2] = 1.0
                    else:
                        # Calculate correlation
                        prices1 = price_data[symbol1]
                        prices2 = price_data[symbol2]
                        
                        # Align data lengths
                        min_length = min(len(prices1), len(prices2))
                        if min_length < 10:
                            correlations[symbol1][symbol2] = 0.0
                        else:
                            aligned_prices1 = prices1[-min_length:]
                            aligned_prices2 = prices2[-min_length:]
                            
                            correlation = np.corrcoef(aligned_prices1, aligned_prices2)[0, 1]
                            correlations[symbol1][symbol2] = correlation if not np.isnan(correlation) else 0.0
            
            # Store correlation matrix
            self.correlation_matrix = correlations
            
            return {
                'correlations': correlations,
                'timeframe': timeframe,
                'symbols_analyzed': list(price_data.keys()),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in correlation analysis: {e}")
            return {'error': str(e)}
    
    async def get_market_regime(self, symbol: str) -> Dict[str, Any]:
        """Get current market regime for symbol"""
        try:
            if symbol not in self.price_data or len(self.price_data[symbol]) < 20:
                return {'regime': 'unknown', 'confidence': 0.0}
            
            prices = list(self.price_data[symbol])
            volumes = list(self.volume_data[symbol]) if symbol in self.volume_data else []
            volatilities = list(self.volatility_data[symbol]) if symbol in self.volatility_data else []
            
            # Calculate regime indicators
            regime_analysis = {}
            
            # Trend regime
            if len(prices) >= 20:
                short_ma = np.mean(prices[-10:])
                long_ma = np.mean(prices[-20:])
                trend_strength = abs(short_ma - long_ma) / long_ma if long_ma != 0 else 0
                
                if short_ma > long_ma * 1.02:
                    trend_regime = 'bullish'
                elif short_ma < long_ma * 0.98:
                    trend_regime = 'bearish'
                else:
                    trend_regime = 'sideways'
                
                regime_analysis['trend'] = {
                    'regime': trend_regime,
                    'strength': trend_strength,
                    'confidence': min(trend_strength * 10, 1.0)
                }
            
            # Volatility regime
            if volatilities:
                recent_vol = np.mean(volatilities[-10:]) if len(volatilities) >= 10 else volatilities[-1]
                historical_vol = np.mean(volatilities)
                
                if recent_vol > historical_vol * 1.5:
                    vol_regime = 'high_volatility'
                elif recent_vol < historical_vol * 0.5:
                    vol_regime = 'low_volatility'
                else:
                    vol_regime = 'normal_volatility'
                
                regime_analysis['volatility'] = {
                    'regime': vol_regime,
                    'current_vol': recent_vol,
                    'historical_vol': historical_vol,
                    'confidence': 0.8
                }
            
            # Volume regime
            if volumes:
                recent_volume = np.mean(volumes[-10:]) if len(volumes) >= 10 else volumes[-1]
                historical_volume = np.mean(volumes)
                
                if recent_volume > historical_volume * 1.5:
                    volume_regime = 'high_volume'
                elif recent_volume < historical_volume * 0.5:
                    volume_regime = 'low_volume'
                else:
                    volume_regime = 'normal_volume'
                
                regime_analysis['volume'] = {
                    'regime': volume_regime,
                    'current_volume': recent_volume,
                    'historical_volume': historical_volume,
                    'confidence': 0.7
                }
            
            # Overall regime
            overall_regime = 'normal'
            overall_confidence = 0.5
            
            if regime_analysis:
                # Simple regime combination logic
                trend_regime = regime_analysis.get('trend', {}).get('regime', 'sideways')
                vol_regime = regime_analysis.get('volatility', {}).get('regime', 'normal_volatility')
                
                if trend_regime == 'bullish' and vol_regime == 'low_volatility':
                    overall_regime = 'bull_market'
                    overall_confidence = 0.8
                elif trend_regime == 'bearish' and vol_regime == 'high_volatility':
                    overall_regime = 'bear_market'
                    overall_confidence = 0.8
                elif vol_regime == 'high_volatility':
                    overall_regime = 'volatile_market'
                    overall_confidence = 0.7
                elif trend_regime == 'sideways':
                    overall_regime = 'range_bound'
                    overall_confidence = 0.6
            
            # Store regime
            self.market_regime[symbol] = overall_regime
            
            return {
                'symbol': symbol,
                'overall_regime': overall_regime,
                'overall_confidence': overall_confidence,
                'regime_analysis': regime_analysis,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting market regime for {symbol}: {e}")
            return {'regime': 'unknown', 'confidence': 0.0, 'error': str(e)}
    
    async def calculate_performance(self, performance_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate performance metrics for a portfolio"""
        try:
            portfolio_id = performance_data.get('portfolio_id')
            start_date = performance_data.get('start_date')
            end_date = performance_data.get('end_date')

            # Mock performance calculation for testing
            # In a real implementation, this would analyze actual portfolio data

            import random

            # Generate realistic performance metrics
            total_return = random.uniform(-0.2, 0.3)  # -20% to +30%
            sharpe_ratio = random.uniform(0.5, 2.5)
            max_drawdown = random.uniform(0.05, 0.25)  # 5% to 25%
            volatility = random.uniform(0.1, 0.4)  # 10% to 40%

            performance_metrics = {
                'portfolio_id': portfolio_id,
                'period': f"{start_date} to {end_date}",
                'total_return': total_return,
                'annualized_return': total_return * (365 / 252),  # Approximate annualization
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'volatility': volatility,
                'win_rate': random.uniform(0.4, 0.7),
                'profit_factor': random.uniform(1.1, 2.5),
                'calmar_ratio': total_return / max_drawdown if max_drawdown > 0 else 0,
                'sortino_ratio': sharpe_ratio * 1.2,  # Approximate
                'var_95': volatility * 1.65,  # 95% VaR approximation
                'beta': random.uniform(0.7, 1.3),
                'alpha': random.uniform(-0.02, 0.05),
                'information_ratio': random.uniform(-0.5, 1.0),
                'tracking_error': random.uniform(0.02, 0.08),
                'timestamp': datetime.now().isoformat()
            }

            return performance_metrics

        except Exception as e:
            logger.error(f"Error calculating performance: {e}")
            return {'error': str(e)}

    async def generate_report(self, report_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate analytics report"""
        try:
            report_id = f"report_{int(time.time())}"

            if report_type == 'daily':
                return await self._generate_daily_report(report_id, data)
            elif report_type == 'weekly':
                return await self._generate_weekly_report(report_id, data)
            elif report_type == 'monthly':
                return await self._generate_monthly_report(report_id, data)
            elif report_type == 'performance':
                return await self._generate_performance_report(report_id, data)
            elif report_type == 'risk':
                return await self._generate_risk_report(report_id, data)
            else:
                return {
                    'error': f'Unknown report type: {report_type}',
                    'available_types': ['daily', 'weekly', 'monthly', 'performance', 'risk']
                }

        except Exception as e:
            logger.error(f"Error generating {report_type} report: {e}")
            return {'error': str(e)}

    async def _generate_daily_report(self, report_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate daily analytics report"""
        portfolio_id = data.get('portfolio_id', 'unknown')

        # Mock daily report data
        return {
            'report_id': report_id,
            'report_type': 'daily',
            'portfolio_id': portfolio_id,
            'date': datetime.now().strftime('%Y-%m-%d'),
            'summary': {
                'total_value': 100000 + random.uniform(-5000, 5000),
                'daily_pnl': random.uniform(-2000, 3000),
                'daily_return': random.uniform(-0.05, 0.05),
                'positions_count': random.randint(5, 15),
                'cash_balance': random.uniform(5000, 25000)
            },
            'top_performers': [
                {'symbol': 'AAPL', 'return': 0.025, 'pnl': 1250},
                {'symbol': 'TSLA', 'return': 0.018, 'pnl': 900},
                {'symbol': 'GOOGL', 'return': 0.012, 'pnl': 600}
            ],
            'top_losers': [
                {'symbol': 'META', 'return': -0.015, 'pnl': -750},
                {'symbol': 'NFLX', 'return': -0.008, 'pnl': -400}
            ],
            'risk_metrics': {
                'portfolio_var': random.uniform(0.01, 0.03),
                'max_position_size': random.uniform(0.15, 0.25),
                'correlation_risk': random.uniform(0.3, 0.7)
            },
            'alerts': [],
            'timestamp': datetime.now().isoformat()
        }

    async def _generate_weekly_report(self, report_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate weekly analytics report"""
        # Similar structure to daily but with weekly aggregations
        return {
            'report_id': report_id,
            'report_type': 'weekly',
            'summary': 'Weekly performance summary',
            'timestamp': datetime.now().isoformat()
        }

    async def _generate_monthly_report(self, report_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate monthly analytics report"""
        return {
            'report_id': report_id,
            'report_type': 'monthly',
            'summary': 'Monthly performance summary',
            'timestamp': datetime.now().isoformat()
        }

    async def _generate_performance_report(self, report_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate performance analytics report"""
        performance_metrics = await self.calculate_performance(data)

        return {
            'report_id': report_id,
            'report_type': 'performance',
            'performance_metrics': performance_metrics,
            'timestamp': datetime.now().isoformat()
        }

    async def _generate_risk_report(self, report_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate risk analytics report"""
        return {
            'report_id': report_id,
            'report_type': 'risk',
            'risk_summary': 'Risk analysis summary',
            'timestamp': datetime.now().isoformat()
        }

    async def get_analytics_metrics(self) -> Dict[str, Any]:
        """Get analytics performance metrics"""
        try:
            metrics = self.analytics_metrics.copy()
            
            # Calculate additional metrics
            if metrics['total_analyses'] > 0:
                metrics['success_rate'] = metrics['successful_analyses'] / metrics['total_analyses']
            else:
                metrics['success_rate'] = 0.0
            
            metrics['active_symbols'] = len(self.active_symbols)
            metrics['total_insights'] = len(self.market_insights)
            metrics['analysis_results_count'] = sum(len(results) for results in self.analysis_results.values())
            
            # Component status
            metrics['components_status'] = {
                'market_analytics': self.market_analytics is not None and getattr(self.market_analytics, 'running', False),
                'pattern_detector': self.pattern_detector is not None and getattr(self.pattern_detector, 'running', False),
                'real_time_analyzer': self.real_time_analyzer is not None and getattr(self.real_time_analyzer, 'running', False),
                'predictive_engine': self.predictive_engine is not None and getattr(self.predictive_engine, 'running', False),
                'anomaly_detector': self.anomaly_detector is not None and getattr(self.anomaly_detector, 'running', False),
                'sentiment_analyzer': self.sentiment_analyzer is not None and getattr(self.sentiment_analyzer, 'running', False)
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting analytics metrics: {e}")
            return self.analytics_metrics.copy()
    
    # Private methods
    
    async def _initialize_analytics_components(self):
        """Initialize all analytics components"""
        try:
            # Initialize Market Analytics
            self.market_analytics = MarketAnalytics(self.config)
            await self.market_analytics.initialize()
            logger.info("✓ Market Analytics initialized")
            
            # Initialize Pattern Detector
            self.pattern_detector = PatternDetector(self.config)
            await self.pattern_detector.initialize()
            logger.info("✓ Pattern Detector initialized")
            
            # Initialize Real-time Analyzer
            self.real_time_analyzer = RealTimeAnalyzer(self.config)
            await self.real_time_analyzer.initialize()
            logger.info("✓ Real-time Analyzer initialized")
            
            # Initialize Predictive Engine
            self.predictive_engine = PredictiveEngine(self.config)
            await self.predictive_engine.initialize()
            logger.info("✓ Predictive Engine initialized")
            
            # Initialize Anomaly Detector
            self.anomaly_detector = AnomalyDetector(self.config)
            await self.anomaly_detector.initialize()
            logger.info("✓ Anomaly Detector initialized")
            
            # Initialize Sentiment Analyzer
            self.sentiment_analyzer = SentimentAnalyzer(self.config)
            await self.sentiment_analyzer.initialize()
            logger.info("✓ Sentiment Analyzer initialized")
            
        except Exception as e:
            logger.error(f"Error initializing analytics components: {e}")
            raise

    async def _setup_analysis_pipelines(self):
        """Setup analysis pipelines"""
        try:
            # Configure analysis pipelines for different timeframes
            self.analysis_pipelines = {
                'real_time': {
                    'interval': self.analysis_intervals['real_time'],
                    'components': ['real_time_analyzer', 'anomaly_detector'],
                    'priority': 'high'
                },
                'short_term': {
                    'interval': self.analysis_intervals['short_term'],
                    'components': ['pattern_detector', 'market_analytics'],
                    'priority': 'medium'
                },
                'medium_term': {
                    'interval': self.analysis_intervals['medium_term'],
                    'components': ['predictive_engine', 'sentiment_analyzer'],
                    'priority': 'medium'
                },
                'long_term': {
                    'interval': self.analysis_intervals['long_term'],
                    'components': ['market_analytics', 'predictive_engine'],
                    'priority': 'low'
                }
            }

            logger.info("✓ Analysis pipelines configured")

        except Exception as e:
            logger.error(f"Error setting up analysis pipelines: {e}")
            raise

    async def _initialize_data_streams(self):
        """Initialize market data streams"""
        try:
            # Initialize data streams for configured symbols
            symbols = self.config.get('symbols', {})
            for symbol_group, symbol_list in symbols.items():
                for symbol in symbol_list:
                    self.active_symbols.add(symbol)
                    # Initialize empty streams
                    self.price_data[symbol] = deque(maxlen=1000)
                    self.volume_data[symbol] = deque(maxlen=1000)
                    self.volatility_data[symbol] = deque(maxlen=1000)

            logger.info(f"✓ Data streams initialized for {len(self.active_symbols)} symbols")

        except Exception as e:
            logger.error(f"Error initializing data streams: {e}")
            raise

    async def _start_analysis_tasks(self):
        """Start background analysis tasks"""
        try:
            # Start analysis tasks for each pipeline
            for pipeline_name, pipeline_config in self.analysis_pipelines.items():
                task = asyncio.create_task(
                    self._analysis_loop(pipeline_name, pipeline_config)
                )
                self.analysis_tasks.append(task)

            # Start insight cleanup task
            cleanup_task = asyncio.create_task(self._insight_cleanup_loop())
            self.analysis_tasks.append(cleanup_task)

            logger.info(f"✓ Started {len(self.analysis_tasks)} analysis tasks")

        except Exception as e:
            logger.error(f"Error starting analysis tasks: {e}")
            raise

    async def _analysis_loop(self, pipeline_name: str, pipeline_config: Dict[str, Any]):
        """Background analysis loop for a pipeline"""
        interval = pipeline_config['interval']
        components = pipeline_config['components']

        while self.running:
            try:
                start_time = time.time()

                # Analyze all active symbols
                for symbol in list(self.active_symbols):
                    try:
                        # Get market data
                        market_data = await self._get_market_data(symbol)
                        if not market_data:
                            continue

                        # Run analysis for each component in pipeline
                        for component_name in components:
                            if component_name == 'real_time_analyzer' and self.real_time_analyzer:
                                await self.real_time_analyzer.analyze_symbol(symbol, market_data)
                            elif component_name == 'pattern_detector' and self.pattern_detector:
                                await self.pattern_detector.detect_patterns(symbol, market_data)
                            elif component_name == 'market_analytics' and self.market_analytics:
                                await self.market_analytics.update_market_data(symbol, market_data.get('price', 0), market_data.get('volume', 0))
                            elif component_name == 'predictive_engine' and self.predictive_engine:
                                await self.predictive_engine.generate_predictions(symbol, market_data)
                            elif component_name == 'anomaly_detector' and self.anomaly_detector:
                                await self.anomaly_detector.detect_anomalies(symbol, market_data)
                            elif component_name == 'sentiment_analyzer' and self.sentiment_analyzer:
                                await self.sentiment_analyzer.analyze_sentiment(symbol)

                    except Exception as e:
                        logger.error(f"Error in {pipeline_name} analysis for {symbol}: {e}")

                # Update processing time metric
                processing_time = (time.time() - start_time) * 1000
                self.analytics_metrics['processing_time_ms'] = processing_time

                # Wait for next interval
                await asyncio.sleep(interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in {pipeline_name} analysis loop: {e}")
                await asyncio.sleep(interval)

    async def _insight_cleanup_loop(self):
        """Background task to clean up expired insights"""
        while self.running:
            try:
                current_time = datetime.now()
                expired_insights = []

                for insight_id, insight in self.market_insights.items():
                    if insight.expiry_time and current_time > insight.expiry_time:
                        expired_insights.append(insight_id)

                # Remove expired insights
                for insight_id in expired_insights:
                    del self.market_insights[insight_id]

                if expired_insights:
                    logger.debug(f"Cleaned up {len(expired_insights)} expired insights")

                # Limit total insights
                if len(self.market_insights) > self.max_insights:
                    # Remove oldest insights
                    insights_by_time = sorted(
                        self.market_insights.items(),
                        key=lambda x: x[1].metadata.get('created_at', datetime.min)
                    )

                    to_remove = len(self.market_insights) - self.max_insights
                    for i in range(to_remove):
                        insight_id = insights_by_time[i][0]
                        del self.market_insights[insight_id]

                await asyncio.sleep(300)  # Clean up every 5 minutes

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in insight cleanup loop: {e}")
                await asyncio.sleep(300)

    async def _get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get market data for a symbol"""
        try:
            if symbol not in self.price_data or len(self.price_data[symbol]) == 0:
                return None

            # Get latest data
            latest_price = self.price_data[symbol][-1] if self.price_data[symbol] else 0.0
            latest_volume = self.volume_data[symbol][-1] if self.volume_data[symbol] else 0.0
            latest_volatility = self.volatility_data[symbol][-1] if self.volatility_data[symbol] else 0.0

            # Get historical data
            prices = list(self.price_data[symbol])
            volumes = list(self.volume_data[symbol])
            volatilities = list(self.volatility_data[symbol])

            return {
                'symbol': symbol,
                'price': latest_price,
                'volume': latest_volume,
                'volatility': latest_volatility,
                'prices': prices,
                'volumes': volumes,
                'volatilities': volatilities,
                'timestamp': datetime.now()
            }

        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {e}")
            return None

    async def _perform_technical_analysis(self, symbol: str, market_data: Dict[str, Any]) -> Optional[AnalyticsResult]:
        """Perform technical analysis"""
        try:
            if not self.market_analytics:
                return None

            # Get technical indicators
            analysis = await self.market_analytics.get_technical_indicators(symbol)

            if not analysis:
                return None

            # Calculate confidence based on signal strength
            confidence = self._calculate_technical_confidence(analysis)

            return AnalyticsResult(
                analysis_type='technical',
                symbol=symbol,
                timestamp=datetime.now(),
                confidence=confidence,
                result_data=analysis,
                metadata={'source': 'market_analytics'}
            )

        except Exception as e:
            logger.error(f"Error in technical analysis for {symbol}: {e}")
            return None

    async def _perform_pattern_analysis(self, symbol: str, market_data: Dict[str, Any]) -> Optional[AnalyticsResult]:
        """Perform pattern analysis"""
        try:
            if not self.pattern_detector:
                return None

            # Convert market data to DataFrame
            prices = market_data.get('prices', [])
            if len(prices) < 10:
                return None

            df = pd.DataFrame({
                'close': prices,
                'volume': market_data.get('volumes', [0] * len(prices))
            })

            # Detect patterns
            patterns = await self.pattern_detector.detect_patterns(symbol, df)

            if not patterns.get('success', False):
                return None

            # Calculate confidence based on pattern strength
            confidence = self._calculate_pattern_confidence(patterns)

            return AnalyticsResult(
                analysis_type='pattern',
                symbol=symbol,
                timestamp=datetime.now(),
                confidence=confidence,
                result_data=patterns,
                metadata={'source': 'pattern_detector'}
            )

        except Exception as e:
            logger.error(f"Error in pattern analysis for {symbol}: {e}")
            return None

    async def _perform_predictive_analysis(self, symbol: str, market_data: Dict[str, Any]) -> Optional[AnalyticsResult]:
        """Perform predictive analysis"""
        try:
            if not self.predictive_engine:
                return None

            # Generate predictions
            predictions = await self.predictive_engine.predict_price_movement(symbol, market_data)

            if not predictions:
                return None

            # Calculate confidence based on prediction accuracy
            confidence = predictions.get('confidence', 0.5)

            return AnalyticsResult(
                analysis_type='predictive',
                symbol=symbol,
                timestamp=datetime.now(),
                confidence=confidence,
                result_data=predictions,
                metadata={'source': 'predictive_engine'}
            )

        except Exception as e:
            logger.error(f"Error in predictive analysis for {symbol}: {e}")
            return None

    async def _perform_anomaly_analysis(self, symbol: str, market_data: Dict[str, Any]) -> Optional[AnalyticsResult]:
        """Perform anomaly analysis"""
        try:
            if not self.anomaly_detector:
                return None

            # Detect anomalies
            anomalies = await self.anomaly_detector.detect_anomalies(symbol, market_data)

            if not anomalies:
                return None

            # Calculate confidence based on anomaly score
            confidence = anomalies.get('confidence', 0.5)

            return AnalyticsResult(
                analysis_type='anomaly',
                symbol=symbol,
                timestamp=datetime.now(),
                confidence=confidence,
                result_data=anomalies,
                metadata={'source': 'anomaly_detector'}
            )

        except Exception as e:
            logger.error(f"Error in anomaly analysis for {symbol}: {e}")
            return None

    async def _perform_sentiment_analysis(self, symbol: str, market_data: Dict[str, Any]) -> Optional[AnalyticsResult]:
        """Perform sentiment analysis"""
        try:
            if not self.sentiment_analyzer:
                return None

            # Analyze sentiment
            sentiment = await self.sentiment_analyzer.get_sentiment_score(symbol)

            if not sentiment:
                return None

            # Calculate confidence based on sentiment strength
            confidence = sentiment.get('confidence', 0.5)

            return AnalyticsResult(
                analysis_type='sentiment',
                symbol=symbol,
                timestamp=datetime.now(),
                confidence=confidence,
                result_data=sentiment,
                metadata={'source': 'sentiment_analyzer'}
            )

        except Exception as e:
            logger.error(f"Error in sentiment analysis for {symbol}: {e}")
            return None

    async def _generate_insights(self, result: AnalyticsResult):
        """Generate market insights from analysis results"""
        try:
            insight_id = f"{result.symbol}_{result.analysis_type}_{int(time.time())}"

            # Generate insight based on analysis type
            if result.analysis_type == 'technical':
                insight = self._generate_technical_insight(result)
            elif result.analysis_type == 'pattern':
                insight = self._generate_pattern_insight(result)
            elif result.analysis_type == 'predictive':
                insight = self._generate_predictive_insight(result)
            elif result.analysis_type == 'anomaly':
                insight = self._generate_anomaly_insight(result)
            elif result.analysis_type == 'sentiment':
                insight = self._generate_sentiment_insight(result)
            else:
                return

            if insight:
                insight.insight_id = insight_id
                insight.metadata['symbol'] = result.symbol
                insight.metadata['created_at'] = datetime.now()

                self.market_insights[insight_id] = insight
                self.analytics_metrics['insights_generated'] += 1

                logger.debug(f"Generated insight: {insight.description}")

        except Exception as e:
            logger.error(f"Error generating insights: {e}")

    def _calculate_technical_confidence(self, analysis: Dict[str, Any]) -> float:
        """Calculate confidence for technical analysis"""
        try:
            # Simple confidence calculation based on signal strength
            signals = analysis.get('signals', [])
            if not signals:
                return 0.5

            signal_strengths = [signal.get('strength', 0.5) for signal in signals]
            return min(np.mean(signal_strengths), 1.0)

        except Exception:
            return 0.5

    def _calculate_pattern_confidence(self, patterns: Dict[str, Any]) -> float:
        """Calculate confidence for pattern analysis"""
        try:
            detected_patterns = patterns.get('patterns', [])
            if not detected_patterns:
                return 0.5

            confidences = [pattern.get('confidence', 0.5) for pattern in detected_patterns]
            return min(np.mean(confidences), 1.0)

        except Exception:
            return 0.5

    def _generate_technical_insight(self, result: AnalyticsResult) -> Optional[MarketInsight]:
        """Generate insight from technical analysis"""
        try:
            data = result.result_data
            signals = data.get('signals', [])

            if not signals:
                return None

            # Find strongest signal
            strongest_signal = max(signals, key=lambda x: x.get('strength', 0))

            description = f"Technical analysis indicates {strongest_signal.get('direction', 'neutral')} signal for {result.symbol}"

            return MarketInsight(
                insight_id="",  # Will be set by caller
                insight_type="technical_signal",
                description=description,
                confidence=result.confidence,
                impact_score=strongest_signal.get('strength', 0.5),
                supporting_evidence=[f"Signal: {strongest_signal.get('indicator', 'unknown')}"],
                recommended_actions=[f"Consider {strongest_signal.get('action', 'monitoring')} position"],
                expiry_time=datetime.now() + timedelta(hours=1)
            )

        except Exception as e:
            logger.error(f"Error generating technical insight: {e}")
            return None

    def _generate_pattern_insight(self, result: AnalyticsResult) -> Optional[MarketInsight]:
        """Generate insight from pattern analysis"""
        try:
            data = result.result_data
            patterns = data.get('patterns', [])

            if not patterns:
                return None

            # Find highest confidence pattern
            best_pattern = max(patterns, key=lambda x: x.get('confidence', 0))

            description = f"Pattern detected: {best_pattern.get('pattern_type', 'unknown')} for {result.symbol}"

            return MarketInsight(
                insight_id="",  # Will be set by caller
                insight_type="pattern_detection",
                description=description,
                confidence=result.confidence,
                impact_score=best_pattern.get('confidence', 0.5),
                supporting_evidence=[f"Pattern: {best_pattern.get('pattern_type', 'unknown')}"],
                recommended_actions=["Monitor for pattern completion"],
                expiry_time=datetime.now() + timedelta(hours=2)
            )

        except Exception as e:
            logger.error(f"Error generating pattern insight: {e}")
            return None

    def _generate_predictive_insight(self, result: AnalyticsResult) -> Optional[MarketInsight]:
        """Generate insight from predictive analysis"""
        try:
            data = result.result_data
            prediction = data.get('prediction', 0)

            direction = "bullish" if prediction > 0 else "bearish" if prediction < 0 else "neutral"
            description = f"Predictive model suggests {direction} movement for {result.symbol}"

            return MarketInsight(
                insight_id="",  # Will be set by caller
                insight_type="price_prediction",
                description=description,
                confidence=result.confidence,
                impact_score=abs(prediction),
                supporting_evidence=[f"Predicted change: {prediction:.2%}"],
                recommended_actions=[f"Consider {direction} positioning"],
                expiry_time=datetime.now() + timedelta(hours=4)
            )

        except Exception as e:
            logger.error(f"Error generating predictive insight: {e}")
            return None

    def _generate_anomaly_insight(self, result: AnalyticsResult) -> Optional[MarketInsight]:
        """Generate insight from anomaly analysis"""
        try:
            data = result.result_data
            anomaly_score = data.get('anomaly_score', 0)

            description = f"Anomaly detected in {result.symbol} with score {anomaly_score:.2f}"

            return MarketInsight(
                insight_id="",  # Will be set by caller
                insight_type="anomaly_alert",
                description=description,
                confidence=result.confidence,
                impact_score=anomaly_score,
                supporting_evidence=[f"Anomaly score: {anomaly_score:.2f}"],
                recommended_actions=["Investigate unusual market behavior", "Consider risk adjustment"],
                expiry_time=datetime.now() + timedelta(minutes=30)
            )

        except Exception as e:
            logger.error(f"Error generating anomaly insight: {e}")
            return None

    def _generate_sentiment_insight(self, result: AnalyticsResult) -> Optional[MarketInsight]:
        """Generate insight from sentiment analysis"""
        try:
            data = result.result_data
            sentiment_score = data.get('sentiment_score', 0)

            sentiment_label = "positive" if sentiment_score > 0.1 else "negative" if sentiment_score < -0.1 else "neutral"
            description = f"Market sentiment for {result.symbol} is {sentiment_label}"

            return MarketInsight(
                insight_id="",  # Will be set by caller
                insight_type="sentiment_analysis",
                description=description,
                confidence=result.confidence,
                impact_score=abs(sentiment_score),
                supporting_evidence=[f"Sentiment score: {sentiment_score:.2f}"],
                recommended_actions=[f"Consider sentiment in {result.symbol} decisions"],
                expiry_time=datetime.now() + timedelta(hours=6)
            )

        except Exception as e:
            logger.error(f"Error generating sentiment insight: {e}")
            return None


# Simplified AnalyticsEngine for proof testing
class AnalyticsEngine:
    """Simplified Analytics Engine for proof testing"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.initialized = False

    async def initialize(self) -> bool:
        """Initialize analytics engine"""
        try:
            logger.info("🚀 Initializing Analytics Engine...")
            await asyncio.sleep(0.1)  # Simulate initialization
            self.initialized = True
            logger.info("✅ Analytics Engine initialized successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Analytics Engine initialization failed: {e}")
            return False

    async def analyze_market_data(self, symbol: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze market data"""
        try:
            if not self.initialized:
                return {'error': 'Analytics engine not initialized'}

            # Simulate analysis
            await asyncio.sleep(0.05)

            # Generate mock signals
            signals = [
                {'type': 'momentum', 'strength': random.uniform(0.3, 0.9), 'direction': random.choice(['buy', 'sell'])},
                {'type': 'mean_reversion', 'strength': random.uniform(0.2, 0.8), 'direction': random.choice(['buy', 'sell'])},
                {'type': 'volatility', 'strength': random.uniform(0.4, 0.7), 'direction': 'neutral'}
            ]

            confidence = random.uniform(0.6, 0.95)

            return {
                'symbol': symbol,
                'signals': signals,
                'confidence': confidence,
                'timestamp': datetime.now().isoformat(),
                'analysis_type': 'comprehensive'
            }

        except Exception as e:
            logger.error(f"❌ Market analysis failed for {symbol}: {e}")
            return {'error': str(e)}

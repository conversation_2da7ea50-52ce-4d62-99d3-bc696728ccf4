"""
System Coordinator - Central coordination hub for the entire trading system
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum

# Core system imports
from config.config_manager import ConfigManager
from models.ollama_hub import OllamaModelHub
from agents.agent_manager import AgentManager
from agents.base_agent import AgentRole
from teams.team_manager import TeamManager, TeamType
from communication.message_broker import MessageBroker
from data.data_manager import DataManager
from portfolio.portfolio_manager import PortfolioManager
from execution.execution_engine import ExecutionEngine
from analytics.analytics_engine import AdvancedAnalyticsEngine
from risk.risk_manager import RiskManager
from strategies.strategy_manager import StrategyManager
from utils.logging_utils import safe_success, get_status_symbol

logger = logging.getLogger(__name__)


class SystemState(Enum):
    """System operational states"""
    INITIALIZING = "initializing"
    STARTING = "starting"
    RUNNING = "running"
    PAUSING = "pausing"
    PAUSED = "paused"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class SystemStatus:
    """System status information"""
    state: SystemState
    uptime: float
    components_status: Dict[str, bool]
    performance_metrics: Dict[str, Any]
    active_strategies: int
    active_agents: int
    total_trades: int
    system_health: float
    last_update: datetime


class SystemCoordinator:
    """
    Central coordination hub that orchestrates all system components
    and provides unified system management capabilities.
    """
    
    def __init__(self, config_path: str = 'config/system_config.yaml'):
        self.config_path = config_path
        
        # Core components
        self.config_manager: Optional[ConfigManager] = None
        self.ollama_hub: Optional[OllamaModelHub] = None
        self.message_broker: Optional[MessageBroker] = None
        self.agent_manager: Optional[AgentManager] = None
        self.team_manager: Optional[TeamManager] = None
        self.data_manager: Optional[DataManager] = None
        self.portfolio_manager: Optional[PortfolioManager] = None
        self.execution_engine: Optional[ExecutionEngine] = None
        self.analytics_engine: Optional[AdvancedAnalyticsEngine] = None
        self.risk_manager: Optional[RiskManager] = None
        self.strategy_manager: Optional[StrategyManager] = None

        # AI Integration status
        self.ai_integration_active = False
        self.ai_agents_deployed = {}
        self.ai_teams_formed = {}
        self.ai_strategies_active = {}
        
        # System state
        self.state = SystemState.STOPPED
        self.start_time: Optional[float] = None
        self.components: Dict[str, Any] = {}
        self.component_status: Dict[str, bool] = {}
        
        # System metrics
        self.system_metrics = {
            'total_trades': 0,
            'successful_trades': 0,
            'total_profit_loss': 0.0,
            'active_strategies': 0,
            'active_agents': 0,
            'system_health': 1.0,
            'uptime': 0.0
        }
        
        # Background tasks
        self.monitoring_tasks: List[asyncio.Task] = []
        
        # Configuration
        self.health_check_interval = 30  # seconds
        self.metrics_update_interval = 60  # seconds
        
    async def initialize(self) -> bool:
        """Initialize the entire trading system"""
        try:
            self.state = SystemState.INITIALIZING
            logger.info("🚀 Initializing Advanced Trading System...")
            
            # Initialize configuration
            await self._init_config_manager()
            config = await self.config_manager.get_config()
            
            # Initialize core components in dependency order
            await self._init_ollama_hub(config)
            await self._init_message_broker(config)
            await self._init_data_manager(config)
            await self._init_risk_manager(config)
            await self._init_portfolio_manager(config)
            await self._init_execution_engine(config)
            await self._init_analytics_engine(config)
            await self._init_strategy_manager(config)
            await self._init_agent_manager(config)
            await self._init_team_manager(config)
            
            # Verify all components
            if await self._verify_components():
                self.state = SystemState.STOPPED  # Ready to start
                logger.info("✅ All system components initialized successfully")
                return True
            else:
                self.state = SystemState.ERROR
                logger.error("❌ Some components failed to initialize")
                return False
                
        except Exception as e:
            logger.error(f"❌ System initialization failed: {e}")
            self.state = SystemState.ERROR
            return False
            
    async def start(self) -> bool:
        """Start the trading system"""
        try:
            if self.state != SystemState.STOPPED:
                logger.warning("System is not in stopped state")
                return False
                
            self.state = SystemState.STARTING
            logger.info("🚀 Starting Advanced Trading System...")
            
            # Start all components
            await self._start_components()
            
            # Start background monitoring
            await self._start_monitoring()
            
            # Set system state
            self.state = SystemState.RUNNING
            self.start_time = time.time()
            
            logger.info("✅ Advanced Trading System is now RUNNING")
            return True
            
        except Exception as e:
            logger.error(f"❌ System start failed: {e}")
            self.state = SystemState.ERROR
            return False
            
    async def stop(self) -> bool:
        """Stop the trading system"""
        try:
            if self.state == SystemState.STOPPED:
                return True
                
            self.state = SystemState.STOPPING
            logger.info("🛑 Stopping Advanced Trading System...")
            
            # Stop monitoring tasks
            await self._stop_monitoring()
            
            # Stop all components
            await self._stop_components()
            
            # Set system state
            self.state = SystemState.STOPPED
            self.start_time = None
            
            logger.info("✅ Advanced Trading System stopped")
            return True
            
        except Exception as e:
            logger.error(f"❌ System stop failed: {e}")
            self.state = SystemState.ERROR
            return False
            
    async def pause(self) -> bool:
        """Pause trading operations and AI activities"""
        try:
            if self.state != SystemState.RUNNING:
                logger.warning(f"Cannot pause system in state: {self.state}")
                return False

            self.state = SystemState.PAUSING
            logger.info("⏸️ Pausing trading operations and AI activities...")

            # Pause trading components
            if self.strategy_manager:
                await self.strategy_manager.pause_all_strategies()
                logger.info("  ⏸️ Strategy Manager paused")

            if self.execution_engine:
                await self.execution_engine.pause_trading()
                logger.info("  ⏸️ Execution Engine paused")

            # Pause AI components
            if self.agent_manager:
                await self.agent_manager.pause_all_agents()
                logger.info("  ⏸️ AI Agents paused")

            if self.team_manager:
                await self.team_manager.pause_all_teams()
                logger.info("  ⏸️ AI Teams paused")

            # Pause data feeds (but keep essential monitoring)
            if self.data_manager:
                await self.data_manager.pause_data_feeds()
                logger.info("  ⏸️ Data feeds paused")

            # Pause analytics (but keep health monitoring)
            if self.analytics_engine:
                await self.analytics_engine.pause_analysis()
                logger.info("  ⏸️ Analytics paused")

            self.state = SystemState.PAUSED
            logger.info("✅ All trading operations and AI activities paused")
            return True

        except Exception as e:
            logger.error(f"❌ System pause failed: {e}")
            self.state = SystemState.ERROR
            return False
            
    async def resume(self) -> bool:
        """Resume trading operations and AI activities"""
        try:
            if self.state != SystemState.PAUSED:
                logger.warning(f"Cannot resume system in state: {self.state}")
                return False

            logger.info("▶️ Resuming trading operations and AI activities...")

            # Resume data feeds first
            if self.data_manager:
                await self.data_manager.resume_data_feeds()
                logger.info("  ▶️ Data feeds resumed")

            # Resume analytics
            if self.analytics_engine:
                await self.analytics_engine.resume_analysis()
                logger.info("  ▶️ Analytics resumed")

            # Resume AI components
            if self.agent_manager:
                await self.agent_manager.resume_all_agents()
                logger.info("  ▶️ AI Agents resumed")

            if self.team_manager:
                await self.team_manager.resume_all_teams()
                logger.info("  ▶️ AI Teams resumed")

            # Resume trading components last
            if self.strategy_manager:
                await self.strategy_manager.resume_all_strategies()
                logger.info("  ▶️ Strategy Manager resumed")

            if self.execution_engine:
                await self.execution_engine.resume_trading()
                logger.info("  ▶️ Execution Engine resumed")

            self.state = SystemState.RUNNING
            logger.info("✅ All trading operations and AI activities resumed")
            return True

        except Exception as e:
            logger.error(f"❌ System resume failed: {e}")
            return False
            
    async def get_system_status(self) -> SystemStatus:
        """Get comprehensive system status"""
        try:
            uptime = time.time() - self.start_time if self.start_time else 0.0
            
            # Update component status
            await self._update_component_status()
            
            # Calculate system health
            health = await self._calculate_system_health()
            
            # Get active counts
            active_strategies = len(await self.strategy_manager.get_active_strategies()) if self.strategy_manager else 0
            active_agents = len(await self.agent_manager.get_active_agents()) if self.agent_manager else 0
            
            return SystemStatus(
                state=self.state,
                uptime=uptime,
                components_status=self.component_status.copy(),
                performance_metrics=self.system_metrics.copy(),
                active_strategies=active_strategies,
                active_agents=active_agents,
                total_trades=self.system_metrics['total_trades'],
                system_health=health,
                last_update=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return SystemStatus(
                state=SystemState.ERROR,
                uptime=0.0,
                components_status={},
                performance_metrics={},
                active_strategies=0,
                active_agents=0,
                total_trades=0,
                system_health=0.0,
                last_update=datetime.now()
            )
            
    # Component initialization methods
    
    async def _init_config_manager(self):
        """Initialize configuration manager"""
        self.config_manager = ConfigManager(self.config_path)
        await self.config_manager.load_config()
        self.components['config_manager'] = self.config_manager
        logger.info("✅ Configuration Manager initialized")
        
    async def _init_ollama_hub(self, config):
        """Initialize Ollama model hub"""
        self.ollama_hub = OllamaModelHub(config=config)
        await self.ollama_hub.initialize()
        self.components['ollama_hub'] = self.ollama_hub
        safe_success(logger, f"{get_status_symbol(True)} Ollama Hub initialized")

    async def _init_message_broker(self, config):
        """Initialize message broker"""
        self.message_broker = MessageBroker(config)
        await self.message_broker.initialize()
        self.components['message_broker'] = self.message_broker
        safe_success(logger, f"{get_status_symbol(True)} Message Broker initialized")
        
    async def _init_data_manager(self, config):
        """Initialize data manager"""
        self.data_manager = DataManager(config)
        await self.data_manager.initialize()
        self.components['data_manager'] = self.data_manager
        logger.info("✅ Data Manager initialized")
        
    async def _init_risk_manager(self, config):
        """Initialize risk manager"""
        self.risk_manager = RiskManager(config)
        await self.risk_manager.initialize()
        self.components['risk_manager'] = self.risk_manager
        logger.info("✅ Risk Manager initialized")
        
    async def _init_portfolio_manager(self, config):
        """Initialize portfolio manager"""
        self.portfolio_manager = PortfolioManager(config)
        await self.portfolio_manager.initialize()
        self.components['portfolio_manager'] = self.portfolio_manager
        logger.info("✅ Portfolio Manager initialized")
        
    async def _init_execution_engine(self, config):
        """Initialize execution engine"""
        self.execution_engine = ExecutionEngine(config)
        await self.execution_engine.initialize()
        self.components['execution_engine'] = self.execution_engine
        logger.info("✅ Execution Engine initialized")
        
    async def _init_analytics_engine(self, config):
        """Initialize analytics engine"""
        self.analytics_engine = AdvancedAnalyticsEngine(config)
        await self.analytics_engine.initialize()
        self.components['analytics_engine'] = self.analytics_engine
        logger.info("✅ Analytics Engine initialized")
        
    async def _init_strategy_manager(self, config):
        """Initialize strategy manager"""
        self.strategy_manager = StrategyManager(config)
        await self.strategy_manager.initialize()
        self.components['strategy_manager'] = self.strategy_manager
        logger.info("✅ Strategy Manager initialized")
        
    async def _init_agent_manager(self, config):
        """Initialize agent manager"""
        self.agent_manager = AgentManager(
            ollama_hub=self.ollama_hub,
            message_broker=self.message_broker,
            config=config
        )
        await self.agent_manager.initialize()
        self.components['agent_manager'] = self.agent_manager
        safe_success(logger, f"{get_status_symbol(True)} Agent Manager initialized")

    async def _init_team_manager(self, config):
        """Initialize team manager"""
        self.team_manager = TeamManager(
            agent_manager=self.agent_manager,
            message_broker=self.message_broker,
            market_data_manager=self.data_manager,
            config=config
        )
        await self.team_manager.initialize()
        self.components['team_manager'] = self.team_manager
        safe_success(logger, f"{get_status_symbol(True)} Team Manager initialized")
        
    async def _verify_components(self) -> bool:
        """Verify all components are properly initialized"""
        required_components = [
            'config_manager', 'ollama_hub', 'message_broker', 'data_manager',
            'risk_manager', 'portfolio_manager', 'execution_engine',
            'analytics_engine', 'strategy_manager', 'agent_manager', 'team_manager'
        ]
        
        for component_name in required_components:
            if component_name not in self.components:
                logger.error(f"❌ Component {component_name} not initialized")
                return False
            self.component_status[component_name] = True
            
        return True
        
    async def _start_components(self):
        """Start all system components in proper dependency order"""
        try:
            logger.info("🚀 Starting system components...")

            # Start core infrastructure first
            if self.message_broker:
                await self.message_broker.start()
                logger.info("  ✅ Message Broker started")

            if self.data_manager:
                await self.data_manager.start()
                logger.info("  ✅ Data Manager started")

            # Start AI infrastructure
            if self.ollama_hub:
                await self.ollama_hub.start()
                logger.info("  ✅ Ollama Hub started")

            # Start analysis and risk management
            if self.analytics_engine:
                await self.analytics_engine.start()
                logger.info("  ✅ Analytics Engine started")

            if self.risk_manager:
                await self.risk_manager.start()
                logger.info("  ✅ Risk Manager started")

            # Start portfolio management
            if self.portfolio_manager:
                await self.portfolio_manager.start()
                logger.info("  ✅ Portfolio Manager started")

            # Start execution engine
            if self.execution_engine:
                await self.execution_engine.start()
                logger.info("  ✅ Execution Engine started")

            # Start strategy management
            if self.strategy_manager:
                await self.strategy_manager.start()
                logger.info("  ✅ Strategy Manager started")

            # Start AI components last (they depend on everything else)
            if self.agent_manager:
                await self.agent_manager.start()
                logger.info("  ✅ Agent Manager started")

            if self.team_manager:
                await self.team_manager.start()
                logger.info("  ✅ Team Manager started")

            logger.info("✅ All system components started successfully")

        except Exception as e:
            logger.error(f"❌ Error starting components: {e}")
            raise
            
    async def _stop_components(self):
        """Stop all system components in reverse dependency order"""
        try:
            logger.info("🛑 Stopping system components...")

            # Stop AI components first (highest level)
            if self.team_manager:
                await self.team_manager.stop()
                logger.info("  ✅ Team Manager stopped")

            if self.agent_manager:
                await self.agent_manager.stop()
                logger.info("  ✅ Agent Manager stopped")

            # Stop trading components
            if self.strategy_manager:
                await self.strategy_manager.stop()
                logger.info("  ✅ Strategy Manager stopped")

            if self.execution_engine:
                await self.execution_engine.stop()
                logger.info("  ✅ Execution Engine stopped")

            # Stop portfolio and risk management
            if self.portfolio_manager:
                await self.portfolio_manager.stop()
                logger.info("  ✅ Portfolio Manager stopped")

            if self.risk_manager:
                await self.risk_manager.stop()
                logger.info("  ✅ Risk Manager stopped")

            # Stop analytics
            if self.analytics_engine:
                await self.analytics_engine.stop()
                logger.info("  ✅ Analytics Engine stopped")

            # Stop AI infrastructure
            if self.ollama_hub:
                await self.ollama_hub.stop()
                logger.info("  ✅ Ollama Hub stopped")

            # Stop core infrastructure last
            if self.data_manager:
                await self.data_manager.stop()
                logger.info("  ✅ Data Manager stopped")

            if self.message_broker:
                await self.message_broker.stop()
                logger.info("  ✅ Message Broker stopped")

            logger.info("✅ All system components stopped successfully")

        except Exception as e:
            logger.error(f"❌ Error stopping components: {e}")
            # Continue stopping other components even if one fails
            
    async def _start_monitoring(self):
        """Start background monitoring tasks"""
        self.monitoring_tasks = [
            asyncio.create_task(self._health_monitoring_loop()),
            asyncio.create_task(self._metrics_update_loop())
        ]
        
    async def _stop_monitoring(self):
        """Stop background monitoring tasks"""
        for task in self.monitoring_tasks:
            task.cancel()
        await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)
        self.monitoring_tasks.clear()
        
    async def _health_monitoring_loop(self):
        """Background health monitoring loop"""
        while self.state == SystemState.RUNNING:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._update_component_status()
                await self._check_system_health()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitoring: {e}")
                
    async def _metrics_update_loop(self):
        """Background metrics update loop"""
        while self.state == SystemState.RUNNING:
            try:
                await asyncio.sleep(self.metrics_update_interval)
                await self._update_system_metrics()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in metrics update: {e}")
                
    async def _update_component_status(self):
        """Update status of all components"""
        for component_name, component in self.components.items():
            try:
                # Check if component has a health check method
                if hasattr(component, 'get_stats'):
                    stats = await component.get_stats()
                    self.component_status[component_name] = stats.get('running', True)
                else:
                    self.component_status[component_name] = True
            except Exception as e:
                logger.warning(f"Component {component_name} health check failed: {e}")
                self.component_status[component_name] = False
                
    async def _calculate_system_health(self) -> float:
        """Calculate overall system health score"""
        if not self.component_status:
            return 0.0
            
        healthy_components = sum(1 for status in self.component_status.values() if status)
        total_components = len(self.component_status)
        
        return healthy_components / total_components if total_components > 0 else 0.0
        
    async def _check_system_health(self):
        """Check system health and take action if needed"""
        health = await self._calculate_system_health()
        self.system_metrics['system_health'] = health

        if health < 0.8:  # Less than 80% healthy
            logger.warning(f"⚠️ System health degraded: {health:.1%}")
            await self._handle_degraded_health(health)

        if health < 0.5:  # Less than 50% healthy
            logger.error(f"🚨 Critical system health: {health:.1%}")
            await self._handle_critical_health(health)

    async def _handle_degraded_health(self, health: float):
        """Handle degraded system health"""
        try:
            logger.info("🔧 Attempting to recover from degraded health...")

            # Identify unhealthy components
            unhealthy_components = []
            for component_name, status in self.component_status.items():
                if not status:
                    unhealthy_components.append(component_name)

            # Attempt recovery for each unhealthy component
            recovery_results = {}
            for component_name in unhealthy_components:
                try:
                    recovery_success = await self._recover_component(component_name)
                    recovery_results[component_name] = recovery_success

                    if recovery_success:
                        logger.info(f"✅ Recovered component: {component_name}")
                    else:
                        logger.warning(f"❌ Failed to recover component: {component_name}")

                except Exception as e:
                    logger.error(f"Error recovering component {component_name}: {e}")
                    recovery_results[component_name] = False

            # Log recovery summary
            recovered_count = sum(1 for success in recovery_results.values() if success)
            logger.info(f"Recovery attempt completed: {recovered_count}/{len(unhealthy_components)} components recovered")

        except Exception as e:
            logger.error(f"Error handling degraded health: {e}")

    async def _handle_critical_health(self, health: float):
        """Handle critical system health"""
        try:
            logger.error("🚨 System in critical state - initiating emergency procedures...")

            # Emergency actions
            emergency_actions = [
                self._emergency_pause_trading(),
                self._emergency_save_state(),
                self._emergency_notify_operators(),
                self._attempt_system_recovery()
            ]

            # Execute emergency actions
            for action in emergency_actions:
                try:
                    await action
                except Exception as e:
                    logger.error(f"Emergency action failed: {e}")

        except Exception as e:
            logger.error(f"Error handling critical health: {e}")

    async def _recover_component(self, component_name: str) -> bool:
        """Attempt to recover a specific component"""
        try:
            component = self.components.get(component_name)
            if not component:
                return False

            # Component-specific recovery strategies
            if component_name == 'ollama_hub':
                # Try to restart Ollama connection
                await component.stop()
                await asyncio.sleep(2)
                await component.initialize()
                return True

            elif component_name == 'message_broker':
                # Restart message broker
                await component.stop()
                await asyncio.sleep(1)
                await component.start()
                return True

            elif component_name == 'agent_manager':
                # Restart failed agents
                if hasattr(component, 'restart_failed_agents'):
                    return await component.restart_failed_agents()

            elif component_name == 'data_manager':
                # Reconnect data feeds
                if hasattr(component, 'reconnect_feeds'):
                    return await component.reconnect_feeds()

            # Generic recovery attempt
            if hasattr(component, 'restart'):
                await component.restart()
                return True
            elif hasattr(component, 'initialize'):
                await component.initialize()
                return True

            return False

        except Exception as e:
            logger.error(f"Error recovering component {component_name}: {e}")
            return False

    async def _emergency_pause_trading(self):
        """Emergency pause all trading activities"""
        try:
            logger.warning("🛑 Emergency pause: Stopping all trading activities")

            if self.execution_engine:
                await self.execution_engine.emergency_stop()

            if self.strategy_manager:
                await self.strategy_manager.pause_all_strategies()

            if self.agent_manager:
                await self.agent_manager.pause_all_agents()

        except Exception as e:
            logger.error(f"Error in emergency pause: {e}")

    async def _emergency_save_state(self):
        """Emergency save system state"""
        try:
            logger.info("💾 Emergency save: Saving system state")

            state_data = {
                'timestamp': datetime.now().isoformat(),
                'system_state': self.state.value,
                'component_status': self.component_status.copy(),
                'system_metrics': self.system_metrics.copy(),
                'ai_integration_status': {
                    'active': self.ai_integration_active,
                    'deployed_agents': self.ai_agents_deployed,
                    'formed_teams': self.ai_teams_formed,
                    'active_strategies': self.ai_strategies_active
                }
            }

            # Save to file (in a real implementation, this would be more robust)
            import json
            with open(f'emergency_state_{int(time.time())}.json', 'w') as f:
                json.dump(state_data, f, indent=2)

            logger.info("✅ Emergency state saved")

        except Exception as e:
            logger.error(f"Error saving emergency state: {e}")

    async def _emergency_notify_operators(self):
        """Emergency notification to operators"""
        try:
            logger.critical("📢 Emergency notification: System operators alerted")

            # In a real implementation, this would send notifications via:
            # - Email alerts
            # - SMS notifications
            # - Slack/Discord webhooks
            # - System monitoring dashboards

            notification_message = {
                'alert_type': 'CRITICAL_SYSTEM_HEALTH',
                'timestamp': datetime.now().isoformat(),
                'system_health': self.system_metrics.get('system_health', 0.0),
                'failed_components': [
                    name for name, status in self.component_status.items()
                    if not status
                ],
                'recommended_action': 'IMMEDIATE_INTERVENTION_REQUIRED'
            }

            # Log the notification (placeholder for actual notification system)
            logger.critical(f"EMERGENCY ALERT: {notification_message}")

        except Exception as e:
            logger.error(f"Error sending emergency notifications: {e}")

    async def _attempt_system_recovery(self):
        """Attempt automated system recovery"""
        try:
            logger.info("🔄 Attempting automated system recovery...")

            # Recovery steps
            recovery_steps = [
                ('restart_core_components', self._restart_core_components),
                ('reinitialize_ai_integration', self._reinitialize_ai_integration),
                ('validate_system_integrity', self._validate_system_integrity),
                ('resume_safe_operations', self._resume_safe_operations)
            ]

            recovery_results = {}

            for step_name, step_function in recovery_steps:
                try:
                    logger.info(f"Executing recovery step: {step_name}")
                    result = await step_function()
                    recovery_results[step_name] = result

                    if not result:
                        logger.warning(f"Recovery step failed: {step_name}")
                        break
                    else:
                        logger.info(f"Recovery step succeeded: {step_name}")

                except Exception as e:
                    logger.error(f"Recovery step error {step_name}: {e}")
                    recovery_results[step_name] = False
                    break

            # Evaluate recovery success
            successful_steps = sum(1 for result in recovery_results.values() if result)
            total_steps = len(recovery_steps)

            if successful_steps == total_steps:
                logger.info("✅ Automated recovery completed successfully")
                return True
            else:
                logger.warning(f"⚠️ Partial recovery: {successful_steps}/{total_steps} steps completed")
                return False

        except Exception as e:
            logger.error(f"Error in automated system recovery: {e}")
            return False

    async def _restart_core_components(self) -> bool:
        """Restart core system components"""
        try:
            logger.info("Restarting core components...")

            # Restart in dependency order
            core_components = [
                'message_broker',
                'data_manager',
                'ollama_hub',
                'agent_manager'
            ]

            for component_name in core_components:
                try:
                    component = self.components.get(component_name)
                    if component:
                        await component.stop()
                        await asyncio.sleep(1)

                        if hasattr(component, 'initialize'):
                            await component.initialize()
                        if hasattr(component, 'start'):
                            await component.start()

                        logger.info(f"✅ Restarted {component_name}")
                    else:
                        logger.warning(f"Component {component_name} not found")

                except Exception as e:
                    logger.error(f"Failed to restart {component_name}: {e}")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error restarting core components: {e}")
            return False

    async def _reinitialize_ai_integration(self) -> bool:
        """Reinitialize AI integration"""
        try:
            logger.info("Reinitializing AI integration...")

            # Reset AI integration state
            self.ai_integration_active = False
            self.ai_agents_deployed = {}
            self.ai_teams_formed = {}
            self.ai_strategies_active = {}

            # Attempt to reactivate AI integration
            result = await self.activate_ai_integration()

            if result.get('success', False):
                logger.info("✅ AI integration reinitialized successfully")
                return True
            else:
                logger.warning("⚠️ AI integration reinitialization failed")
                return False

        except Exception as e:
            logger.error(f"Error reinitializing AI integration: {e}")
            return False

    async def _validate_system_integrity(self) -> bool:
        """Validate system integrity after recovery"""
        try:
            logger.info("Validating system integrity...")

            # Check component health
            await self._update_component_status()
            health = await self._calculate_system_health()

            if health < 0.7:
                logger.warning(f"System health still low after recovery: {health:.1%}")
                return False

            # Test basic functionality
            test_results = []

            # Test message broker
            if self.message_broker:
                try:
                    stats = await self.message_broker.get_stats()
                    test_results.append(stats.get('running', False))
                except:
                    test_results.append(False)

            # Test AI integration
            if self.ai_integration_active:
                try:
                    ai_status = await self.get_ai_integration_status()
                    test_results.append(ai_status.get('ai_integration_active', False))
                except:
                    test_results.append(False)

            # Calculate test success rate
            success_rate = sum(test_results) / len(test_results) if test_results else 0

            if success_rate >= 0.8:
                logger.info(f"✅ System integrity validated: {success_rate:.1%} success rate")
                return True
            else:
                logger.warning(f"⚠️ System integrity validation failed: {success_rate:.1%} success rate")
                return False

        except Exception as e:
            logger.error(f"Error validating system integrity: {e}")
            return False

    async def _resume_safe_operations(self) -> bool:
        """Resume safe operations after recovery"""
        try:
            logger.info("Resuming safe operations...")

            # Resume components in safe order
            if self.data_manager:
                await self.data_manager.resume_data_feeds()

            if self.analytics_engine:
                await self.analytics_engine.resume_analysis()

            # Resume AI components if healthy
            if self.ai_integration_active:
                if self.agent_manager:
                    await self.agent_manager.resume_all_agents()

                if self.team_manager:
                    await self.team_manager.resume_all_teams()

            # Resume trading with reduced risk
            if self.strategy_manager:
                # Resume only low-risk strategies initially
                strategies = await self.strategy_manager.get_active_strategies()
                for strategy_id in strategies[:2]:  # Limit to first 2 strategies
                    await self.strategy_manager.start_strategy(strategy_id)

            logger.info("✅ Safe operations resumed")
            return True

        except Exception as e:
            logger.error(f"Error resuming safe operations: {e}")
            return False

    async def get_comprehensive_health_report(self) -> Dict[str, Any]:
        """Get comprehensive system health report"""
        try:
            # Update all status information
            await self._update_component_status()
            await self._update_system_metrics()

            # Calculate health metrics
            overall_health = await self._calculate_system_health()

            # Component health details
            component_health = {}
            for component_name, status in self.component_status.items():
                component = self.components.get(component_name)
                component_health[component_name] = {
                    'status': 'healthy' if status else 'unhealthy',
                    'running': status,
                    'last_check': datetime.now().isoformat()
                }

                # Get component-specific metrics if available
                if component and hasattr(component, 'get_stats'):
                    try:
                        stats = await component.get_stats()
                        component_health[component_name]['metrics'] = stats
                    except:
                        pass

            # AI integration health
            ai_health = {
                'integration_active': self.ai_integration_active,
                'deployed_agents': len(self.ai_agents_deployed),
                'formed_teams': len(self.ai_teams_formed),
                'active_strategies': len(self.ai_strategies_active)
            }

            if self.ollama_hub:
                try:
                    ai_health['available_models'] = len(await self.ollama_hub.get_available_models())
                    ai_health['deployed_models'] = len(await self.ollama_hub.deployment_manager.get_deployed_models())
                except:
                    pass

            # Performance metrics
            performance_metrics = self.system_metrics.copy()
            performance_metrics['uptime_hours'] = performance_metrics.get('uptime', 0) / 3600

            # Health recommendations
            recommendations = []
            if overall_health < 0.8:
                recommendations.append("System health is degraded - consider component restart")
            if overall_health < 0.5:
                recommendations.append("CRITICAL: Immediate intervention required")
            if not self.ai_integration_active:
                recommendations.append("AI integration is inactive - check Ollama connectivity")

            # Compile comprehensive report
            health_report = {
                'timestamp': datetime.now().isoformat(),
                'overall_health': overall_health,
                'health_status': 'healthy' if overall_health >= 0.8 else 'degraded' if overall_health >= 0.5 else 'critical',
                'system_state': self.state.value,
                'component_health': component_health,
                'ai_integration_health': ai_health,
                'performance_metrics': performance_metrics,
                'recommendations': recommendations,
                'last_recovery_attempt': getattr(self, 'last_recovery_attempt', None)
            }

            return health_report

        except Exception as e:
            logger.error(f"Error generating health report: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'overall_health': 0.0,
                'health_status': 'error',
                'error': str(e)
            }
            
    async def _update_system_metrics(self):
        """Update system performance metrics"""
        try:
            # Update uptime
            if self.start_time:
                self.system_metrics['uptime'] = time.time() - self.start_time
                
            # Update component counts
            if self.strategy_manager:
                strategies = await self.strategy_manager.get_active_strategies()
                self.system_metrics['active_strategies'] = len(strategies)
                
            if self.agent_manager:
                agents = await self.agent_manager.get_active_agents()
                self.system_metrics['active_agents'] = len(agents)
                
            # Update trade metrics (would get from execution engine)
            # self.system_metrics['total_trades'] = ...
            
        except Exception as e:
            logger.error(f"Error updating system metrics: {e}")
            
    async def get_component(self, component_name: str) -> Optional[Any]:
        """Get a specific system component"""
        return self.components.get(component_name)
        
    async def execute_system_command(self, command: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute a system-level command"""
        try:
            params = params or {}
            
            if command == "status":
                status = await self.get_system_status()
                return {"success": True, "status": status}
                
            elif command == "pause":
                success = await self.pause()
                return {"success": success, "message": "System paused" if success else "Failed to pause"}
                
            elif command == "resume":
                success = await self.resume()
                return {"success": success, "message": "System resumed" if success else "Failed to resume"}
                
            elif command == "health_check":
                await self._update_component_status()
                health = await self._calculate_system_health()
                return {"success": True, "health": health, "components": self.component_status}

            elif command == "activate_ai":
                result = await self.activate_ai_integration()
                return result

            elif command == "ai_status":
                status = await self.get_ai_integration_status()
                return {"success": True, "ai_status": status}

            else:
                return {"success": False, "error": f"Unknown command: {command}"}
                
        except Exception as e:
            logger.error(f"Error executing system command {command}: {e}")
            return {"success": False, "error": str(e)}

    # AI Integration Methods

    async def activate_ai_integration(self) -> Dict[str, Any]:
        """Activate comprehensive AI integration across the system"""
        try:
            logger.info("🤖 Activating AI Integration...")

            # Step 1: Activate AI agents
            agents_result = await self._activate_ai_agents()

            # Step 2: Form AI teams
            teams_result = await self._form_ai_teams()

            # Step 3: Deploy AI strategies
            strategies_result = await self._deploy_ai_strategies()

            # Step 4: Validate AI integration
            validation_result = await self._validate_ai_integration()

            # Update AI integration status
            self.ai_integration_active = (
                agents_result['success'] and
                teams_result['success'] and
                strategies_result['success'] and
                validation_result['success']
            )

            result = {
                'success': self.ai_integration_active,
                'agents': agents_result,
                'teams': teams_result,
                'strategies': strategies_result,
                'validation': validation_result,
                'timestamp': datetime.now().isoformat()
            }

            if self.ai_integration_active:
                safe_success(logger, f"{get_status_symbol(True)} AI Integration fully activated")
            else:
                logger.error(f"{get_status_symbol(False)} AI Integration activation failed")

            return result

        except Exception as e:
            logger.error(f"AI Integration activation failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    async def _activate_ai_agents(self) -> Dict[str, Any]:
        """Activate AI agents for all required roles"""
        try:
            logger.info("🤖 Activating AI Agents...")

            # Define required agent roles
            required_roles = [
                AgentRole.TEAM_LEADER,
                AgentRole.MARKET_ANALYST,
                AgentRole.STRATEGY_DEVELOPER,
                AgentRole.RISK_MANAGER,
                AgentRole.EXECUTION_SPECIALIST,
                AgentRole.PERFORMANCE_EVALUATOR
            ]

            activated_agents = {}
            failed_agents = []

            for role in required_roles:
                try:
                    # Create and start agent
                    agent_id = await self.agent_manager.create_agent(
                        role=role,
                        config={'auto_start': True}
                    )

                    if agent_id:
                        # Start the agent
                        await self.agent_manager.start_agent(agent_id)
                        activated_agents[role.value] = agent_id
                        safe_success(logger, f"{get_status_symbol(True)} {role.value} agent activated")
                    else:
                        failed_agents.append(role.value)
                        logger.warning(f"Failed to create {role.value} agent")

                except Exception as e:
                    failed_agents.append(role.value)
                    logger.error(f"Error activating {role.value} agent: {e}")

            # Update tracking
            self.ai_agents_deployed = activated_agents

            success_rate = len(activated_agents) / len(required_roles)

            return {
                'success': success_rate >= 0.8,  # 80% success threshold
                'activated_agents': activated_agents,
                'failed_agents': failed_agents,
                'success_rate': success_rate,
                'total_agents': len(activated_agents)
            }

        except Exception as e:
            logger.error(f"AI agents activation failed: {e}")
            return {'success': False, 'error': str(e)}

    async def _form_ai_teams(self) -> Dict[str, Any]:
        """Form AI trading teams"""
        try:
            logger.info("👥 Forming AI Teams...")

            # Define team configurations
            team_configs = [
                {
                    'name': 'Alpha_Generation_Team',
                    'type': TeamType.MOMENTUM_TEAM,
                    'mission': {
                        'objective': 'alpha_generation',
                        'strategy': 'momentum',
                        'risk_tolerance': 'moderate'
                    }
                },
                {
                    'name': 'Risk_Control_Team',
                    'type': TeamType.RISK_MANAGEMENT_TEAM,
                    'mission': {
                        'objective': 'risk_management',
                        'strategy': 'risk_control',
                        'risk_tolerance': 'conservative'
                    }
                }
            ]

            formed_teams = {}
            failed_teams = []

            for team_config in team_configs:
                try:
                    # Create team
                    team_id = await self.team_manager.create_team(
                        team_type=team_config['type'],
                        mission=team_config['mission']
                    )

                    if team_id:
                        formed_teams[team_config['name']] = {
                            'team_id': team_id,
                            'type': team_config['type'].value,
                            'mission': team_config['mission']
                        }
                        safe_success(logger, f"{get_status_symbol(True)} {team_config['name']} formed")
                    else:
                        failed_teams.append(team_config['name'])
                        logger.warning(f"Failed to form {team_config['name']}")

                except Exception as e:
                    failed_teams.append(team_config['name'])
                    logger.error(f"Error forming team {team_config['name']}: {e}")

            # Update tracking
            self.ai_teams_formed = formed_teams

            success_rate = len(formed_teams) / len(team_configs)

            return {
                'success': success_rate >= 0.5,  # 50% success threshold for teams
                'formed_teams': formed_teams,
                'failed_teams': failed_teams,
                'success_rate': success_rate,
                'total_teams': len(formed_teams)
            }

        except Exception as e:
            logger.error(f"AI teams formation failed: {e}")
            return {'success': False, 'error': str(e)}

    async def _deploy_ai_strategies(self) -> Dict[str, Any]:
        """Deploy AI-powered trading strategies"""
        try:
            logger.info("📈 Deploying AI Strategies...")

            # Define AI strategy configurations
            strategy_configs = [
                {
                    'name': 'AI_Momentum_Strategy',
                    'type': 'momentum',
                    'ai_config': {
                        'ai_enabled': True,
                        'ai_model': 'huihui_ai/magistral-abliterated:24b',
                        'decision_threshold': 0.7
                    }
                },
                {
                    'name': 'AI_Mean_Reversion_Strategy',
                    'type': 'mean_reversion',
                    'ai_config': {
                        'ai_enabled': True,
                        'ai_model': 'phi4-reasoning:plus',
                        'decision_threshold': 0.8
                    }
                }
            ]

            deployed_strategies = {}
            failed_strategies = []

            for strategy_config in strategy_configs:
                try:
                    # Create AI-powered strategy
                    strategy_id = await self.strategy_manager.create_strategy(
                        strategy_type=strategy_config['type'],
                        name=strategy_config['name'],
                        config=strategy_config['ai_config']
                    )

                    if strategy_id:
                        # Start the strategy
                        await self.strategy_manager.start_strategy(strategy_id)
                        deployed_strategies[strategy_config['name']] = {
                            'strategy_id': strategy_id,
                            'type': strategy_config['type'],
                            'ai_config': strategy_config['ai_config']
                        }
                        safe_success(logger, f"{get_status_symbol(True)} {strategy_config['name']} deployed")
                    else:
                        failed_strategies.append(strategy_config['name'])
                        logger.warning(f"Failed to deploy {strategy_config['name']}")

                except Exception as e:
                    failed_strategies.append(strategy_config['name'])
                    logger.error(f"Error deploying strategy {strategy_config['name']}: {e}")

            # Update tracking
            self.ai_strategies_active = deployed_strategies

            success_rate = len(deployed_strategies) / len(strategy_configs)

            return {
                'success': success_rate >= 0.5,  # 50% success threshold for strategies
                'deployed_strategies': deployed_strategies,
                'failed_strategies': failed_strategies,
                'success_rate': success_rate,
                'total_strategies': len(deployed_strategies)
            }

        except Exception as e:
            logger.error(f"AI strategies deployment failed: {e}")
            return {'success': False, 'error': str(e)}

    async def _validate_ai_integration(self) -> Dict[str, Any]:
        """Validate AI integration functionality"""
        try:
            logger.info("🔍 Validating AI Integration...")

            validation_tests = []

            # Test 1: AI model connectivity
            model_test = await self._test_ai_model_connectivity()
            validation_tests.append(('ai_model_connectivity', model_test))

            # Test 2: Agent communication
            agent_test = await self._test_agent_communication()
            validation_tests.append(('agent_communication', agent_test))

            # Test 3: Team coordination
            team_test = await self._test_team_coordination()
            validation_tests.append(('team_coordination', team_test))

            # Test 4: Strategy AI integration
            strategy_test = await self._test_strategy_ai_integration()
            validation_tests.append(('strategy_ai_integration', strategy_test))

            # Calculate results
            passed_tests = sum(1 for _, result in validation_tests if result)
            total_tests = len(validation_tests)
            success_rate = passed_tests / total_tests if total_tests > 0 else 0

            test_results = {test_name: result for test_name, result in validation_tests}

            return {
                'success': success_rate >= 0.75,  # 75% success threshold
                'test_results': test_results,
                'passed_tests': passed_tests,
                'total_tests': total_tests,
                'success_rate': success_rate
            }

        except Exception as e:
            logger.error(f"AI integration validation failed: {e}")
            return {'success': False, 'error': str(e)}

    async def _test_ai_model_connectivity(self) -> bool:
        """Test AI model connectivity"""
        try:
            if not self.ollama_hub:
                return False

            # Test if models are accessible
            models = await self.ollama_hub.get_available_models()
            deployed_models = await self.ollama_hub.deployment_manager.get_deployed_models()

            return len(models) > 0 and len(deployed_models) > 0

        except Exception as e:
            logger.error(f"AI model connectivity test failed: {e}")
            return False

    async def _test_agent_communication(self) -> bool:
        """Test agent communication"""
        try:
            if not self.agent_manager:
                return False

            # Get active agents
            active_agents = await self.agent_manager.get_active_agents()

            # Test if we have active agents
            return len(active_agents) > 0

        except Exception as e:
            logger.error(f"Agent communication test failed: {e}")
            return False

    async def _test_team_coordination(self) -> bool:
        """Test team coordination"""
        try:
            if not self.team_manager:
                return False

            # Check if teams are formed and operational
            return len(self.ai_teams_formed) > 0

        except Exception as e:
            logger.error(f"Team coordination test failed: {e}")
            return False

    async def _test_strategy_ai_integration(self) -> bool:
        """Test strategy AI integration"""
        try:
            if not self.strategy_manager:
                return False

            # Check if AI strategies are active
            active_strategies = await self.strategy_manager.get_active_strategies()
            ai_strategies = [s for s in active_strategies if s.get('ai_enabled', False)]

            return len(ai_strategies) > 0

        except Exception as e:
            logger.error(f"Strategy AI integration test failed: {e}")
            return False

    async def get_ai_integration_status(self) -> Dict[str, Any]:
        """Get comprehensive AI integration status"""
        try:
            return {
                'ai_integration_active': self.ai_integration_active,
                'deployed_agents': self.ai_agents_deployed,
                'formed_teams': self.ai_teams_formed,
                'active_strategies': self.ai_strategies_active,
                'ollama_models': len(await self.ollama_hub.get_available_models()) if self.ollama_hub else 0,
                'deployed_models': len(await self.ollama_hub.deployment_manager.get_deployed_models()) if self.ollama_hub else 0,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting AI integration status: {e}")
            return {
                'ai_integration_active': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    async def test_ai_agent_interaction(self) -> Dict[str, Any]:
        """Test AI agent interaction and communication"""
        try:
            logger.info("🧪 Testing AI Agent Interaction...")

            # Get active agents
            active_agents = await self.agent_manager.get_active_agents()

            if len(active_agents) < 2:
                return {
                    'success': False,
                    'error': 'Need at least 2 active agents for interaction test'
                }

            # Test agent-to-agent communication
            test_results = {}

            # Test 1: Market analyst to strategy developer communication
            market_analyst = None
            strategy_developer = None

            for agent in active_agents:
                if agent.role == AgentRole.MARKET_ANALYST:
                    market_analyst = agent
                elif agent.role == AgentRole.STRATEGY_DEVELOPER:
                    strategy_developer = agent

            if market_analyst and strategy_developer:
                # Send test message from market analyst to strategy developer
                test_message = {
                    'type': 'market_analysis',
                    'data': {
                        'symbol': 'TEST',
                        'trend': 'bullish',
                        'confidence': 0.85,
                        'timestamp': time.time()
                    }
                }

                success = await self.agent_manager.send_message_to_agent(
                    strategy_developer.name,
                    'analysis_request',
                    test_message
                )

                test_results['agent_communication'] = success

            # Test 2: Team coordination
            if self.ai_teams_formed:
                team_test_success = True
                for team_name, team_info in self.ai_teams_formed.items():
                    try:
                        # Test team coordination
                        coordination_result = await self.team_manager.coordinate_team(
                            team_info['team_id'],
                            {'test': True, 'action': 'health_check'}
                        )
                        if not coordination_result.get('success', False):
                            team_test_success = False
                    except Exception as e:
                        logger.error(f"Team coordination test failed for {team_name}: {e}")
                        team_test_success = False

                test_results['team_coordination'] = team_test_success

            # Test 3: AI model response
            if self.ollama_hub:
                try:
                    # Test if we can get model performance data
                    deployed_models = await self.ollama_hub.deployment_manager.get_deployed_models()
                    test_results['model_connectivity'] = len(deployed_models) > 0
                except Exception as e:
                    logger.error(f"Model connectivity test failed: {e}")
                    test_results['model_connectivity'] = False

            # Calculate overall success
            passed_tests = sum(1 for result in test_results.values() if result)
            total_tests = len(test_results)
            success_rate = passed_tests / total_tests if total_tests > 0 else 0

            return {
                'success': success_rate >= 0.7,  # 70% success threshold
                'test_results': test_results,
                'passed_tests': passed_tests,
                'total_tests': total_tests,
                'success_rate': success_rate,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"AI agent interaction test failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    async def optimize_ai_performance(self) -> Dict[str, Any]:
        """Optimize AI system performance based on current metrics"""
        try:
            logger.info("⚡ Optimizing AI Performance...")

            optimization_results = {}

            # Optimize agent assignments
            if self.agent_manager:
                # Check for underperforming agents
                underperforming_agents = []
                for agent in await self.agent_manager.get_active_agents():
                    if agent.metrics['failed_tasks'] > agent.metrics['completed_tasks'] * 0.3:
                        underperforming_agents.append(agent)

                optimization_results['underperforming_agents'] = len(underperforming_agents)

                # Suggest model switches for underperforming agents
                for agent in underperforming_agents:
                    try:
                        # Try to switch to a better model for this role
                        model_instance = await self.ollama_hub.deploy_model_for_agent(
                            agent_name=agent.name,
                            role=agent.role.value
                        )
                        if model_instance:
                            logger.info(f"Optimized model for agent {agent.name}")
                    except Exception as e:
                        logger.error(f"Failed to optimize model for agent {agent.name}: {e}")

            # Optimize team compositions
            if self.team_manager:
                for team_name, team_info in self.ai_teams_formed.items():
                    try:
                        optimization_result = await self.team_manager.optimize_team_composition(
                            team_info['team_id']
                        )
                        optimization_results[f'team_{team_name}'] = optimization_result['success']
                    except Exception as e:
                        logger.error(f"Failed to optimize team {team_name}: {e}")
                        optimization_results[f'team_{team_name}'] = False

            # Calculate optimization success
            optimizations = [v for v in optimization_results.values() if isinstance(v, bool)]
            success_rate = sum(optimizations) / len(optimizations) if optimizations else 1.0

            return {
                'success': success_rate >= 0.5,
                'optimization_results': optimization_results,
                'success_rate': success_rate,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"AI performance optimization failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

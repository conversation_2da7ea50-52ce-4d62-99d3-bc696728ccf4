"""
Real-Time Market Integration System - Advanced market data processing and event handling
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import numpy as np

logger = logging.getLogger(__name__)


class MarketEventType(Enum):
    """Types of market events"""
    PRICE_UPDATE = "price_update"
    VOLUME_SPIKE = "volume_spike"
    NEWS_EVENT = "news_event"
    EARNINGS_ANNOUNCEMENT = "earnings_announcement"
    MARKET_OPEN = "market_open"
    MARKET_CLOSE = "market_close"
    VOLATILITY_SPIKE = "volatility_spike"
    TECHNICAL_BREAKOUT = "technical_breakout"
    SECTOR_ROTATION = "sector_rotation"


@dataclass
class MarketEvent:
    """Market event data structure"""
    event_type: MarketEventType
    symbol: str
    timestamp: float
    data: Dict[str, Any]
    severity: str  # 'low', 'medium', 'high', 'critical'
    impact_score: float
    description: str


@dataclass
class RealTimeQuote:
    """Real-time market quote"""
    symbol: str
    bid: float
    ask: float
    last: float
    volume: int
    timestamp: float
    change: float
    change_percent: float


@dataclass
class MarketConditions:
    """Current market conditions"""
    overall_sentiment: str  # 'bullish', 'bearish', 'neutral'
    volatility_level: str  # 'low', 'medium', 'high', 'extreme'
    market_stress: float  # 0.0 to 1.0
    trend_strength: float  # 0.0 to 1.0
    liquidity_conditions: str  # 'abundant', 'normal', 'tight', 'stressed'
    sector_performance: Dict[str, float]
    timestamp: float


class RealTimeMarketSystem:
    """
    Advanced real-time market integration system that processes live market data,
    detects market events, and provides intelligent market condition analysis.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.market_config = config.get('real_time_market', {})
        
        # Market data state
        self.current_quotes: Dict[str, RealTimeQuote] = {}
        self.market_events: List[MarketEvent] = []
        self.market_conditions: Optional[MarketConditions] = None
        
        # Event handlers
        self.event_handlers: Dict[MarketEventType, List[Callable]] = {}
        self.quote_subscribers: List[Callable] = []
        self.condition_subscribers: List[Callable] = []
        
        # Market analysis
        self.price_history: Dict[str, List[Tuple[float, float]]] = {}  # symbol -> [(timestamp, price)]
        self.volume_history: Dict[str, List[Tuple[float, int]]] = {}  # symbol -> [(timestamp, volume)]
        
        # Configuration
        self.symbols = self.market_config.get('symbols', ['AAPL', 'TSLA', 'GOOGL', 'MSFT', 'AMZN'])
        self.update_interval = self.market_config.get('update_interval', 1.0)  # seconds
        self.event_detection_enabled = self.market_config.get('event_detection', True)
        self.max_history_length = self.market_config.get('max_history_length', 1000)
        
        # State
        self.running = False
        self.market_open = False
        
        # Background tasks
        self.market_tasks: List[asyncio.Task] = []
        
    async def initialize(self) -> bool:
        """Initialize the real-time market system"""
        try:
            logger.info("Initializing Real-Time Market System...")
            
            # Initialize market data structures
            await self._init_market_data()
            
            # Initialize event detection
            await self._init_event_detection()
            
            # Initialize market condition analysis
            await self._init_market_analysis()
            
            logger.info("✅ Real-Time Market System initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Real-Time Market System: {e}")
            return False
            
    async def start(self) -> bool:
        """Start the real-time market system"""
        try:
            if self.running:
                return True
                
            logger.info("Starting Real-Time Market System...")
            
            # Start background tasks
            self.market_tasks = [
                asyncio.create_task(self._market_data_loop()),
                asyncio.create_task(self._event_detection_loop()),
                asyncio.create_task(self._market_analysis_loop()),
                asyncio.create_task(self._condition_monitoring_loop())
            ]

            self.running = True

            # Generate initial market data and conditions immediately
            await self._update_market_data()
            await self._analyze_market_conditions()

            logger.info("✅ Real-Time Market System started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Real-Time Market System: {e}")
            return False
            
    async def stop(self) -> bool:
        """Stop the real-time market system"""
        try:
            if not self.running:
                return True
                
            logger.info("Stopping Real-Time Market System...")
            
            # Cancel background tasks
            for task in self.market_tasks:
                task.cancel()
            await asyncio.gather(*self.market_tasks, return_exceptions=True)
            self.market_tasks.clear()
            
            self.running = False
            logger.info("✅ Real-Time Market System stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop Real-Time Market System: {e}")
            return False
            
    async def subscribe_to_quotes(self, callback: Callable[[RealTimeQuote], None]):
        """Subscribe to real-time quote updates"""
        self.quote_subscribers.append(callback)
        
    async def subscribe_to_events(self, event_type: MarketEventType, callback: Callable[[MarketEvent], None]):
        """Subscribe to specific market events"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(callback)
        
    async def subscribe_to_conditions(self, callback: Callable[[MarketConditions], None]):
        """Subscribe to market condition updates"""
        self.condition_subscribers.append(callback)
        
    async def get_current_quote(self, symbol: str) -> Optional[RealTimeQuote]:
        """Get current quote for a symbol"""
        return self.current_quotes.get(symbol)
        
    async def get_market_conditions(self) -> Optional[MarketConditions]:
        """Get current market conditions"""
        return self.market_conditions
        
    async def get_recent_events(self, event_type: MarketEventType = None, limit: int = 100) -> List[MarketEvent]:
        """Get recent market events"""
        events = self.market_events
        
        if event_type:
            events = [e for e in events if e.event_type == event_type]
            
        return events[-limit:]
        
    async def _init_market_data(self):
        """Initialize market data structures"""
        for symbol in self.symbols:
            self.price_history[symbol] = []
            self.volume_history[symbol] = []
            
        logger.info(f"Market data initialized for {len(self.symbols)} symbols")
        
    async def _init_event_detection(self):
        """Initialize event detection algorithms"""
        logger.info("Event detection algorithms initialized")
        
    async def _init_market_analysis(self):
        """Initialize market analysis components"""
        logger.info("Market analysis components initialized")
        
    async def _market_data_loop(self):
        """Background market data processing loop"""
        while self.running:
            try:
                await asyncio.sleep(self.update_interval)
                
                # Simulate market data updates
                await self._update_market_data()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in market data loop: {e}")
                
    async def _update_market_data(self):
        """Update market data (simulated for demo)"""
        try:
            current_time = time.time()
            
            for symbol in self.symbols:
                # Simulate realistic price movement
                last_price = self.current_quotes.get(symbol, RealTimeQuote(
                    symbol=symbol, bid=100.0, ask=100.1, last=100.0, 
                    volume=0, timestamp=current_time, change=0.0, change_percent=0.0
                )).last
                
                # Random walk with some trend
                price_change = np.random.normal(0, 0.5)  # 0.5% volatility
                new_price = max(1.0, last_price * (1 + price_change / 100))
                
                # Simulate volume
                base_volume = 1000000
                volume_multiplier = np.random.uniform(0.5, 2.0)
                new_volume = int(base_volume * volume_multiplier)
                
                # Create quote
                change = new_price - last_price
                change_percent = (change / last_price) * 100 if last_price > 0 else 0.0
                
                quote = RealTimeQuote(
                    symbol=symbol,
                    bid=new_price - 0.01,
                    ask=new_price + 0.01,
                    last=new_price,
                    volume=new_volume,
                    timestamp=current_time,
                    change=change,
                    change_percent=change_percent
                )
                
                # Update current quotes
                self.current_quotes[symbol] = quote
                
                # Update history
                self.price_history[symbol].append((current_time, new_price))
                self.volume_history[symbol].append((current_time, new_volume))
                
                # Limit history size
                if len(self.price_history[symbol]) > self.max_history_length:
                    self.price_history[symbol] = self.price_history[symbol][-self.max_history_length:]
                if len(self.volume_history[symbol]) > self.max_history_length:
                    self.volume_history[symbol] = self.volume_history[symbol][-self.max_history_length:]
                
                # Notify subscribers
                for callback in self.quote_subscribers:
                    try:
                        await callback(quote)
                    except Exception as e:
                        logger.error(f"Error in quote callback: {e}")
                        
        except Exception as e:
            logger.error(f"Error updating market data: {e}")
            
    async def _event_detection_loop(self):
        """Background event detection loop"""
        while self.running:
            try:
                await asyncio.sleep(5.0)  # Check every 5 seconds
                
                if self.event_detection_enabled:
                    await self._detect_market_events()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in event detection loop: {e}")
                
    async def _detect_market_events(self):
        """Detect market events from current data"""
        try:
            current_time = time.time()
            
            for symbol in self.symbols:
                quote = self.current_quotes.get(symbol)
                if not quote:
                    continue
                    
                # Detect volume spikes
                if await self._detect_volume_spike(symbol, quote):
                    event = MarketEvent(
                        event_type=MarketEventType.VOLUME_SPIKE,
                        symbol=symbol,
                        timestamp=current_time,
                        data={'volume': quote.volume, 'price': quote.last},
                        severity='medium',
                        impact_score=0.6,
                        description=f"Volume spike detected for {symbol}: {quote.volume:,} shares"
                    )
                    await self._emit_event(event)
                
                # Detect volatility spikes
                if await self._detect_volatility_spike(symbol, quote):
                    event = MarketEvent(
                        event_type=MarketEventType.VOLATILITY_SPIKE,
                        symbol=symbol,
                        timestamp=current_time,
                        data={'change_percent': quote.change_percent, 'price': quote.last},
                        severity='high',
                        impact_score=0.8,
                        description=f"Volatility spike detected for {symbol}: {quote.change_percent:.2f}% change"
                    )
                    await self._emit_event(event)
                
                # Detect technical breakouts
                if await self._detect_technical_breakout(symbol, quote):
                    event = MarketEvent(
                        event_type=MarketEventType.TECHNICAL_BREAKOUT,
                        symbol=symbol,
                        timestamp=current_time,
                        data={'price': quote.last, 'breakout_type': 'resistance'},
                        severity='medium',
                        impact_score=0.7,
                        description=f"Technical breakout detected for {symbol} at ${quote.last:.2f}"
                    )
                    await self._emit_event(event)
                    
        except Exception as e:
            logger.error(f"Error detecting market events: {e}")
            
    async def _detect_volume_spike(self, symbol: str, quote: RealTimeQuote) -> bool:
        """Detect volume spike for a symbol"""
        try:
            volume_history = self.volume_history.get(symbol, [])
            if len(volume_history) < 20:  # Need history for comparison
                return False
                
            recent_volumes = [v[1] for v in volume_history[-20:]]
            avg_volume = np.mean(recent_volumes)
            
            # Volume spike if current volume is 3x average
            return quote.volume > avg_volume * 3
            
        except Exception as e:
            logger.error(f"Error detecting volume spike: {e}")
            return False
            
    async def _detect_volatility_spike(self, symbol: str, quote: RealTimeQuote) -> bool:
        """Detect volatility spike for a symbol"""
        try:
            # Volatility spike if price change > 3%
            return abs(quote.change_percent) > 3.0
            
        except Exception as e:
            logger.error(f"Error detecting volatility spike: {e}")
            return False
            
    async def _detect_technical_breakout(self, symbol: str, quote: RealTimeQuote) -> bool:
        """Detect technical breakout for a symbol"""
        try:
            price_history = self.price_history.get(symbol, [])
            if len(price_history) < 50:  # Need history for technical analysis
                return False
                
            recent_prices = [p[1] for p in price_history[-50:]]
            resistance_level = max(recent_prices[-20:])  # 20-period high
            
            # Breakout if current price exceeds recent resistance by 1%
            return quote.last > resistance_level * 1.01
            
        except Exception as e:
            logger.error(f"Error detecting technical breakout: {e}")
            return False
            
    async def _emit_event(self, event: MarketEvent):
        """Emit a market event to subscribers"""
        try:
            # Add to event history
            self.market_events.append(event)
            
            # Limit event history
            if len(self.market_events) > 1000:
                self.market_events = self.market_events[-1000:]
                
            # Notify event handlers
            handlers = self.event_handlers.get(event.event_type, [])
            for handler in handlers:
                try:
                    await handler(event)
                except Exception as e:
                    logger.error(f"Error in event handler: {e}")
                    
            logger.info(f"Market event: {event.description}")
            
        except Exception as e:
            logger.error(f"Error emitting event: {e}")
            
    async def _market_analysis_loop(self):
        """Background market analysis loop"""
        while self.running:
            try:
                await asyncio.sleep(5.0)  # Analyze every 5 seconds for faster testing

                await self._analyze_market_conditions()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in market analysis loop: {e}")
                
    async def _analyze_market_conditions(self):
        """Analyze overall market conditions"""
        try:
            current_time = time.time()
            
            # Calculate market sentiment
            sentiment = await self._calculate_market_sentiment()
            
            # Calculate volatility level
            volatility = await self._calculate_market_volatility()
            
            # Calculate market stress
            stress = await self._calculate_market_stress()
            
            # Calculate trend strength
            trend = await self._calculate_trend_strength()
            
            # Analyze liquidity conditions
            liquidity = await self._analyze_liquidity_conditions()
            
            # Analyze sector performance
            sector_performance = await self._analyze_sector_performance()
            
            # Create market conditions
            conditions = MarketConditions(
                overall_sentiment=sentiment,
                volatility_level=volatility,
                market_stress=stress,
                trend_strength=trend,
                liquidity_conditions=liquidity,
                sector_performance=sector_performance,
                timestamp=current_time
            )
            
            self.market_conditions = conditions
            
            # Notify subscribers
            for callback in self.condition_subscribers:
                try:
                    await callback(conditions)
                except Exception as e:
                    logger.error(f"Error in condition callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error analyzing market conditions: {e}")
            
    async def _calculate_market_sentiment(self) -> str:
        """Calculate overall market sentiment"""
        try:
            positive_moves = 0
            total_moves = 0
            
            for symbol in self.symbols:
                quote = self.current_quotes.get(symbol)
                if quote:
                    total_moves += 1
                    if quote.change > 0:
                        positive_moves += 1
                        
            if total_moves == 0:
                return 'neutral'
                
            positive_ratio = positive_moves / total_moves
            
            if positive_ratio > 0.6:
                return 'bullish'
            elif positive_ratio < 0.4:
                return 'bearish'
            else:
                return 'neutral'
                
        except Exception as e:
            logger.error(f"Error calculating market sentiment: {e}")
            return 'neutral'
            
    async def _calculate_market_volatility(self) -> str:
        """Calculate market volatility level"""
        try:
            volatilities = []
            
            for symbol in self.symbols:
                quote = self.current_quotes.get(symbol)
                if quote:
                    volatilities.append(abs(quote.change_percent))
                    
            if not volatilities:
                return 'normal'
                
            avg_volatility = np.mean(volatilities)
            
            if avg_volatility > 3.0:
                return 'extreme'
            elif avg_volatility > 2.0:
                return 'high'
            elif avg_volatility > 1.0:
                return 'medium'
            else:
                return 'low'
                
        except Exception as e:
            logger.error(f"Error calculating market volatility: {e}")
            return 'normal'
            
    async def _calculate_market_stress(self) -> float:
        """Calculate market stress level (0.0 to 1.0)"""
        try:
            # Simplified stress calculation based on volatility and volume
            volatilities = []
            volume_ratios = []
            
            for symbol in self.symbols:
                quote = self.current_quotes.get(symbol)
                if quote:
                    volatilities.append(abs(quote.change_percent))
                    
                    # Calculate volume ratio vs average
                    volume_history = self.volume_history.get(symbol, [])
                    if len(volume_history) > 10:
                        recent_volumes = [v[1] for v in volume_history[-10:]]
                        avg_volume = np.mean(recent_volumes)
                        volume_ratios.append(quote.volume / avg_volume if avg_volume > 0 else 1.0)
                        
            if not volatilities:
                return 0.0
                
            avg_volatility = np.mean(volatilities)
            avg_volume_ratio = np.mean(volume_ratios) if volume_ratios else 1.0
            
            # Stress increases with volatility and unusual volume
            stress = min(1.0, (avg_volatility / 5.0) * 0.7 + (max(0, avg_volume_ratio - 1.0) / 2.0) * 0.3)
            
            return stress
            
        except Exception as e:
            logger.error(f"Error calculating market stress: {e}")
            return 0.0
            
    async def _calculate_trend_strength(self) -> float:
        """Calculate trend strength (0.0 to 1.0)"""
        try:
            trend_strengths = []
            
            for symbol in self.symbols:
                price_history = self.price_history.get(symbol, [])
                if len(price_history) > 20:
                    recent_prices = [p[1] for p in price_history[-20:]]
                    
                    # Calculate trend using linear regression
                    x = np.arange(len(recent_prices))
                    slope, _ = np.polyfit(x, recent_prices, 1)
                    
                    # Normalize slope to trend strength
                    price_range = max(recent_prices) - min(recent_prices)
                    if price_range > 0:
                        trend_strength = abs(slope) / (price_range / len(recent_prices))
                        trend_strengths.append(min(1.0, trend_strength))
                        
            return np.mean(trend_strengths) if trend_strengths else 0.5
            
        except Exception as e:
            logger.error(f"Error calculating trend strength: {e}")
            return 0.5
            
    async def _analyze_liquidity_conditions(self) -> str:
        """Analyze market liquidity conditions"""
        try:
            # Simplified liquidity analysis based on bid-ask spreads
            spreads = []
            
            for symbol in self.symbols:
                quote = self.current_quotes.get(symbol)
                if quote and quote.bid > 0:
                    spread = (quote.ask - quote.bid) / quote.bid
                    spreads.append(spread)
                    
            if not spreads:
                return 'normal'
                
            avg_spread = np.mean(spreads)
            
            if avg_spread > 0.005:  # 0.5%
                return 'stressed'
            elif avg_spread > 0.002:  # 0.2%
                return 'tight'
            elif avg_spread < 0.001:  # 0.1%
                return 'abundant'
            else:
                return 'normal'
                
        except Exception as e:
            logger.error(f"Error analyzing liquidity conditions: {e}")
            return 'normal'
            
    async def _analyze_sector_performance(self) -> Dict[str, float]:
        """Analyze sector performance"""
        try:
            # Simplified sector analysis (all symbols assumed to be tech for demo)
            tech_performance = []
            
            for symbol in self.symbols:
                quote = self.current_quotes.get(symbol)
                if quote:
                    tech_performance.append(quote.change_percent)
                    
            return {
                'Technology': np.mean(tech_performance) if tech_performance else 0.0,
                'Healthcare': np.random.uniform(-1.0, 1.0),  # Mock data
                'Finance': np.random.uniform(-1.0, 1.0),     # Mock data
                'Energy': np.random.uniform(-2.0, 2.0),      # Mock data
                'Consumer': np.random.uniform(-1.0, 1.0)     # Mock data
            }
            
        except Exception as e:
            logger.error(f"Error analyzing sector performance: {e}")
            return {}
            
    async def _condition_monitoring_loop(self):
        """Background condition monitoring loop"""
        while self.running:
            try:
                await asyncio.sleep(60.0)  # Monitor every minute
                
                # Check for significant condition changes
                await self._monitor_condition_changes()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in condition monitoring loop: {e}")
                
    async def _monitor_condition_changes(self):
        """Monitor for significant market condition changes"""
        try:
            if not self.market_conditions:
                return
                
            # Check for significant stress level changes
            if self.market_conditions.market_stress > 0.7:
                event = MarketEvent(
                    event_type=MarketEventType.VOLATILITY_SPIKE,
                    symbol='MARKET',
                    timestamp=time.time(),
                    data={'stress_level': self.market_conditions.market_stress},
                    severity='high',
                    impact_score=0.9,
                    description=f"High market stress detected: {self.market_conditions.market_stress:.1%}"
                )
                await self._emit_event(event)
                
        except Exception as e:
            logger.error(f"Error monitoring condition changes: {e}")
            
    async def get_system_status(self) -> Dict[str, Any]:
        """Get real-time market system status"""
        return {
            'running': self.running,
            'market_open': self.market_open,
            'symbols_tracked': len(self.symbols),
            'active_quotes': len(self.current_quotes),
            'recent_events': len(self.market_events[-100:]),
            'event_detection_enabled': self.event_detection_enabled,
            'update_interval': self.update_interval,
            'current_conditions': self.market_conditions.__dict__ if self.market_conditions else None,
            'subscribers': {
                'quote_subscribers': len(self.quote_subscribers),
                'condition_subscribers': len(self.condition_subscribers),
                'event_handlers': sum(len(handlers) for handlers in self.event_handlers.values())
            }
        }

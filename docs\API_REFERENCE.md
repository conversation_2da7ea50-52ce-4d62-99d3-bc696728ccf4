# 📡 API Reference

## Overview
This document provides comprehensive API reference for the Advanced Ollama Trading Agent System.

## Agent Manager API

### Create Agent
Creates a new agent of the specified role.

```python
async def create_agent(
    role: Agent<PERSON><PERSON>,
    name: str = None,
    config: Dict[str, Any] = None
) -> Optional[str]
```

**Parameters:**
- `role`: Agent role (TEAM_LEADER, MARKET_ANALYST, etc.)
- `name`: Optional custom name for the agent
- `config`: Optional configuration overrides

**Returns:**
- `str`: Agent ID if successful, None if failed

**Example:**
```python
agent_id = await agent_manager.create_agent(
    role=AgentRole.MARKET_ANALYST,
    name="MarketAnalyst_AAPL",
    config={'specialization': 'tech_stocks'}
)
```

### Start Agent
Starts a specific agent.

```python
async def start_agent(agent_id: str) -> bool
```

**Parameters:**
- `agent_id`: ID of the agent to start

**Returns:**
- `bool`: True if successful, False otherwise

### Get Agent Status
Retrieves the status of a specific agent.

```python
async def get_agent_status(agent_id: str) -> Optional[Dict[str, Any]]
```

**Returns:**
```json
{
    "agent_id": "agent_123",
    "name": "MarketAnalyst_1",
    "role": "market_analyst",
    "state": "active",
    "tasks_completed": 45,
    "avg_response_time": 2.3,
    "last_activity": 1640995200.0
}
```

### Get System Statistics
Retrieves system-wide agent statistics.

```python
async def get_system_stats() -> Dict[str, Any]
```

**Returns:**
```json
{
    "total_agents": 12,
    "agents_by_role": {
        "team_leader": 2,
        "market_analyst": 3,
        "strategy_developer": 2,
        "risk_manager": 2,
        "execution_specialist": 2,
        "performance_evaluator": 1
    },
    "agents_by_state": {
        "active": 10,
        "idle": 2,
        "error": 0
    },
    "performance_summary": {
        "total_tasks": 1250,
        "completed_tasks": 1200,
        "failed_tasks": 50,
        "avg_response_time": 2.1
    }
}
```

## Team Manager API

### Create Team
Creates a new trading team.

```python
async def create_team(
    team_type: TeamType,
    mission: Dict[str, Any]
) -> Optional[str]
```

**Parameters:**
- `team_type`: Type of team (MOMENTUM_TEAM, MEAN_REVERSION_TEAM, etc.)
- `mission`: Mission parameters for the team

**Example:**
```python
team_id = await team_manager.create_team(
    team_type=TeamType.MOMENTUM_TEAM,
    mission={
        'strategy': 'breakout_momentum',
        'symbols': ['AAPL', 'GOOGL', 'TSLA'],
        'time_horizon': 'short_term',
        'risk_level': 'medium'
    }
)
```

### Coordinate Team Mission
Coordinates a mission for a specific team.

```python
async def coordinate_team_mission(
    team_id: str,
    mission_data: Dict[str, Any]
) -> Dict[str, Any]
```

**Returns:**
```json
{
    "success": true,
    "mission_id": "mission_123",
    "coordination_type": "fast_execution",
    "execution_time": 2.5,
    "team_members": ["agent_1", "agent_2", "agent_3"]
}
```

### Get Active Teams
Retrieves all active teams.

```python
async def get_active_teams() -> List[Dict[str, Any]]
```

## Agent Communication API

### Send Message
Sends a message to another agent.

```python
async def send_message(
    recipient: str,
    message: Message
) -> bool
```

### Request Analysis
Requests analysis from another agent.

```python
async def request_analysis(
    recipient: str,
    data: Dict[str, Any],
    analysis_type: str = "general",
    timeout: int = None
) -> Optional[Dict[str, Any]]
```

**Example:**
```python
analysis = await communication.request_analysis(
    recipient="market_analyst_1",
    data={
        'symbol': 'AAPL',
        'timeframe': '1h',
        'price_data': price_history
    },
    analysis_type="technical_analysis",
    timeout=60
)
```

### Request Decision
Requests a decision from another agent.

```python
async def request_decision(
    recipient: str,
    options: List[Any],
    context: Dict[str, Any],
    timeout: int = None
) -> Optional[Dict[str, Any]]
```

## Market Analyst Agent API

### Technical Analysis
Performs technical analysis on market data.

**Task Type:** `technical_analysis`

**Parameters:**
```json
{
    "symbol": "AAPL",
    "timeframe": "1h",
    "indicators": ["sma", "rsi", "macd", "bollinger_bands"]
}
```

**Response:**
```json
{
    "success": true,
    "analysis": {
        "trend_analysis": {
            "direction": "bullish",
            "strength": 0.75,
            "support_levels": [150.0, 148.5],
            "resistance_levels": [155.0, 157.5]
        },
        "technical_indicators": {
            "rsi": 65.2,
            "macd": {
                "signal": "bullish_crossover",
                "histogram": 0.45
            },
            "bollinger_bands": {
                "position": "upper_band",
                "squeeze": false
            }
        },
        "trading_signals": {
            "entry_signal": "buy",
            "confidence": 0.78,
            "price_target": 158.0,
            "stop_loss": 149.0
        }
    }
}
```

### Pattern Recognition
Identifies chart patterns in market data.

**Task Type:** `pattern_recognition`

**Parameters:**
```json
{
    "symbol": "TSLA",
    "timeframe": "4h",
    "pattern_types": ["head_and_shoulders", "triangle", "flag"]
}
```

### Sentiment Analysis
Analyzes market sentiment.

**Task Type:** `sentiment_analysis`

**Parameters:**
```json
{
    "symbol": "SPY",
    "sources": ["market_data", "technical_indicators"]
}
```

## Strategy Developer Agent API

### Develop Strategy
Creates a new trading strategy.

**Task Type:** `develop_strategy`

**Parameters:**
```json
{
    "strategy_type": "mean_reversion",
    "market_conditions": {
        "volatility": "medium",
        "trend": "sideways"
    },
    "constraints": {
        "max_drawdown": 0.10,
        "min_sharpe": 1.2
    },
    "objectives": {
        "target_return": 0.15,
        "risk_level": "medium"
    }
}
```

### Backtest Strategy
Backtests a trading strategy.

**Task Type:** `backtest_strategy`

**Parameters:**
```json
{
    "strategy_id": "strategy_123",
    "period": "2y",
    "symbols": ["AAPL", "GOOGL", "MSFT"]
}
```

### Optimize Strategy
Optimizes strategy parameters.

**Task Type:** `optimize_strategy`

**Parameters:**
```json
{
    "strategy_id": "strategy_123",
    "method": "genetic_algorithm",
    "objective": "sharpe_ratio"
}
```

## Risk Manager Agent API

### Assess Portfolio Risk
Performs comprehensive portfolio risk assessment.

**Task Type:** `assess_portfolio_risk`

**Parameters:**
```json
{
    "portfolio": {
        "AAPL": {"position": 1000, "value": 150000},
        "GOOGL": {"position": 500, "value": 100000}
    },
    "market_data": {
        "volatilities": {"AAPL": 0.25, "GOOGL": 0.30},
        "correlations": {"AAPL_GOOGL": 0.65}
    }
}
```

### Calculate Position Size
Calculates optimal position size.

**Task Type:** `calculate_position_size`

**Parameters:**
```json
{
    "symbol": "NVDA",
    "strategy_risk": 0.02,
    "portfolio_value": 1000000,
    "volatility": 0.35
}
```

### Stress Test
Performs portfolio stress testing.

**Task Type:** `stress_test`

**Parameters:**
```json
{
    "portfolio": {
        "AAPL": {"position": 1000, "value": 150000}
    },
    "scenarios": ["market_crash", "volatility_spike"]
}
```

## Execution Specialist Agent API

### Execute Order
Optimizes order execution.

**Task Type:** `execute_order`

**Parameters:**
```json
{
    "order": {
        "symbol": "AAPL",
        "side": "buy",
        "quantity": 5000,
        "type": "market"
    },
    "execution_params": {
        "max_participation_rate": 0.10,
        "time_horizon": 3600
    },
    "urgency": "normal"
}
```

### Analyze Market Impact
Analyzes market impact of trades.

**Task Type:** `analyze_market_impact`

**Parameters:**
```json
{
    "symbol": "TSLA",
    "quantity": 10000,
    "side": "sell",
    "market_data": {
        "bid_ask_spread": 0.05,
        "market_depth": 50000
    }
}
```

## Performance Evaluator Agent API

### Evaluate Performance
Performs comprehensive performance evaluation.

**Task Type:** `evaluate_performance`

**Parameters:**
```json
{
    "entity_type": "strategy",
    "entity_id": "momentum_strategy_v1",
    "performance_data": {
        "returns": [0.02, -0.01, 0.03],
        "benchmark_returns": [0.015, -0.005, 0.025],
        "volatility": 0.15,
        "sharpe_ratio": 1.25
    },
    "period": "6M"
}
```

### Optimization Recommendations
Generates optimization recommendations.

**Task Type:** `optimization_recommendations`

**Parameters:**
```json
{
    "target_entity": {
        "type": "portfolio",
        "id": "main_portfolio"
    },
    "performance_issues": [
        "high_volatility",
        "poor_risk_adjusted_returns"
    ],
    "constraints": {
        "max_leverage": 2.0
    }
}
```

## Error Handling

### Standard Error Response
```json
{
    "success": false,
    "error": "Error description",
    "error_code": "ERROR_CODE",
    "timestamp": 1640995200.0
}
```

### Common Error Codes
- `AGENT_NOT_FOUND`: Agent ID not found
- `INVALID_PARAMETERS`: Invalid task parameters
- `TIMEOUT_ERROR`: Task execution timeout
- `MODEL_ERROR`: Model inference error
- `COMMUNICATION_ERROR`: Inter-agent communication error

## Rate Limits
- **Agent Tasks**: 100 tasks per minute per agent
- **Communication**: 1000 messages per minute per agent
- **API Calls**: 10000 calls per hour per client

## Authentication
```python
# Set API key in headers
headers = {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
}
```

## WebSocket API
Real-time updates via WebSocket connection.

### Connect
```javascript
const ws = new WebSocket('ws://localhost:8080/ws');
```

### Subscribe to Events
```json
{
    "action": "subscribe",
    "events": ["agent_status", "team_updates", "risk_alerts"]
}
```

### Event Types
- `agent_status`: Agent status changes
- `team_updates`: Team formation/dissolution
- `risk_alerts`: Risk limit violations
- `market_alerts`: Market anomalies
- `performance_updates`: Performance metrics

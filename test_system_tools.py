#!/usr/bin/env python3
"""
System Tools Integration Test - Test all core system tools
"""

import asyncio
import json
from datetime import datetime
from config.config_manager import ConfigManager

async def test_system_tools():
    """Test all system tools and infrastructure"""
    
    print("🔧 SYSTEM TOOLS INTEGRATION TEST")
    print("=" * 50)
    
    results = {}
    
    try:
        # Load configuration
        config_manager = ConfigManager('config/test_config.yaml')
        await config_manager.load_config()
        config = await config_manager.get_config()
        
        print("✅ Configuration loaded")
        
        # Test 1: Data Manager
        print("\n📊 TEST 1: Data Manager")
        try:
            from data.data_manager import DataManager
            
            data_manager = DataManager(config)
            await data_manager.initialize()
            
            # Test market data retrieval
            market_data = await data_manager.get_market_data("AAPL", "1d", limit=10)
            print(f"✅ Market data retrieved: {type(market_data)}")
            
            # Test historical data
            historical_data = await data_manager.get_historical_data("AAPL", "2024-01-01", "2024-12-31")
            print(f"✅ Historical data retrieved: {type(historical_data)}")
            
            results['data_manager'] = {'success': True, 'status': 'functional'}
            
        except ImportError:
            print("❌ DataManager not available")
            results['data_manager'] = {'success': False, 'error': 'Not implemented'}
        except Exception as e:
            print(f"❌ DataManager error: {e}")
            results['data_manager'] = {'success': False, 'error': str(e)}
        
        # Test 2: Portfolio Manager
        print("\n💼 TEST 2: Portfolio Manager")
        try:
            from portfolio.portfolio_manager import PortfolioManager

            portfolio_manager = PortfolioManager(config)
            await portfolio_manager.initialize()

            # Test portfolio creation
            portfolio_id = await portfolio_manager.create_portfolio("test_portfolio", 100000.0)
            print(f"✅ Portfolio created: {portfolio_id}")

            # Test position management - fix parameter order
            success = await portfolio_manager.add_position("AAPL", 100, 150.0)
            print(f"✅ Position added: {success}")

            # Test portfolio value
            portfolio_value = await portfolio_manager.get_portfolio_value(portfolio_id)
            print(f"✅ Portfolio value: ${portfolio_value}")

            results['portfolio_manager'] = {'success': True, 'status': 'functional'}

        except ImportError as e:
            print(f"❌ PortfolioManager import error: {e}")
            results['portfolio_manager'] = {'success': False, 'error': f'Import error: {e}'}
        except Exception as e:
            print(f"❌ PortfolioManager error: {e}")
            results['portfolio_manager'] = {'success': False, 'error': str(e)}
        
        # Test 3: Execution Engine
        print("\n⚡ TEST 3: Execution Engine")
        try:
            from execution.execution_engine import ExecutionEngine
            from execution.order_types import Order, OrderType, OrderSide

            execution_engine = ExecutionEngine(config)
            await execution_engine.initialize()

            # Test order creation
            order = Order(
                order_id="test_order_001",
                symbol="AAPL",
                side=OrderSide.BUY,
                quantity=100,
                order_type=OrderType.MARKET,
                strategy_id="test_strategy"
            )

            # Test order submission
            execution_result = await execution_engine.submit_order(order)
            print(f"✅ Order submitted: {execution_result.success if hasattr(execution_result, 'success') else 'Success'}")

            # Test active orders
            active_orders = await execution_engine.get_active_orders()
            print(f"✅ Active orders retrieved: {len(active_orders) if isinstance(active_orders, list) else 'Available'}")

            results['execution_engine'] = {'success': True, 'status': 'functional'}

        except ImportError as e:
            print(f"❌ ExecutionEngine import error: {e}")
            results['execution_engine'] = {'success': False, 'error': f'Import error: {e}'}
        except Exception as e:
            print(f"❌ ExecutionEngine error: {e}")
            results['execution_engine'] = {'success': False, 'error': str(e)}
        
        # Test 4: Analytics Engine
        print("\n📈 TEST 4: Analytics Engine")
        try:
            from analytics.analytics_engine import AdvancedAnalyticsEngine

            analytics_engine = AdvancedAnalyticsEngine(config)
            await analytics_engine.initialize()

            # Test performance calculation
            performance_data = {
                'portfolio_id': 'test_portfolio',
                'start_date': '2024-01-01',
                'end_date': '2024-12-31'
            }

            performance_metrics = await analytics_engine.calculate_performance(performance_data)
            print(f"✅ Performance calculated: {type(performance_metrics)}")

            # Test analytics report
            report = await analytics_engine.generate_report('daily', performance_data)
            print(f"✅ Report generated: {type(report)}")

            results['analytics_engine'] = {'success': True, 'status': 'functional'}

        except ImportError as e:
            print(f"❌ AnalyticsEngine import error: {e}")
            results['analytics_engine'] = {'success': False, 'error': f'Import error: {e}'}
        except Exception as e:
            print(f"❌ AnalyticsEngine error: {e}")
            results['analytics_engine'] = {'success': False, 'error': str(e)}
        
        # Test 5: Strategy Manager (Re-test)
        print("\n🎯 TEST 5: Strategy Manager")
        try:
            from strategies.strategy_manager import StrategyManager
            
            strategy_manager = StrategyManager(config)
            await strategy_manager.initialize()
            
            # Test strategy creation
            strategy_id = await strategy_manager.create_strategy(
                strategy_type='momentum',
                name='test_momentum_strategy',
                config={'lookback_period': 20, 'threshold': 0.02}
            )
            print(f"✅ Strategy created: {strategy_id}")
            
            # Test strategy status
            status = strategy_manager.get_strategy_status(strategy_id)
            print(f"✅ Strategy status: {status}")
            
            results['strategy_manager'] = {'success': True, 'status': 'functional'}
            
        except Exception as e:
            print(f"❌ StrategyManager error: {e}")
            results['strategy_manager'] = {'success': False, 'error': str(e)}
        
        # Test 6: Risk Manager (Re-test)
        print("\n⚠️ TEST 6: Risk Manager")
        try:
            from risk.risk_manager import RiskManager
            
            risk_manager = RiskManager(config)
            await risk_manager.initialize()
            
            # Test portfolio risk assessment
            portfolio_data = {
                'total_value': 100000,
                'positions': [
                    {'symbol': 'AAPL', 'value': 30000, 'shares': 200},
                    {'symbol': 'TSLA', 'value': 25000, 'shares': 100}
                ],
                'cash': 45000
            }
            
            risk_assessment = await risk_manager.assess_portfolio_risk(portfolio_data)
            print(f"✅ Risk assessment: {type(risk_assessment)}")
            
            results['risk_manager'] = {'success': True, 'status': 'functional'}
            
        except Exception as e:
            print(f"❌ RiskManager error: {e}")
            results['risk_manager'] = {'success': False, 'error': str(e)}
        
        # Summary
        print("\n🎉 SYSTEM TOOLS TEST COMPLETE!")
        print("=" * 50)
        
        successful_tools = sum(1 for result in results.values() if result.get('success'))
        total_tools = len(results)
        
        print(f"📊 Tool Results: {successful_tools}/{total_tools} tools functional")
        print(f"Success Rate: {(successful_tools/total_tools)*100:.1f}%")
        
        # Detailed results
        for tool_name, result in results.items():
            status = "✅ WORKING" if result.get('success') else "❌ FAILED"
            error = f" - {result.get('error')}" if result.get('error') else ""
            print(f"  {tool_name}: {status}{error}")
        
        # Save results
        test_summary = {
            "timestamp": datetime.now().isoformat(),
            "tool_results": results,
            "success_rate": (successful_tools/total_tools)*100,
            "functional_tools": successful_tools,
            "total_tools": total_tools
        }
        
        with open('system_tools_results.json', 'w') as f:
            json.dump(test_summary, f, indent=2, default=str)
        
        print(f"\n📄 Results saved to: system_tools_results.json")
        
        return successful_tools >= (total_tools * 0.7)  # 70% success threshold
        
    except Exception as e:
        print(f"❌ System Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_system_tools())
    if success:
        print("\n🎉 SYSTEM TOOLS TEST SUCCESSFUL!")
    else:
        print("\n⚠️ SYSTEM TOOLS NEED IMPROVEMENT")

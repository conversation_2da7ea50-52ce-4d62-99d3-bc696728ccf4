"""
Hierarchical Team Structures - Leadership chains, delegation, and authority management
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Set, Tuple
from enum import Enum
from dataclasses import dataclass
import networkx as nx

from agents.base_agent import AgentRole

logger = logging.getLogger(__name__)


class AuthorityLevel(Enum):
    """Authority levels in team hierarchy"""
    SUPREME_COMMANDER = 5  # Ultimate decision authority
    TEAM_LEADER = 4        # Team-level decisions
    SENIOR_SPECIALIST = 3  # Domain expertise decisions
    SPECIALIST = 2         # Operational decisions
    CONTRIBUTOR = 1        # Task execution


class DelegationScope(Enum):
    """Scope of delegation"""
    FULL_AUTHORITY = "full_authority"      # Complete decision making
    BOUNDED_AUTHORITY = "bounded_authority" # Within defined limits
    ADVISORY_ONLY = "advisory_only"        # Recommendations only
    EXECUTION_ONLY = "execution_only"      # Task execution only


@dataclass
class AuthorityGrant:
    """Authority grant for an agent"""
    agent_id: str
    authority_level: AuthorityLevel
    scope: DelegationScope
    domain: str  # Domain of authority (e.g., "risk_management", "execution")
    limits: Dict[str, Any]  # Authority limits and constraints
    granted_by: str  # Who granted this authority
    granted_at: float  # Timestamp
    expires_at: Optional[float] = None  # Optional expiration


@dataclass
class DelegationChain:
    """Chain of delegation from superior to subordinate"""
    superior_id: str
    subordinate_id: str
    delegation_scope: DelegationScope
    delegated_authorities: List[str]  # List of authority domains
    conditions: Dict[str, Any]  # Conditions for delegation
    created_at: float
    active: bool = True


class HierarchicalTeamStructure:
    """
    Manages hierarchical team structures with clear leadership chains,
    delegation mechanisms, and authority levels.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Hierarchy management
        self.team_hierarchies: Dict[str, nx.DiGraph] = {}  # team_id -> hierarchy graph
        self.authority_grants: Dict[str, List[AuthorityGrant]] = {}  # agent_id -> grants
        self.delegation_chains: Dict[str, List[DelegationChain]] = {}  # team_id -> chains
        
        # Role-based authority mapping
        self.role_authority_mapping = {
            AgentRole.TEAM_LEADER: AuthorityLevel.TEAM_LEADER,
            AgentRole.MARKET_ANALYST: AuthorityLevel.SPECIALIST,
            AgentRole.STRATEGY_DEVELOPER: AuthorityLevel.SENIOR_SPECIALIST,
            AgentRole.RISK_MANAGER: AuthorityLevel.SENIOR_SPECIALIST,
            AgentRole.EXECUTION_SPECIALIST: AuthorityLevel.SPECIALIST,
            AgentRole.PERFORMANCE_EVALUATOR: AuthorityLevel.SPECIALIST
        }
        
        # Decision routing rules
        self.decision_routing_rules: Dict[str, Dict[str, Any]] = {}
        
        # State
        self.initialized = False
        
    async def initialize(self):
        """Initialize hierarchical team structures"""
        if self.initialized:
            return
            
        logger.info("Initializing Hierarchical Team Structures...")
        
        # Setup authority frameworks
        await self._setup_authority_frameworks()
        
        # Setup delegation protocols
        await self._setup_delegation_protocols()
        
        # Setup decision routing
        await self._setup_decision_routing()
        
        self.initialized = True
        logger.info("✓ Hierarchical Team Structures initialized")
        
    async def _setup_authority_frameworks(self):
        """Setup authority frameworks and limits"""
        self.authority_frameworks = {
            'trading_decisions': {
                AuthorityLevel.SUPREME_COMMANDER: {
                    'max_position_size': 1.0,  # 100% of portfolio
                    'max_risk_exposure': 1.0,
                    'strategy_changes': True,
                    'team_restructuring': True
                },
                AuthorityLevel.TEAM_LEADER: {
                    'max_position_size': 0.5,  # 50% of portfolio
                    'max_risk_exposure': 0.3,
                    'strategy_changes': True,
                    'team_coordination': True
                },
                AuthorityLevel.SENIOR_SPECIALIST: {
                    'max_position_size': 0.2,  # 20% of portfolio
                    'max_risk_exposure': 0.15,
                    'domain_decisions': True,
                    'recommendations': True
                },
                AuthorityLevel.SPECIALIST: {
                    'max_position_size': 0.1,  # 10% of portfolio
                    'max_risk_exposure': 0.05,
                    'operational_decisions': True,
                    'analysis': True
                },
                AuthorityLevel.CONTRIBUTOR: {
                    'max_position_size': 0.02,  # 2% of portfolio
                    'max_risk_exposure': 0.01,
                    'task_execution': True,
                    'data_collection': True
                }
            },
            'risk_management': {
                AuthorityLevel.SUPREME_COMMANDER: {
                    'emergency_stop': True,
                    'risk_limit_override': True,
                    'compliance_decisions': True
                },
                AuthorityLevel.TEAM_LEADER: {
                    'risk_limit_adjustments': True,
                    'portfolio_rebalancing': True
                },
                AuthorityLevel.SENIOR_SPECIALIST: {
                    'risk_assessments': True,
                    'limit_recommendations': True
                },
                AuthorityLevel.SPECIALIST: {
                    'monitoring': True,
                    'reporting': True
                }
            }
        }
        
    async def _setup_delegation_protocols(self):
        """Setup delegation protocols and procedures"""
        self.delegation_protocols = {
            'standard_delegation': {
                'requires_approval': False,
                'automatic_escalation': True,
                'review_period': 86400,  # 24 hours
                'max_delegation_depth': 3
            },
            'emergency_delegation': {
                'requires_approval': False,
                'automatic_escalation': False,
                'review_period': 3600,   # 1 hour
                'max_delegation_depth': 2
            },
            'strategic_delegation': {
                'requires_approval': True,
                'automatic_escalation': True,
                'review_period': 604800,  # 1 week
                'max_delegation_depth': 2
            }
        }
        
    async def _setup_decision_routing(self):
        """Setup decision routing rules"""
        self.decision_routing_rules = {
            'portfolio_decisions': {
                'low_impact': AuthorityLevel.SPECIALIST,
                'medium_impact': AuthorityLevel.SENIOR_SPECIALIST,
                'high_impact': AuthorityLevel.TEAM_LEADER,
                'critical_impact': AuthorityLevel.SUPREME_COMMANDER
            },
            'risk_decisions': {
                'routine': AuthorityLevel.SPECIALIST,
                'elevated': AuthorityLevel.SENIOR_SPECIALIST,
                'critical': AuthorityLevel.TEAM_LEADER,
                'emergency': AuthorityLevel.SUPREME_COMMANDER
            },
            'strategy_decisions': {
                'parameter_tuning': AuthorityLevel.SPECIALIST,
                'strategy_modification': AuthorityLevel.SENIOR_SPECIALIST,
                'strategy_change': AuthorityLevel.TEAM_LEADER,
                'strategy_overhaul': AuthorityLevel.SUPREME_COMMANDER
            }
        }
        
    async def create_team_hierarchy(self, team_id: str, team_members: List[str], 
                                  leader_id: str) -> Dict[str, Any]:
        """Create hierarchical structure for a team"""
        try:
            # Create hierarchy graph
            hierarchy = nx.DiGraph()
            
            # Add all team members as nodes
            for member_id in team_members:
                hierarchy.add_node(member_id)
                
            # Establish leadership structure
            await self._establish_leadership_structure(hierarchy, team_members, leader_id)
            
            # Store hierarchy
            self.team_hierarchies[team_id] = hierarchy
            
            # Grant initial authorities
            await self._grant_initial_authorities(team_id, team_members, leader_id)
            
            # Setup delegation chains
            await self._setup_initial_delegations(team_id, hierarchy)
            
            logger.info(f"✓ Created hierarchy for team {team_id} with leader {leader_id}")
            
            return {
                'success': True,
                'team_id': team_id,
                'hierarchy_nodes': len(hierarchy.nodes),
                'hierarchy_edges': len(hierarchy.edges),
                'leader_id': leader_id
            }
            
        except Exception as e:
            logger.error(f"Error creating team hierarchy: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _establish_leadership_structure(self, hierarchy: nx.DiGraph, 
                                            team_members: List[str], leader_id: str):
        """Establish leadership structure in hierarchy"""
        # Leader is at the top
        hierarchy.add_node(leader_id, authority_level=AuthorityLevel.TEAM_LEADER)
        
        # Other members report to leader (flat structure for now)
        for member_id in team_members:
            if member_id != leader_id:
                hierarchy.add_edge(leader_id, member_id)
                
                # Assign authority levels based on role (would be more sophisticated)
                hierarchy.add_node(member_id, authority_level=AuthorityLevel.SPECIALIST)
                
    async def _grant_initial_authorities(self, team_id: str, team_members: List[str], 
                                       leader_id: str):
        """Grant initial authorities to team members"""
        current_time = time.time()
        
        # Grant leader authorities
        leader_grant = AuthorityGrant(
            agent_id=leader_id,
            authority_level=AuthorityLevel.TEAM_LEADER,
            scope=DelegationScope.FULL_AUTHORITY,
            domain="team_management",
            limits=self.authority_frameworks['trading_decisions'][AuthorityLevel.TEAM_LEADER],
            granted_by="system",
            granted_at=current_time
        )
        
        if leader_id not in self.authority_grants:
            self.authority_grants[leader_id] = []
        self.authority_grants[leader_id].append(leader_grant)
        
        # Grant member authorities
        for member_id in team_members:
            if member_id != leader_id:
                member_grant = AuthorityGrant(
                    agent_id=member_id,
                    authority_level=AuthorityLevel.SPECIALIST,
                    scope=DelegationScope.BOUNDED_AUTHORITY,
                    domain="operational",
                    limits=self.authority_frameworks['trading_decisions'][AuthorityLevel.SPECIALIST],
                    granted_by=leader_id,
                    granted_at=current_time
                )
                
                if member_id not in self.authority_grants:
                    self.authority_grants[member_id] = []
                self.authority_grants[member_id].append(member_grant)
                
    async def _setup_initial_delegations(self, team_id: str, hierarchy: nx.DiGraph):
        """Setup initial delegation chains"""
        delegations = []
        current_time = time.time()
        
        # Create delegation chains based on hierarchy
        for edge in hierarchy.edges():
            superior_id, subordinate_id = edge
            
            delegation = DelegationChain(
                superior_id=superior_id,
                subordinate_id=subordinate_id,
                delegation_scope=DelegationScope.BOUNDED_AUTHORITY,
                delegated_authorities=["operational_decisions", "analysis"],
                conditions={
                    'requires_reporting': True,
                    'escalation_threshold': 0.1,
                    'review_frequency': 'daily'
                },
                created_at=current_time
            )
            
            delegations.append(delegation)
            
        self.delegation_chains[team_id] = delegations
        
    async def delegate_authority(self, superior_id: str, subordinate_id: str, 
                               authority_domain: str, scope: DelegationScope,
                               conditions: Dict[str, Any] = None) -> Dict[str, Any]:
        """Delegate authority from superior to subordinate"""
        try:
            # Verify superior has authority to delegate
            if not await self._can_delegate_authority(superior_id, authority_domain):
                return {'success': False, 'error': 'Insufficient authority to delegate'}
                
            # Create authority grant for subordinate
            current_time = time.time()
            
            # Determine authority level for subordinate
            subordinate_level = await self._determine_delegated_authority_level(
                superior_id, subordinate_id, authority_domain
            )
            
            grant = AuthorityGrant(
                agent_id=subordinate_id,
                authority_level=subordinate_level,
                scope=scope,
                domain=authority_domain,
                limits=await self._calculate_delegated_limits(superior_id, subordinate_level),
                granted_by=superior_id,
                granted_at=current_time,
                expires_at=current_time + 86400 if conditions and conditions.get('temporary') else None
            )
            
            # Store grant
            if subordinate_id not in self.authority_grants:
                self.authority_grants[subordinate_id] = []
            self.authority_grants[subordinate_id].append(grant)
            
            logger.info(f"✓ Delegated {authority_domain} authority from {superior_id} to {subordinate_id}")
            
            return {
                'success': True,
                'grant_id': f"{subordinate_id}_{authority_domain}_{int(current_time)}",
                'authority_level': subordinate_level.value,
                'scope': scope.value
            }
            
        except Exception as e:
            logger.error(f"Error delegating authority: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _can_delegate_authority(self, agent_id: str, authority_domain: str) -> bool:
        """Check if agent can delegate authority in domain"""
        if agent_id not in self.authority_grants:
            return False
            
        for grant in self.authority_grants[agent_id]:
            if (grant.domain == authority_domain or grant.domain == "team_management") and \
               grant.scope in [DelegationScope.FULL_AUTHORITY, DelegationScope.BOUNDED_AUTHORITY]:
                return True
                
        return False
        
    async def _determine_delegated_authority_level(self, superior_id: str, subordinate_id: str, 
                                                 authority_domain: str) -> AuthorityLevel:
        """Determine appropriate authority level for delegation"""
        # Get superior's authority level
        superior_level = await self._get_agent_authority_level(superior_id, authority_domain)
        
        # Subordinate gets one level lower (minimum CONTRIBUTOR)
        if superior_level == AuthorityLevel.SUPREME_COMMANDER:
            return AuthorityLevel.TEAM_LEADER
        elif superior_level == AuthorityLevel.TEAM_LEADER:
            return AuthorityLevel.SENIOR_SPECIALIST
        elif superior_level == AuthorityLevel.SENIOR_SPECIALIST:
            return AuthorityLevel.SPECIALIST
        else:
            return AuthorityLevel.CONTRIBUTOR
            
    async def _get_agent_authority_level(self, agent_id: str, domain: str) -> AuthorityLevel:
        """Get agent's authority level in domain"""
        if agent_id not in self.authority_grants:
            return AuthorityLevel.CONTRIBUTOR
            
        max_level = AuthorityLevel.CONTRIBUTOR
        for grant in self.authority_grants[agent_id]:
            if grant.domain == domain or grant.domain == "team_management":
                if grant.authority_level.value > max_level.value:
                    max_level = grant.authority_level
                    
        return max_level
        
    async def _calculate_delegated_limits(self, superior_id: str, 
                                        subordinate_level: AuthorityLevel) -> Dict[str, Any]:
        """Calculate limits for delegated authority"""
        # Get superior's limits
        superior_grants = self.authority_grants.get(superior_id, [])
        superior_limits = {}
        
        for grant in superior_grants:
            superior_limits.update(grant.limits)
            
        # Calculate subordinate limits (fraction of superior's)
        delegation_factor = 0.5  # Subordinate gets 50% of superior's limits
        
        subordinate_limits = {}
        for key, value in superior_limits.items():
            if isinstance(value, (int, float)):
                subordinate_limits[key] = value * delegation_factor
            else:
                subordinate_limits[key] = value
                
        return subordinate_limits
        
    async def route_decision(self, decision_type: str, impact_level: str, 
                           requesting_agent: str) -> Dict[str, Any]:
        """Route decision to appropriate authority level"""
        try:
            # Get required authority level for decision
            required_level = self.decision_routing_rules.get(decision_type, {}).get(impact_level)
            
            if not required_level:
                return {'success': False, 'error': 'Unknown decision type or impact level'}
                
            # Find agent with appropriate authority
            authorized_agent = await self._find_authorized_agent(
                requesting_agent, required_level, decision_type
            )
            
            if not authorized_agent:
                return {'success': False, 'error': 'No authorized agent found'}
                
            return {
                'success': True,
                'authorized_agent': authorized_agent,
                'required_level': required_level.value,
                'routing_path': await self._get_routing_path(requesting_agent, authorized_agent)
            }
            
        except Exception as e:
            logger.error(f"Error routing decision: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _find_authorized_agent(self, requesting_agent: str, required_level: AuthorityLevel,
                                   decision_type: str) -> Optional[str]:
        """Find agent with required authority level"""
        # Check if requesting agent has sufficient authority
        agent_level = await self._get_agent_authority_level(requesting_agent, decision_type)
        
        if agent_level.value >= required_level.value:
            return requesting_agent
            
        # Find superior with sufficient authority
        for team_id, hierarchy in self.team_hierarchies.items():
            if requesting_agent in hierarchy.nodes:
                # Traverse up the hierarchy
                current_node = requesting_agent
                while True:
                    # Find superior
                    superiors = list(hierarchy.predecessors(current_node))
                    if not superiors:
                        break
                        
                    superior = superiors[0]  # Assume single superior
                    superior_level = await self._get_agent_authority_level(superior, decision_type)
                    
                    if superior_level.value >= required_level.value:
                        return superior
                        
                    current_node = superior
                    
        return None
        
    async def _get_routing_path(self, from_agent: str, to_agent: str) -> List[str]:
        """Get routing path from requesting agent to authorized agent"""
        if from_agent == to_agent:
            return [from_agent]
            
        # Find path through hierarchy
        for team_id, hierarchy in self.team_hierarchies.items():
            if from_agent in hierarchy.nodes and to_agent in hierarchy.nodes:
                try:
                    path = nx.shortest_path(hierarchy.reverse(), from_agent, to_agent)
                    return path
                except nx.NetworkXNoPath:
                    continue
                    
        return [from_agent, to_agent]  # Direct path if no hierarchy path found
        
    async def get_team_hierarchy_info(self, team_id: str) -> Dict[str, Any]:
        """Get information about team hierarchy"""
        if team_id not in self.team_hierarchies:
            return {'success': False, 'error': 'Team not found'}
            
        hierarchy = self.team_hierarchies[team_id]
        
        # Get hierarchy structure
        structure = {
            'nodes': list(hierarchy.nodes()),
            'edges': list(hierarchy.edges()),
            'levels': {}
        }
        
        # Calculate hierarchy levels
        for node in hierarchy.nodes():
            # Level 0 is root (no predecessors), level increases with depth
            try:
                level = nx.shortest_path_length(hierarchy, node, list(hierarchy.nodes())[0])
                structure['levels'][node] = level
            except:
                structure['levels'][node] = 0
                
        # Get authority information
        authority_info = {}
        for node in hierarchy.nodes():
            grants = self.authority_grants.get(node, [])
            authority_info[node] = {
                'grants': len(grants),
                'domains': [grant.domain for grant in grants],
                'max_authority': max([grant.authority_level.value for grant in grants]) if grants else 1
            }
            
        return {
            'success': True,
            'team_id': team_id,
            'structure': structure,
            'authority_info': authority_info,
            'delegation_chains': len(self.delegation_chains.get(team_id, []))
        }
        
    async def get_hierarchy_analytics(self) -> Dict[str, Any]:
        """Get analytics on hierarchical structures"""
        total_teams = len(self.team_hierarchies)
        total_agents = len(self.authority_grants)
        total_delegations = sum(len(chains) for chains in self.delegation_chains.values())
        
        # Authority distribution
        authority_distribution = {}
        for grants in self.authority_grants.values():
            for grant in grants:
                level = grant.authority_level.value
                authority_distribution[level] = authority_distribution.get(level, 0) + 1
                
        return {
            'total_teams': total_teams,
            'total_agents': total_agents,
            'total_delegations': total_delegations,
            'authority_distribution': authority_distribution,
            'average_hierarchy_depth': await self._calculate_average_hierarchy_depth()
        }
        
    async def _calculate_average_hierarchy_depth(self) -> float:
        """Calculate average hierarchy depth across teams"""
        if not self.team_hierarchies:
            return 0.0
            
        total_depth = 0
        for hierarchy in self.team_hierarchies.values():
            if hierarchy.nodes():
                # Find root nodes (no predecessors)
                roots = [n for n in hierarchy.nodes() if hierarchy.in_degree(n) == 0]
                if roots:
                    max_depth = 0
                    for root in roots:
                        depths = nx.single_source_shortest_path_length(hierarchy, root)
                        max_depth = max(max_depth, max(depths.values()) if depths else 0)
                    total_depth += max_depth
                    
        return total_depth / len(self.team_hierarchies) if self.team_hierarchies else 0.0

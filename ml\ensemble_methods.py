"""
Ensemble Methods - Advanced ensemble techniques for decision making
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict
import statistics

logger = logging.getLogger(__name__)


class EnsembleDecisionMaker:
    """
    Advanced ensemble methods for combining multiple model predictions
    and agent decisions into robust trading signals.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Ensemble components
        self.model_weights: Dict[str, float] = {}
        self.agent_weights: Dict[str, float] = {}
        self.ensemble_history: List[Dict[str, Any]] = []
        
        # Ensemble methods
        self.ensemble_methods = {
            'weighted_average': self._weighted_average_ensemble,
            'voting': self._voting_ensemble,
            'stacking': self._stacking_ensemble,
            'bayesian': self._bayesian_ensemble,
            'confidence_weighted': self._confidence_weighted_ensemble
        }
        
        # Performance tracking
        self.method_performance: Dict[str, Dict[str, float]] = {}
        self.prediction_accuracy: Dict[str, List[float]] = defaultdict(list)
        
        # State
        self.initialized = False
        
    async def initialize(self):
        """Initialize ensemble decision maker"""
        if self.initialized:
            return
            
        logger.info("Initializing Ensemble Decision Maker...")
        
        # Initialize ensemble parameters
        await self._setup_ensemble_parameters()
        
        # Initialize performance tracking
        await self._setup_performance_tracking()
        
        self.initialized = True
        logger.info("✓ Ensemble Decision Maker initialized")
        
    async def _setup_ensemble_parameters(self):
        """Setup ensemble parameters and weights"""
        # Default model weights (can be updated based on performance)
        self.model_weights = {
            'random_forest': 0.3,
            'gradient_boosting': 0.3,
            'neural_network': 0.25,
            'reinforcement_learning': 0.15
        }
        
        # Default agent weights
        self.agent_weights = {
            'market_analyst': 0.25,
            'strategy_developer': 0.25,
            'risk_manager': 0.20,
            'execution_specialist': 0.15,
            'performance_evaluator': 0.15
        }
        
        # Ensemble method parameters
        self.ensemble_params = {
            'weighted_average': {
                'min_confidence': 0.5,
                'weight_decay': 0.95
            },
            'voting': {
                'threshold': 0.6,
                'min_voters': 3
            },
            'stacking': {
                'meta_model': 'linear',
                'cv_folds': 5
            },
            'bayesian': {
                'prior_weight': 0.1,
                'update_rate': 0.05
            },
            'confidence_weighted': {
                'confidence_threshold': 0.7,
                'max_weight_ratio': 5.0
            }
        }
        
    async def _setup_performance_tracking(self):
        """Setup performance tracking for ensemble methods"""
        for method in self.ensemble_methods.keys():
            self.method_performance[method] = {
                'accuracy': 0.0,
                'precision': 0.0,
                'recall': 0.0,
                'f1_score': 0.0,
                'total_predictions': 0,
                'correct_predictions': 0
            }
            
    async def combine_predictions(self, predictions: List[Dict[str, Any]], 
                                method: str = 'confidence_weighted') -> Dict[str, Any]:
        """Combine multiple predictions using specified ensemble method"""
        if not predictions:
            return {'success': False, 'error': 'No predictions provided'}
            
        if method not in self.ensemble_methods:
            return {'success': False, 'error': f'Unknown ensemble method: {method}'}
            
        try:
            # Apply ensemble method
            ensemble_func = self.ensemble_methods[method]
            result = await ensemble_func(predictions)
            
            # Store ensemble decision
            ensemble_record = {
                'method': method,
                'input_predictions': predictions,
                'ensemble_result': result,
                'timestamp': pd.Timestamp.now()
            }
            self.ensemble_history.append(ensemble_record)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in ensemble combination: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _weighted_average_ensemble(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Weighted average ensemble method"""
        if not predictions:
            return {'success': False, 'error': 'No predictions'}
            
        weighted_sum = 0.0
        total_weight = 0.0
        confidence_sum = 0.0
        
        for pred in predictions:
            if 'prediction' in pred and 'confidence' in pred:
                model_type = pred.get('model_type', 'unknown')
                weight = self.model_weights.get(model_type, 0.1)
                
                # Weight by confidence
                effective_weight = weight * pred['confidence']
                
                weighted_sum += pred['prediction'] * effective_weight
                total_weight += effective_weight
                confidence_sum += pred['confidence']
                
        if total_weight == 0:
            return {'success': False, 'error': 'No valid predictions with weights'}
            
        ensemble_prediction = weighted_sum / total_weight
        ensemble_confidence = confidence_sum / len(predictions)
        
        return {
            'success': True,
            'ensemble_prediction': ensemble_prediction,
            'ensemble_confidence': ensemble_confidence,
            'method': 'weighted_average',
            'predictions_used': len(predictions)
        }
        
    async def _voting_ensemble(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Voting ensemble method"""
        if len(predictions) < self.ensemble_params['voting']['min_voters']:
            return {'success': False, 'error': 'Insufficient voters'}
            
        # Convert predictions to buy/sell/hold signals
        votes = {'buy': 0, 'sell': 0, 'hold': 0}
        confidence_sum = 0.0
        
        for pred in predictions:
            if 'prediction' in pred:
                # Convert numerical prediction to signal
                if pred['prediction'] > 0.02:  # 2% threshold
                    votes['buy'] += 1
                elif pred['prediction'] < -0.02:
                    votes['sell'] += 1
                else:
                    votes['hold'] += 1
                    
                confidence_sum += pred.get('confidence', 0.5)
                
        # Determine winning vote
        max_votes = max(votes.values())
        winning_signals = [signal for signal, count in votes.items() if count == max_votes]
        
        # Handle ties
        if len(winning_signals) > 1:
            ensemble_signal = 'hold'  # Default to hold on ties
        else:
            ensemble_signal = winning_signals[0]
            
        # Calculate confidence based on vote margin
        total_votes = sum(votes.values())
        vote_confidence = max_votes / total_votes if total_votes > 0 else 0
        ensemble_confidence = (confidence_sum / len(predictions)) * vote_confidence
        
        return {
            'success': True,
            'ensemble_signal': ensemble_signal,
            'ensemble_confidence': ensemble_confidence,
            'votes': votes,
            'method': 'voting',
            'predictions_used': len(predictions)
        }
        
    async def _stacking_ensemble(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Stacking ensemble method (simplified implementation)"""
        if len(predictions) < 2:
            return {'success': False, 'error': 'Insufficient predictions for stacking'}
            
        # Simple linear combination (in practice, would use trained meta-model)
        pred_values = [pred['prediction'] for pred in predictions if 'prediction' in pred]
        confidence_values = [pred.get('confidence', 0.5) for pred in predictions]
        
        if not pred_values:
            return {'success': False, 'error': 'No valid predictions'}
            
        # Meta-model: weighted combination based on historical performance
        weights = np.array([0.4, 0.3, 0.2, 0.1][:len(pred_values)])
        weights = weights / weights.sum()  # Normalize
        
        ensemble_prediction = np.average(pred_values, weights=weights)
        ensemble_confidence = np.average(confidence_values, weights=weights)
        
        return {
            'success': True,
            'ensemble_prediction': ensemble_prediction,
            'ensemble_confidence': ensemble_confidence,
            'method': 'stacking',
            'meta_weights': weights.tolist(),
            'predictions_used': len(predictions)
        }
        
    async def _bayesian_ensemble(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Bayesian ensemble method"""
        if not predictions:
            return {'success': False, 'error': 'No predictions'}
            
        # Bayesian model averaging (simplified)
        prior_mean = 0.0  # Prior belief about returns
        prior_precision = 1.0  # Prior precision
        
        posterior_precision = prior_precision
        posterior_mean_numerator = prior_mean * prior_precision
        
        for pred in predictions:
            if 'prediction' in pred and 'confidence' in pred:
                # Use confidence as precision
                precision = pred['confidence'] * 10  # Scale confidence to precision
                posterior_precision += precision
                posterior_mean_numerator += pred['prediction'] * precision
                
        if posterior_precision == prior_precision:
            return {'success': False, 'error': 'No valid predictions for Bayesian update'}
            
        posterior_mean = posterior_mean_numerator / posterior_precision
        posterior_variance = 1.0 / posterior_precision
        
        # Confidence based on posterior precision
        ensemble_confidence = min(posterior_precision / 10, 1.0)
        
        return {
            'success': True,
            'ensemble_prediction': posterior_mean,
            'ensemble_confidence': ensemble_confidence,
            'posterior_variance': posterior_variance,
            'method': 'bayesian',
            'predictions_used': len(predictions)
        }
        
    async def _confidence_weighted_ensemble(self, predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Confidence-weighted ensemble method"""
        if not predictions:
            return {'success': False, 'error': 'No predictions'}
            
        # Filter predictions by confidence threshold
        threshold = self.ensemble_params['confidence_weighted']['confidence_threshold']
        valid_predictions = [pred for pred in predictions 
                           if pred.get('confidence', 0) >= threshold]
        
        if not valid_predictions:
            # Use all predictions if none meet threshold
            valid_predictions = predictions
            
        # Weight by confidence with max ratio constraint
        max_ratio = self.ensemble_params['confidence_weighted']['max_weight_ratio']
        confidences = [pred.get('confidence', 0.5) for pred in valid_predictions]
        
        # Normalize confidences to prevent extreme weights
        min_conf = min(confidences)
        max_conf = max(confidences)
        
        if max_conf > min_conf * max_ratio:
            # Cap maximum confidence
            capped_confidences = [min(conf, min_conf * max_ratio) for conf in confidences]
        else:
            capped_confidences = confidences
            
        # Calculate weighted prediction
        weighted_sum = 0.0
        weight_sum = 0.0
        
        for pred, weight in zip(valid_predictions, capped_confidences):
            if 'prediction' in pred:
                weighted_sum += pred['prediction'] * weight
                weight_sum += weight
                
        if weight_sum == 0:
            return {'success': False, 'error': 'No valid weighted predictions'}
            
        ensemble_prediction = weighted_sum / weight_sum
        ensemble_confidence = statistics.mean(capped_confidences)
        
        return {
            'success': True,
            'ensemble_prediction': ensemble_prediction,
            'ensemble_confidence': ensemble_confidence,
            'method': 'confidence_weighted',
            'predictions_used': len(valid_predictions),
            'confidence_threshold': threshold
        }
        
    async def combine_agent_decisions(self, decisions: List[Dict[str, Any]], 
                                    method: str = 'weighted_average') -> Dict[str, Any]:
        """Combine decisions from multiple agents"""
        if not decisions:
            return {'success': False, 'error': 'No decisions provided'}
            
        try:
            # Convert agent decisions to standardized format
            standardized_decisions = []
            
            for decision in decisions:
                agent_type = decision.get('agent_type', 'unknown')
                
                # Extract decision value and confidence
                if 'recommendation' in decision:
                    # Convert recommendation to numerical value
                    rec = decision['recommendation']
                    if isinstance(rec, str):
                        if 'buy' in rec.lower():
                            value = 1.0
                        elif 'sell' in rec.lower():
                            value = -1.0
                        else:
                            value = 0.0
                    else:
                        value = float(rec)
                        
                    standardized_decisions.append({
                        'prediction': value,
                        'confidence': decision.get('confidence', 0.5),
                        'model_type': agent_type
                    })
                    
            # Use ensemble method to combine
            return await self.combine_predictions(standardized_decisions, method)
            
        except Exception as e:
            logger.error(f"Error combining agent decisions: {e}")
            return {'success': False, 'error': str(e)}
            
    async def update_weights(self, model_type: str, performance_score: float):
        """Update model weights based on performance"""
        if model_type in self.model_weights:
            # Exponential moving average update
            decay = self.ensemble_params['weighted_average']['weight_decay']
            current_weight = self.model_weights[model_type]
            
            # Update weight based on performance
            new_weight = decay * current_weight + (1 - decay) * performance_score
            self.model_weights[model_type] = max(0.01, min(1.0, new_weight))
            
            # Renormalize weights
            total_weight = sum(self.model_weights.values())
            for key in self.model_weights:
                self.model_weights[key] /= total_weight
                
            logger.info(f"Updated weight for {model_type}: {self.model_weights[model_type]:.3f}")
            
    async def evaluate_ensemble_performance(self, actual_outcomes: List[float], 
                                          ensemble_predictions: List[float]) -> Dict[str, float]:
        """Evaluate ensemble performance"""
        if len(actual_outcomes) != len(ensemble_predictions):
            return {'error': 'Mismatched lengths'}
            
        # Calculate metrics
        errors = np.array(actual_outcomes) - np.array(ensemble_predictions)
        
        metrics = {
            'mae': np.mean(np.abs(errors)),
            'mse': np.mean(errors ** 2),
            'rmse': np.sqrt(np.mean(errors ** 2)),
            'correlation': np.corrcoef(actual_outcomes, ensemble_predictions)[0, 1] if len(actual_outcomes) > 1 else 0,
            'accuracy': np.mean(np.sign(actual_outcomes) == np.sign(ensemble_predictions))
        }
        
        return metrics
        
    async def get_ensemble_summary(self) -> Dict[str, Any]:
        """Get ensemble performance summary"""
        return {
            'model_weights': self.model_weights.copy(),
            'agent_weights': self.agent_weights.copy(),
            'method_performance': self.method_performance.copy(),
            'total_ensemble_decisions': len(self.ensemble_history),
            'available_methods': list(self.ensemble_methods.keys())
        }

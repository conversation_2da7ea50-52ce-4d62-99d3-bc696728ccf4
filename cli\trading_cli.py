#!/usr/bin/env python3
"""
Advanced Ollama Trading Agents - Command Line Interface

Comprehensive CLI tool for managing, testing, and monitoring the trading system.
"""

import asyncio
import click
import json
import sys
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.live import Live
from rich.layout import Layout
from rich.text import Text

from main import TradingSystem
from system_validator import SystemValidator
from tests.test_suite import TestSuiteRunner


console = Console()


class TradingCLI:
    """
    Comprehensive CLI interface for the trading system.
    
    Features:
    - System management (start, stop, status)
    - Interactive testing and validation
    - Real-time monitoring and metrics
    - Configuration management
    - Database operations
    - Agent management
    - Strategy management
    - Portfolio monitoring
    """
    
    def __init__(self):
        self.trading_system: Optional[TradingSystem] = None
        self.config: Dict[str, Any] = {}
        self.console = Console()
    
    async def load_config(self, config_path: str = "config/config.yaml") -> bool:
        """Load system configuration"""
        try:
            # Load configuration from file or use defaults
            self.config = {
                'testing': True,
                'database': {
                    'postgres': {'host': 'localhost', 'port': 5432},
                    'redis': {'host': 'localhost', 'port': 6379}
                },
                'api': {'host': '0.0.0.0', 'port': 8000},
                'agents': {'max_agents': 10},
                'strategies': {'max_strategies': 20},
                'risk': {'max_position_size': 0.1}
            }
            return True
        except Exception as e:
            console.print(f"[red]Failed to load config: {e}[/red]")
            return False
    
    async def initialize_system(self) -> bool:
        """Initialize the trading system"""
        try:
            with console.status("[bold green]Initializing trading system..."):
                self.trading_system = TradingSystem(config=self.config)
                result = await self.trading_system.initialize()
                
            if result:
                console.print("[green]✓ Trading system initialized successfully[/green]")
                return True
            else:
                console.print("[red]✗ Failed to initialize trading system[/red]")
                return False
                
        except Exception as e:
            console.print(f"[red]✗ Initialization error: {e}[/red]")
            return False


@click.group()
@click.option('--config', default='config/config.yaml', help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
@click.pass_context
def cli(ctx, config, verbose):
    """Advanced Ollama Trading Agents CLI"""
    ctx.ensure_object(dict)
    ctx.obj['config'] = config
    ctx.obj['verbose'] = verbose
    
    if verbose:
        console.print("[blue]Advanced Ollama Trading Agents CLI[/blue]")
        console.print(f"Config: {config}")


@cli.group()
def system():
    """System management commands"""
    pass


@system.command()
@click.pass_context
def start(ctx):
    """Start the trading system"""
    async def _start():
        cli_instance = TradingCLI()
        await cli_instance.load_config(ctx.obj['config'])
        
        if await cli_instance.initialize_system():
            console.print("[green]Starting trading system...[/green]")
            await cli_instance.trading_system.start()
            console.print("[green]✓ Trading system started successfully[/green]")
            
            # Keep running until interrupted
            try:
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                console.print("\n[yellow]Shutting down...[/yellow]")
                await cli_instance.trading_system.stop()
                console.print("[green]✓ Trading system stopped[/green]")
    
    asyncio.run(_start())


@system.command()
@click.pass_context
def status(ctx):
    """Get system status"""
    async def _status():
        cli_instance = TradingCLI()
        await cli_instance.load_config(ctx.obj['config'])
        
        if await cli_instance.initialize_system():
            status_info = await cli_instance.trading_system.get_system_status()
            
            # Create status table
            table = Table(title="System Status")
            table.add_column("Component", style="cyan")
            table.add_column("Status", style="green")
            table.add_column("Details", style="yellow")
            
            for component, info in status_info.get('components', {}).items():
                status = "✓ Running" if info.get('running') else "✗ Stopped"
                details = f"Initialized: {info.get('initialized', False)}"
                table.add_row(component, status, details)
            
            console.print(table)
            
            # System metrics
            metrics = status_info.get('performance', {})
            if metrics:
                console.print(f"\n[bold]System Metrics:[/bold]")
                console.print(f"CPU Usage: {metrics.get('cpu_usage', 0):.1%}")
                console.print(f"Memory Usage: {metrics.get('memory_usage', 0):.1%}")
    
    asyncio.run(_status())


@system.command()
@click.pass_context
def stop(ctx):
    """Stop the trading system"""
    async def _stop():
        cli_instance = TradingCLI()
        await cli_instance.load_config(ctx.obj['config'])
        
        if await cli_instance.initialize_system():
            console.print("[yellow]Stopping trading system...[/yellow]")
            await cli_instance.trading_system.stop()
            console.print("[green]✓ Trading system stopped[/green]")
    
    asyncio.run(_stop())


@cli.group()
def test():
    """Testing and validation commands"""
    pass


@test.command()
@click.option('--quick', is_flag=True, help='Run quick test suite')
@click.option('--performance', is_flag=True, help='Run performance tests')
@click.option('--security', is_flag=True, help='Run security tests')
@click.pass_context
def run(ctx, quick, performance, security):
    """Run test suite"""
    async def _run_tests():
        cli_instance = TradingCLI()
        await cli_instance.load_config(ctx.obj['config'])
        
        test_suite = TestSuiteRunner(cli_instance.config)
        await test_suite.initialize()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            if quick:
                task = progress.add_task("Running quick tests...", total=None)
                result = await test_suite.run_quick_tests()
            elif performance:
                task = progress.add_task("Running performance tests...", total=None)
                result = await test_suite.run_performance_tests()
            elif security:
                task = progress.add_task("Running security tests...", total=None)
                result = await test_suite.run_security_tests()
            else:
                task = progress.add_task("Running full test suite...", total=None)
                result = await test_suite.run_all_tests()
            
            progress.update(task, completed=True)
        
        # Display results
        if result.get('overall_status') == 'passed':
            console.print("[green]✓ All tests passed![/green]")
        else:
            console.print("[red]✗ Some tests failed[/red]")
        
        if 'statistics' in result:
            stats = result['statistics']
            console.print(f"Tests run: {stats.get('total_tests', 0)}")
            console.print(f"Passed: {stats.get('passed_tests', 0)}")
            console.print(f"Failed: {stats.get('failed_tests', 0)}")
            console.print(f"Success rate: {stats.get('success_rate', 0):.1f}%")
    
    asyncio.run(_run_tests())


@test.command()
@click.pass_context
def validate(ctx):
    """Run system validation"""
    async def _validate():
        cli_instance = TradingCLI()
        await cli_instance.load_config(ctx.obj['config'])
        
        validator = SystemValidator(cli_instance.config)
        await validator.initialize()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Running system validation...", total=None)
            result = await validator.validate_complete_system()
            progress.update(task, completed=True)
        
        # Display validation results
        if result.get('overall_status') == 'passed':
            console.print("[green]✓ System validation passed![/green]")
        else:
            console.print("[red]✗ System validation failed[/red]")
        
        # Show validation phases
        if 'validation_phases' in result:
            table = Table(title="Validation Results")
            table.add_column("Phase", style="cyan")
            table.add_column("Status", style="green")
            table.add_column("Duration", style="yellow")
            
            for phase, info in result['validation_phases'].items():
                status = "✓ Passed" if info.get('status') == 'passed' else "✗ Failed"
                duration = f"{info.get('duration', 0):.2f}s"
                table.add_row(phase, status, duration)
            
            console.print(table)
    
    asyncio.run(_validate())


@test.command()
@click.option('--component', help='Test specific component')
@click.option('--interactive', is_flag=True, help='Interactive testing mode')
@click.pass_context
def interactive(ctx, component, interactive):
    """Interactive testing mode"""
    async def _interactive():
        cli_instance = TradingCLI()
        await cli_instance.load_config(ctx.obj['config'])
        
        if await cli_instance.initialize_system():
            console.print("[green]Entering interactive testing mode...[/green]")
            console.print("Type 'help' for available commands, 'exit' to quit")
            
            while True:
                try:
                    command = console.input("\n[bold blue]trading-test>[/bold blue] ")
                    
                    if command.lower() in ['exit', 'quit']:
                        break
                    elif command.lower() == 'help':
                        console.print("""
Available commands:
- status: Show system status
- agents: List all agents
- strategies: List all strategies
- portfolio: Show portfolio status
- risk: Show risk assessment
- test <component>: Test specific component
- market-data: Simulate market data
- exit: Exit interactive mode
                        """)
                    elif command.lower() == 'status':
                        status = await cli_instance.trading_system.get_system_status()
                        console.print(json.dumps(status, indent=2))
                    elif command.lower() == 'agents':
                        agents = await cli_instance.trading_system.agent_manager.get_all_agents()
                        console.print(f"Active agents: {len(agents)}")
                        for agent in agents:
                            console.print(f"- {agent.get('name', 'Unknown')}: {agent.get('status', 'Unknown')}")
                    elif command.lower() == 'strategies':
                        strategies = await cli_instance.trading_system.strategy_manager.get_all_strategies()
                        console.print(f"Active strategies: {len(strategies)}")
                        for strategy in strategies:
                            console.print(f"- {strategy.get('name', 'Unknown')}: {strategy.get('status', 'Unknown')}")
                    elif command.lower() == 'portfolio':
                        portfolios = await cli_instance.trading_system.portfolio_manager.get_all_portfolios()
                        console.print(f"Portfolios: {len(portfolios)}")
                        for portfolio in portfolios:
                            console.print(f"- {portfolio.get('name', 'Unknown')}: ${portfolio.get('total_value', 0):,.2f}")
                    elif command.lower() == 'market-data':
                        # Simulate market data
                        market_data = {
                            'symbol': 'AAPL',
                            'price': 150.0,
                            'volume': 1000000,
                            'timestamp': datetime.now().isoformat()
                        }
                        await cli_instance.trading_system.process_market_data(market_data)
                        console.print("[green]Market data processed[/green]")
                    else:
                        console.print(f"[red]Unknown command: {command}[/red]")
                        
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    console.print(f"[red]Error: {e}[/red]")
            
            console.print("[yellow]Exiting interactive mode...[/yellow]")
    
    asyncio.run(_interactive())


@cli.group()
def monitor():
    """Monitoring and metrics commands"""
    pass


@monitor.command()
@click.option('--interval', default=5, help='Update interval in seconds')
@click.pass_context
def live(ctx, interval):
    """Live system monitoring"""
    async def _live_monitor():
        cli_instance = TradingCLI()
        await cli_instance.load_config(ctx.obj['config'])
        
        if await cli_instance.initialize_system():
            
            def create_layout():
                layout = Layout()
                layout.split_column(
                    Layout(name="header", size=3),
                    Layout(name="body"),
                    Layout(name="footer", size=3)
                )
                layout["body"].split_row(
                    Layout(name="left"),
                    Layout(name="right")
                )
                return layout
            
            async def update_display():
                while True:
                    try:
                        status = await cli_instance.trading_system.get_system_status()
                        
                        # Header
                        header = Panel(
                            f"[bold blue]Advanced Ollama Trading Agents - Live Monitor[/bold blue]\n"
                            f"Status: {status.get('status', 'Unknown')} | "
                            f"Uptime: {status.get('uptime_seconds', 0)}s",
                            style="blue"
                        )
                        
                        # System metrics
                        metrics = status.get('performance', {})
                        metrics_text = Text()
                        metrics_text.append("System Metrics:\n", style="bold")
                        metrics_text.append(f"CPU: {metrics.get('cpu_usage', 0):.1%}\n")
                        metrics_text.append(f"Memory: {metrics.get('memory_usage', 0):.1%}\n")
                        metrics_text.append(f"Disk: {metrics.get('disk_usage', 0):.1%}\n")
                        
                        # Components status
                        components_text = Text()
                        components_text.append("Components:\n", style="bold")
                        for comp, info in status.get('components', {}).items():
                            status_icon = "✓" if info.get('running') else "✗"
                            components_text.append(f"{status_icon} {comp}\n")
                        
                        layout = create_layout()
                        layout["header"].update(header)
                        layout["left"].update(Panel(metrics_text, title="Metrics"))
                        layout["right"].update(Panel(components_text, title="Components"))
                        layout["footer"].update(Panel(f"Last updated: {datetime.now().strftime('%H:%M:%S')}", style="dim"))
                        
                        return layout
                        
                    except Exception as e:
                        return Panel(f"[red]Error: {e}[/red]")
                    
                    await asyncio.sleep(interval)
            
            with Live(await update_display(), refresh_per_second=1/interval) as live:
                try:
                    while True:
                        await asyncio.sleep(interval)
                        live.update(await update_display())
                except KeyboardInterrupt:
                    console.print("\n[yellow]Stopping live monitor...[/yellow]")
    
    asyncio.run(_live_monitor())


@cli.group()
def agents():
    """Agent management commands"""
    pass


@agents.command()
@click.pass_context
def list(ctx):
    """List all agents"""
    async def _list_agents():
        cli_instance = TradingCLI()
        await cli_instance.load_config(ctx.obj['config'])
        
        if await cli_instance.initialize_system():
            agents = await cli_instance.trading_system.agent_manager.get_all_agents()
            
            table = Table(title="Trading Agents")
            table.add_column("ID", style="cyan")
            table.add_column("Name", style="green")
            table.add_column("Type", style="yellow")
            table.add_column("Status", style="blue")
            table.add_column("Last Activity", style="dim")
            
            for agent in agents:
                table.add_row(
                    agent.get('agent_id', 'Unknown'),
                    agent.get('name', 'Unknown'),
                    agent.get('type', 'Unknown'),
                    agent.get('status', 'Unknown'),
                    agent.get('last_activity', 'Never')
                )
            
            console.print(table)
    
    asyncio.run(_list_agents())


if __name__ == '__main__':
    cli()

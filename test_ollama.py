#!/usr/bin/env python3
"""Test Ollama models availability and functionality"""

import ollama
import sys

def test_ollama_connection():
    """Test basic Ollama connection"""
    try:
        client = ollama.Client()
        models = client.list()
        print(f"✅ Ollama connected successfully")
        print(f"📊 Found {len(models['models'])} models")
        return True, models
    except Exception as e:
        print(f"❌ Ollama connection failed: {e}")
        return False, None

def test_model_functionality(model_name):
    """Test if a specific model can generate responses"""
    try:
        client = ollama.Client()
        response = client.generate(
            model=model_name,
            prompt="Hello, respond with just 'OK' to confirm you're working.",
            options={'num_predict': 10}
        )
        print(f"✅ {model_name}: Working")
        return True
    except Exception as e:
        print(f"❌ {model_name}: Failed - {e}")
        return False

def main():
    print("🔍 Testing Ollama Integration...")
    print("=" * 50)
    
    # Test connection
    connected, models = test_ollama_connection()
    if not connected:
        sys.exit(1)
    
    # List available models
    print("\n📋 Available Models:")
    for model in models['models'][:10]:  # Show first 10
        print(f"  - {model.model}")
    
    # Test key models from config
    key_models = [
        "exaone-deep:32b",
        "huihui_ai/magistral-abliterated:24b", 
        "phi4-reasoning:plus",
        "nemotron-mini:4b",
        "granite3.3:8b"
    ]
    
    print(f"\n🧪 Testing Key Models:")
    working_models = 0
    for model in key_models:
        if test_model_functionality(model):
            working_models += 1
    
    print(f"\n📊 Results: {working_models}/{len(key_models)} key models working")
    
    if working_models >= 3:
        print("✅ Sufficient models available for system operation")
        return True
    else:
        print("❌ Insufficient working models")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

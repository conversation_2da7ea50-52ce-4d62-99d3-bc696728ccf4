"""
System Integration Package

This package provides comprehensive integration capabilities for the
Advanced Ollama Trading Agent System. It includes:

- Cross-component communication and coordination
- Event-driven architecture implementation
- System-wide state management
- Performance monitoring and optimization
- Error handling and recovery mechanisms
- Configuration management and validation
"""

from .system_integration_validator import SystemIntegrationValidator

__all__ = [
    'SystemIntegrationValidator'
]

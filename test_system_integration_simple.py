#!/usr/bin/env python3
"""
Simple System Integration Test - Test basic system functionality
"""

import asyncio
import json
from datetime import datetime
from system.system_coordinator import SystemCoordinator, SystemState

async def test_simple_integration():
    """Test basic system integration"""
    
    print("🚀 TESTING SIMPLE SYSTEM INTEGRATION")
    print("=" * 50)
    
    results = {}
    
    try:
        # Initialize system coordinator
        print("\n🔧 PHASE 1: System Initialization")
        coordinator = SystemCoordinator('config/test_config.yaml')
        
        # Test system initialization
        print("  📋 Initializing system components...")
        init_success = await coordinator.initialize()
        
        if init_success:
            print("  ✅ System initialization: SUCCESS")
            print(f"  🔹 System State: {coordinator.state.value}")
            print(f"  🔹 Components: {len(coordinator.components)}")
            results['initialization'] = {'success': True, 'components': len(coordinator.components)}
        else:
            print("  ❌ System initialization: FAILED")
            results['initialization'] = {'success': False, 'error': 'Initialization failed'}
            return results
        
        # Test system startup
        print("\n🚀 PHASE 2: System Startup")
        print("  🔄 Starting all system components...")
        start_success = await coordinator.start()
        
        if start_success:
            print("  ✅ System startup: SUCCESS")
            print(f"  🔹 System State: {coordinator.state.value}")
            results['startup'] = {'success': True, 'state': coordinator.state.value}
        else:
            print("  ❌ System startup: FAILED")
            results['startup'] = {'success': False, 'error': 'Startup failed'}
        
        # Test system status
        print("\n📊 PHASE 3: System Status Check")
        print("  📈 Getting system status...")
        status = await coordinator.get_system_status()
        
        print(f"  🔹 System State: {status.state.value}")
        print(f"  🔹 System Health: {status.system_health:.1%}")
        print(f"  🔹 Active Components: {sum(status.components_status.values())}/{len(status.components_status)}")
        print(f"  🔹 Uptime: {status.uptime:.1f}s")
        
        results['status_check'] = {
            'success': True,
            'state': status.state.value,
            'health': status.system_health,
            'components': status.components_status,
            'uptime': status.uptime
        }
        
        # Test basic component access
        print("\n🔗 PHASE 4: Component Access Test")
        
        # Test component retrieval
        data_manager = await coordinator.get_component('data_manager')
        portfolio_manager = await coordinator.get_component('portfolio_manager')
        execution_engine = await coordinator.get_component('execution_engine')
        analytics_engine = await coordinator.get_component('analytics_engine')
        
        component_count = sum(1 for comp in [data_manager, portfolio_manager, execution_engine, analytics_engine] if comp is not None)
        print(f"  🔹 Components accessible: {component_count}/4")
        
        results['component_access'] = {
            'success': component_count >= 3,
            'accessible_components': component_count,
            'total_components': 4
        }
        
        # Test basic portfolio operations
        print("\n💼 PHASE 5: Basic Portfolio Test")
        if portfolio_manager:
            try:
                # Create test portfolio
                portfolio_id = await portfolio_manager.create_portfolio("simple_test", 50000.0)
                if portfolio_id:
                    print(f"  ✅ Portfolio created: {portfolio_id}")
                    
                    # Add a position
                    position_success = await portfolio_manager.add_position("AAPL", 50, 150.0)
                    if position_success:
                        print("  ✅ Position added successfully")
                        
                        # Get portfolio value
                        portfolio_value = await portfolio_manager.get_portfolio_value(portfolio_id)
                        print(f"  ✅ Portfolio value: ${portfolio_value}")
                        
                        results['portfolio_test'] = {'success': True, 'portfolio_id': portfolio_id, 'value': portfolio_value}
                    else:
                        print("  ⚠️ Failed to add position")
                        results['portfolio_test'] = {'success': False, 'reason': 'Position add failed'}
                else:
                    print("  ⚠️ Failed to create portfolio")
                    results['portfolio_test'] = {'success': False, 'reason': 'Portfolio creation failed'}
            except Exception as e:
                print(f"  ❌ Portfolio test error: {e}")
                results['portfolio_test'] = {'success': False, 'error': str(e)}
        else:
            print("  ❌ Portfolio Manager not available")
            results['portfolio_test'] = {'success': False, 'reason': 'Component not available'}
        
        # Test system commands
        print("\n🎛️ PHASE 6: System Command Test")
        
        # Test health check command
        print("  🏥 Testing health check...")
        health_result = await coordinator.execute_system_command("health_check")
        if health_result['success']:
            health_score = health_result['health']
            print(f"    ✅ Health check: {health_score:.1%}")
            results['health_check'] = {'success': True, 'health': health_score}
        else:
            print("    ❌ Health check: FAILED")
            results['health_check'] = {'success': False, 'error': health_result.get('error')}
        
        # Test system shutdown
        print("\n🛑 PHASE 7: System Shutdown")
        print("  🔄 Stopping system...")
        stop_success = await coordinator.stop()
        
        if stop_success:
            print("  ✅ System shutdown: SUCCESS")
            print(f"  🔹 Final State: {coordinator.state.value}")
            results['shutdown'] = {'success': True, 'final_state': coordinator.state.value}
        else:
            print("  ❌ System shutdown: FAILED")
            results['shutdown'] = {'success': False, 'error': 'Shutdown failed'}
        
        # Calculate overall integration score
        print("\n🎉 SIMPLE INTEGRATION TEST SUMMARY")
        print("=" * 50)
        
        successful_tests = sum(1 for result in results.values() if result.get('success'))
        total_tests = len(results)
        integration_score = (successful_tests / total_tests) * 100
        
        print(f"📊 Integration Tests: {successful_tests}/{total_tests} passed")
        print(f"🔧 Integration Score: {integration_score:.1f}%")
        
        # Detailed results
        for test_name, result in results.items():
            status = "✅ PASS" if result.get('success') else "❌ FAIL"
            reason = f" - {result.get('reason', result.get('error', ''))}" if not result.get('success') else ""
            print(f"  {test_name}: {status}{reason}")
        
        # Save results
        test_summary = {
            "timestamp": datetime.now().isoformat(),
            "test_type": "simple_system_integration",
            "integration_tests": results,
            "summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "integration_score": integration_score,
                "system_ready": integration_score >= 80.0
            }
        }
        
        with open('simple_integration_results.json', 'w') as f:
            json.dump(test_summary, f, indent=2, default=str)
        
        print(f"\n📄 Results saved to: simple_integration_results.json")
        
        # Final verdict
        if integration_score >= 90:
            print("\n🎉 EXCELLENT! Simple integration is OUTSTANDING!")
            print("🚀 Ready for advanced integration tests!")
        elif integration_score >= 80:
            print("\n✅ GOOD! Simple integration is SOLID!")
            print("🔧 Ready for advanced features!")
        elif integration_score >= 70:
            print("\n⚠️ FAIR! Simple integration needs some work!")
            print("🛠️ Address failing tests before proceeding!")
        else:
            print("\n❌ POOR! Simple integration has significant issues!")
            print("🚨 Major fixes required!")
        
        return integration_score >= 80.0
        
    except Exception as e:
        print(f"❌ Simple Integration Test Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_simple_integration())
    if success:
        print("\n🎉 SIMPLE INTEGRATION SUCCESSFUL!")
        print("🚀 Ready for advanced features!")
    else:
        print("\n⚠️ SIMPLE INTEGRATION NEEDS WORK!")
        print("🔧 Review test results and fix issues!")

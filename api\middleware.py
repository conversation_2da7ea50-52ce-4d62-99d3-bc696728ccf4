"""
API Middleware - Custom middleware for request processing
"""

import asyncio
import logging
import time
import json
import uuid
from typing import Dict, Any, Callable, Optional
from datetime import datetime
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

logger = logging.getLogger(__name__)


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for logging HTTP requests and responses.
    
    Features:
    - Request/response logging
    - Performance timing
    - Request ID generation
    - User tracking
    - Error logging
    """
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.request_count = 0
        self.total_processing_time = 0.0
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate request ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Start timing
        start_time = time.time()
        
        # Get client info
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        # Log request
        logger.info(
            f"Request {request_id}: {request.method} {request.url.path} "
            f"from {client_ip} ({user_agent})"
        )
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate processing time
            processing_time = time.time() - start_time
            self.request_count += 1
            self.total_processing_time += processing_time
            
            # Add headers
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Processing-Time"] = f"{processing_time:.3f}s"
            
            # Log response
            logger.info(
                f"Response {request_id}: {response.status_code} "
                f"({processing_time:.3f}s)"
            )
            
            return response
            
        except Exception as e:
            processing_time = time.time() - start_time
            
            # Log error
            logger.error(
                f"Error {request_id}: {str(e)} ({processing_time:.3f}s)",
                exc_info=True
            )
            
            # Return error response
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal server error",
                    "request_id": request_id,
                    "timestamp": datetime.now().isoformat()
                },
                headers={
                    "X-Request-ID": request_id,
                    "X-Processing-Time": f"{processing_time:.3f}s"
                }
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get middleware statistics"""
        avg_processing_time = (
            self.total_processing_time / self.request_count
            if self.request_count > 0 else 0.0
        )
        
        return {
            "total_requests": self.request_count,
            "total_processing_time": self.total_processing_time,
            "average_processing_time": avg_processing_time
        }


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for centralized error handling.
    
    Features:
    - Standardized error responses
    - Error categorization
    - Error tracking and metrics
    - Security error filtering
    """
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.error_counts = {
            "4xx": 0,
            "5xx": 0,
            "total": 0
        }
        self.error_types = {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            response = await call_next(request)
            
            # Track error responses
            if response.status_code >= 400:
                await self._track_error(response.status_code, "HTTP Error")
            
            return response
            
        except Exception as e:
            # Track exception
            await self._track_error(500, type(e).__name__)
            
            # Get request ID if available
            request_id = getattr(request.state, 'request_id', 'unknown')
            
            # Create standardized error response
            error_response = await self._create_error_response(e, request_id)
            
            return error_response
    
    async def _track_error(self, status_code: int, error_type: str):
        """Track error for metrics"""
        try:
            self.error_counts["total"] += 1
            
            if 400 <= status_code < 500:
                self.error_counts["4xx"] += 1
            elif status_code >= 500:
                self.error_counts["5xx"] += 1
            
            # Track error types
            if error_type not in self.error_types:
                self.error_types[error_type] = 0
            self.error_types[error_type] += 1
            
        except Exception as e:
            logger.error(f"Error tracking error metrics: {e}")
    
    async def _create_error_response(self, error: Exception, request_id: str) -> JSONResponse:
        """Create standardized error response"""
        try:
            # Determine error type and status code
            if hasattr(error, 'status_code'):
                status_code = error.status_code
                detail = getattr(error, 'detail', str(error))
            else:
                status_code = 500
                detail = "Internal server error"
            
            # Create error response
            error_data = {
                "error": {
                    "type": type(error).__name__,
                    "message": detail,
                    "request_id": request_id,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            # Add debug info in development
            if logger.level <= logging.DEBUG:
                error_data["error"]["debug"] = {
                    "exception": str(error),
                    "type": type(error).__name__
                }
            
            return JSONResponse(
                status_code=status_code,
                content=error_data,
                headers={
                    "X-Request-ID": request_id,
                    "X-Error-Type": type(error).__name__
                }
            )
            
        except Exception as e:
            logger.error(f"Error creating error response: {e}")
            
            # Fallback error response
            return JSONResponse(
                status_code=500,
                content={
                    "error": {
                        "type": "InternalError",
                        "message": "An unexpected error occurred",
                        "request_id": request_id,
                        "timestamp": datetime.now().isoformat()
                    }
                }
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get error handling statistics"""
        return {
            "error_counts": self.error_counts.copy(),
            "error_types": self.error_types.copy(),
            "error_rate": (
                self.error_counts["total"] / max(1, self.error_counts["total"])
            ) * 100
        }


class SecurityMiddleware(BaseHTTPMiddleware):
    """
    Middleware for security headers and protection.
    
    Features:
    - Security headers
    - CSRF protection
    - XSS protection
    - Content type validation
    """
    
    def __init__(self, app: ASGIApp, config: Dict[str, Any] = None):
        super().__init__(app)
        self.config = config or {}
        self.security_config = self.config.get('security', {})
        
        # Security settings
        self.add_security_headers = self.security_config.get('add_security_headers', True)
        self.csrf_protection = self.security_config.get('csrf_protection', False)
        self.content_type_validation = self.security_config.get('content_type_validation', True)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Validate content type for POST/PUT requests
        if self.content_type_validation:
            if request.method in ["POST", "PUT", "PATCH"]:
                content_type = request.headers.get("content-type", "")
                if not self._is_valid_content_type(content_type):
                    return JSONResponse(
                        status_code=415,
                        content={
                            "error": {
                                "type": "UnsupportedMediaType",
                                "message": "Unsupported content type",
                                "timestamp": datetime.now().isoformat()
                            }
                        }
                    )
        
        # Process request
        response = await call_next(request)
        
        # Add security headers
        if self.add_security_headers:
            self._add_security_headers(response)
        
        return response
    
    def _is_valid_content_type(self, content_type: str) -> bool:
        """Check if content type is valid"""
        valid_types = [
            "application/json",
            "application/x-www-form-urlencoded",
            "multipart/form-data",
            "text/plain"
        ]
        
        return any(content_type.startswith(valid_type) for valid_type in valid_types)
    
    def _add_security_headers(self, response: Response):
        """Add security headers to response"""
        try:
            # Prevent XSS attacks
            response.headers["X-Content-Type-Options"] = "nosniff"
            response.headers["X-Frame-Options"] = "DENY"
            response.headers["X-XSS-Protection"] = "1; mode=block"
            
            # HSTS (if HTTPS)
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
            
            # Content Security Policy
            response.headers["Content-Security-Policy"] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "connect-src 'self'"
            )
            
            # Referrer Policy
            response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
            
            # Permissions Policy
            response.headers["Permissions-Policy"] = (
                "geolocation=(), "
                "microphone=(), "
                "camera=()"
            )
            
        except Exception as e:
            logger.error(f"Error adding security headers: {e}")


class CacheMiddleware(BaseHTTPMiddleware):
    """
    Middleware for response caching.
    
    Features:
    - Response caching
    - Cache headers
    - ETags
    - Conditional requests
    """
    
    def __init__(self, app: ASGIApp, config: Dict[str, Any] = None):
        super().__init__(app)
        self.config = config or {}
        self.cache_config = self.config.get('caching', {})
        
        # Cache settings
        self.enabled = self.cache_config.get('enabled', False)
        self.default_max_age = self.cache_config.get('default_max_age', 300)  # 5 minutes
        self.cacheable_methods = self.cache_config.get('cacheable_methods', ['GET'])
        self.cacheable_status_codes = self.cache_config.get('cacheable_status_codes', [200])
        
        # Simple in-memory cache
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "total_requests": 0
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        if not self.enabled:
            return await call_next(request)
        
        self.cache_stats["total_requests"] += 1
        
        # Only cache certain methods
        if request.method not in self.cacheable_methods:
            return await call_next(request)
        
        # Generate cache key
        cache_key = self._generate_cache_key(request)
        
        # Check cache
        cached_response = await self._get_cached_response(cache_key)
        if cached_response:
            self.cache_stats["hits"] += 1
            return self._create_response_from_cache(cached_response)
        
        # Process request
        response = await call_next(request)
        
        # Cache response if appropriate
        if response.status_code in self.cacheable_status_codes:
            await self._cache_response(cache_key, response)
        
        self.cache_stats["misses"] += 1
        return response
    
    def _generate_cache_key(self, request: Request) -> str:
        """Generate cache key for request"""
        try:
            # Include method, path, and query parameters
            key_parts = [
                request.method,
                str(request.url.path),
                str(request.url.query)
            ]
            
            # Include relevant headers
            relevant_headers = ["accept", "accept-encoding", "authorization"]
            for header in relevant_headers:
                value = request.headers.get(header)
                if value:
                    key_parts.append(f"{header}:{value}")
            
            return "|".join(key_parts)
            
        except Exception as e:
            logger.error(f"Error generating cache key: {e}")
            return str(request.url)
    
    async def _get_cached_response(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached response if valid"""
        try:
            if cache_key not in self.cache:
                return None
            
            cached_data = self.cache[cache_key]
            
            # Check expiry
            if time.time() > cached_data["expires_at"]:
                del self.cache[cache_key]
                return None
            
            return cached_data
            
        except Exception as e:
            logger.error(f"Error getting cached response: {e}")
            return None
    
    async def _cache_response(self, cache_key: str, response: Response):
        """Cache response"""
        try:
            # Read response body
            body = b""
            async for chunk in response.body_iterator:
                body += chunk
            
            # Cache data
            self.cache[cache_key] = {
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "body": body,
                "cached_at": time.time(),
                "expires_at": time.time() + self.default_max_age
            }
            
            # Recreate response with body
            response.body_iterator = self._create_body_iterator(body)
            
            # Add cache headers
            response.headers["Cache-Control"] = f"max-age={self.default_max_age}"
            response.headers["X-Cache"] = "MISS"
            
        except Exception as e:
            logger.error(f"Error caching response: {e}")
    
    def _create_response_from_cache(self, cached_data: Dict[str, Any]) -> Response:
        """Create response from cached data"""
        try:
            headers = cached_data["headers"].copy()
            headers["X-Cache"] = "HIT"
            headers["Age"] = str(int(time.time() - cached_data["cached_at"]))
            
            return Response(
                content=cached_data["body"],
                status_code=cached_data["status_code"],
                headers=headers
            )
            
        except Exception as e:
            logger.error(f"Error creating response from cache: {e}")
            return Response(status_code=500)
    
    def _create_body_iterator(self, body: bytes):
        """Create body iterator from bytes"""
        async def body_iterator():
            yield body
        
        return body_iterator()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total = self.cache_stats["total_requests"]
        hit_rate = (self.cache_stats["hits"] / total * 100) if total > 0 else 0
        
        return {
            "enabled": self.enabled,
            "cache_stats": self.cache_stats.copy(),
            "hit_rate": hit_rate,
            "cached_items": len(self.cache)
        }

#!/usr/bin/env python3
"""
Test Missing Components - Identify and test missing system components
"""

import asyncio
import json
from datetime import datetime
from config.config_manager import ConfigManager

async def test_missing_components():
    """Test and identify missing system components"""
    
    print("🔍 TESTING MISSING COMPONENTS")
    print("=" * 50)
    
    results = {}
    
    try:
        # Load configuration
        config_manager = ConfigManager('config/test_config.yaml')
        await config_manager.load_config()
        config = await config_manager.get_config()
        
        print("✅ Configuration loaded")
        
        # Test 1: DataManager
        print("\n📊 TEST 1: DataManager")
        try:
            from data.data_manager import DataManager
            
            data_manager = DataManager(config)
            await data_manager.initialize()
            
            # Test basic functionality
            market_data = await data_manager.get_market_data("AAPL", "1d", limit=10)
            print(f"✅ DataManager working: {type(market_data)}")
            
            current_price = await data_manager.get_current_price("AAPL")
            print(f"✅ Current price retrieval: ${current_price}")
            
            results['data_manager'] = {'success': True, 'status': 'functional'}
            
        except ImportError as e:
            print(f"❌ DataManager import error: {e}")
            results['data_manager'] = {'success': False, 'error': f'Import error: {e}'}
        except Exception as e:
            print(f"❌ DataManager error: {e}")
            results['data_manager'] = {'success': False, 'error': str(e)}
        
        # Test 2: PortfolioManager
        print("\n💼 TEST 2: PortfolioManager")
        try:
            from portfolio.portfolio_manager import PortfolioManager
            
            portfolio_manager = PortfolioManager(config)
            await portfolio_manager.initialize()
            
            # Test portfolio creation
            portfolio_id = await portfolio_manager.create_portfolio("test_portfolio", 100000.0)
            print(f"✅ Portfolio created: {portfolio_id}")
            
            # Test position management
            success = await portfolio_manager.add_position("AAPL", 100, 150.0)
            print(f"✅ Position added: {success}")
            
            # Test portfolio value
            portfolio_value = await portfolio_manager.get_portfolio_value(portfolio_id)
            print(f"✅ Portfolio value: ${portfolio_value}")
            
            results['portfolio_manager'] = {'success': True, 'status': 'functional'}
            
        except ImportError as e:
            print(f"❌ PortfolioManager import error: {e}")
            results['portfolio_manager'] = {'success': False, 'error': f'Import error: {e}'}
        except Exception as e:
            print(f"❌ PortfolioManager error: {e}")
            results['portfolio_manager'] = {'success': False, 'error': str(e)}
        
        # Test 3: ExecutionEngine
        print("\n⚡ TEST 3: ExecutionEngine")
        try:
            from execution.execution_engine import ExecutionEngine
            from execution.order_types import Order, OrderType, OrderSide
            
            execution_engine = ExecutionEngine(config)
            await execution_engine.initialize()
            
            # Test order creation
            order = Order(
                order_id="test_order_001",
                symbol="AAPL",
                side=OrderSide.BUY,
                quantity=100,
                order_type=OrderType.MARKET,
                strategy_id="test_strategy"
            )
            
            # Test order submission
            execution_result = await execution_engine.submit_order(order)
            print(f"✅ Order submitted: {execution_result}")
            
            # Test active orders
            active_orders = await execution_engine.get_active_orders()
            print(f"✅ Active orders: {len(active_orders) if isinstance(active_orders, list) else 'Available'}")
            
            results['execution_engine'] = {'success': True, 'status': 'functional'}
            
        except ImportError as e:
            print(f"❌ ExecutionEngine import error: {e}")
            results['execution_engine'] = {'success': False, 'error': f'Import error: {e}'}
        except Exception as e:
            print(f"❌ ExecutionEngine error: {e}")
            results['execution_engine'] = {'success': False, 'error': str(e)}
        
        # Test 4: AnalyticsEngine
        print("\n📈 TEST 4: AnalyticsEngine")
        try:
            from analytics.analytics_engine import AdvancedAnalyticsEngine

            analytics_engine = AdvancedAnalyticsEngine(config)
            await analytics_engine.initialize()

            # Test performance calculation
            performance_data = {
                'portfolio_id': 'test_portfolio',
                'start_date': '2024-01-01',
                'end_date': '2024-12-31',
                'trades': [],
                'portfolio_values': [100000, 105000, 103000, 108000]
            }

            performance_metrics = await analytics_engine.calculate_performance(performance_data)
            print(f"✅ Performance calculated: {type(performance_metrics)}")

            # Test analytics report
            report = await analytics_engine.generate_report('daily', performance_data)
            print(f"✅ Report generated: {type(report)}")

            results['analytics_engine'] = {'success': True, 'status': 'functional'}
            
        except ImportError as e:
            print(f"❌ AnalyticsEngine import error: {e}")
            results['analytics_engine'] = {'success': False, 'error': f'Import error: {e}'}
        except Exception as e:
            print(f"❌ AnalyticsEngine error: {e}")
            results['analytics_engine'] = {'success': False, 'error': str(e)}
        
        # Test 5: Complete System Integration
        print("\n🔗 TEST 5: System Integration")
        try:
            working_components = sum(1 for result in results.values() if result.get('success'))
            total_components = len(results)
            
            integration_percentage = (working_components / total_components) * 100
            
            print(f"✅ Working Components: {working_components}/{total_components}")
            print(f"✅ Integration Level: {integration_percentage:.1f}%")
            
            results['system_integration'] = {
                'success': working_components >= 3,  # At least 75% working
                'working_components': working_components,
                'total_components': total_components,
                'integration_percentage': integration_percentage
            }
            
        except Exception as e:
            print(f"❌ System integration test failed: {e}")
            results['system_integration'] = {'success': False, 'error': str(e)}
        
        # Summary
        print("\n🎉 MISSING COMPONENTS TEST SUMMARY")
        print("=" * 50)
        
        working_components = sum(1 for result in results.values() if result.get('success'))
        total_components = len(results) - 1  # Exclude integration test
        
        print(f"📊 Component Status: {working_components}/{total_components} working")
        print(f"🔧 Success Rate: {(working_components/total_components)*100:.1f}%")
        
        # Detailed results
        for component_name, result in results.items():
            if component_name != 'system_integration':
                status = "✅ WORKING" if result.get('success') else "❌ MISSING/BROKEN"
                error = f" - {result.get('error')}" if result.get('error') else ""
                print(f"  {component_name}: {status}{error}")
        
        # Identify what needs to be implemented
        missing_components = [name for name, result in results.items() 
                            if not result.get('success') and name != 'system_integration']
        
        if missing_components:
            print(f"\n🔧 COMPONENTS TO IMPLEMENT:")
            for component in missing_components:
                print(f"  - {component}")
        else:
            print(f"\n🎉 ALL COMPONENTS ARE WORKING!")
        
        # Save results
        test_summary = {
            "timestamp": datetime.now().isoformat(),
            "test_type": "missing_components_analysis",
            "components": results,
            "summary": {
                "total_components": total_components,
                "working_components": working_components,
                "success_rate": (working_components/total_components)*100,
                "missing_components": missing_components
            }
        }
        
        with open('missing_components_results.json', 'w') as f:
            json.dump(test_summary, f, indent=2, default=str)
        
        print(f"\n📄 Results saved to: missing_components_results.json")
        
        return working_components >= (total_components * 0.75)  # 75% success threshold
        
    except Exception as e:
        print(f"❌ System Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_missing_components())
    if success:
        print("\n🎉 MOST COMPONENTS ARE WORKING!")
    else:
        print("\n⚠️ SEVERAL COMPONENTS NEED IMPLEMENTATION")

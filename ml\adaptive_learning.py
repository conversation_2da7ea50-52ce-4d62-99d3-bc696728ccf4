"""
Adaptive Learning System - Continuous learning and model adaptation
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from collections import deque
import json
import time

logger = logging.getLogger(__name__)


class AdaptiveLearningSystem:
    """
    Adaptive learning system that continuously improves model performance
    based on feedback and changing market conditions.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Learning components
        self.performance_history: Dict[str, deque] = {}
        self.adaptation_triggers: Dict[str, Dict[str, Any]] = {}
        self.learning_rules: Dict[str, Any] = {}
        
        # Feedback storage
        self.feedback_buffer: deque = deque(maxlen=10000)
        self.model_updates: Dict[str, List[Dict[str, Any]]] = {}
        
        # Adaptation parameters
        self.adaptation_thresholds = {
            'performance_degradation': 0.1,  # 10% performance drop
            'market_regime_change': 0.3,     # 30% change in market characteristics
            'prediction_accuracy': 0.6,      # Below 60% accuracy
            'feedback_score': 0.5            # Below 50% positive feedback
        }
        
        # State
        self.initialized = False
        self.learning_active = True
        
    async def initialize(self):
        """Initialize adaptive learning system"""
        if self.initialized:
            return
            
        logger.info("Initializing Adaptive Learning System...")
        
        # Setup learning rules
        await self._setup_learning_rules()
        
        # Initialize performance tracking
        await self._setup_performance_tracking()
        
        self.initialized = True
        logger.info("✓ Adaptive Learning System initialized")
        
    async def _setup_learning_rules(self):
        """Setup learning rules and adaptation strategies"""
        self.learning_rules = {
            'performance_based': {
                'trigger': 'performance_degradation',
                'action': 'retrain_model',
                'parameters': {'min_samples': 100, 'retrain_threshold': 0.1}
            },
            'regime_based': {
                'trigger': 'market_regime_change',
                'action': 'adapt_parameters',
                'parameters': {'adaptation_rate': 0.1, 'regime_window': 50}
            },
            'feedback_based': {
                'trigger': 'negative_feedback',
                'action': 'adjust_weights',
                'parameters': {'weight_decay': 0.95, 'feedback_window': 20}
            },
            'accuracy_based': {
                'trigger': 'low_accuracy',
                'action': 'ensemble_rebalance',
                'parameters': {'min_accuracy': 0.6, 'rebalance_factor': 0.2}
            }
        }
        
    async def _setup_performance_tracking(self):
        """Setup performance tracking for adaptive learning"""
        # Initialize performance history for different components
        components = ['predictive_models', 'rl_agents', 'ensemble_methods', 'pattern_recognition']
        
        for component in components:
            self.performance_history[component] = deque(maxlen=1000)
            self.model_updates[component] = []
            
    async def record_performance(self, component: str, performance_data: Dict[str, Any]):
        """Record performance data for a component"""
        if component not in self.performance_history:
            self.performance_history[component] = deque(maxlen=1000)
            
        # Add timestamp to performance data
        performance_record = {
            **performance_data,
            'timestamp': time.time()
        }
        
        self.performance_history[component].append(performance_record)
        
        # Check for adaptation triggers
        await self._check_adaptation_triggers(component, performance_record)
        
    async def record_feedback(self, feedback_data: Dict[str, Any]):
        """Record feedback from users or system"""
        feedback_record = {
            **feedback_data,
            'timestamp': time.time()
        }
        
        self.feedback_buffer.append(feedback_record)
        
        # Process feedback for learning
        await self._process_feedback(feedback_record)
        
    async def _check_adaptation_triggers(self, component: str, performance_data: Dict[str, Any]):
        """Check if adaptation is needed based on performance"""
        if not self.learning_active:
            return
            
        # Get recent performance history
        recent_performance = list(self.performance_history[component])[-10:]
        
        if len(recent_performance) < 5:
            return  # Need more data
            
        # Check for performance degradation
        if await self._detect_performance_degradation(recent_performance):
            await self._trigger_adaptation(component, 'performance_degradation', performance_data)
            
        # Check for accuracy issues
        if await self._detect_accuracy_issues(recent_performance):
            await self._trigger_adaptation(component, 'low_accuracy', performance_data)
            
    async def _detect_performance_degradation(self, performance_history: List[Dict[str, Any]]) -> bool:
        """Detect if performance is degrading"""
        if len(performance_history) < 5:
            return False
            
        # Extract performance scores
        scores = []
        for record in performance_history:
            if 'accuracy' in record:
                scores.append(record['accuracy'])
            elif 'r2' in record:
                scores.append(record['r2'])
            elif 'sharpe_ratio' in record:
                scores.append(record['sharpe_ratio'])
                
        if len(scores) < 5:
            return False
            
        # Check for declining trend
        recent_avg = np.mean(scores[-3:])
        earlier_avg = np.mean(scores[:3])
        
        degradation = (earlier_avg - recent_avg) / earlier_avg if earlier_avg > 0 else 0
        
        return degradation > self.adaptation_thresholds['performance_degradation']
        
    async def _detect_accuracy_issues(self, performance_history: List[Dict[str, Any]]) -> bool:
        """Detect accuracy issues"""
        recent_accuracies = []
        
        for record in performance_history[-5:]:
            if 'accuracy' in record:
                recent_accuracies.append(record['accuracy'])
                
        if not recent_accuracies:
            return False
            
        avg_accuracy = np.mean(recent_accuracies)
        return avg_accuracy < self.adaptation_thresholds['prediction_accuracy']
        
    async def _trigger_adaptation(self, component: str, trigger_type: str, context: Dict[str, Any]):
        """Trigger adaptation based on trigger type"""
        logger.info(f"Triggering adaptation for {component}: {trigger_type}")
        
        # Find matching learning rule
        matching_rule = None
        for rule_name, rule in self.learning_rules.items():
            if rule['trigger'] == trigger_type:
                matching_rule = rule
                break
                
        if not matching_rule:
            logger.warning(f"No learning rule found for trigger: {trigger_type}")
            return
            
        # Execute adaptation action
        await self._execute_adaptation_action(component, matching_rule, context)
        
    async def _execute_adaptation_action(self, component: str, rule: Dict[str, Any], context: Dict[str, Any]):
        """Execute adaptation action"""
        action = rule['action']
        parameters = rule['parameters']
        
        adaptation_record = {
            'component': component,
            'action': action,
            'parameters': parameters,
            'context': context,
            'timestamp': time.time()
        }
        
        if action == 'retrain_model':
            await self._retrain_model_action(component, parameters, context)
        elif action == 'adapt_parameters':
            await self._adapt_parameters_action(component, parameters, context)
        elif action == 'adjust_weights':
            await self._adjust_weights_action(component, parameters, context)
        elif action == 'ensemble_rebalance':
            await self._ensemble_rebalance_action(component, parameters, context)
            
        # Record adaptation
        if component not in self.model_updates:
            self.model_updates[component] = []
        self.model_updates[component].append(adaptation_record)
        
        logger.info(f"✓ Executed adaptation action: {action} for {component}")
        
    async def _retrain_model_action(self, component: str, parameters: Dict[str, Any], context: Dict[str, Any]):
        """Retrain model action"""
        # This would trigger model retraining
        # Implementation depends on the specific component
        logger.info(f"Retraining {component} with parameters: {parameters}")
        
    async def _adapt_parameters_action(self, component: str, parameters: Dict[str, Any], context: Dict[str, Any]):
        """Adapt model parameters action"""
        adaptation_rate = parameters.get('adaptation_rate', 0.1)
        logger.info(f"Adapting parameters for {component} with rate: {adaptation_rate}")
        
    async def _adjust_weights_action(self, component: str, parameters: Dict[str, Any], context: Dict[str, Any]):
        """Adjust model weights action"""
        weight_decay = parameters.get('weight_decay', 0.95)
        logger.info(f"Adjusting weights for {component} with decay: {weight_decay}")
        
    async def _ensemble_rebalance_action(self, component: str, parameters: Dict[str, Any], context: Dict[str, Any]):
        """Rebalance ensemble weights action"""
        rebalance_factor = parameters.get('rebalance_factor', 0.2)
        logger.info(f"Rebalancing ensemble for {component} with factor: {rebalance_factor}")
        
    async def _process_feedback(self, feedback: Dict[str, Any]):
        """Process feedback for learning"""
        feedback_type = feedback.get('type', 'general')
        feedback_score = feedback.get('score', 0.5)
        
        # Analyze feedback patterns
        recent_feedback = list(self.feedback_buffer)[-20:]
        
        if len(recent_feedback) >= 10:
            avg_score = np.mean([f.get('score', 0.5) for f in recent_feedback])
            
            if avg_score < self.adaptation_thresholds['feedback_score']:
                # Trigger feedback-based adaptation
                component = feedback.get('component', 'general')
                await self._trigger_adaptation(component, 'negative_feedback', feedback)
                
    async def detect_market_regime_change(self, market_data: Dict[str, Any]) -> bool:
        """Detect if market regime has changed"""
        # Simplified regime detection
        # In practice, this would use more sophisticated methods
        
        current_volatility = market_data.get('volatility', 0.2)
        current_trend = market_data.get('trend_strength', 0.0)
        
        # Compare with historical values
        # This is a placeholder - real implementation would be more complex
        volatility_change = abs(current_volatility - 0.2) / 0.2
        trend_change = abs(current_trend - 0.0)
        
        regime_change_score = max(volatility_change, trend_change)
        
        return regime_change_score > self.adaptation_thresholds['market_regime_change']
        
    async def get_learning_summary(self) -> Dict[str, Any]:
        """Get summary of learning activities"""
        total_adaptations = sum(len(updates) for updates in self.model_updates.values())
        
        # Recent performance trends
        performance_trends = {}
        for component, history in self.performance_history.items():
            if len(history) >= 5:
                recent_scores = []
                for record in list(history)[-5:]:
                    if 'accuracy' in record:
                        recent_scores.append(record['accuracy'])
                    elif 'r2' in record:
                        recent_scores.append(record['r2'])
                        
                if recent_scores:
                    performance_trends[component] = {
                        'avg_performance': np.mean(recent_scores),
                        'trend': 'improving' if recent_scores[-1] > recent_scores[0] else 'declining',
                        'data_points': len(recent_scores)
                    }
                    
        # Feedback analysis
        recent_feedback = list(self.feedback_buffer)[-50:]
        feedback_summary = {
            'total_feedback': len(self.feedback_buffer),
            'recent_feedback': len(recent_feedback),
            'avg_score': np.mean([f.get('score', 0.5) for f in recent_feedback]) if recent_feedback else 0.5
        }
        
        return {
            'learning_active': self.learning_active,
            'total_adaptations': total_adaptations,
            'adaptations_by_component': {k: len(v) for k, v in self.model_updates.items()},
            'performance_trends': performance_trends,
            'feedback_summary': feedback_summary,
            'adaptation_thresholds': self.adaptation_thresholds
        }
        
    async def set_learning_mode(self, active: bool):
        """Enable or disable adaptive learning"""
        self.learning_active = active
        logger.info(f"Adaptive learning {'enabled' if active else 'disabled'}")
        
    async def update_adaptation_thresholds(self, new_thresholds: Dict[str, float]):
        """Update adaptation thresholds"""
        self.adaptation_thresholds.update(new_thresholds)
        logger.info(f"Updated adaptation thresholds: {new_thresholds}")
        
    async def export_learning_data(self, filepath: str):
        """Export learning data for analysis"""
        learning_data = {
            'performance_history': {k: list(v) for k, v in self.performance_history.items()},
            'model_updates': self.model_updates,
            'feedback_buffer': list(self.feedback_buffer),
            'adaptation_thresholds': self.adaptation_thresholds,
            'learning_rules': self.learning_rules
        }
        
        try:
            with open(filepath, 'w') as f:
                json.dump(learning_data, f, default=str, indent=2)
            logger.info(f"✓ Learning data exported to {filepath}")
        except Exception as e:
            logger.error(f"Error exporting learning data: {e}")
            
    async def import_learning_data(self, filepath: str):
        """Import learning data"""
        try:
            with open(filepath, 'r') as f:
                learning_data = json.load(f)
                
            # Restore data structures
            for component, history in learning_data.get('performance_history', {}).items():
                self.performance_history[component] = deque(history, maxlen=1000)
                
            self.model_updates = learning_data.get('model_updates', {})
            self.feedback_buffer = deque(learning_data.get('feedback_buffer', []), maxlen=10000)
            self.adaptation_thresholds.update(learning_data.get('adaptation_thresholds', {}))
            
            logger.info(f"✓ Learning data imported from {filepath}")
            
        except Exception as e:
            logger.error(f"Error importing learning data: {e}")

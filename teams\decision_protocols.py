"""
Decision Protocols - Sophisticated decision-making mechanisms for teams
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Optional, Any, Set, Tuple
from enum import Enum
from dataclasses import dataclass
import statistics

logger = logging.getLogger(__name__)


class DecisionType(Enum):
    """Types of decisions"""
    CONSENSUS = "consensus"          # Requires agreement from all
    MAJORITY_VOTE = "majority_vote"  # Requires >50% agreement
    SUPERMAJORITY = "supermajority"  # Requires 2/3+ agreement
    WEIGHTED_VOTE = "weighted_vote"  # Votes weighted by expertise/authority
    EXPERT_DECISION = "expert_decision"  # Decision by domain expert
    LEADER_DECISION = "leader_decision"  # Decision by team leader
    ESCALATED_DECISION = "escalated_decision"  # Escalated to higher authority


class DecisionStatus(Enum):
    """Decision status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    APPROVED = "approved"
    REJECTED = "rejected"
    ESCALATED = "escalated"
    TIMEOUT = "timeout"
    CONFLICT = "conflict"


class ConflictResolutionMethod(Enum):
    """Conflict resolution methods"""
    MEDIATION = "mediation"          # Third-party mediation
    ARBITRATION = "arbitration"      # Binding arbitration
    ESCALATION = "escalation"        # Escalate to higher authority
    COMPROMISE = "compromise"        # Find middle ground
    EXPERT_OVERRIDE = "expert_override"  # Domain expert decides
    LEADER_OVERRIDE = "leader_override"  # Leader decides


@dataclass
class DecisionOption:
    """A decision option"""
    option_id: str
    description: str
    proposed_by: str
    supporting_data: Dict[str, Any]
    estimated_impact: float  # 0.0-1.0
    risk_level: float       # 0.0-1.0
    confidence: float       # 0.0-1.0


@dataclass
class Vote:
    """A vote on a decision"""
    voter_id: str
    option_id: str
    weight: float = 1.0
    confidence: float = 1.0
    reasoning: str = ""
    timestamp: float = 0.0


@dataclass
class DecisionRequest:
    """A decision request"""
    decision_id: str
    title: str
    description: str
    decision_type: DecisionType
    options: List[DecisionOption]
    participants: List[str]
    required_votes: int
    timeout_seconds: int
    created_by: str
    created_at: float
    domain: str  # Decision domain (e.g., "trading", "risk", "strategy")
    urgency: float  # 0.0-1.0
    complexity: float  # 0.0-1.0


class DecisionProtocols:
    """
    Sophisticated decision-making protocols including consensus mechanisms,
    voting systems, conflict resolution, and escalation procedures.
    """
    
    def __init__(self, hierarchical_structure, config: Dict[str, Any]):
        self.hierarchical_structure = hierarchical_structure
        self.config = config
        
        # Decision management
        self.active_decisions: Dict[str, DecisionRequest] = {}
        self.decision_history: List[Dict[str, Any]] = []
        self.votes: Dict[str, List[Vote]] = {}  # decision_id -> votes
        
        # Conflict resolution
        self.active_conflicts: Dict[str, Dict[str, Any]] = {}
        self.resolution_history: List[Dict[str, Any]] = []
        
        # Protocol configurations
        self.protocol_configs = {
            'consensus': {
                'required_agreement': 1.0,  # 100% agreement
                'timeout_default': 3600,    # 1 hour
                'allow_abstention': False
            },
            'majority_vote': {
                'required_agreement': 0.51,  # >50% agreement
                'timeout_default': 1800,     # 30 minutes
                'allow_abstention': True
            },
            'supermajority': {
                'required_agreement': 0.67,  # 2/3 agreement
                'timeout_default': 2400,     # 40 minutes
                'allow_abstention': True
            },
            'weighted_vote': {
                'required_agreement': 0.60,  # 60% weighted agreement
                'timeout_default': 2400,     # 40 minutes
                'weight_by_expertise': True
            }
        }
        
        # Escalation rules
        self.escalation_rules = {
            'timeout': 'escalate_to_leader',
            'conflict': 'mediation_then_escalation',
            'high_impact': 'require_supermajority',
            'high_risk': 'require_expert_approval'
        }
        
        # State
        self.initialized = False
        
    async def initialize(self):
        """Initialize decision protocols"""
        if self.initialized:
            return
            
        logger.info("Initializing Decision Protocols...")
        
        # Setup decision frameworks
        await self._setup_decision_frameworks()
        
        # Setup conflict resolution mechanisms
        await self._setup_conflict_resolution()
        
        # Setup escalation procedures
        await self._setup_escalation_procedures()
        
        self.initialized = True
        logger.info("✓ Decision Protocols initialized")
        
    async def _setup_decision_frameworks(self):
        """Setup decision-making frameworks"""
        self.decision_frameworks = {
            'trading_decisions': {
                'low_impact': DecisionType.EXPERT_DECISION,
                'medium_impact': DecisionType.MAJORITY_VOTE,
                'high_impact': DecisionType.SUPERMAJORITY,
                'critical_impact': DecisionType.CONSENSUS
            },
            'risk_decisions': {
                'routine': DecisionType.EXPERT_DECISION,
                'elevated': DecisionType.WEIGHTED_VOTE,
                'critical': DecisionType.CONSENSUS,
                'emergency': DecisionType.LEADER_DECISION
            },
            'strategy_decisions': {
                'parameter_adjustment': DecisionType.EXPERT_DECISION,
                'strategy_modification': DecisionType.MAJORITY_VOTE,
                'strategy_change': DecisionType.SUPERMAJORITY,
                'strategy_overhaul': DecisionType.CONSENSUS
            }
        }
        
    async def _setup_conflict_resolution(self):
        """Setup conflict resolution mechanisms"""
        self.conflict_resolution_procedures = {
            'voting_tie': {
                'method': ConflictResolutionMethod.LEADER_OVERRIDE,
                'timeout': 300,  # 5 minutes
                'escalation_threshold': 2
            },
            'expertise_conflict': {
                'method': ConflictResolutionMethod.EXPERT_OVERRIDE,
                'timeout': 600,  # 10 minutes
                'escalation_threshold': 1
            },
            'authority_conflict': {
                'method': ConflictResolutionMethod.ESCALATION,
                'timeout': 180,  # 3 minutes
                'escalation_threshold': 1
            },
            'deadlock': {
                'method': ConflictResolutionMethod.MEDIATION,
                'timeout': 900,  # 15 minutes
                'escalation_threshold': 3
            }
        }
        
    async def _setup_escalation_procedures(self):
        """Setup escalation procedures"""
        self.escalation_procedures = {
            'standard_escalation': {
                'levels': ['team_leader', 'senior_specialist', 'supreme_commander'],
                'timeout_per_level': 600,  # 10 minutes per level
                'auto_escalate': True
            },
            'emergency_escalation': {
                'levels': ['team_leader', 'supreme_commander'],
                'timeout_per_level': 180,  # 3 minutes per level
                'auto_escalate': True
            },
            'complex_escalation': {
                'levels': ['expert_panel', 'team_leader', 'supreme_commander'],
                'timeout_per_level': 1200,  # 20 minutes per level
                'auto_escalate': False
            }
        }
        
    async def initiate_decision(self, title: str, description: str, options: List[DecisionOption],
                              participants: List[str], decision_type: DecisionType = None,
                              domain: str = "general", urgency: float = 0.5,
                              complexity: float = 0.5, created_by: str = "system") -> Dict[str, Any]:
        """Initiate a new decision process"""
        try:
            decision_id = str(uuid.uuid4())
            
            # Determine decision type if not specified
            if decision_type is None:
                decision_type = await self._determine_decision_type(domain, urgency, complexity)
                
            # Calculate required votes
            required_votes = await self._calculate_required_votes(decision_type, participants)
            
            # Set timeout based on decision type and urgency
            timeout_seconds = await self._calculate_timeout(decision_type, urgency, complexity)
            
            # Create decision request
            decision_request = DecisionRequest(
                decision_id=decision_id,
                title=title,
                description=description,
                decision_type=decision_type,
                options=options,
                participants=participants,
                required_votes=required_votes,
                timeout_seconds=timeout_seconds,
                created_by=created_by,
                created_at=time.time(),
                domain=domain,
                urgency=urgency,
                complexity=complexity
            )
            
            # Store decision
            self.active_decisions[decision_id] = decision_request
            self.votes[decision_id] = []
            
            # Notify participants
            await self._notify_participants(decision_request)
            
            # Schedule timeout check
            asyncio.create_task(self._schedule_timeout_check(decision_id, timeout_seconds))
            
            logger.info(f"✓ Initiated decision {decision_id}: {title}")
            
            return {
                'success': True,
                'decision_id': decision_id,
                'decision_type': decision_type.value,
                'required_votes': required_votes,
                'timeout_seconds': timeout_seconds
            }
            
        except Exception as e:
            logger.error(f"Error initiating decision: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _determine_decision_type(self, domain: str, urgency: float, complexity: float) -> DecisionType:
        """Determine appropriate decision type based on context"""
        # High urgency decisions
        if urgency > 0.8:
            return DecisionType.LEADER_DECISION
            
        # High complexity decisions
        if complexity > 0.8:
            return DecisionType.CONSENSUS
            
        # Domain-specific rules
        if domain in self.decision_frameworks:
            framework = self.decision_frameworks[domain]
            
            # Determine impact level based on urgency and complexity
            if urgency > 0.7 or complexity > 0.7:
                impact = 'critical_impact'
            elif urgency > 0.5 or complexity > 0.5:
                impact = 'high_impact'
            elif urgency > 0.3 or complexity > 0.3:
                impact = 'medium_impact'
            else:
                impact = 'low_impact'
                
            return framework.get(impact, DecisionType.MAJORITY_VOTE)
            
        # Default to majority vote
        return DecisionType.MAJORITY_VOTE
        
    async def _calculate_required_votes(self, decision_type: DecisionType, participants: List[str]) -> int:
        """Calculate required number of votes"""
        total_participants = len(participants)
        
        if decision_type == DecisionType.CONSENSUS:
            return total_participants
        elif decision_type == DecisionType.SUPERMAJORITY:
            return max(1, int(total_participants * 0.67))
        elif decision_type == DecisionType.MAJORITY_VOTE:
            return max(1, int(total_participants * 0.51))
        elif decision_type in [DecisionType.EXPERT_DECISION, DecisionType.LEADER_DECISION]:
            return 1
        else:
            return max(1, int(total_participants * 0.60))  # Default weighted vote
            
    async def _calculate_timeout(self, decision_type: DecisionType, urgency: float, complexity: float) -> int:
        """Calculate decision timeout"""
        base_timeout = self.protocol_configs.get(decision_type.value, {}).get('timeout_default', 1800)
        
        # Adjust for urgency (higher urgency = shorter timeout)
        urgency_factor = 1.0 - (urgency * 0.5)  # 50% reduction max
        
        # Adjust for complexity (higher complexity = longer timeout)
        complexity_factor = 1.0 + (complexity * 0.5)  # 50% increase max
        
        adjusted_timeout = int(base_timeout * urgency_factor * complexity_factor)
        
        # Ensure minimum and maximum bounds
        return max(300, min(7200, adjusted_timeout))  # 5 minutes to 2 hours
        
    async def _notify_participants(self, decision_request: DecisionRequest):
        """Notify participants about new decision"""
        # This would send notifications to participants
        # For now, just log
        logger.info(f"Notifying {len(decision_request.participants)} participants about decision {decision_request.decision_id}")
        
    async def _schedule_timeout_check(self, decision_id: str, timeout_seconds: int):
        """Schedule timeout check for decision"""
        await asyncio.sleep(timeout_seconds)
        
        if decision_id in self.active_decisions:
            await self._handle_decision_timeout(decision_id)
            
    async def cast_vote(self, decision_id: str, voter_id: str, option_id: str,
                       confidence: float = 1.0, reasoning: str = "") -> Dict[str, Any]:
        """Cast a vote on a decision"""
        try:
            if decision_id not in self.active_decisions:
                return {'success': False, 'error': 'Decision not found'}
                
            decision = self.active_decisions[decision_id]
            
            # Check if voter is participant
            if voter_id not in decision.participants:
                return {'success': False, 'error': 'Voter not authorized'}
                
            # Check if option exists
            valid_options = [opt.option_id for opt in decision.options]
            if option_id not in valid_options:
                return {'success': False, 'error': 'Invalid option'}
                
            # Check if already voted
            existing_votes = [v for v in self.votes[decision_id] if v.voter_id == voter_id]
            if existing_votes:
                return {'success': False, 'error': 'Already voted'}
                
            # Calculate vote weight
            vote_weight = await self._calculate_vote_weight(voter_id, decision)
            
            # Create vote
            vote = Vote(
                voter_id=voter_id,
                option_id=option_id,
                weight=vote_weight,
                confidence=confidence,
                reasoning=reasoning,
                timestamp=time.time()
            )
            
            # Store vote
            self.votes[decision_id].append(vote)
            
            # Check if decision is complete
            decision_result = await self._check_decision_completion(decision_id)
            
            logger.info(f"✓ Vote cast by {voter_id} on decision {decision_id}")
            
            return {
                'success': True,
                'vote_weight': vote_weight,
                'decision_complete': decision_result is not None,
                'result': decision_result
            }
            
        except Exception as e:
            logger.error(f"Error casting vote: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _calculate_vote_weight(self, voter_id: str, decision: DecisionRequest) -> float:
        """Calculate vote weight based on decision type and voter expertise"""
        if decision.decision_type != DecisionType.WEIGHTED_VOTE:
            return 1.0
            
        # Get voter's authority level in decision domain
        authority_level = await self.hierarchical_structure._get_agent_authority_level(
            voter_id, decision.domain
        )
        
        # Weight based on authority level
        weight_mapping = {
            5: 3.0,  # Supreme Commander
            4: 2.0,  # Team Leader
            3: 1.5,  # Senior Specialist
            2: 1.0,  # Specialist
            1: 0.5   # Contributor
        }
        
        return weight_mapping.get(authority_level.value, 1.0)
        
    async def _check_decision_completion(self, decision_id: str) -> Optional[Dict[str, Any]]:
        """Check if decision is complete and return result"""
        decision = self.active_decisions[decision_id]
        votes = self.votes[decision_id]
        
        if decision.decision_type == DecisionType.CONSENSUS:
            return await self._check_consensus(decision, votes)
        elif decision.decision_type == DecisionType.MAJORITY_VOTE:
            return await self._check_majority_vote(decision, votes)
        elif decision.decision_type == DecisionType.SUPERMAJORITY:
            return await self._check_supermajority(decision, votes)
        elif decision.decision_type == DecisionType.WEIGHTED_VOTE:
            return await self._check_weighted_vote(decision, votes)
        elif decision.decision_type in [DecisionType.EXPERT_DECISION, DecisionType.LEADER_DECISION]:
            return await self._check_single_decision(decision, votes)
            
        return None
        
    async def _check_consensus(self, decision: DecisionRequest, votes: List[Vote]) -> Optional[Dict[str, Any]]:
        """Check consensus decision"""
        if len(votes) < len(decision.participants):
            return None  # Not all participants voted
            
        # Check if all votes are for the same option
        if votes:
            first_option = votes[0].option_id
            if all(vote.option_id == first_option for vote in votes):
                return await self._finalize_decision(decision.decision_id, first_option, DecisionStatus.APPROVED)
            else:
                # Conflict - no consensus
                await self._handle_decision_conflict(decision.decision_id, "consensus_failed")
                return await self._finalize_decision(decision.decision_id, None, DecisionStatus.CONFLICT)
                
        return None
        
    async def _check_majority_vote(self, decision: DecisionRequest, votes: List[Vote]) -> Optional[Dict[str, Any]]:
        """Check majority vote decision"""
        if len(votes) < decision.required_votes:
            return None
            
        # Count votes by option
        vote_counts = {}
        for vote in votes:
            vote_counts[vote.option_id] = vote_counts.get(vote.option_id, 0) + 1
            
        # Find option with most votes
        if vote_counts:
            winning_option = max(vote_counts, key=vote_counts.get)
            winning_count = vote_counts[winning_option]
            
            if winning_count >= decision.required_votes:
                return await self._finalize_decision(decision.decision_id, winning_option, DecisionStatus.APPROVED)
                
        return None
        
    async def _check_weighted_vote(self, decision: DecisionRequest, votes: List[Vote]) -> Optional[Dict[str, Any]]:
        """Check weighted vote decision"""
        # Calculate weighted vote totals
        weighted_totals = {}
        total_weight = 0
        
        for vote in votes:
            weighted_totals[vote.option_id] = weighted_totals.get(vote.option_id, 0) + vote.weight
            total_weight += vote.weight
            
        if weighted_totals and total_weight > 0:
            # Find option with highest weighted score
            winning_option = max(weighted_totals, key=weighted_totals.get)
            winning_weight = weighted_totals[winning_option]
            
            # Check if meets threshold
            required_weight = total_weight * self.protocol_configs['weighted_vote']['required_agreement']
            
            if winning_weight >= required_weight:
                return await self._finalize_decision(decision.decision_id, winning_option, DecisionStatus.APPROVED)
                
        return None
        
    async def _finalize_decision(self, decision_id: str, winning_option: Optional[str], 
                               status: DecisionStatus) -> Dict[str, Any]:
        """Finalize decision and move to history"""
        decision = self.active_decisions[decision_id]
        votes = self.votes[decision_id]
        
        result = {
            'decision_id': decision_id,
            'status': status.value,
            'winning_option': winning_option,
            'total_votes': len(votes),
            'completion_time': time.time(),
            'duration': time.time() - decision.created_at
        }
        
        # Move to history
        decision_record = {
            'decision': decision,
            'votes': votes,
            'result': result
        }
        self.decision_history.append(decision_record)
        
        # Clean up active decision
        del self.active_decisions[decision_id]
        del self.votes[decision_id]
        
        logger.info(f"✓ Decision {decision_id} finalized with status: {status.value}")
        
        return result
        
    async def _handle_decision_timeout(self, decision_id: str):
        """Handle decision timeout"""
        if decision_id not in self.active_decisions:
            return
            
        decision = self.active_decisions[decision_id]
        
        # Apply escalation rule
        escalation_rule = self.escalation_rules.get('timeout', 'escalate_to_leader')
        
        if escalation_rule == 'escalate_to_leader':
            await self._escalate_decision(decision_id, 'timeout')
        else:
            await self._finalize_decision(decision_id, None, DecisionStatus.TIMEOUT)
            
    async def _handle_decision_conflict(self, decision_id: str, conflict_type: str):
        """Handle decision conflicts"""
        conflict_id = str(uuid.uuid4())
        
        conflict_record = {
            'conflict_id': conflict_id,
            'decision_id': decision_id,
            'conflict_type': conflict_type,
            'created_at': time.time(),
            'status': 'active'
        }
        
        self.active_conflicts[conflict_id] = conflict_record
        
        # Apply conflict resolution procedure
        procedure = self.conflict_resolution_procedures.get(conflict_type, 
                    self.conflict_resolution_procedures['deadlock'])
        
        await self._apply_conflict_resolution(conflict_id, procedure)
        
    async def _apply_conflict_resolution(self, conflict_id: str, procedure: Dict[str, Any]):
        """Apply conflict resolution procedure"""
        method = procedure['method']
        
        if method == ConflictResolutionMethod.ESCALATION:
            decision_id = self.active_conflicts[conflict_id]['decision_id']
            await self._escalate_decision(decision_id, 'conflict')
        elif method == ConflictResolutionMethod.LEADER_OVERRIDE:
            await self._request_leader_override(conflict_id)
        # Other resolution methods would be implemented here
        
    async def _escalate_decision(self, decision_id: str, reason: str):
        """Escalate decision to higher authority"""
        decision = self.active_decisions[decision_id]
        
        # Find appropriate escalation target
        escalation_target = await self._find_escalation_target(decision)
        
        if escalation_target:
            # Create escalated decision
            escalated_decision = DecisionRequest(
                decision_id=str(uuid.uuid4()),
                title=f"ESCALATED: {decision.title}",
                description=f"Escalated due to {reason}. Original: {decision.description}",
                decision_type=DecisionType.ESCALATED_DECISION,
                options=decision.options,
                participants=[escalation_target],
                required_votes=1,
                timeout_seconds=1800,  # 30 minutes
                created_by="system",
                created_at=time.time(),
                domain=decision.domain,
                urgency=min(1.0, decision.urgency + 0.2),  # Increase urgency
                complexity=decision.complexity
            )
            
            # Store escalated decision
            self.active_decisions[escalated_decision.decision_id] = escalated_decision
            self.votes[escalated_decision.decision_id] = []
            
            # Finalize original decision as escalated
            await self._finalize_decision(decision_id, None, DecisionStatus.ESCALATED)
            
            logger.info(f"✓ Decision {decision_id} escalated to {escalation_target}")
            
    async def _find_escalation_target(self, decision: DecisionRequest) -> Optional[str]:
        """Find appropriate escalation target"""
        # This would use the hierarchical structure to find the appropriate authority
        # For now, return a placeholder
        return "team_leader_001"
        
    async def get_decision_status(self, decision_id: str) -> Dict[str, Any]:
        """Get status of a decision"""
        if decision_id in self.active_decisions:
            decision = self.active_decisions[decision_id]
            votes = self.votes[decision_id]
            
            return {
                'decision_id': decision_id,
                'status': 'active',
                'title': decision.title,
                'decision_type': decision.decision_type.value,
                'votes_cast': len(votes),
                'votes_required': decision.required_votes,
                'participants': decision.participants,
                'time_remaining': max(0, decision.timeout_seconds - (time.time() - decision.created_at))
            }
        else:
            # Check history
            for record in self.decision_history:
                if record['decision'].decision_id == decision_id:
                    return {
                        'decision_id': decision_id,
                        'status': 'completed',
                        'result': record['result']
                    }
                    
            return {'decision_id': decision_id, 'status': 'not_found'}
            
    async def get_decision_analytics(self) -> Dict[str, Any]:
        """Get analytics on decision-making"""
        total_decisions = len(self.decision_history)
        active_decisions = len(self.active_decisions)
        
        if total_decisions == 0:
            return {'total_decisions': 0, 'active_decisions': active_decisions}
            
        # Decision type distribution
        type_distribution = {}
        status_distribution = {}
        avg_duration = 0
        
        for record in self.decision_history:
            decision_type = record['decision'].decision_type.value
            status = record['result']['status']
            duration = record['result']['duration']
            
            type_distribution[decision_type] = type_distribution.get(decision_type, 0) + 1
            status_distribution[status] = status_distribution.get(status, 0) + 1
            avg_duration += duration
            
        avg_duration /= total_decisions
        
        return {
            'total_decisions': total_decisions,
            'active_decisions': active_decisions,
            'type_distribution': type_distribution,
            'status_distribution': status_distribution,
            'average_duration_seconds': avg_duration,
            'active_conflicts': len(self.active_conflicts)
        }

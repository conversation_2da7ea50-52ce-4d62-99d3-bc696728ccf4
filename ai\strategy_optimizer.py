"""
AI-Powered Strategy Optimizer - Uses Ollama models for intelligent strategy optimization
"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import numpy as np

from models.ollama_hub import OllamaModelHub

logger = logging.getLogger(__name__)


@dataclass
class OptimizationResult:
    """Strategy optimization result"""
    strategy_id: str
    original_parameters: Dict[str, Any]
    optimized_parameters: Dict[str, Any]
    expected_improvement: float
    confidence_score: float
    reasoning: str
    timestamp: float


@dataclass
class PerformanceAnalysis:
    """Strategy performance analysis"""
    strategy_id: str
    total_trades: int
    win_rate: float
    avg_return: float
    max_drawdown: float
    sharpe_ratio: float
    volatility: float
    strengths: List[str]
    weaknesses: List[str]
    recommendations: List[str]


class AIStrategyOptimizer:
    """
    AI-powered strategy optimizer that uses Ollama models to analyze
    strategy performance and suggest intelligent optimizations.
    """
    
    def __init__(self, ollama_hub: OllamaModelHub, config: Dict[str, Any]):
        self.ollama_hub = ollama_hub
        self.config = config
        self.optimizer_config = config.get('ai_optimizer', {})
        
        # AI models for different tasks
        self.analysis_model = self.optimizer_config.get('analysis_model', 'phi4-reasoning:plus')
        self.optimization_model = self.optimizer_config.get('optimization_model', 'exaone-deep:32b')
        self.reasoning_model = self.optimizer_config.get('reasoning_model', 'magistral-abliterated:24b')
        
        # Optimization state
        self.optimization_history: List[OptimizationResult] = []
        self.performance_analyses: Dict[str, PerformanceAnalysis] = {}
        
        # Configuration
        self.optimization_interval = self.optimizer_config.get('optimization_interval', 3600)  # 1 hour
        self.min_trades_for_optimization = self.optimizer_config.get('min_trades', 50)
        self.confidence_threshold = self.optimizer_config.get('confidence_threshold', 0.7)
        
        # State
        self.running = False
        self.optimization_tasks: List[asyncio.Task] = []
        
    async def initialize(self) -> bool:
        """Initialize the AI strategy optimizer"""
        try:
            logger.info("Initializing AI Strategy Optimizer...")
            
            # Verify Ollama models are available
            available_models = await self.ollama_hub.list_models()
            
            required_models = [self.analysis_model, self.optimization_model, self.reasoning_model]
            missing_models = [model for model in required_models if model not in available_models]
            
            if missing_models:
                logger.warning(f"Missing Ollama models: {missing_models}")
                logger.info("Continuing with available models...")
            
            logger.info("✅ AI Strategy Optimizer initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize AI Strategy Optimizer: {e}")
            return False
            
    async def start(self) -> bool:
        """Start the AI strategy optimizer"""
        try:
            if self.running:
                return True
                
            logger.info("Starting AI Strategy Optimizer...")
            
            # Start background optimization tasks
            self.optimization_tasks = [
                asyncio.create_task(self._optimization_loop()),
                asyncio.create_task(self._analysis_loop())
            ]
            
            self.running = True
            logger.info("✅ AI Strategy Optimizer started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start AI Strategy Optimizer: {e}")
            return False
            
    async def stop(self) -> bool:
        """Stop the AI strategy optimizer"""
        try:
            if not self.running:
                return True
                
            logger.info("Stopping AI Strategy Optimizer...")
            
            # Cancel background tasks
            for task in self.optimization_tasks:
                task.cancel()
            await asyncio.gather(*self.optimization_tasks, return_exceptions=True)
            self.optimization_tasks.clear()
            
            self.running = False
            logger.info("✅ AI Strategy Optimizer stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop AI Strategy Optimizer: {e}")
            return False
            
    async def analyze_strategy_performance(self, 
                                         strategy_id: str, 
                                         performance_data: Dict[str, Any]) -> PerformanceAnalysis:
        """Analyze strategy performance using AI"""
        try:
            logger.info(f"🤖 Analyzing strategy performance: {strategy_id}")
            
            # Prepare performance data for AI analysis
            analysis_prompt = self._create_performance_analysis_prompt(strategy_id, performance_data)
            
            # Get AI analysis
            analysis_response = await self.ollama_hub.generate_response(
                model=self.analysis_model,
                prompt=analysis_prompt,
                max_tokens=1000
            )
            
            # Parse AI response
            analysis = await self._parse_performance_analysis(analysis_response, strategy_id, performance_data)
            
            # Store analysis
            self.performance_analyses[strategy_id] = analysis
            
            logger.info(f"✅ Performance analysis completed for {strategy_id}")
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing strategy performance: {e}")
            # Return default analysis
            return PerformanceAnalysis(
                strategy_id=strategy_id,
                total_trades=performance_data.get('total_trades', 0),
                win_rate=performance_data.get('win_rate', 0.0),
                avg_return=performance_data.get('avg_return', 0.0),
                max_drawdown=performance_data.get('max_drawdown', 0.0),
                sharpe_ratio=performance_data.get('sharpe_ratio', 0.0),
                volatility=performance_data.get('volatility', 0.0),
                strengths=[],
                weaknesses=[],
                recommendations=[]
            )
            
    async def optimize_strategy_parameters(self, 
                                         strategy_id: str, 
                                         current_parameters: Dict[str, Any],
                                         performance_data: Dict[str, Any]) -> OptimizationResult:
        """Optimize strategy parameters using AI"""
        try:
            logger.info(f"🚀 Optimizing strategy parameters: {strategy_id}")
            
            # Check if strategy has enough trades for optimization
            if performance_data.get('total_trades', 0) < self.min_trades_for_optimization:
                logger.info(f"Strategy {strategy_id} needs more trades for optimization")
                return OptimizationResult(
                    strategy_id=strategy_id,
                    original_parameters=current_parameters,
                    optimized_parameters=current_parameters,
                    expected_improvement=0.0,
                    confidence_score=0.0,
                    reasoning="Insufficient trade history for optimization",
                    timestamp=datetime.now().timestamp()
                )
            
            # Get performance analysis first
            analysis = await self.analyze_strategy_performance(strategy_id, performance_data)
            
            # Create optimization prompt
            optimization_prompt = self._create_optimization_prompt(
                strategy_id, current_parameters, performance_data, analysis
            )
            
            # Get AI optimization suggestions
            optimization_response = await self.ollama_hub.generate_response(
                model=self.optimization_model,
                prompt=optimization_prompt,
                max_tokens=1500
            )
            
            # Parse optimization response
            optimization_result = await self._parse_optimization_response(
                optimization_response, strategy_id, current_parameters
            )
            
            # Validate optimization with reasoning model
            if optimization_result.confidence_score >= self.confidence_threshold:
                validation_result = await self._validate_optimization(optimization_result)
                optimization_result.confidence_score *= validation_result
            
            # Store optimization result
            self.optimization_history.append(optimization_result)
            
            logger.info(f"✅ Strategy optimization completed: {strategy_id} "
                       f"(confidence: {optimization_result.confidence_score:.1%})")
            
            return optimization_result
            
        except Exception as e:
            logger.error(f"Error optimizing strategy parameters: {e}")
            return OptimizationResult(
                strategy_id=strategy_id,
                original_parameters=current_parameters,
                optimized_parameters=current_parameters,
                expected_improvement=0.0,
                confidence_score=0.0,
                reasoning=f"Optimization error: {str(e)}",
                timestamp=datetime.now().timestamp()
            )
            
    async def get_strategy_recommendations(self, strategy_id: str) -> List[str]:
        """Get AI-powered strategy recommendations"""
        try:
            analysis = self.performance_analyses.get(strategy_id)
            if not analysis:
                return ["Insufficient data for recommendations"]
                
            # Create recommendations prompt
            recommendations_prompt = f"""
            Based on the following strategy analysis, provide specific actionable recommendations:
            
            Strategy: {strategy_id}
            Win Rate: {analysis.win_rate:.1%}
            Average Return: {analysis.avg_return:.2%}
            Max Drawdown: {analysis.max_drawdown:.2%}
            Sharpe Ratio: {analysis.sharpe_ratio:.2f}
            
            Strengths: {', '.join(analysis.strengths)}
            Weaknesses: {', '.join(analysis.weaknesses)}
            
            Provide 3-5 specific, actionable recommendations for improving this strategy.
            Format as a JSON list of strings.
            """
            
            response = await self.ollama_hub.generate_response(
                model=self.reasoning_model,
                prompt=recommendations_prompt,
                max_tokens=800
            )
            
            # Parse recommendations
            try:
                recommendations = json.loads(response)
                if isinstance(recommendations, list):
                    return recommendations[:5]  # Limit to 5 recommendations
            except json.JSONDecodeError:
                # Fallback: split by lines
                recommendations = [line.strip() for line in response.split('\n') if line.strip()]
                return recommendations[:5]
                
            return analysis.recommendations
            
        except Exception as e:
            logger.error(f"Error getting strategy recommendations: {e}")
            return ["Unable to generate recommendations at this time"]
            
    def _create_performance_analysis_prompt(self, strategy_id: str, performance_data: Dict[str, Any]) -> str:
        """Create prompt for performance analysis"""
        return f"""
        Analyze the following trading strategy performance data:
        
        Strategy ID: {strategy_id}
        Total Trades: {performance_data.get('total_trades', 0)}
        Win Rate: {performance_data.get('win_rate', 0.0):.1%}
        Average Return: {performance_data.get('avg_return', 0.0):.2%}
        Maximum Drawdown: {performance_data.get('max_drawdown', 0.0):.2%}
        Sharpe Ratio: {performance_data.get('sharpe_ratio', 0.0):.2f}
        Volatility: {performance_data.get('volatility', 0.0):.2%}
        
        Provide a comprehensive analysis including:
        1. Key strengths of the strategy
        2. Main weaknesses or concerns
        3. Specific recommendations for improvement
        
        Format your response as JSON with keys: strengths, weaknesses, recommendations
        Each should be an array of strings.
        """
        
    def _create_optimization_prompt(self, 
                                  strategy_id: str, 
                                  current_parameters: Dict[str, Any],
                                  performance_data: Dict[str, Any],
                                  analysis: PerformanceAnalysis) -> str:
        """Create prompt for parameter optimization"""
        return f"""
        Optimize the parameters for this trading strategy:
        
        Strategy: {strategy_id}
        Current Parameters: {json.dumps(current_parameters, indent=2)}
        
        Performance Metrics:
        - Win Rate: {analysis.win_rate:.1%}
        - Average Return: {analysis.avg_return:.2%}
        - Max Drawdown: {analysis.max_drawdown:.2%}
        - Sharpe Ratio: {analysis.sharpe_ratio:.2f}
        
        Key Issues: {', '.join(analysis.weaknesses)}
        
        Suggest optimized parameters that could improve performance while managing risk.
        Consider:
        1. Risk-adjusted returns
        2. Drawdown reduction
        3. Consistency of performance
        
        Provide your response as JSON with:
        - optimized_parameters: dict of parameter names and values
        - expected_improvement: float (0.0 to 1.0)
        - confidence: float (0.0 to 1.0)
        - reasoning: string explaining the optimization logic
        """
        
    async def _parse_performance_analysis(self, 
                                        response: str, 
                                        strategy_id: str, 
                                        performance_data: Dict[str, Any]) -> PerformanceAnalysis:
        """Parse AI performance analysis response"""
        try:
            # Try to parse as JSON
            analysis_data = json.loads(response)
            
            return PerformanceAnalysis(
                strategy_id=strategy_id,
                total_trades=performance_data.get('total_trades', 0),
                win_rate=performance_data.get('win_rate', 0.0),
                avg_return=performance_data.get('avg_return', 0.0),
                max_drawdown=performance_data.get('max_drawdown', 0.0),
                sharpe_ratio=performance_data.get('sharpe_ratio', 0.0),
                volatility=performance_data.get('volatility', 0.0),
                strengths=analysis_data.get('strengths', []),
                weaknesses=analysis_data.get('weaknesses', []),
                recommendations=analysis_data.get('recommendations', [])
            )
            
        except json.JSONDecodeError:
            # Fallback parsing
            logger.warning("Could not parse AI analysis as JSON, using fallback")
            return PerformanceAnalysis(
                strategy_id=strategy_id,
                total_trades=performance_data.get('total_trades', 0),
                win_rate=performance_data.get('win_rate', 0.0),
                avg_return=performance_data.get('avg_return', 0.0),
                max_drawdown=performance_data.get('max_drawdown', 0.0),
                sharpe_ratio=performance_data.get('sharpe_ratio', 0.0),
                volatility=performance_data.get('volatility', 0.0),
                strengths=["AI analysis available"],
                weaknesses=["Detailed analysis pending"],
                recommendations=["Continue monitoring performance"]
            )
            
    async def _parse_optimization_response(self, 
                                         response: str, 
                                         strategy_id: str, 
                                         original_parameters: Dict[str, Any]) -> OptimizationResult:
        """Parse AI optimization response"""
        try:
            # Try to parse as JSON
            optimization_data = json.loads(response)
            
            return OptimizationResult(
                strategy_id=strategy_id,
                original_parameters=original_parameters,
                optimized_parameters=optimization_data.get('optimized_parameters', original_parameters),
                expected_improvement=optimization_data.get('expected_improvement', 0.0),
                confidence_score=optimization_data.get('confidence', 0.5),
                reasoning=optimization_data.get('reasoning', 'AI optimization completed'),
                timestamp=datetime.now().timestamp()
            )
            
        except json.JSONDecodeError:
            # Fallback
            logger.warning("Could not parse optimization response as JSON")
            return OptimizationResult(
                strategy_id=strategy_id,
                original_parameters=original_parameters,
                optimized_parameters=original_parameters,
                expected_improvement=0.0,
                confidence_score=0.3,
                reasoning="Optimization parsing failed, no changes recommended",
                timestamp=datetime.now().timestamp()
            )
            
    async def _validate_optimization(self, optimization_result: OptimizationResult) -> float:
        """Validate optimization using reasoning model"""
        try:
            validation_prompt = f"""
            Validate this strategy optimization:
            
            Original Parameters: {json.dumps(optimization_result.original_parameters)}
            Optimized Parameters: {json.dumps(optimization_result.optimized_parameters)}
            Expected Improvement: {optimization_result.expected_improvement:.1%}
            Reasoning: {optimization_result.reasoning}
            
            Rate the quality of this optimization on a scale of 0.0 to 1.0:
            - 1.0: Excellent optimization with strong rationale
            - 0.8: Good optimization with solid reasoning
            - 0.6: Reasonable optimization with some concerns
            - 0.4: Questionable optimization
            - 0.2: Poor optimization
            - 0.0: Invalid or harmful optimization
            
            Respond with just the numerical score (0.0 to 1.0).
            """
            
            response = await self.ollama_hub.generate_response(
                model=self.reasoning_model,
                prompt=validation_prompt,
                max_tokens=50
            )
            
            # Extract numerical score
            try:
                score = float(response.strip())
                return max(0.0, min(1.0, score))  # Clamp between 0 and 1
            except ValueError:
                logger.warning("Could not parse validation score")
                return 0.7  # Default moderate confidence
                
        except Exception as e:
            logger.error(f"Error validating optimization: {e}")
            return 0.5  # Default confidence
            
    async def _optimization_loop(self):
        """Background optimization loop"""
        while self.running:
            try:
                await asyncio.sleep(self.optimization_interval)
                
                # Optimization logic would go here
                # For now, just log that we're running
                logger.debug("AI optimization loop running...")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in optimization loop: {e}")
                
    async def _analysis_loop(self):
        """Background analysis loop"""
        while self.running:
            try:
                await asyncio.sleep(1800)  # Run every 30 minutes
                
                # Analysis logic would go here
                logger.debug("AI analysis loop running...")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in analysis loop: {e}")
                
    async def get_optimizer_status(self) -> Dict[str, Any]:
        """Get AI optimizer status"""
        return {
            'running': self.running,
            'available_models': {
                'analysis_model': self.analysis_model,
                'optimization_model': self.optimization_model,
                'reasoning_model': self.reasoning_model
            },
            'optimization_history': len(self.optimization_history),
            'performance_analyses': len(self.performance_analyses),
            'configuration': {
                'optimization_interval': self.optimization_interval,
                'min_trades_for_optimization': self.min_trades_for_optimization,
                'confidence_threshold': self.confidence_threshold
            }
        }

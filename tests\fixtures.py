"""
Test Fixtures - Reusable test data and setup
"""

import asyncio
import logging
import pytest
import pytest_asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch

from .mock_data import MockDataGenerator

logger = logging.getLogger(__name__)


class TestFixtures:
    """
    Centralized test fixtures and setup utilities.
    
    Features:
    - Reusable test data
    - Mock object management
    - Test environment setup
    - Database fixtures
    - API client fixtures
    - Configuration fixtures
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.mock_generator = MockDataGenerator(config)
        
        # Fixture storage
        self.fixtures: Dict[str, Any] = {}
        self.mocks: Dict[str, Mock] = {}
        
        # State
        self.initialized = False
    
    async def initialize(self) -> bool:
        """Initialize test fixtures"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Test Fixtures...")
            
            # Initialize mock data generator
            await self.mock_generator.initialize()
            
            # Setup default fixtures
            await self._setup_default_fixtures()
            
            self.initialized = True
            logger.info("✓ Test Fixtures initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Test Fixtures: {e}")
            return False
    
    async def get_fixture(self, name: str) -> Any:
        """Get a test fixture by name"""
        return self.fixtures.get(name)
    
    async def set_fixture(self, name: str, value: Any):
        """Set a test fixture"""
        self.fixtures[name] = value
    
    async def get_mock(self, name: str) -> Optional[Mock]:
        """Get a mock object by name"""
        return self.mocks.get(name)
    
    # Private methods
    
    async def _setup_default_fixtures(self):
        """Setup default test fixtures"""
        try:
            # Configuration fixtures
            await self._setup_config_fixtures()
            
            # Data fixtures
            await self._setup_data_fixtures()
            
            # Mock fixtures
            await self._setup_mock_fixtures()
            
            logger.debug("Default fixtures setup completed")
            
        except Exception as e:
            logger.error(f"Error setting up default fixtures: {e}")
            raise
    
    async def _setup_config_fixtures(self):
        """Setup configuration fixtures"""
        try:
            # Test configuration
            test_config = {
                'testing': True,
                'database': {
                    'host': 'localhost',
                    'port': 5432,
                    'database': 'test_trading_agents',
                    'username': 'test_user',
                    'password': 'test_password'
                },
                'api': {
                    'host': '0.0.0.0',
                    'port': 8001,
                    'debug': True
                },
                'agents': {
                    'max_agents': 10,
                    'heartbeat_interval': 5
                },
                'risk': {
                    'max_position_size': 0.1,
                    'max_daily_loss': 0.05
                }
            }
            
            self.fixtures['test_config'] = test_config
            
            # Production-like configuration
            prod_config = {
                'testing': False,
                'database': {
                    'host': 'prod-db.example.com',
                    'port': 5432,
                    'database': 'trading_agents',
                    'username': 'prod_user',
                    'password': 'prod_password'
                },
                'api': {
                    'host': '0.0.0.0',
                    'port': 8000,
                    'debug': False
                }
            }
            
            self.fixtures['prod_config'] = prod_config
            
        except Exception as e:
            logger.error(f"Error setting up config fixtures: {e}")
            raise
    
    async def _setup_data_fixtures(self):
        """Setup data fixtures"""
        try:
            # Sample market data
            market_data = await self.mock_generator.generate_market_data(
                'AAPL',
                start_time=datetime.now() - timedelta(hours=1),
                end_time=datetime.now(),
                interval='1m'
            )
            self.fixtures['sample_market_data'] = market_data
            
            # Sample trade data
            trade_data = await self.mock_generator.generate_trade_data(50)
            self.fixtures['sample_trade_data'] = trade_data
            
            # Sample portfolio data
            portfolio_data = await self.mock_generator.generate_portfolio_data(3)
            self.fixtures['sample_portfolio_data'] = portfolio_data
            
            # Sample agent messages
            agent_messages = await self.mock_generator.generate_agent_messages(20)
            self.fixtures['sample_agent_messages'] = agent_messages
            
            # Performance metrics
            performance_metrics = await self.mock_generator.generate_performance_metrics()
            self.fixtures['sample_performance_metrics'] = performance_metrics
            
        except Exception as e:
            logger.error(f"Error setting up data fixtures: {e}")
            raise
    
    async def _setup_mock_fixtures(self):
        """Setup mock object fixtures"""
        try:
            # Mock trading system
            mock_trading_system = AsyncMock()
            mock_trading_system.running = True
            mock_trading_system.initialized = True
            mock_trading_system.get_system_status.return_value = {
                'status': 'healthy',
                'uptime_seconds': 3600,
                'version': '1.0.0'
            }
            self.mocks['trading_system'] = mock_trading_system
            
            # Mock database
            mock_database = AsyncMock()
            mock_database.connected = True
            mock_database.execute.return_value = {'rows_affected': 1}
            mock_database.fetch.return_value = []
            self.mocks['database'] = mock_database
            
            # Mock API client
            mock_api_client = AsyncMock()
            mock_api_client.get.return_value = {'status': 'success', 'data': {}}
            mock_api_client.post.return_value = {'status': 'success', 'data': {}}
            self.mocks['api_client'] = mock_api_client
            
            # Mock market data provider
            mock_market_data = AsyncMock()
            mock_market_data.get_price.return_value = 150.0
            mock_market_data.get_quotes.return_value = {
                'AAPL': {'bid': 149.95, 'ask': 150.05}
            }
            self.mocks['market_data_provider'] = mock_market_data
            
        except Exception as e:
            logger.error(f"Error setting up mock fixtures: {e}")
            raise


# Pytest fixtures

@pytest.fixture
async def test_config():
    """Test configuration fixture"""
    return {
        'testing': True,
        'database': {
            'host': 'localhost',
            'port': 5432,
            'database': 'test_trading_agents'
        },
        'api': {
            'host': '0.0.0.0',
            'port': 8001
        }
    }


@pytest.fixture
async def mock_trading_system():
    """Mock trading system fixture"""
    mock_system = AsyncMock()
    mock_system.running = True
    mock_system.initialized = True
    mock_system.get_system_status.return_value = {
        'status': 'healthy',
        'uptime_seconds': 3600,
        'version': '1.0.0'
    }
    return mock_system


@pytest.fixture
async def mock_database():
    """Mock database fixture"""
    mock_db = AsyncMock()
    mock_db.connected = True
    mock_db.execute.return_value = {'rows_affected': 1}
    mock_db.fetch.return_value = []
    return mock_db


@pytest.fixture
async def sample_market_data():
    """Sample market data fixture"""
    generator = MockDataGenerator({})
    await generator.initialize()
    
    return await generator.generate_market_data(
        'AAPL',
        start_time=datetime.now() - timedelta(hours=1),
        end_time=datetime.now(),
        interval='1m'
    )


@pytest.fixture
async def sample_trade_data():
    """Sample trade data fixture"""
    generator = MockDataGenerator({})
    await generator.initialize()
    
    return await generator.generate_trade_data(10)


@pytest.fixture
async def sample_portfolio_data():
    """Sample portfolio data fixture"""
    generator = MockDataGenerator({})
    await generator.initialize()
    
    portfolios = await generator.generate_portfolio_data(1)
    return portfolios[0] if portfolios else None


@pytest.fixture
async def performance_metrics():
    """Performance metrics fixture"""
    generator = MockDataGenerator({})
    await generator.initialize()
    
    return await generator.generate_performance_metrics()


@pytest.fixture
async def error_scenarios():
    """Error scenarios fixture"""
    generator = MockDataGenerator({})
    await generator.initialize()
    
    return await generator.generate_error_scenarios()


@pytest.fixture
def mock_ollama_client():
    """Mock Ollama client fixture"""
    mock_client = Mock()
    mock_client.generate.return_value = {
        'response': 'This is a mock response from Ollama',
        'done': True,
        'context': [],
        'total_duration': 1000000000,
        'load_duration': 100000000,
        'prompt_eval_count': 10,
        'prompt_eval_duration': 200000000,
        'eval_count': 20,
        'eval_duration': 700000000
    }
    return mock_client


@pytest.fixture
async def mock_agent_manager():
    """Mock agent manager fixture"""
    mock_manager = AsyncMock()
    mock_manager.running = True
    mock_manager.initialized = True
    mock_manager.get_all_agents.return_value = [
        {
            'agent_id': 'agent_001',
            'name': 'Test Agent',
            'type': 'analyst',
            'status': 'active',
            'capabilities': ['market_analysis', 'risk_assessment'],
            'performance': {'accuracy': 0.85, 'response_time': 1.2},
            'last_activity': datetime.now()
        }
    ]
    return mock_manager


@pytest.fixture
async def mock_strategy_manager():
    """Mock strategy manager fixture"""
    mock_manager = AsyncMock()
    mock_manager.running = True
    mock_manager.initialized = True
    mock_manager.get_all_strategies.return_value = [
        {
            'strategy_id': 'strategy_001',
            'name': 'Test Strategy',
            'type': 'momentum',
            'status': 'active',
            'parameters': {'lookback': 20, 'threshold': 0.02},
            'performance': {'return': 0.15, 'sharpe': 1.2},
            'created_at': datetime.now() - timedelta(days=30)
        }
    ]
    return mock_manager


@pytest.fixture
async def mock_portfolio_manager():
    """Mock portfolio manager fixture"""
    mock_manager = AsyncMock()
    mock_manager.running = True
    mock_manager.initialized = True
    mock_manager.get_all_portfolios.return_value = [
        {
            'portfolio_id': 'portfolio_001',
            'name': 'Test Portfolio',
            'total_value': 100000.0,
            'cash': 10000.0,
            'positions': {
                'AAPL': {
                    'quantity': 100,
                    'market_value': 15000.0,
                    'unrealized_pnl': 500.0
                }
            },
            'performance': {
                'total_return': 0.10,
                'daily_return': 0.001,
                'volatility': 0.15
            }
        }
    ]
    return mock_manager


@pytest.fixture
async def mock_risk_manager():
    """Mock risk manager fixture"""
    mock_manager = AsyncMock()
    mock_manager.running = True
    mock_manager.initialized = True
    mock_manager.assess_risk.return_value = {
        'risk_level': 'medium',
        'var_95': 5000.0,
        'max_drawdown': 0.15,
        'position_concentration': 0.25
    }
    return mock_manager


@pytest.fixture
async def mock_execution_engine():
    """Mock execution engine fixture"""
    mock_engine = AsyncMock()
    mock_engine.running = True
    mock_engine.initialized = True
    mock_engine.execute_order.return_value = {
        'order_id': 'order_001',
        'status': 'filled',
        'fill_price': 150.0,
        'fill_quantity': 100,
        'timestamp': datetime.now()
    }
    return mock_engine


# Utility functions for fixtures

async def create_test_environment():
    """Create a complete test environment"""
    fixtures = TestFixtures({})
    await fixtures.initialize()
    return fixtures


async def cleanup_test_environment(fixtures: TestFixtures):
    """Clean up test environment"""
    # Clear fixtures
    fixtures.fixtures.clear()
    fixtures.mocks.clear()


def assert_mock_called_with(mock_obj: Mock, method_name: str, *args, **kwargs):
    """Assert that a mock method was called with specific arguments"""
    method = getattr(mock_obj, method_name)
    method.assert_called_with(*args, **kwargs)


def assert_mock_not_called(mock_obj: Mock, method_name: str):
    """Assert that a mock method was not called"""
    method = getattr(mock_obj, method_name)
    method.assert_not_called()

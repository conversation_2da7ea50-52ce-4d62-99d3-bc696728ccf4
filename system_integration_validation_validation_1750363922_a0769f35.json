{"validation_id": "validation_**********_a0769f35", "validation_level": "production", "overall_status": "failed", "overall_score": 0.7777775467530157, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "partial", "integration_score": 0.7441282188627546, "error_count": 0, "warnings": ["Integration issues in system_coordinator"], "dependencies_met": "True"}, {"component_name": "team_manager", "component_type": "core", "status": "partial", "integration_score": 0.784899872892125, "error_count": 0, "warnings": ["Functionality concerns in team_manager"], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "partial", "integration_score": 0.8135198751472716, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "analytics_engine", "component_type": "core", "status": "partial", "integration_score": 0.8236172446158221, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "partial", "integration_score": 0.7672485653718024, "error_count": 0, "warnings": ["Functionality concerns in ollama_hub"], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "partial", "integration_score": 0.7943390486358881, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "partial", "integration_score": 0.8041586767517135, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "risk_manager", "component_type": "core", "status": "partial", "integration_score": 0.7465820176232234, "error_count": 0, "warnings": ["Functionality concerns in risk_manager"], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "partial", "integration_score": 0.7736365421095606, "error_count": 0, "warnings": ["Integration issues in strategy_manager"], "dependencies_met": "True"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "partial", "integration_score": 0.8095700659956767, "error_count": 0, "warnings": ["Integration issues in competitive_framework"], "dependencies_met": "True"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "partial", "integration_score": 0.8236810199381639, "error_count": 0, "warnings": ["Integration issues in tournament_framework"], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.8199895339803931, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "partial", "integration_score": 0.8220400436840135, "error_count": 0, "warnings": ["Functionality concerns in regime_adaptation_system"], "dependencies_met": "False"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "partial", "integration_score": 0.7636302554200212, "error_count": 0, "warnings": ["Functionality concerns in performance_optimizer", "Integration issues in performance_optimizer"], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "partial", "integration_score": 0.8024679127812084, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "partial", "integration_score": 0.7822356332786304, "error_count": 0, "warnings": ["Integration issues in ai_coordinator"], "dependencies_met": "True"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "partial", "integration_score": 0.7776293772176016, "error_count": 0, "warnings": ["Functionality concerns in configuration_manager"], "dependencies_met": "False"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "partial", "integration_score": 0.8459736688357141, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "partial", "integration_score": 0.8493068554838004, "error_count": 0, "warnings": ["Integration issues in paper_trading_engine"], "dependencies_met": "True"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "partial", "integration_score": 0.8175841076447613, "error_count": 0, "warnings": [], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.9022725319825208, "data_manager": 0.6035342332188348, "analytics_engine": 0.7905144371322035, "ollama_hub": 0.7802644720763966, "execution_engine": 0.6281591953189959, "portfolio_manager": 0.8355636994504749, "risk_manager": 0.6710287108259307, "strategy_manager": 0.686762158842857, "competitive_framework": 0.6773555777371371, "tournament_framework": 0.699847210850012, "self_improvement_engine": 0.7003683376874792, "regime_adaptation_system": 0.7650398869610151, "performance_optimizer": 0.6848223149970787, "advanced_trading_engine": 0.7650590995323997, "ai_coordinator": 0.784117827360221, "configuration_manager": 0.8011864823252809, "mock_data_providers": 0.7013737221000954, "paper_trading_engine": 0.6461139548414014, "logging_audit_system": 0.6256960226868159}, "team_manager": {"system_coordinator": 0.6227651808090484, "team_manager": 1.0, "data_manager": 0.9953643131289045, "analytics_engine": 0.6355105265610191, "ollama_hub": 0.8758234037510789, "execution_engine": 0.6247903672450947, "portfolio_manager": 0.8653371277366928, "risk_manager": 0.8079334065214914, "strategy_manager": 0.6800673420077623, "competitive_framework": 0.667850959032663, "tournament_framework": 0.6039392081302165, "self_improvement_engine": 0.6130079720644497, "regime_adaptation_system": 0.740009168350058, "performance_optimizer": 0.6605937741131451, "advanced_trading_engine": 0.6648418583216489, "ai_coordinator": 0.8983270282428096, "configuration_manager": 0.8723743335960535, "mock_data_providers": 0.7354244979074851, "paper_trading_engine": 0.891252872619059, "logging_audit_system": 0.8755040895786976}, "data_manager": {"system_coordinator": 0.7004929648547844, "team_manager": 0.7716174197741917, "data_manager": 1.0, "analytics_engine": 0.8275204515228977, "ollama_hub": 0.8023195212391483, "execution_engine": 0.8365966655265662, "portfolio_manager": 0.6869783234516144, "risk_manager": 0.6270333931814146, "strategy_manager": 0.7398837778719503, "competitive_framework": 0.6481527545950592, "tournament_framework": 0.7632439847600564, "self_improvement_engine": 0.7109523865929956, "regime_adaptation_system": 0.8578198060329701, "performance_optimizer": 0.8872040434987674, "advanced_trading_engine": 0.7621634140154918, "ai_coordinator": 0.7099255880137978, "configuration_manager": 0.8390694424598132, "mock_data_providers": 0.7326544650008886, "paper_trading_engine": 0.8341495568030194, "logging_audit_system": 0.8516899373111295}, "analytics_engine": {"system_coordinator": 0.8916922548098485, "team_manager": 0.726581396045588, "data_manager": 0.646266148474021, "analytics_engine": 1.0, "ollama_hub": 0.704273506020434, "execution_engine": 0.6424330946643119, "portfolio_manager": 0.6042302016106736, "risk_manager": 0.6360255820733799, "strategy_manager": 0.8838688377959897, "competitive_framework": 0.7815928917018979, "tournament_framework": 0.6965360600752568, "self_improvement_engine": 0.8978908758030328, "regime_adaptation_system": 0.6492586174563608, "performance_optimizer": 0.6394639816498422, "advanced_trading_engine": 0.6350536666289408, "ai_coordinator": 0.7402698534845901, "configuration_manager": 0.8945946384603998, "mock_data_providers": 0.7808060866062292, "paper_trading_engine": 0.8312729218689202, "logging_audit_system": 0.6135051638193966}, "ollama_hub": {"system_coordinator": 0.786947158537197, "team_manager": 0.7304141572321967, "data_manager": 0.7574651332624103, "analytics_engine": 0.8137421900572416, "ollama_hub": 1.0, "execution_engine": 0.7722455116679366, "portfolio_manager": 0.6568683159045339, "risk_manager": 0.617688631296659, "strategy_manager": 0.7758785951471046, "competitive_framework": 0.6424563034716004, "tournament_framework": 0.6584123204189107, "self_improvement_engine": 0.7263865094710693, "regime_adaptation_system": 0.752209143637419, "performance_optimizer": 0.7242158002689982, "advanced_trading_engine": 0.7809085601675809, "ai_coordinator": 0.6527713484994239, "configuration_manager": 0.6069954463325673, "mock_data_providers": 0.6731864942385771, "paper_trading_engine": 0.7170463153361176, "logging_audit_system": 0.6488825328340307}, "execution_engine": {"system_coordinator": 0.8071745871904179, "team_manager": 0.6210037692912493, "data_manager": 0.8217907364156501, "analytics_engine": 0.6920847663890516, "ollama_hub": 0.676748511408709, "execution_engine": 1.0, "portfolio_manager": 0.9805295351777047, "risk_manager": 0.7250946745821883, "strategy_manager": 0.6142100724340532, "competitive_framework": 0.7123826635768242, "tournament_framework": 0.7217667889850323, "self_improvement_engine": 0.8209725508700764, "regime_adaptation_system": 0.8745157617869419, "performance_optimizer": 0.8833408047317803, "advanced_trading_engine": 0.8444389648401515, "ai_coordinator": 0.8452316812940804, "configuration_manager": 0.8652446347320811, "mock_data_providers": 0.6014236476077315, "paper_trading_engine": 0.6151670932635606, "logging_audit_system": 0.723179580510855}, "portfolio_manager": {"system_coordinator": 0.8525855605359385, "team_manager": 0.707857074907058, "data_manager": 0.7837863152744478, "analytics_engine": 0.6150302053875732, "ollama_hub": 0.8217104342031735, "execution_engine": 0.882330763937557, "portfolio_manager": 1.0, "risk_manager": 0.8129865235772785, "strategy_manager": 0.739479695890366, "competitive_framework": 0.8409793248646198, "tournament_framework": 0.7692746352258966, "self_improvement_engine": 0.643492791760138, "regime_adaptation_system": 0.659335628534532, "performance_optimizer": 0.6961494078864964, "advanced_trading_engine": 0.7264959626311525, "ai_coordinator": 0.7438790433812686, "configuration_manager": 0.7174529342478781, "mock_data_providers": 0.8388900603944014, "paper_trading_engine": 0.8779389697822632, "logging_audit_system": 0.7146990412105221}, "risk_manager": {"system_coordinator": 0.8338503176069991, "team_manager": 0.7654391276981829, "data_manager": 0.6091902947304024, "analytics_engine": 0.6360738399216455, "ollama_hub": 0.8363428577342782, "execution_engine": 0.7160921313701611, "portfolio_manager": 0.8632620594331615, "risk_manager": 1.0, "strategy_manager": 0.8373554434099544, "competitive_framework": 0.8097124010112651, "tournament_framework": 0.6888475051814942, "self_improvement_engine": 0.6147585257920332, "regime_adaptation_system": 0.8409312772158426, "performance_optimizer": 0.7096629118848051, "advanced_trading_engine": 0.8694488267183553, "ai_coordinator": 0.8714579905155253, "configuration_manager": 0.689756172527773, "mock_data_providers": 0.7000714446546518, "paper_trading_engine": 0.7040933566816806, "logging_audit_system": 0.7409012090028445}, "strategy_manager": {"system_coordinator": 0.6127576637812153, "team_manager": 0.8147911456410829, "data_manager": 0.8132273474033206, "analytics_engine": 0.8596308911040882, "ollama_hub": 0.8453455945962242, "execution_engine": 0.7052966017532372, "portfolio_manager": 0.7918695516083901, "risk_manager": 0.6605537920003465, "strategy_manager": 1.0, "competitive_framework": 0.8867006412606482, "tournament_framework": 0.7998004811121049, "self_improvement_engine": 0.8279671462660825, "regime_adaptation_system": 0.789136090600708, "performance_optimizer": 0.8516574119826217, "advanced_trading_engine": 0.6222387837130868, "ai_coordinator": 0.6622050688438283, "configuration_manager": 0.8494639160072635, "mock_data_providers": 0.7749093562853802, "paper_trading_engine": 0.6465703094371258, "logging_audit_system": 0.6539678735554304}, "competitive_framework": {"system_coordinator": 0.7430027548044815, "team_manager": 0.7298661457973908, "data_manager": 0.6598913191212448, "analytics_engine": 0.7800619111924926, "ollama_hub": 0.8362454781828881, "execution_engine": 0.8779864691646997, "portfolio_manager": 0.6017820048365324, "risk_manager": 0.7291575537344845, "strategy_manager": 0.635178247782279, "competitive_framework": 1.0, "tournament_framework": 0.7949642599628219, "self_improvement_engine": 0.7398235899004177, "regime_adaptation_system": 0.8221979304725561, "performance_optimizer": 0.7606478827576897, "advanced_trading_engine": 0.6569304421163065, "ai_coordinator": 0.605256406647003, "configuration_manager": 0.6102011677891906, "mock_data_providers": 0.7618385726222183, "paper_trading_engine": 0.6699162387099996, "logging_audit_system": 0.8771362135881385}, "tournament_framework": {"system_coordinator": 0.6307621607696435, "team_manager": 0.7118071043392289, "data_manager": 0.874346179368714, "analytics_engine": 0.7481391870615995, "ollama_hub": 0.8478813756753882, "execution_engine": 0.7660551852329471, "portfolio_manager": 0.8703102464458303, "risk_manager": 0.8652445538843164, "strategy_manager": 0.7172698721752888, "competitive_framework": 0.6857649178146772, "tournament_framework": 1.0, "self_improvement_engine": 0.6090322100283343, "regime_adaptation_system": 0.84276275114927, "performance_optimizer": 0.6383534024084401, "advanced_trading_engine": 0.807093263674149, "ai_coordinator": 0.6238463699703001, "configuration_manager": 0.6054706887352731, "mock_data_providers": 0.8480514064043148, "paper_trading_engine": 0.8211280239579494, "logging_audit_system": 0.6854872167527065}, "self_improvement_engine": {"system_coordinator": 0.7058922358718274, "team_manager": 0.6316739567972993, "data_manager": 0.794706654319012, "analytics_engine": 0.8851012991288704, "ollama_hub": 0.8677368284726743, "execution_engine": 0.7045978739118929, "portfolio_manager": 0.8191174169976774, "risk_manager": 0.8536064788288442, "strategy_manager": 0.7937707761646613, "competitive_framework": 0.8048111930354299, "tournament_framework": 0.6710446960044129, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.6294104460851486, "performance_optimizer": 0.6221842432669421, "advanced_trading_engine": 0.7704440329499678, "ai_coordinator": 0.845141051975031, "configuration_manager": 0.80587973184582, "mock_data_providers": 0.8374003779830688, "paper_trading_engine": 0.6089554977444631, "logging_audit_system": 0.6281138484056094}, "regime_adaptation_system": {"system_coordinator": 0.7377165856081631, "team_manager": 0.6791632233824277, "data_manager": 0.8596456982717167, "analytics_engine": 0.6593864434321501, "ollama_hub": 0.7113790264943949, "execution_engine": 0.8036402951347176, "portfolio_manager": 0.8051200438134408, "risk_manager": 0.6916366987035706, "strategy_manager": 0.6533194291399578, "competitive_framework": 0.7573008484087693, "tournament_framework": 0.6932760718586986, "self_improvement_engine": 0.8771816154049199, "regime_adaptation_system": 1.0, "performance_optimizer": 0.8742499494887372, "advanced_trading_engine": 0.8370525896052099, "ai_coordinator": 0.7707667598286114, "configuration_manager": 0.7607443388898185, "mock_data_providers": 0.650727915582726, "paper_trading_engine": 0.7069412935318511, "logging_audit_system": 0.6577135731721435}, "performance_optimizer": {"system_coordinator": 0.729037017806978, "team_manager": 0.8494309989758946, "data_manager": 0.6122794070230498, "analytics_engine": 0.8070898721696509, "ollama_hub": 0.8762701534597741, "execution_engine": 0.7767274333842834, "portfolio_manager": 0.7621935621708135, "risk_manager": 0.8311381915890643, "strategy_manager": 0.7036131778811222, "competitive_framework": 0.779141648738652, "tournament_framework": 0.8654265428808585, "self_improvement_engine": 0.8351193003856614, "regime_adaptation_system": 0.8131805718975911, "performance_optimizer": 1.0, "advanced_trading_engine": 0.6457347823351023, "ai_coordinator": 0.7285269975535643, "configuration_manager": 0.7957531721996058, "mock_data_providers": 0.7728636309491235, "paper_trading_engine": 0.797246845002352, "logging_audit_system": 0.8341671302622398}, "advanced_trading_engine": {"system_coordinator": 0.641630393725492, "team_manager": 0.7661615485294055, "data_manager": 0.7473294241079758, "analytics_engine": 0.8134597837534899, "ollama_hub": 0.820113359618351, "execution_engine": 0.6497213061835864, "portfolio_manager": 0.8810856789687064, "risk_manager": 0.8964251389830655, "strategy_manager": 0.8022922249987043, "competitive_framework": 0.8564680328530676, "tournament_framework": 0.7696754637800871, "self_improvement_engine": 0.6466506570827972, "regime_adaptation_system": 0.7962123739981168, "performance_optimizer": 0.8057150796217921, "advanced_trading_engine": 1.0, "ai_coordinator": 0.6436165939874604, "configuration_manager": 0.8856443618022984, "mock_data_providers": 0.8267241689490245, "paper_trading_engine": 0.8173693612883606, "logging_audit_system": 0.6697000078700154}, "ai_coordinator": {"system_coordinator": 0.7772523884920791, "team_manager": 0.6147725143907895, "data_manager": 0.7498661757137431, "analytics_engine": 0.759018359335703, "ollama_hub": 0.7493141756591288, "execution_engine": 0.6746602364104565, "portfolio_manager": 0.7654503333315592, "risk_manager": 0.7108949394584837, "strategy_manager": 0.6492971890366187, "competitive_framework": 0.7627830950723136, "tournament_framework": 0.7500804511378861, "self_improvement_engine": 0.6224928496541815, "regime_adaptation_system": 0.8765281072943488, "performance_optimizer": 0.6829160093546216, "advanced_trading_engine": 0.6979820881227732, "ai_coordinator": 1.0, "configuration_manager": 0.7081944017596954, "mock_data_providers": 0.7649206894525746, "paper_trading_engine": 0.643770755289876, "logging_audit_system": 0.7989823415679753}, "configuration_manager": {"system_coordinator": 0.8858032554109773, "team_manager": 0.6053068659176094, "data_manager": 0.7497042434579692, "analytics_engine": 0.625092878068218, "ollama_hub": 0.7437081057626289, "execution_engine": 0.7829139831400804, "portfolio_manager": 0.8936619350457555, "risk_manager": 0.8374098153442933, "strategy_manager": 0.7691256277601719, "competitive_framework": 0.7537902106255145, "tournament_framework": 0.6628282310109184, "self_improvement_engine": 0.673454735226138, "regime_adaptation_system": 0.7320222001209029, "performance_optimizer": 0.7431596509846203, "advanced_trading_engine": 0.8570005061831836, "ai_coordinator": 0.7933213933237282, "configuration_manager": 1.0, "mock_data_providers": 0.6990772039861849, "paper_trading_engine": 0.8646750109456397, "logging_audit_system": 0.8389578211310644}, "mock_data_providers": {"system_coordinator": 0.8645241577162694, "team_manager": 0.744130600622471, "data_manager": 0.7456648160584269, "analytics_engine": 0.7499403314894035, "ollama_hub": 0.724823966539932, "execution_engine": 0.7533841629120039, "portfolio_manager": 0.8633464257486636, "risk_manager": 0.8840855262306548, "strategy_manager": 0.8451693939789802, "competitive_framework": 0.6093350873348788, "tournament_framework": 0.849248179760704, "self_improvement_engine": 0.6701363437636193, "regime_adaptation_system": 0.6624950743202916, "performance_optimizer": 0.8505490255945376, "advanced_trading_engine": 0.812909154786941, "ai_coordinator": 0.7933457861634577, "configuration_manager": 0.7088568046561767, "mock_data_providers": 1.0, "paper_trading_engine": 0.6904930565675391, "logging_audit_system": 0.6550600524674023}, "paper_trading_engine": {"system_coordinator": 0.7693379199710667, "team_manager": 0.7423353307415378, "data_manager": 0.7626865287685178, "analytics_engine": 0.8133204430751322, "ollama_hub": 0.871615126794707, "execution_engine": 0.6396149228474485, "portfolio_manager": 0.651372976022513, "risk_manager": 0.8334398886771215, "strategy_manager": 0.8588816423396057, "competitive_framework": 0.6818398302895279, "tournament_framework": 0.8980456599188745, "self_improvement_engine": 0.7692827087182197, "regime_adaptation_system": 0.871643417013852, "performance_optimizer": 0.8788919633848535, "advanced_trading_engine": 0.8991358685748556, "ai_coordinator": 0.7757489615257654, "configuration_manager": 0.8859589286615339, "mock_data_providers": 0.8084269387492662, "paper_trading_engine": 1.0, "logging_audit_system": 0.8804221780735386}, "logging_audit_system": {"system_coordinator": 0.6545641935749893, "team_manager": 0.7172748425498274, "data_manager": 0.7158745526861892, "analytics_engine": 0.8533417148326615, "ollama_hub": 0.8858526848654369, "execution_engine": 0.6489274946681923, "portfolio_manager": 0.6541629126111228, "risk_manager": 0.8839767262443601, "strategy_manager": 0.7567434203094897, "competitive_framework": 0.8028514733910133, "tournament_framework": 0.6575234687781066, "self_improvement_engine": 0.8128803561432926, "regime_adaptation_system": 0.6160275210150766, "performance_optimizer": 0.8595916950522897, "advanced_trading_engine": 0.8741111991486235, "ai_coordinator": 0.7057392117315544, "configuration_manager": 0.6021826592968993, "mock_data_providers": 0.6898688497885, "paper_trading_engine": 0.6864318405928668, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.7393271253093225, "response_time": 0.8919419501439438, "throughput": 0.7421985328307364, "memory_usage": 0.7518083224572705, "cpu_usage": 0.8378797500364645, "concurrent_operations": 0.7405672232263805}, "critical_issues": ["Components with dependency issues: regime_adaptation_system, configuration_manager, mock_data_providers"], "recommendations": ["Improve 20 partially working components", "Improve overall system performance and stability", "Enhance component integration and communication", "Address all issues before production deployment"], "production_ready": "False", "timestamp": **********.8543627}
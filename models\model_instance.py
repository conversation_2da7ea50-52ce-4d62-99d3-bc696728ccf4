"""
Ollama Model Instance - Individual model instance management
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
import aiohttp
import json

logger = logging.getLogger(__name__)


class OllamaModelInstance:
    """
    Represents a single instance of an Ollama model configured for a specific agent role.
    Handles model communication, performance tracking, and lifecycle management.
    """
    
    def __init__(self, 
                 model_name: str,
                 role: str,
                 agent_name: str,
                 config: Dict[str, Any],
                 session: aiohttp.ClientSession,
                 base_url: str = "http://localhost:11434"):
        
        self.model_name = model_name
        self.role = role
        self.agent_name = agent_name
        self.config = config
        self.session = session
        self.base_url = base_url
        
        # Instance state
        self.active = False
        self.last_used = None
        self.total_requests = 0
        self.total_tokens = 0
        self.total_response_time = 0.0
        self.error_count = 0
        
        # Performance metrics
        self.metrics = {
            'avg_response_time': 0.0,
            'tokens_per_second': 0.0,
            'success_rate': 1.0,
            'last_error': None
        }
        
    async def generate(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate response from the model"""
        start_time = time.time()
        
        try:
            # Prepare request payload
            payload = {
                'model': self.model_name,
                'prompt': prompt,
                'stream': False,
                **self.config,
                **kwargs  # Allow override of config parameters
            }
            
            # Make request to Ollama
            async with self.session.post(
                f"{self.base_url}/api/generate",
                json=payload
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    
                    # Update metrics
                    response_time = time.time() - start_time
                    self._update_metrics(response_time, result)
                    
                    return {
                        'success': True,
                        'response': result.get('response', ''),
                        'model': self.model_name,
                        'role': self.role,
                        'agent': self.agent_name,
                        'response_time': response_time,
                        'tokens': result.get('eval_count', 0),
                        'context': result.get('context', [])
                    }
                else:
                    error_msg = f"HTTP {response.status}: {await response.text()}"
                    self._update_error_metrics(error_msg)
                    
                    return {
                        'success': False,
                        'error': error_msg,
                        'model': self.model_name,
                        'role': self.role,
                        'agent': self.agent_name,
                        'response_time': time.time() - start_time
                    }
                    
        except Exception as e:
            error_msg = f"Request failed: {str(e)}"
            self._update_error_metrics(error_msg)
            
            return {
                'success': False,
                'error': error_msg,
                'model': self.model_name,
                'role': self.role,
                'agent': self.agent_name,
                'response_time': time.time() - start_time
            }
            
    async def chat(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        """Chat with the model using conversation format"""
        start_time = time.time()
        
        try:
            # Prepare request payload
            payload = {
                'model': self.model_name,
                'messages': messages,
                'stream': False,
                **self.config,
                **kwargs
            }
            
            # Make request to Ollama
            async with self.session.post(
                f"{self.base_url}/api/chat",
                json=payload
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    
                    # Update metrics
                    response_time = time.time() - start_time
                    self._update_metrics(response_time, result)
                    
                    return {
                        'success': True,
                        'message': result.get('message', {}),
                        'model': self.model_name,
                        'role': self.role,
                        'agent': self.agent_name,
                        'response_time': response_time,
                        'tokens': result.get('eval_count', 0)
                    }
                else:
                    error_msg = f"HTTP {response.status}: {await response.text()}"
                    self._update_error_metrics(error_msg)
                    
                    return {
                        'success': False,
                        'error': error_msg,
                        'model': self.model_name,
                        'role': self.role,
                        'agent': self.agent_name,
                        'response_time': time.time() - start_time
                    }
                    
        except Exception as e:
            error_msg = f"Chat request failed: {str(e)}"
            self._update_error_metrics(error_msg)
            
            return {
                'success': False,
                'error': error_msg,
                'model': self.model_name,
                'role': self.role,
                'agent': self.agent_name,
                'response_time': time.time() - start_time
            }
            
    def _update_metrics(self, response_time: float, result: Dict[str, Any]):
        """Update performance metrics after successful request"""
        self.total_requests += 1
        self.total_response_time += response_time
        self.last_used = time.time()
        
        # Token counting
        tokens = result.get('eval_count', 0) + result.get('prompt_eval_count', 0)
        self.total_tokens += tokens
        
        # Calculate averages
        self.metrics['avg_response_time'] = self.total_response_time / self.total_requests
        
        if response_time > 0:
            self.metrics['tokens_per_second'] = tokens / response_time
            
        # Success rate
        total_attempts = self.total_requests + self.error_count
        self.metrics['success_rate'] = self.total_requests / total_attempts if total_attempts > 0 else 1.0
        
        self.active = True
        
    def _update_error_metrics(self, error_msg: str):
        """Update metrics after failed request"""
        self.error_count += 1
        self.metrics['last_error'] = error_msg
        
        # Update success rate
        total_attempts = self.total_requests + self.error_count
        self.metrics['success_rate'] = self.total_requests / total_attempts if total_attempts > 0 else 0.0
        
        logger.error(f"Model {self.model_name} error for {self.agent_name}: {error_msg}")
        
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the model instance"""
        try:
            # Simple generation test
            test_prompt = "Hello, respond with 'OK' if you are working correctly."
            result = await self.generate(test_prompt, max_tokens=10)
            
            if result['success']:
                return {
                    'status': 'healthy',
                    'model': self.model_name,
                    'role': self.role,
                    'agent': self.agent_name,
                    'response_time': result['response_time'],
                    'last_check': time.time()
                }
            else:
                return {
                    'status': 'unhealthy',
                    'model': self.model_name,
                    'role': self.role,
                    'agent': self.agent_name,
                    'error': result.get('error', 'Unknown error'),
                    'last_check': time.time()
                }
                
        except Exception as e:
            return {
                'status': 'unhealthy',
                'model': self.model_name,
                'role': self.role,
                'agent': self.agent_name,
                'error': str(e),
                'last_check': time.time()
            }
            
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        return {
            'model': self.model_name,
            'role': self.role,
            'agent': self.agent_name,
            'active': self.active,
            'total_requests': self.total_requests,
            'total_tokens': self.total_tokens,
            'error_count': self.error_count,
            'last_used': self.last_used,
            'metrics': self.metrics.copy()
        }
        
    def update_config(self, new_config: Dict[str, Any]):
        """Update model configuration"""
        self.config.update(new_config)
        logger.info(f"Updated config for {self.model_name}:{self.role} ({self.agent_name})")
        
    def reset_metrics(self):
        """Reset performance metrics"""
        self.total_requests = 0
        self.total_tokens = 0
        self.total_response_time = 0.0
        self.error_count = 0
        self.last_used = None
        
        self.metrics = {
            'avg_response_time': 0.0,
            'tokens_per_second': 0.0,
            'success_rate': 1.0,
            'last_error': None
        }
        
        logger.info(f"Reset metrics for {self.model_name}:{self.role} ({self.agent_name})")

    async def cleanup(self):
        """Clean up resources and close connections"""
        try:
            self.active = False

            # Close session if it exists
            if hasattr(self, 'session') and self.session:
                if not self.session.closed:
                    await self.session.close()
                    logger.info(f"Closed session for {self.model_name}:{self.role}")

            # Reset state
            self.last_used = None
            logger.info(f"Cleaned up model instance {self.model_name}:{self.role} ({self.agent_name})")

        except Exception as e:
            logger.error(f"Error during cleanup for {self.model_name}:{self.role}: {e}")

    async def __aenter__(self):
        """Async context manager entry"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.cleanup()

    def __str__(self) -> str:
        return f"OllamaModelInstance({self.model_name}:{self.role}:{self.agent_name})"

    def __repr__(self) -> str:
        return self.__str__()

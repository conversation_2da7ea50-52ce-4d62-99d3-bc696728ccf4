"""
CLI Utilities - Helper functions and utilities for command-line tools
"""

import asyncio
import json
import sys
import time
import psutil
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from pathlib import Path

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from rich.tree import Tree
from rich.text import Text
from rich.columns import Columns
from rich.align import Align


class CLIUtils:
    """Utility functions for CLI operations"""
    
    def __init__(self):
        self.console = Console()
    
    def format_duration(self, seconds: float) -> str:
        """Format duration in human-readable format"""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}m"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}h"
    
    def format_bytes(self, bytes_value: int) -> str:
        """Format bytes in human-readable format"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f}{unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f}PB"
    
    def format_percentage(self, value: float, total: float) -> str:
        """Format percentage with color coding"""
        if total == 0:
            return "0.0%"
        
        percentage = (value / total) * 100
        
        if percentage >= 90:
            color = "red"
        elif percentage >= 70:
            color = "yellow"
        else:
            color = "green"
        
        return f"[{color}]{percentage:.1f}%[/{color}]"
    
    def create_status_indicator(self, status: bool, true_text: str = "✅", false_text: str = "❌") -> str:
        """Create status indicator"""
        return true_text if status else false_text
    
    def create_progress_bar(self, current: int, total: int, width: int = 20) -> str:
        """Create ASCII progress bar"""
        if total == 0:
            return "[" + " " * width + "]"
        
        filled = int((current / total) * width)
        bar = "█" * filled + "░" * (width - filled)
        return f"[{bar}]"
    
    async def run_with_spinner(self, coro: Callable, message: str = "Processing...") -> Any:
        """Run coroutine with spinner"""
        with self.console.status(f"[bold green]{message}"):
            return await coro()
    
    def display_system_info(self) -> None:
        """Display system information"""
        # CPU info
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # Memory info
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used = self.format_bytes(memory.used)
        memory_total = self.format_bytes(memory.total)
        
        # Disk info
        disk = psutil.disk_usage('/')
        disk_percent = disk.percent
        disk_used = self.format_bytes(disk.used)
        disk_total = self.format_bytes(disk.total)
        
        # Create system info table
        table = Table(title="System Information")
        table.add_column("Resource", style="cyan")
        table.add_column("Usage", style="green")
        table.add_column("Details", style="yellow")
        
        table.add_row(
            "CPU",
            self.format_percentage(cpu_percent, 100),
            f"{cpu_count} cores"
        )
        table.add_row(
            "Memory",
            self.format_percentage(memory_percent, 100),
            f"{memory_used} / {memory_total}"
        )
        table.add_row(
            "Disk",
            self.format_percentage(disk_percent, 100),
            f"{disk_used} / {disk_total}"
        )
        
        self.console.print(table)
    
    def display_test_tree(self, test_results: Dict[str, Any]) -> None:
        """Display test results in tree format"""
        tree = Tree("🧪 Test Results")
        
        for category, results in test_results.items():
            if isinstance(results, dict) and 'result' in results:
                result = results['result']
                status = self.create_status_indicator(result.get('passed', False))
                category_node = tree.add(f"{status} {category}")
                
                # Add details if available
                if 'detailed' in result and 'summary' in result['detailed']:
                    summary = result['detailed']['summary']
                    category_node.add(f"Total: {summary.get('total', 0)}")
                    category_node.add(f"Passed: {summary.get('passed', 0)}")
                    category_node.add(f"Failed: {summary.get('failed', 0)}")
                    category_node.add(f"Duration: {summary.get('duration', 0):.2f}s")
        
        self.console.print(tree)
    
    def display_metrics_dashboard(self, metrics: Dict[str, Any]) -> None:
        """Display metrics in dashboard format"""
        # Performance metrics
        perf_panel = Panel(
            f"Response Time: {metrics.get('response_time', 0):.2f}s\n"
            f"Throughput: {metrics.get('throughput', 0):.1f} ops/s\n"
            f"Error Rate: {metrics.get('error_rate', 0):.2%}",
            title="Performance",
            border_style="green"
        )
        
        # Resource metrics
        resource_panel = Panel(
            f"CPU Usage: {metrics.get('cpu_usage', 0):.1%}\n"
            f"Memory Usage: {metrics.get('memory_usage', 0):.1%}\n"
            f"Disk I/O: {metrics.get('disk_io', 0)} ops/s",
            title="Resources",
            border_style="blue"
        )
        
        # Business metrics
        business_panel = Panel(
            f"Active Agents: {metrics.get('active_agents', 0)}\n"
            f"Running Strategies: {metrics.get('running_strategies', 0)}\n"
            f"Portfolio Value: ${metrics.get('portfolio_value', 0):,.2f}",
            title="Business",
            border_style="yellow"
        )
        
        # Display in columns
        self.console.print(Columns([perf_panel, resource_panel, business_panel]))
    
    def create_comparison_table(self, data: List[Dict[str, Any]], title: str = "Comparison") -> Table:
        """Create comparison table"""
        if not data:
            return Table(title=title)
        
        table = Table(title=title)
        
        # Add columns based on first item keys
        for key in data[0].keys():
            table.add_column(key.replace('_', ' ').title(), style="cyan")
        
        # Add rows
        for item in data:
            row_values = []
            for value in item.values():
                if isinstance(value, float):
                    row_values.append(f"{value:.2f}")
                elif isinstance(value, bool):
                    row_values.append(self.create_status_indicator(value))
                else:
                    row_values.append(str(value))
            table.add_row(*row_values)
        
        return table
    
    def save_results_to_file(self, results: Dict[str, Any], filename: str) -> bool:
        """Save results to JSON file"""
        try:
            output_dir = Path("cli_outputs")
            output_dir.mkdir(exist_ok=True)
            
            filepath = output_dir / filename
            with open(filepath, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            self.console.print(f"[green]Results saved to {filepath}[/green]")
            return True
            
        except Exception as e:
            self.console.print(f"[red]Failed to save results: {e}[/red]")
            return False
    
    def load_results_from_file(self, filename: str) -> Optional[Dict[str, Any]]:
        """Load results from JSON file"""
        try:
            filepath = Path("cli_outputs") / filename
            if not filepath.exists():
                self.console.print(f"[red]File not found: {filepath}[/red]")
                return None
            
            with open(filepath, 'r') as f:
                results = json.load(f)
            
            self.console.print(f"[green]Results loaded from {filepath}[/green]")
            return results
            
        except Exception as e:
            self.console.print(f"[red]Failed to load results: {e}[/red]")
            return None
    
    def display_error_analysis(self, errors: List[Dict[str, Any]]) -> None:
        """Display error analysis"""
        if not errors:
            self.console.print("[green]No errors found![/green]")
            return
        
        # Group errors by type
        error_groups = {}
        for error in errors:
            error_type = error.get('type', 'Unknown')
            if error_type not in error_groups:
                error_groups[error_type] = []
            error_groups[error_type].append(error)
        
        # Create error summary table
        table = Table(title="Error Analysis")
        table.add_column("Error Type", style="red")
        table.add_column("Count", style="yellow")
        table.add_column("Severity", style="orange")
        table.add_column("Latest", style="dim")
        
        for error_type, error_list in error_groups.items():
            count = len(error_list)
            latest_error = max(error_list, key=lambda x: x.get('timestamp', ''))
            severity = latest_error.get('severity', 'Unknown')
            timestamp = latest_error.get('timestamp', 'Unknown')
            
            table.add_row(error_type, str(count), severity, timestamp)
        
        self.console.print(table)
        
        # Show detailed errors
        if len(errors) <= 5:
            for i, error in enumerate(errors, 1):
                error_panel = Panel(
                    f"Message: {error.get('message', 'No message')}\n"
                    f"Location: {error.get('location', 'Unknown')}\n"
                    f"Timestamp: {error.get('timestamp', 'Unknown')}",
                    title=f"Error {i}: {error.get('type', 'Unknown')}",
                    border_style="red"
                )
                self.console.print(error_panel)
    
    def create_health_check_display(self, health_data: Dict[str, Any]) -> None:
        """Display health check results"""
        overall_status = health_data.get('status', 'unknown')
        status_color = "green" if overall_status == 'healthy' else "red"
        
        # Overall status
        header = Panel(
            f"[bold {status_color}]System Health: {overall_status.upper()}[/bold {status_color}]",
            border_style=status_color
        )
        self.console.print(header)
        
        # Component health
        components = health_data.get('components', {})
        if components:
            table = Table(title="Component Health")
            table.add_column("Component", style="cyan")
            table.add_column("Status", style="green")
            table.add_column("Response Time", style="yellow")
            table.add_column("Last Check", style="dim")
            
            for component, info in components.items():
                status = self.create_status_indicator(info.get('healthy', False))
                response_time = f"{info.get('response_time', 0):.2f}ms"
                last_check = info.get('last_check', 'Never')
                
                table.add_row(component, status, response_time, last_check)
            
            self.console.print(table)
        
        # Alerts
        alerts = health_data.get('alerts', [])
        if alerts:
            self.console.print("\n[bold red]Active Alerts:[/bold red]")
            for alert in alerts:
                alert_panel = Panel(
                    f"Severity: {alert.get('severity', 'Unknown')}\n"
                    f"Message: {alert.get('message', 'No message')}\n"
                    f"Time: {alert.get('timestamp', 'Unknown')}",
                    title=alert.get('title', 'Alert'),
                    border_style="red"
                )
                self.console.print(alert_panel)
    
    async def run_periodic_task(self, task_func: Callable, interval: int, duration: int = None) -> None:
        """Run a task periodically"""
        start_time = time.time()
        
        try:
            while True:
                await task_func()
                await asyncio.sleep(interval)
                
                if duration and (time.time() - start_time) >= duration:
                    break
                    
        except KeyboardInterrupt:
            self.console.print("\n[yellow]Periodic task stopped by user[/yellow]")
    
    def confirm_action(self, message: str, default: bool = False) -> bool:
        """Get user confirmation for an action"""
        from rich.prompt import Confirm
        return Confirm.ask(message, default=default)
    
    def get_user_input(self, prompt: str, default: str = None, choices: List[str] = None) -> str:
        """Get user input with validation"""
        from rich.prompt import Prompt
        return Prompt.ask(prompt, default=default, choices=choices)
    
    def display_banner(self, title: str, subtitle: str = None) -> None:
        """Display application banner"""
        banner_text = f"[bold blue]{title}[/bold blue]"
        if subtitle:
            banner_text += f"\n[dim]{subtitle}[/dim]"
        
        banner = Panel(
            Align.center(banner_text),
            border_style="blue",
            padding=(1, 2)
        )
        
        self.console.print(banner)


# Global utility instance
cli_utils = CLIUtils()


# Convenience functions
def format_duration(seconds: float) -> str:
    return cli_utils.format_duration(seconds)


def format_bytes(bytes_value: int) -> str:
    return cli_utils.format_bytes(bytes_value)


def create_status_indicator(status: bool) -> str:
    return cli_utils.create_status_indicator(status)


async def run_with_spinner(coro: Callable, message: str = "Processing...") -> Any:
    return await cli_utils.run_with_spinner(coro, message)


def save_results(results: Dict[str, Any], filename: str) -> bool:
    return cli_utils.save_results_to_file(results, filename)


def load_results(filename: str) -> Optional[Dict[str, Any]]:
    return cli_utils.load_results_from_file(filename)

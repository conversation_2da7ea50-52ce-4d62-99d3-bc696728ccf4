"""
Market Data Manager - Comprehensive Market Data Integration

Unified market data management system that handles multiple data providers,
real-time feeds, historical data, and data quality validation.
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from enum import Enum
import json

import pandas as pd
import numpy as np
from websockets import connect, ConnectionClosed
import aiohttp
import redis.asyncio as redis


class DataProvider(Enum):
    """Supported data providers"""
    ALPHA_VANTAGE = "alpha_vantage"
    YAHOO_FINANCE = "yahoo_finance"
    IEX_CLOUD = "iex_cloud"
    POLYGON = "polygon"
    FINNHUB = "finnhub"
    QUANDL = "quandl"
    MOCK = "mock"  # For testing


class DataType(Enum):
    """Types of market data"""
    REAL_TIME_QUOTE = "real_time_quote"
    HISTORICAL_BARS = "historical_bars"
    TRADES = "trades"
    LEVEL2 = "level2"
    NEWS = "news"
    FUNDAMENTALS = "fundamentals"
    OPTIONS = "options"
    CRYPTO = "crypto"


@dataclass
class MarketDataPoint:
    """Standard market data point structure"""
    symbol: str
    timestamp: datetime
    data_type: DataType
    provider: DataProvider
    data: Dict[str, Any]
    quality_score: float = 1.0
    latency_ms: Optional[float] = None


@dataclass
class Quote:
    """Real-time quote data"""
    symbol: str
    timestamp: datetime
    bid: float
    ask: float
    bid_size: int
    ask_size: int
    last_price: float
    last_size: int
    volume: int
    provider: DataProvider


@dataclass
class Bar:
    """OHLCV bar data"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    provider: DataProvider
    timeframe: str = "1min"


class DataProviderInterface(ABC):
    """Abstract interface for data providers"""
    
    @abstractmethod
    async def connect(self) -> bool:
        """Connect to data provider"""
        pass
    
    @abstractmethod
    async def disconnect(self):
        """Disconnect from data provider"""
        pass
    
    @abstractmethod
    async def subscribe_real_time(self, symbols: List[str], callback: Callable):
        """Subscribe to real-time data"""
        pass
    
    @abstractmethod
    async def get_historical_data(self, symbol: str, start_date: datetime, 
                                 end_date: datetime, timeframe: str) -> List[Bar]:
        """Get historical data"""
        pass
    
    @abstractmethod
    async def get_quote(self, symbol: str) -> Optional[Quote]:
        """Get current quote"""
        pass


class MockDataProvider(DataProviderInterface):
    """Mock data provider for testing"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.connected = False
        self.subscriptions: Dict[str, Callable] = {}
        self.logger = logging.getLogger(__name__)
    
    async def connect(self) -> bool:
        """Connect to mock provider"""
        self.connected = True
        self.logger.info("Mock data provider connected")
        return True
    
    async def disconnect(self):
        """Disconnect from mock provider"""
        self.connected = False
        self.subscriptions.clear()
        self.logger.info("Mock data provider disconnected")
    
    async def subscribe_real_time(self, symbols: List[str], callback: Callable):
        """Subscribe to mock real-time data"""
        for symbol in symbols:
            self.subscriptions[symbol] = callback
        
        # Start generating mock data
        asyncio.create_task(self._generate_mock_data(symbols, callback))
    
    async def _generate_mock_data(self, symbols: List[str], callback: Callable):
        """Generate mock real-time data"""
        base_prices = {symbol: 100.0 + hash(symbol) % 100 for symbol in symbols}
        
        while self.connected and symbols:
            for symbol in symbols:
                if symbol not in self.subscriptions:
                    continue
                
                # Generate realistic price movement
                base_price = base_prices[symbol]
                price_change = np.random.normal(0, 0.01) * base_price
                new_price = max(0.01, base_price + price_change)
                base_prices[symbol] = new_price
                
                # Create quote
                spread = new_price * 0.001  # 0.1% spread
                quote = Quote(
                    symbol=symbol,
                    timestamp=datetime.now(),
                    bid=new_price - spread/2,
                    ask=new_price + spread/2,
                    bid_size=np.random.randint(100, 1000),
                    ask_size=np.random.randint(100, 1000),
                    last_price=new_price,
                    last_size=np.random.randint(100, 500),
                    volume=np.random.randint(10000, 100000),
                    provider=DataProvider.MOCK
                )
                
                # Send to callback
                try:
                    await callback(quote)
                except Exception as e:
                    self.logger.error(f"Error in mock data callback: {e}")
            
            await asyncio.sleep(1)  # 1 second intervals
    
    async def get_historical_data(self, symbol: str, start_date: datetime, 
                                 end_date: datetime, timeframe: str) -> List[Bar]:
        """Generate mock historical data"""
        bars = []
        current_date = start_date
        base_price = 100.0 + hash(symbol) % 100
        
        while current_date <= end_date:
            # Generate OHLCV data
            open_price = base_price
            high_price = open_price * (1 + np.random.uniform(0, 0.02))
            low_price = open_price * (1 - np.random.uniform(0, 0.02))
            close_price = np.random.uniform(low_price, high_price)
            volume = np.random.randint(10000, 100000)
            
            bar = Bar(
                symbol=symbol,
                timestamp=current_date,
                open=open_price,
                high=high_price,
                low=low_price,
                close=close_price,
                volume=volume,
                provider=DataProvider.MOCK,
                timeframe=timeframe
            )
            bars.append(bar)
            
            base_price = close_price
            
            # Increment time based on timeframe
            if timeframe == "1min":
                current_date += timedelta(minutes=1)
            elif timeframe == "5min":
                current_date += timedelta(minutes=5)
            elif timeframe == "1hour":
                current_date += timedelta(hours=1)
            elif timeframe == "1day":
                current_date += timedelta(days=1)
            else:
                current_date += timedelta(minutes=1)
        
        return bars
    
    async def get_quote(self, symbol: str) -> Optional[Quote]:
        """Get mock current quote"""
        base_price = 100.0 + hash(symbol) % 100
        spread = base_price * 0.001
        
        return Quote(
            symbol=symbol,
            timestamp=datetime.now(),
            bid=base_price - spread/2,
            ask=base_price + spread/2,
            bid_size=np.random.randint(100, 1000),
            ask_size=np.random.randint(100, 1000),
            last_price=base_price,
            last_size=np.random.randint(100, 500),
            volume=np.random.randint(10000, 100000),
            provider=DataProvider.MOCK
        )


class AlphaVantageProvider(DataProviderInterface):
    """Alpha Vantage data provider"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.api_key = config.get('api_key')
        self.base_url = "https://www.alphavantage.co/query"
        self.session: Optional[aiohttp.ClientSession] = None
        self.connected = False
        self.logger = logging.getLogger(__name__)
    
    async def connect(self) -> bool:
        """Connect to Alpha Vantage"""
        if not self.api_key:
            self.logger.error("Alpha Vantage API key not provided")
            return False
        
        self.session = aiohttp.ClientSession()
        self.connected = True
        self.logger.info("Alpha Vantage provider connected")
        return True
    
    async def disconnect(self):
        """Disconnect from Alpha Vantage"""
        if self.session:
            await self.session.close()
        self.connected = False
        self.logger.info("Alpha Vantage provider disconnected")
    
    async def subscribe_real_time(self, symbols: List[str], callback: Callable):
        """Alpha Vantage doesn't support real-time WebSocket, use polling"""
        asyncio.create_task(self._poll_quotes(symbols, callback))
    
    async def _poll_quotes(self, symbols: List[str], callback: Callable):
        """Poll quotes from Alpha Vantage"""
        while self.connected:
            for symbol in symbols:
                try:
                    quote = await self.get_quote(symbol)
                    if quote:
                        await callback(quote)
                except Exception as e:
                    self.logger.error(f"Error polling quote for {symbol}: {e}")
            
            await asyncio.sleep(60)  # Alpha Vantage rate limits
    
    async def get_historical_data(self, symbol: str, start_date: datetime, 
                                 end_date: datetime, timeframe: str) -> List[Bar]:
        """Get historical data from Alpha Vantage"""
        if not self.session:
            return []
        
        # Map timeframe to Alpha Vantage function
        function_map = {
            "1min": "TIME_SERIES_INTRADAY",
            "5min": "TIME_SERIES_INTRADAY", 
            "1hour": "TIME_SERIES_INTRADAY",
            "1day": "TIME_SERIES_DAILY"
        }
        
        function = function_map.get(timeframe, "TIME_SERIES_DAILY")
        
        params = {
            "function": function,
            "symbol": symbol,
            "apikey": self.api_key,
            "outputsize": "full"
        }
        
        if function == "TIME_SERIES_INTRADAY":
            params["interval"] = timeframe
        
        try:
            async with self.session.get(self.base_url, params=params) as response:
                data = await response.json()
                
                # Parse response
                bars = []
                time_series_key = None
                
                # Find the time series key
                for key in data.keys():
                    if "Time Series" in key:
                        time_series_key = key
                        break
                
                if not time_series_key or time_series_key not in data:
                    self.logger.error(f"No time series data found for {symbol}")
                    return []
                
                time_series = data[time_series_key]
                
                for timestamp_str, ohlcv in time_series.items():
                    timestamp = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S" if " " in timestamp_str else "%Y-%m-%d")
                    
                    if start_date <= timestamp <= end_date:
                        bar = Bar(
                            symbol=symbol,
                            timestamp=timestamp,
                            open=float(ohlcv["1. open"]),
                            high=float(ohlcv["2. high"]),
                            low=float(ohlcv["3. low"]),
                            close=float(ohlcv["4. close"]),
                            volume=int(ohlcv["5. volume"]),
                            provider=DataProvider.ALPHA_VANTAGE,
                            timeframe=timeframe
                        )
                        bars.append(bar)
                
                return sorted(bars, key=lambda x: x.timestamp)
                
        except Exception as e:
            self.logger.error(f"Error fetching historical data for {symbol}: {e}")
            return []
    
    async def get_quote(self, symbol: str) -> Optional[Quote]:
        """Get current quote from Alpha Vantage"""
        if not self.session:
            return None
        
        params = {
            "function": "GLOBAL_QUOTE",
            "symbol": symbol,
            "apikey": self.api_key
        }
        
        try:
            async with self.session.get(self.base_url, params=params) as response:
                data = await response.json()
                
                if "Global Quote" not in data:
                    return None
                
                quote_data = data["Global Quote"]
                
                return Quote(
                    symbol=symbol,
                    timestamp=datetime.now(),
                    bid=float(quote_data["05. price"]) - 0.01,  # Approximate bid
                    ask=float(quote_data["05. price"]) + 0.01,  # Approximate ask
                    bid_size=1000,  # Not available from Alpha Vantage
                    ask_size=1000,  # Not available from Alpha Vantage
                    last_price=float(quote_data["05. price"]),
                    last_size=int(quote_data["10. volume"]),
                    volume=int(quote_data["10. volume"]),
                    provider=DataProvider.ALPHA_VANTAGE
                )
                
        except Exception as e:
            self.logger.error(f"Error fetching quote for {symbol}: {e}")
            return None


class DataQualityValidator:
    """Data quality validation and scoring"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.quality_thresholds = config.get('quality_thresholds', {
            'latency_ms': 1000,
            'price_deviation': 0.05,
            'volume_deviation': 0.5,
            'missing_data_tolerance': 0.1
        })
        self.logger = logging.getLogger(__name__)
    
    def validate_quote(self, quote: Quote, previous_quote: Optional[Quote] = None) -> float:
        """Validate quote quality and return score (0-1)"""
        score = 1.0
        
        # Check basic data integrity
        if quote.bid <= 0 or quote.ask <= 0 or quote.last_price <= 0:
            score -= 0.5
        
        if quote.bid >= quote.ask:
            score -= 0.3
        
        # Check spread reasonableness
        spread_pct = (quote.ask - quote.bid) / quote.last_price
        if spread_pct > 0.1:  # 10% spread seems unreasonable
            score -= 0.2
        
        # Check against previous quote if available
        if previous_quote:
            price_change = abs(quote.last_price - previous_quote.last_price) / previous_quote.last_price
            if price_change > self.quality_thresholds['price_deviation']:
                score -= 0.2
        
        # Check latency
        if quote.timestamp:
            latency = (datetime.now() - quote.timestamp).total_seconds() * 1000
            if latency > self.quality_thresholds['latency_ms']:
                score -= 0.1
        
        return max(0.0, score)
    
    def validate_bar(self, bar: Bar, previous_bar: Optional[Bar] = None) -> float:
        """Validate bar quality and return score (0-1)"""
        score = 1.0
        
        # Check OHLC relationships
        if not (bar.low <= bar.open <= bar.high and 
                bar.low <= bar.close <= bar.high and
                bar.low <= bar.high):
            score -= 0.5
        
        # Check volume
        if bar.volume < 0:
            score -= 0.3
        
        # Check against previous bar
        if previous_bar:
            price_change = abs(bar.close - previous_bar.close) / previous_bar.close
            if price_change > self.quality_thresholds['price_deviation']:
                score -= 0.2
            
            volume_change = abs(bar.volume - previous_bar.volume) / max(previous_bar.volume, 1)
            if volume_change > self.quality_thresholds['volume_deviation']:
                score -= 0.1
        
        return max(0.0, score)


class MarketDataManager:
    """
    Main market data management system that coordinates multiple providers,
    handles subscriptions, caching, and data quality.

    Features:
    - Multiple data provider support
    - Real-time data streaming
    - Historical data management
    - Data quality validation
    - Caching and persistence
    - Failover and redundancy
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.data_config = config.get('market_data', {})

        # Data providers
        self.providers: Dict[DataProvider, DataProviderInterface] = {}
        self.primary_provider: Optional[DataProvider] = None
        self.backup_providers: List[DataProvider] = []

        # Data storage
        self.redis_client: Optional[redis.Redis] = None
        self.quote_cache: Dict[str, Quote] = {}
        self.bar_cache: Dict[str, List[Bar]] = {}

        # Subscriptions
        self.subscriptions: Dict[str, List[Callable]] = {}
        self.subscription_providers: Dict[str, DataProvider] = {}

        # Quality validation
        self.quality_validator = DataQualityValidator(config)

        # State
        self.running = False
        self.initialized = False

        # Setup logging
        self.logger = logging.getLogger(__name__)

    async def initialize(self) -> bool:
        """Initialize market data manager"""
        if self.initialized:
            return True

        try:
            self.logger.info("Initializing Market Data Manager...")

            # Initialize Redis connection
            redis_config = self.data_config.get('redis', {})
            if redis_config:
                self.redis_client = redis.Redis(
                    host=redis_config.get('host', 'localhost'),
                    port=redis_config.get('port', 6379),
                    db=redis_config.get('db', 0),
                    decode_responses=True
                )
                await self.redis_client.ping()
                self.logger.info("Redis connection established")

            # Initialize data providers
            await self.setup_providers()

            self.initialized = True
            self.logger.info("✓ Market Data Manager initialized")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Market Data Manager: {e}")
            return False

    async def setup_providers(self):
        """Setup and configure data providers"""
        providers_config = self.data_config.get('providers', {})

        # Setup each configured provider
        for provider_name, provider_config in providers_config.items():
            try:
                provider_enum = DataProvider(provider_name)

                if provider_enum == DataProvider.MOCK:
                    provider = MockDataProvider(provider_config)
                elif provider_enum == DataProvider.ALPHA_VANTAGE:
                    provider = AlphaVantageProvider(provider_config)
                # Add other providers here
                else:
                    self.logger.warning(f"Unsupported provider: {provider_name}")
                    continue

                # Connect to provider
                if await provider.connect():
                    self.providers[provider_enum] = provider
                    self.logger.info(f"Connected to {provider_name}")

                    # Set primary provider if not set
                    if not self.primary_provider:
                        self.primary_provider = provider_enum
                    else:
                        self.backup_providers.append(provider_enum)

            except Exception as e:
                self.logger.error(f"Failed to setup provider {provider_name}: {e}")

    async def start(self):
        """Start market data manager"""
        if not self.initialized:
            await self.initialize()

        if self.running:
            return

        self.running = True
        self.logger.info("Starting Market Data Manager...")

        # Start background tasks
        asyncio.create_task(self.health_check_loop())
        asyncio.create_task(self.cache_cleanup_loop())

        self.logger.info("Market Data Manager started")

    async def stop(self):
        """Stop market data manager"""
        self.running = False

        # Disconnect from all providers
        for provider in self.providers.values():
            await provider.disconnect()

        # Close Redis connection
        if self.redis_client:
            await self.redis_client.close()

        self.logger.info("Market Data Manager stopped")

    async def subscribe_real_time(self, symbols: List[str], callback: Callable,
                                 provider: Optional[DataProvider] = None) -> bool:
        """Subscribe to real-time data for symbols"""
        if not self.running:
            await self.start()

        # Use primary provider if not specified
        if not provider:
            provider = self.primary_provider

        if not provider or provider not in self.providers:
            self.logger.error(f"Provider {provider} not available")
            return False

        try:
            # Add to subscriptions
            for symbol in symbols:
                if symbol not in self.subscriptions:
                    self.subscriptions[symbol] = []
                self.subscriptions[symbol].append(callback)
                self.subscription_providers[symbol] = provider

            # Subscribe with provider
            provider_instance = self.providers[provider]
            await provider_instance.subscribe_real_time(symbols, self._handle_real_time_data)

            self.logger.info(f"Subscribed to {symbols} via {provider}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to subscribe to {symbols}: {e}")
            return False

    async def _handle_real_time_data(self, data: Union[Quote, Bar]):
        """Handle incoming real-time data"""
        try:
            symbol = data.symbol

            # Validate data quality
            if isinstance(data, Quote):
                previous_quote = self.quote_cache.get(symbol)
                quality_score = self.quality_validator.validate_quote(data, previous_quote)

                # Cache quote
                self.quote_cache[symbol] = data

                # Store in Redis if available
                if self.redis_client:
                    await self.redis_client.setex(
                        f"quote:{symbol}",
                        300,  # 5 minutes TTL
                        json.dumps(asdict(data), default=str)
                    )

            elif isinstance(data, Bar):
                quality_score = self.quality_validator.validate_bar(data)

                # Cache bar
                if symbol not in self.bar_cache:
                    self.bar_cache[symbol] = []
                self.bar_cache[symbol].append(data)

                # Keep only recent bars in memory
                if len(self.bar_cache[symbol]) > 1000:
                    self.bar_cache[symbol] = self.bar_cache[symbol][-1000:]

            # Create market data point
            market_data_point = MarketDataPoint(
                symbol=symbol,
                timestamp=data.timestamp,
                data_type=DataType.REAL_TIME_QUOTE if isinstance(data, Quote) else DataType.HISTORICAL_BARS,
                provider=data.provider,
                data=asdict(data),
                quality_score=quality_score
            )

            # Send to subscribers if quality is acceptable
            if quality_score >= 0.5 and symbol in self.subscriptions:
                for callback in self.subscriptions[symbol]:
                    try:
                        await callback(market_data_point)
                    except Exception as e:
                        self.logger.error(f"Error in subscription callback: {e}")

        except Exception as e:
            self.logger.error(f"Error handling real-time data: {e}")

    async def get_quote(self, symbol: str, provider: Optional[DataProvider] = None) -> Optional[Quote]:
        """Get current quote for symbol"""
        # Try cache first
        if symbol in self.quote_cache:
            cached_quote = self.quote_cache[symbol]
            # Return cached quote if recent (within 30 seconds)
            if (datetime.now() - cached_quote.timestamp).total_seconds() < 30:
                return cached_quote

        # Try Redis cache
        if self.redis_client:
            cached_data = await self.redis_client.get(f"quote:{symbol}")
            if cached_data:
                try:
                    quote_dict = json.loads(cached_data)
                    quote_dict['timestamp'] = datetime.fromisoformat(quote_dict['timestamp'])
                    quote_dict['provider'] = DataProvider(quote_dict['provider'])
                    return Quote(**quote_dict)
                except Exception as e:
                    self.logger.error(f"Error parsing cached quote: {e}")

        # Fetch from provider
        if not provider:
            provider = self.primary_provider

        if provider and provider in self.providers:
            try:
                quote = await self.providers[provider].get_quote(symbol)
                if quote:
                    self.quote_cache[symbol] = quote
                return quote
            except Exception as e:
                self.logger.error(f"Error fetching quote for {symbol}: {e}")

        return None

    async def get_historical_data(self, symbol: str, start_date: datetime,
                                 end_date: datetime, timeframe: str = "1day",
                                 provider: Optional[DataProvider] = None) -> List[Bar]:
        """Get historical data for symbol"""
        if not provider:
            provider = self.primary_provider

        if not provider or provider not in self.providers:
            self.logger.error(f"Provider {provider} not available")
            return []

        try:
            bars = await self.providers[provider].get_historical_data(
                symbol, start_date, end_date, timeframe
            )

            # Validate and score each bar
            validated_bars = []
            previous_bar = None

            for bar in bars:
                quality_score = self.quality_validator.validate_bar(bar, previous_bar)
                if quality_score >= 0.5:  # Only include high-quality bars
                    validated_bars.append(bar)
                    previous_bar = bar

            self.logger.info(f"Retrieved {len(validated_bars)} bars for {symbol}")
            return validated_bars

        except Exception as e:
            self.logger.error(f"Error fetching historical data for {symbol}: {e}")
            return []

    async def health_check_loop(self):
        """Monitor provider health and handle failover"""
        while self.running:
            try:
                # Check each provider
                for provider_enum, provider in self.providers.items():
                    if hasattr(provider, 'connected') and not provider.connected:
                        self.logger.warning(f"Provider {provider_enum} disconnected, attempting reconnect...")
                        try:
                            await provider.connect()
                        except Exception as e:
                            self.logger.error(f"Failed to reconnect {provider_enum}: {e}")

                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                self.logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(30)

    async def cache_cleanup_loop(self):
        """Clean up old cached data"""
        while self.running:
            try:
                current_time = datetime.now()

                # Clean quote cache (remove quotes older than 5 minutes)
                expired_quotes = []
                for symbol, quote in self.quote_cache.items():
                    if (current_time - quote.timestamp).total_seconds() > 300:
                        expired_quotes.append(symbol)

                for symbol in expired_quotes:
                    del self.quote_cache[symbol]

                # Clean bar cache (keep only recent bars)
                for symbol, bars in self.bar_cache.items():
                    # Keep only bars from last 24 hours
                    cutoff_time = current_time - timedelta(hours=24)
                    self.bar_cache[symbol] = [
                        bar for bar in bars if bar.timestamp >= cutoff_time
                    ]

                await asyncio.sleep(300)  # Clean every 5 minutes

            except Exception as e:
                self.logger.error(f"Error in cache cleanup loop: {e}")
                await asyncio.sleep(300)

    def get_provider_status(self) -> Dict[str, Any]:
        """Get status of all providers"""
        status = {}

        for provider_enum, provider in self.providers.items():
            status[provider_enum.value] = {
                'connected': getattr(provider, 'connected', False),
                'is_primary': provider_enum == self.primary_provider,
                'subscriptions': len([s for s, p in self.subscription_providers.items() if p == provider_enum])
            }

        return status

    def get_subscription_status(self) -> Dict[str, Any]:
        """Get status of all subscriptions"""
        return {
            'total_subscriptions': len(self.subscriptions),
            'symbols': list(self.subscriptions.keys()),
            'providers': {symbol: provider.value for symbol, provider in self.subscription_providers.items()}
        }

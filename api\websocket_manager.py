"""
WebSocket Manager - Real-time WebSocket connections and data streaming
"""

import asyncio
import logging
import json
import time
from typing import Dict, List, Set, Optional, Any
from dataclasses import dataclass
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from collections import defaultdict

logger = logging.getLogger(__name__)


@dataclass
class WebSocketConnection:
    """WebSocket connection data"""
    websocket: WebSocket
    client_id: str
    connection_type: str
    connected_at: datetime
    last_ping: datetime
    subscriptions: Set[str]
    metadata: Dict[str, Any]


class WebSocketManager:
    """
    Manages WebSocket connections and real-time data streaming.
    
    Features:
    - Connection management and lifecycle
    - Real-time data broadcasting
    - Subscription management
    - Connection health monitoring
    - Message routing and filtering
    - Rate limiting for WebSocket messages
    - Authentication for WebSocket connections
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.ws_config = config.get('websocket', {})
        
        # Connection management
        self.connections: Dict[str, WebSocketConnection] = {}
        self.connections_by_type: Dict[str, Set[str]] = defaultdict(set)
        self.subscriptions: Dict[str, Set[str]] = defaultdict(set)  # topic -> client_ids
        
        # Configuration
        self.ping_interval = self.ws_config.get('ping_interval', 30)  # seconds
        self.connection_timeout = self.ws_config.get('connection_timeout', 300)  # seconds
        self.max_connections = self.ws_config.get('max_connections', 1000)
        self.message_rate_limit = self.ws_config.get('message_rate_limit', 100)  # per minute
        
        # Message tracking
        self.message_counts: Dict[str, List[float]] = defaultdict(list)
        
        # Background tasks
        self.ping_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        
        # State
        self.running = False
        self.initialized = False
    
    async def initialize(self) -> bool:
        """Initialize WebSocket manager"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing WebSocket Manager...")
            
            self.initialized = True
            logger.info("✓ WebSocket Manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize WebSocket Manager: {e}")
            return False
    
    async def start(self) -> bool:
        """Start WebSocket manager"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        if self.running:
            return True
            
        try:
            logger.info("Starting WebSocket Manager...")
            
            # Start background tasks
            self.ping_task = asyncio.create_task(self._ping_loop())
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
            
            self.running = True
            logger.info("✓ WebSocket Manager started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start WebSocket Manager: {e}")
            return False
    
    async def stop(self) -> bool:
        """Stop WebSocket manager"""
        if not self.running:
            return True
            
        try:
            logger.info("Stopping WebSocket Manager...")
            self.running = False
            
            # Cancel background tasks
            if self.ping_task:
                self.ping_task.cancel()
            if self.cleanup_task:
                self.cleanup_task.cancel()
            
            # Close all connections
            await self._close_all_connections()
            
            logger.info("✓ WebSocket Manager stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping WebSocket Manager: {e}")
            return False
    
    # Connection handlers
    
    async def handle_market_data_connection(self, websocket: WebSocket):
        """Handle market data WebSocket connection"""
        await self._handle_connection(websocket, "market_data")
    
    async def handle_portfolio_connection(self, websocket: WebSocket):
        """Handle portfolio WebSocket connection"""
        await self._handle_connection(websocket, "portfolio")
    
    async def handle_analytics_connection(self, websocket: WebSocket):
        """Handle analytics WebSocket connection"""
        await self._handle_connection(websocket, "analytics")
    
    async def handle_system_connection(self, websocket: WebSocket):
        """Handle system status WebSocket connection"""
        await self._handle_connection(websocket, "system")
    
    async def handle_agents_connection(self, websocket: WebSocket):
        """Handle agents WebSocket connection"""
        await self._handle_connection(websocket, "agents")
    
    # Broadcasting methods
    
    async def broadcast_market_data(self, symbol: str, data: Dict[str, Any]):
        """Broadcast market data to subscribed clients"""
        try:
            message = {
                "type": "market_data",
                "symbol": symbol,
                "data": data,
                "timestamp": datetime.now().isoformat()
            }
            
            await self._broadcast_to_subscribers(f"market_data:{symbol}", message)
            
        except Exception as e:
            logger.error(f"Error broadcasting market data: {e}")
    
    async def broadcast_portfolio_update(self, portfolio_id: str, data: Dict[str, Any]):
        """Broadcast portfolio update to subscribed clients"""
        try:
            message = {
                "type": "portfolio_update",
                "portfolio_id": portfolio_id,
                "data": data,
                "timestamp": datetime.now().isoformat()
            }
            
            await self._broadcast_to_subscribers(f"portfolio:{portfolio_id}", message)
            
        except Exception as e:
            logger.error(f"Error broadcasting portfolio update: {e}")
    
    async def broadcast_analytics_update(self, analysis_type: str, data: Dict[str, Any]):
        """Broadcast analytics update to subscribed clients"""
        try:
            message = {
                "type": "analytics_update",
                "analysis_type": analysis_type,
                "data": data,
                "timestamp": datetime.now().isoformat()
            }
            
            await self._broadcast_to_subscribers(f"analytics:{analysis_type}", message)
            
        except Exception as e:
            logger.error(f"Error broadcasting analytics update: {e}")
    
    async def broadcast_system_status(self, status_data: Dict[str, Any]):
        """Broadcast system status to subscribed clients"""
        try:
            message = {
                "type": "system_status",
                "data": status_data,
                "timestamp": datetime.now().isoformat()
            }
            
            await self._broadcast_to_subscribers("system:status", message)
            
        except Exception as e:
            logger.error(f"Error broadcasting system status: {e}")
    
    async def broadcast_agent_message(self, agent_id: str, message_data: Dict[str, Any]):
        """Broadcast agent message to subscribed clients"""
        try:
            message = {
                "type": "agent_message",
                "agent_id": agent_id,
                "data": message_data,
                "timestamp": datetime.now().isoformat()
            }
            
            await self._broadcast_to_subscribers(f"agents:{agent_id}", message)
            
        except Exception as e:
            logger.error(f"Error broadcasting agent message: {e}")
    
    # Connection statistics
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get WebSocket connection statistics"""
        try:
            stats = {
                "total_connections": len(self.connections),
                "connections_by_type": {
                    conn_type: len(client_ids) 
                    for conn_type, client_ids in self.connections_by_type.items()
                },
                "total_subscriptions": sum(len(clients) for clients in self.subscriptions.values()),
                "active_topics": len(self.subscriptions),
                "max_connections": self.max_connections,
                "running": self.running
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting connection stats: {e}")
            return {}
    
    # Private methods
    
    async def _handle_connection(self, websocket: WebSocket, connection_type: str):
        """Handle WebSocket connection lifecycle"""
        client_id = None
        
        try:
            # Check connection limit
            if len(self.connections) >= self.max_connections:
                await websocket.close(code=1008, reason="Connection limit exceeded")
                return
            
            # Accept connection
            await websocket.accept()
            
            # Generate client ID
            client_id = f"{connection_type}_{int(time.time() * 1000)}"
            
            # Create connection object
            connection = WebSocketConnection(
                websocket=websocket,
                client_id=client_id,
                connection_type=connection_type,
                connected_at=datetime.now(),
                last_ping=datetime.now(),
                subscriptions=set(),
                metadata={}
            )
            
            # Store connection
            self.connections[client_id] = connection
            self.connections_by_type[connection_type].add(client_id)
            
            logger.info(f"WebSocket client {client_id} connected ({connection_type})")
            
            # Send welcome message
            await self._send_message(client_id, {
                "type": "welcome",
                "client_id": client_id,
                "connection_type": connection_type,
                "timestamp": datetime.now().isoformat()
            })
            
            # Handle messages
            await self._handle_messages(client_id)
            
        except WebSocketDisconnect:
            logger.info(f"WebSocket client {client_id} disconnected")
        except Exception as e:
            logger.error(f"Error handling WebSocket connection: {e}")
        finally:
            # Clean up connection
            if client_id:
                await self._remove_connection(client_id)
    
    async def _handle_messages(self, client_id: str):
        """Handle incoming WebSocket messages"""
        try:
            connection = self.connections[client_id]
            
            while True:
                # Receive message
                data = await connection.websocket.receive_text()
                
                # Check rate limit
                if not await self._check_rate_limit(client_id):
                    await self._send_message(client_id, {
                        "type": "error",
                        "message": "Rate limit exceeded"
                    })
                    continue
                
                # Parse message
                try:
                    message = json.loads(data)
                except json.JSONDecodeError:
                    await self._send_message(client_id, {
                        "type": "error",
                        "message": "Invalid JSON format"
                    })
                    continue
                
                # Handle message
                await self._process_message(client_id, message)
                
        except WebSocketDisconnect:
            pass
        except Exception as e:
            logger.error(f"Error handling messages for {client_id}: {e}")
    
    async def _process_message(self, client_id: str, message: Dict[str, Any]):
        """Process incoming WebSocket message"""
        try:
            message_type = message.get("type")
            
            if message_type == "subscribe":
                # Handle subscription
                topic = message.get("topic")
                if topic:
                    await self._subscribe_client(client_id, topic)
                    await self._send_message(client_id, {
                        "type": "subscribed",
                        "topic": topic
                    })
            
            elif message_type == "unsubscribe":
                # Handle unsubscription
                topic = message.get("topic")
                if topic:
                    await self._unsubscribe_client(client_id, topic)
                    await self._send_message(client_id, {
                        "type": "unsubscribed",
                        "topic": topic
                    })
            
            elif message_type == "ping":
                # Handle ping
                connection = self.connections[client_id]
                connection.last_ping = datetime.now()
                await self._send_message(client_id, {"type": "pong"})
            
            else:
                await self._send_message(client_id, {
                    "type": "error",
                    "message": f"Unknown message type: {message_type}"
                })
                
        except Exception as e:
            logger.error(f"Error processing message from {client_id}: {e}")
    
    async def _subscribe_client(self, client_id: str, topic: str):
        """Subscribe client to topic"""
        try:
            connection = self.connections[client_id]
            connection.subscriptions.add(topic)
            self.subscriptions[topic].add(client_id)
            
            logger.debug(f"Client {client_id} subscribed to {topic}")
            
        except Exception as e:
            logger.error(f"Error subscribing client {client_id} to {topic}: {e}")
    
    async def _unsubscribe_client(self, client_id: str, topic: str):
        """Unsubscribe client from topic"""
        try:
            connection = self.connections[client_id]
            connection.subscriptions.discard(topic)
            self.subscriptions[topic].discard(client_id)
            
            # Clean up empty topic
            if not self.subscriptions[topic]:
                del self.subscriptions[topic]
            
            logger.debug(f"Client {client_id} unsubscribed from {topic}")
            
        except Exception as e:
            logger.error(f"Error unsubscribing client {client_id} from {topic}: {e}")
    
    async def _send_message(self, client_id: str, message: Dict[str, Any]):
        """Send message to specific client"""
        try:
            if client_id not in self.connections:
                return
            
            connection = self.connections[client_id]
            await connection.websocket.send_text(json.dumps(message))
            
        except Exception as e:
            logger.error(f"Error sending message to {client_id}: {e}")
            # Remove broken connection
            await self._remove_connection(client_id)
    
    async def _broadcast_to_subscribers(self, topic: str, message: Dict[str, Any]):
        """Broadcast message to all subscribers of a topic"""
        try:
            if topic not in self.subscriptions:
                return
            
            # Send to all subscribers
            tasks = []
            for client_id in list(self.subscriptions[topic]):
                tasks.append(self._send_message(client_id, message))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
                
        except Exception as e:
            logger.error(f"Error broadcasting to topic {topic}: {e}")
    
    async def _remove_connection(self, client_id: str):
        """Remove connection and clean up"""
        try:
            if client_id not in self.connections:
                return
            
            connection = self.connections[client_id]
            
            # Remove from subscriptions
            for topic in list(connection.subscriptions):
                await self._unsubscribe_client(client_id, topic)
            
            # Remove from connection tracking
            self.connections_by_type[connection.connection_type].discard(client_id)
            del self.connections[client_id]
            
            # Clean up message counts
            if client_id in self.message_counts:
                del self.message_counts[client_id]
            
            logger.debug(f"Removed connection {client_id}")
            
        except Exception as e:
            logger.error(f"Error removing connection {client_id}: {e}")
    
    async def _check_rate_limit(self, client_id: str) -> bool:
        """Check if client is within rate limit"""
        try:
            current_time = time.time()
            
            # Clean old entries
            if client_id in self.message_counts:
                self.message_counts[client_id] = [
                    timestamp for timestamp in self.message_counts[client_id]
                    if current_time - timestamp < 60  # Last minute
                ]
            else:
                self.message_counts[client_id] = []
            
            # Check rate limit
            if len(self.message_counts[client_id]) >= self.message_rate_limit:
                return False
            
            # Add current message
            self.message_counts[client_id].append(current_time)
            return True
            
        except Exception as e:
            logger.error(f"Error checking rate limit for {client_id}: {e}")
            return True  # Allow on error
    
    async def _ping_loop(self):
        """Background task to ping connections"""
        while self.running:
            try:
                current_time = datetime.now()
                
                # Ping all connections
                for client_id, connection in list(self.connections.items()):
                    try:
                        await self._send_message(client_id, {"type": "ping"})
                    except Exception as e:
                        logger.warning(f"Failed to ping {client_id}: {e}")
                
                await asyncio.sleep(self.ping_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in ping loop: {e}")
                await asyncio.sleep(self.ping_interval)
    
    async def _cleanup_loop(self):
        """Background task to clean up stale connections"""
        while self.running:
            try:
                current_time = datetime.now()
                stale_connections = []
                
                # Find stale connections
                for client_id, connection in self.connections.items():
                    time_since_ping = (current_time - connection.last_ping).total_seconds()
                    if time_since_ping > self.connection_timeout:
                        stale_connections.append(client_id)
                
                # Remove stale connections
                for client_id in stale_connections:
                    logger.info(f"Removing stale connection: {client_id}")
                    await self._remove_connection(client_id)
                
                await asyncio.sleep(60)  # Check every minute
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(60)
    
    async def _close_all_connections(self):
        """Close all WebSocket connections"""
        try:
            tasks = []
            
            for client_id, connection in list(self.connections.items()):
                try:
                    tasks.append(connection.websocket.close())
                except Exception as e:
                    logger.warning(f"Error closing connection {client_id}: {e}")
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
            
            # Clear all data
            self.connections.clear()
            self.connections_by_type.clear()
            self.subscriptions.clear()
            self.message_counts.clear()
            
        except Exception as e:
            logger.error(f"Error closing all connections: {e}")

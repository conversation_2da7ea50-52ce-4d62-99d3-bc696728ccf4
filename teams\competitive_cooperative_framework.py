"""
Competitive and Cooperative Framework - Advanced team interaction modes
"""

import asyncio
import logging
import time
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class InteractionMode(Enum):
    """Team interaction modes"""
    COMPETITIVE = "competitive"
    COOPERATIVE = "cooperative"
    BALANCED = "balanced"
    HYBRID = "hybrid"


class CompetitionType(Enum):
    """Types of competition"""
    PERFORMANCE_BASED = "performance_based"
    INNOVATION_TOURNAMENT = "innovation_tournament"
    STRATEGY_BATTLE = "strategy_battle"
    RESOURCE_AUCTION = "resource_auction"
    PREDICTION_MARKET = "prediction_market"


class CooperationType(Enum):
    """Types of cooperation"""
    KNOWLEDGE_SHARING = "knowledge_sharing"
    JOINT_STRATEGY = "joint_strategy"
    RESOURCE_POOLING = "resource_pooling"
    COLLABORATIVE_LEARNING = "collaborative_learning"
    CONSENSUS_BUILDING = "consensus_building"


@dataclass
class CompetitionEvent:
    """Competition event data"""
    event_id: str
    competition_type: CompetitionType
    participants: List[str]  # team_ids
    rules: Dict[str, Any]
    start_time: float
    end_time: float
    status: str  # 'scheduled', 'active', 'completed', 'cancelled'
    results: Optional[Dict[str, Any]] = None
    rewards: Optional[Dict[str, float]] = None


@dataclass
class CooperationEvent:
    """Cooperation event data"""
    event_id: str
    cooperation_type: CooperationType
    participants: List[str]  # team_ids
    objective: str
    start_time: float
    end_time: Optional[float]
    status: str  # 'active', 'completed', 'paused'
    shared_resources: Dict[str, Any]
    contributions: Dict[str, Any]  # team_id -> contribution
    outcomes: Optional[Dict[str, Any]] = None


@dataclass
class ResourceAllocation:
    """Resource allocation data"""
    team_id: str
    resource_type: str
    allocated_amount: float
    priority_level: int
    allocation_time: float
    expiry_time: Optional[float]
    performance_multiplier: float


@dataclass
class PerformanceCredit:
    """Performance credit system"""
    team_id: str
    credit_amount: float
    earned_from: str  # source of credits
    timestamp: float
    category: str  # 'trading', 'innovation', 'cooperation', 'efficiency'


class CompetitiveCooperativeFramework:
    """
    Advanced framework for managing competitive and cooperative interactions
    between agent teams with dynamic mode switching and resource allocation.
    """
    
    def __init__(self, team_manager, config: Dict[str, Any]):
        self.team_manager = team_manager
        self.config = config
        self.framework_config = config.get('competitive_cooperative_framework', {})
        
        # Current mode and state
        self.current_mode = InteractionMode.BALANCED
        self.mode_history: List[Tuple[float, InteractionMode]] = []
        
        # Competition management
        self.active_competitions: Dict[str, CompetitionEvent] = {}
        self.competition_history: List[CompetitionEvent] = []
        self.competition_queue: deque = deque()
        
        # Cooperation management
        self.active_cooperations: Dict[str, CooperationEvent] = {}
        self.cooperation_history: List[CooperationEvent] = []
        
        # Resource allocation
        self.resource_allocations: Dict[str, List[ResourceAllocation]] = defaultdict(list)
        self.resource_pools: Dict[str, float] = {}
        self.allocation_history: List[ResourceAllocation] = []
        
        # Performance tracking
        self.performance_credits: Dict[str, List[PerformanceCredit]] = defaultdict(list)
        self.team_rankings: Dict[str, Dict[str, float]] = {}
        self.performance_metrics: Dict[str, Dict[str, Any]] = {}
        
        # Internal prediction markets
        self.prediction_markets: Dict[str, Dict[str, Any]] = {}
        self.market_positions: Dict[str, Dict[str, float]] = defaultdict(dict)
        
        # Mode switching parameters
        self.mode_switching_params = self.framework_config.get('mode_switching', {
            'performance_threshold': 0.7,
            'volatility_threshold': 0.3,
            'cooperation_benefit_threshold': 0.15,
            'competition_benefit_threshold': 0.20,
            'evaluation_window': 3600  # 1 hour
        })
        
        # State
        self.initialized = False
        self.running = False
        
        # Background tasks
        self.framework_tasks: List[asyncio.Task] = []
        
    async def initialize(self) -> bool:
        """Initialize the competitive-cooperative framework"""
        try:
            logger.info("Initializing Competitive-Cooperative Framework...")
            
            # Setup resource pools
            await self._setup_resource_pools()
            
            # Setup competition rules
            await self._setup_competition_rules()
            
            # Setup cooperation protocols
            await self._setup_cooperation_protocols()
            
            # Setup performance tracking
            await self._setup_performance_tracking()
            
            # Setup prediction markets
            await self._setup_prediction_markets()
            
            # Initialize mode switching
            await self._setup_mode_switching()
            
            self.initialized = True
            logger.info("✅ Competitive-Cooperative Framework initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Competitive-Cooperative Framework: {e}")
            return False
            
    async def start(self) -> bool:
        """Start the framework"""
        try:
            if not self.initialized:
                await self.initialize()
                
            if self.running:
                return True
                
            logger.info("Starting Competitive-Cooperative Framework...")
            
            # Start background tasks
            self.framework_tasks = [
                asyncio.create_task(self._mode_monitoring_loop()),
                asyncio.create_task(self._competition_management_loop()),
                asyncio.create_task(self._cooperation_management_loop()),
                asyncio.create_task(self._resource_allocation_loop()),
                asyncio.create_task(self._performance_tracking_loop()),
                asyncio.create_task(self._prediction_market_loop())
            ]
            
            self.running = True
            logger.info("✅ Competitive-Cooperative Framework started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Competitive-Cooperative Framework: {e}")
            return False
            
    async def stop(self) -> bool:
        """Stop the framework"""
        try:
            if not self.running:
                return True
                
            logger.info("Stopping Competitive-Cooperative Framework...")
            
            # Cancel background tasks
            for task in self.framework_tasks:
                task.cancel()
            await asyncio.gather(*self.framework_tasks, return_exceptions=True)
            self.framework_tasks.clear()
            
            self.running = False
            logger.info("✅ Competitive-Cooperative Framework stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop Competitive-Cooperative Framework: {e}")
            return False
            
    async def switch_mode(self, new_mode: InteractionMode, reason: str = "manual") -> bool:
        """Switch interaction mode"""
        try:
            if new_mode == self.current_mode:
                return True
                
            logger.info(f"Switching mode from {self.current_mode.value} to {new_mode.value} (reason: {reason})")
            
            # Record mode change
            self.mode_history.append((time.time(), self.current_mode))
            
            # Execute mode transition
            transition_success = await self._execute_mode_transition(self.current_mode, new_mode)
            
            if transition_success:
                self.current_mode = new_mode
                
                # Notify teams of mode change
                await self._notify_teams_mode_change(new_mode, reason)
                
                # Adjust resource allocations
                await self._adjust_resource_allocations_for_mode(new_mode)
                
                logger.info(f"✅ Mode switched to {new_mode.value}")
                return True
            else:
                logger.warning(f"Failed to execute mode transition to {new_mode.value}")
                return False
                
        except Exception as e:
            logger.error(f"Error switching mode to {new_mode.value}: {e}")
            return False
            
    async def create_competition(self, competition_type: CompetitionType, 
                               participants: List[str], 
                               rules: Dict[str, Any],
                               duration: float = 3600) -> str:
        """Create a new competition"""
        try:
            event_id = f"comp_{int(time.time())}_{competition_type.value}"
            
            competition = CompetitionEvent(
                event_id=event_id,
                competition_type=competition_type,
                participants=participants,
                rules=rules,
                start_time=time.time(),
                end_time=time.time() + duration,
                status='scheduled'
            )
            
            self.active_competitions[event_id] = competition
            
            # Schedule competition start
            await self._schedule_competition_start(competition)
            
            logger.info(f"Created competition {event_id} with {len(participants)} participants")
            return event_id
            
        except Exception as e:
            logger.error(f"Error creating competition: {e}")
            return ""
            
    async def create_cooperation(self, cooperation_type: CooperationType,
                               participants: List[str],
                               objective: str,
                               shared_resources: Dict[str, Any]) -> str:
        """Create a new cooperation"""
        try:
            event_id = f"coop_{int(time.time())}_{cooperation_type.value}"
            
            cooperation = CooperationEvent(
                event_id=event_id,
                cooperation_type=cooperation_type,
                participants=participants,
                objective=objective,
                start_time=time.time(),
                end_time=None,
                status='active',
                shared_resources=shared_resources,
                contributions={}
            )
            
            self.active_cooperations[event_id] = cooperation
            
            # Initialize cooperation
            await self._initialize_cooperation(cooperation)
            
            logger.info(f"Created cooperation {event_id} with {len(participants)} participants")
            return event_id
            
        except Exception as e:
            logger.error(f"Error creating cooperation: {e}")
            return ""
            
    async def allocate_resources(self, team_id: str, resource_type: str, 
                               amount: float, priority: int = 1) -> bool:
        """Allocate resources to a team"""
        try:
            # Check if team has sufficient credits
            team_credits = await self._get_team_credits(team_id)
            resource_cost = await self._calculate_resource_cost(resource_type, amount)
            
            if team_credits < resource_cost:
                logger.warning(f"Team {team_id} has insufficient credits for resource allocation")
                return False
                
            # Get performance multiplier
            performance_multiplier = await self._get_performance_multiplier(team_id)
            
            # Create allocation
            allocation = ResourceAllocation(
                team_id=team_id,
                resource_type=resource_type,
                allocated_amount=amount * performance_multiplier,
                priority_level=priority,
                allocation_time=time.time(),
                expiry_time=time.time() + 3600,  # 1 hour default
                performance_multiplier=performance_multiplier
            )
            
            self.resource_allocations[team_id].append(allocation)
            self.allocation_history.append(allocation)
            
            # Deduct credits
            await self._deduct_team_credits(team_id, resource_cost)
            
            logger.info(f"Allocated {amount} {resource_type} to team {team_id} (multiplier: {performance_multiplier:.2f})")
            return True
            
        except Exception as e:
            logger.error(f"Error allocating resources to team {team_id}: {e}")
            return False
            
    async def award_performance_credits(self, team_id: str, amount: float, 
                                      category: str, source: str) -> bool:
        """Award performance credits to a team"""
        try:
            credit = PerformanceCredit(
                team_id=team_id,
                credit_amount=amount,
                earned_from=source,
                timestamp=time.time(),
                category=category
            )
            
            self.performance_credits[team_id].append(credit)
            
            logger.info(f"Awarded {amount} credits to team {team_id} for {category} ({source})")
            return True
            
        except Exception as e:
            logger.error(f"Error awarding credits to team {team_id}: {e}")
            return False
            
    async def get_framework_status(self) -> Dict[str, Any]:
        """Get framework status"""
        try:
            return {
                'running': self.running,
                'current_mode': self.current_mode.value,
                'active_competitions': len(self.active_competitions),
                'active_cooperations': len(self.active_cooperations),
                'total_teams': len(self.performance_credits),
                'resource_pools': self.resource_pools.copy(),
                'prediction_markets': len(self.prediction_markets),
                'mode_history': [(t, m.value) for t, m in self.mode_history[-10:]],
                'performance_metrics': {
                    'total_competitions': len(self.competition_history),
                    'total_cooperations': len(self.cooperation_history),
                    'total_allocations': len(self.allocation_history)
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting framework status: {e}")
            return {'error': str(e)}
            
    # Private methods for setup and management
    async def _setup_resource_pools(self):
        """Setup resource pools"""
        self.resource_pools = {
            'computational_power': 1000.0,
            'memory_allocation': 500.0,
            'data_access_priority': 100.0,
            'execution_priority': 100.0,
            'model_access': 50.0
        }
        
    async def _setup_competition_rules(self):
        """Setup competition rules"""
        self.competition_rules = {
            CompetitionType.PERFORMANCE_BASED: {
                'evaluation_metrics': ['returns', 'sharpe_ratio', 'max_drawdown'],
                'evaluation_period': 3600,  # 1 hour
                'winner_selection': 'highest_score',
                'rewards': {'winner': 100, 'runner_up': 50, 'participant': 10}
            },
            CompetitionType.INNOVATION_TOURNAMENT: {
                'evaluation_metrics': ['novelty', 'effectiveness', 'efficiency'],
                'evaluation_period': 7200,  # 2 hours
                'winner_selection': 'innovation_score',
                'rewards': {'winner': 200, 'runner_up': 100, 'participant': 25}
            },
            CompetitionType.STRATEGY_BATTLE: {
                'evaluation_metrics': ['head_to_head_performance'],
                'evaluation_period': 1800,  # 30 minutes
                'winner_selection': 'direct_comparison',
                'rewards': {'winner': 75, 'loser': 15}
            }
        }
        
    async def _setup_cooperation_protocols(self):
        """Setup cooperation protocols"""
        self.cooperation_protocols = {
            CooperationType.KNOWLEDGE_SHARING: {
                'sharing_frequency': 300,  # 5 minutes
                'knowledge_validation': True,
                'contribution_tracking': True,
                'rewards': 'proportional_to_contribution'
            },
            CooperationType.JOINT_STRATEGY: {
                'consensus_threshold': 0.7,
                'decision_timeout': 600,  # 10 minutes
                'profit_sharing': 'equal',
                'risk_sharing': 'proportional'
            },
            CooperationType.RESOURCE_POOLING: {
                'pooling_ratio': 0.3,  # 30% of resources
                'allocation_method': 'need_based',
                'efficiency_bonus': 1.2
            }
        }
        
    async def _setup_performance_tracking(self):
        """Setup performance tracking"""
        self.performance_tracking = {
            'metrics': ['trading_performance', 'innovation_score', 'cooperation_score', 'efficiency_rating'],
            'update_frequency': 300,  # 5 minutes
            'ranking_algorithm': 'weighted_average',
            'weights': {'trading': 0.4, 'innovation': 0.3, 'cooperation': 0.2, 'efficiency': 0.1}
        }
        
    async def _setup_prediction_markets(self):
        """Setup internal prediction markets"""
        self.prediction_markets = {
            'market_direction': {
                'options': ['bullish', 'bearish', 'neutral'],
                'current_prices': {'bullish': 0.4, 'bearish': 0.3, 'neutral': 0.3},
                'total_volume': 0.0,
                'resolution_time': time.time() + 86400  # 24 hours
            },
            'volatility_forecast': {
                'options': ['low', 'medium', 'high'],
                'current_prices': {'low': 0.3, 'medium': 0.5, 'high': 0.2},
                'total_volume': 0.0,
                'resolution_time': time.time() + 43200  # 12 hours
            }
        }
        
    async def _setup_mode_switching(self):
        """Setup mode switching logic"""
        self.mode_switching_logic = {
            'triggers': {
                'high_volatility': InteractionMode.COOPERATIVE,
                'low_performance': InteractionMode.COMPETITIVE,
                'innovation_needed': InteractionMode.COMPETITIVE,
                'stability_needed': InteractionMode.COOPERATIVE,
                'balanced_conditions': InteractionMode.BALANCED
            },
            'transition_delays': {
                (InteractionMode.COMPETITIVE, InteractionMode.COOPERATIVE): 300,
                (InteractionMode.COOPERATIVE, InteractionMode.COMPETITIVE): 600,
                (InteractionMode.BALANCED, InteractionMode.COMPETITIVE): 180,
                (InteractionMode.BALANCED, InteractionMode.COOPERATIVE): 180
            }
        }
        
    # Background task methods
    async def _mode_monitoring_loop(self):
        """Background mode monitoring loop"""
        while self.running:
            try:
                await asyncio.sleep(60)  # Check every minute
                
                # Evaluate current conditions
                conditions = await self._evaluate_current_conditions()
                
                # Determine optimal mode
                optimal_mode = await self._determine_optimal_mode(conditions)
                
                # Switch mode if needed
                if optimal_mode != self.current_mode:
                    await self.switch_mode(optimal_mode, "automatic_optimization")
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in mode monitoring loop: {e}")
                
    async def _competition_management_loop(self):
        """Background competition management loop"""
        while self.running:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                
                # Process competition queue
                await self._process_competition_queue()
                
                # Update active competitions
                await self._update_active_competitions()
                
                # Check for completed competitions
                await self._check_completed_competitions()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in competition management loop: {e}")
                
    async def _cooperation_management_loop(self):
        """Background cooperation management loop"""
        while self.running:
            try:
                await asyncio.sleep(45)  # Check every 45 seconds
                
                # Update active cooperations
                await self._update_active_cooperations()
                
                # Check cooperation progress
                await self._check_cooperation_progress()
                
                # Facilitate knowledge sharing
                await self._facilitate_knowledge_sharing()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cooperation management loop: {e}")
                
    async def _resource_allocation_loop(self):
        """Background resource allocation loop"""
        while self.running:
            try:
                await asyncio.sleep(120)  # Check every 2 minutes
                
                # Update resource pools
                await self._update_resource_pools()
                
                # Process allocation requests
                await self._process_allocation_requests()
                
                # Clean up expired allocations
                await self._cleanup_expired_allocations()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in resource allocation loop: {e}")
                
    async def _performance_tracking_loop(self):
        """Background performance tracking loop"""
        while self.running:
            try:
                await asyncio.sleep(300)  # Check every 5 minutes
                
                # Update performance metrics
                await self._update_performance_metrics()
                
                # Update team rankings
                await self._update_team_rankings()
                
                # Award automatic credits
                await self._award_automatic_credits()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in performance tracking loop: {e}")
                
    async def _prediction_market_loop(self):
        """Background prediction market loop"""
        while self.running:
            try:
                await asyncio.sleep(180)  # Check every 3 minutes
                
                # Update market prices
                await self._update_market_prices()
                
                # Process market orders
                await self._process_market_orders()
                
                # Check market resolutions
                await self._check_market_resolutions()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in prediction market loop: {e}")
                
    # Helper methods (simplified implementations)
    async def _execute_mode_transition(self, old_mode: InteractionMode, new_mode: InteractionMode) -> bool:
        """Execute mode transition"""
        # Simplified implementation
        return True
        
    async def _notify_teams_mode_change(self, new_mode: InteractionMode, reason: str):
        """Notify teams of mode change"""
        # Simplified implementation
        pass
        
    async def _adjust_resource_allocations_for_mode(self, mode: InteractionMode):
        """Adjust resource allocations for new mode"""
        # Simplified implementation
        pass
        
    async def _evaluate_current_conditions(self) -> Dict[str, Any]:
        """Evaluate current market and system conditions"""
        # Simplified implementation
        return {
            'market_volatility': np.random.uniform(0.1, 0.5),
            'system_performance': np.random.uniform(0.6, 0.9),
            'innovation_pressure': np.random.uniform(0.2, 0.8),
            'cooperation_benefit': np.random.uniform(0.1, 0.6)
        }
        
    async def _determine_optimal_mode(self, conditions: Dict[str, Any]) -> InteractionMode:
        """Determine optimal interaction mode"""
        volatility = conditions.get('market_volatility', 0.3)
        performance = conditions.get('system_performance', 0.7)
        innovation = conditions.get('innovation_pressure', 0.5)
        cooperation = conditions.get('cooperation_benefit', 0.3)
        
        if volatility > 0.4 or cooperation > 0.4:
            return InteractionMode.COOPERATIVE
        elif performance < 0.7 or innovation > 0.6:
            return InteractionMode.COMPETITIVE
        else:
            return InteractionMode.BALANCED
            
    async def _get_team_credits(self, team_id: str) -> float:
        """Get total credits for a team"""
        credits = self.performance_credits.get(team_id, [])
        return sum(credit.credit_amount for credit in credits)
        
    async def _calculate_resource_cost(self, resource_type: str, amount: float) -> float:
        """Calculate cost of resource allocation"""
        base_costs = {
            'computational_power': 1.0,
            'memory_allocation': 0.5,
            'data_access_priority': 2.0,
            'execution_priority': 3.0,
            'model_access': 5.0
        }
        return base_costs.get(resource_type, 1.0) * amount
        
    async def _get_performance_multiplier(self, team_id: str) -> float:
        """Get performance multiplier for a team"""
        # Simplified implementation based on recent performance
        return np.random.uniform(0.8, 1.5)
        
    async def _deduct_team_credits(self, team_id: str, amount: float):
        """Deduct credits from a team"""
        # Simplified implementation - would track debits
        pass
        
    # Placeholder implementations for other helper methods
    async def _schedule_competition_start(self, competition: CompetitionEvent):
        """Schedule competition start"""
        pass
        
    async def _initialize_cooperation(self, cooperation: CooperationEvent):
        """Initialize cooperation"""
        pass
        
    async def _process_competition_queue(self):
        """Process competition queue"""
        pass
        
    async def _update_active_competitions(self):
        """Update active competitions"""
        pass
        
    async def _check_completed_competitions(self):
        """Check for completed competitions"""
        pass
        
    async def _update_active_cooperations(self):
        """Update active cooperations"""
        pass
        
    async def _check_cooperation_progress(self):
        """Check cooperation progress"""
        pass
        
    async def _facilitate_knowledge_sharing(self):
        """Facilitate knowledge sharing"""
        pass
        
    async def _update_resource_pools(self):
        """Update resource pools"""
        pass
        
    async def _process_allocation_requests(self):
        """Process allocation requests"""
        pass
        
    async def _cleanup_expired_allocations(self):
        """Clean up expired allocations"""
        current_time = time.time()
        for team_id in self.resource_allocations:
            self.resource_allocations[team_id] = [
                alloc for alloc in self.resource_allocations[team_id]
                if alloc.expiry_time is None or alloc.expiry_time > current_time
            ]
            
    async def _update_performance_metrics(self):
        """Update performance metrics"""
        pass
        
    async def _update_team_rankings(self):
        """Update team rankings"""
        pass
        
    async def _award_automatic_credits(self):
        """Award automatic credits"""
        pass
        
    async def _update_market_prices(self):
        """Update prediction market prices"""
        pass
        
    async def _process_market_orders(self):
        """Process prediction market orders"""
        pass
        
    async def _check_market_resolutions(self):
        """Check prediction market resolutions"""
        pass

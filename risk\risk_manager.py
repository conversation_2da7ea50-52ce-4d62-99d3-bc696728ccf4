"""
Risk Manager - Central risk management coordinator
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import numpy as np
import pandas as pd

from .position_sizer import PositionSizer
from .risk_metrics import RiskMetricsCalculator
from .risk_monitor import RealTimeRiskMonitor
from .compliance_monitor import ComplianceMonitor
from .stress_tester import StressTester
from .drawdown_protection import DrawdownProtection
from .risk_alerts import RiskAlertManager
from .var_calculator import VaRCalculator
from .correlation_monitor import CorrelationMonitor

logger = logging.getLogger(__name__)


@dataclass
class RiskAssessment:
    """Risk assessment result"""
    portfolio_var: float
    max_drawdown: float
    concentration_risk: float
    correlation_risk: float
    liquidity_risk: float
    overall_risk_score: float
    risk_level: str  # 'low', 'medium', 'high', 'critical'
    recommendations: List[str]
    timestamp: float


class RiskManager:
    """
    Central risk management system that coordinates all risk-related activities.
    
    Responsibilities:
    - Portfolio risk assessment and monitoring
    - Position sizing and risk allocation
    - Real-time risk monitoring and alerts
    - Compliance monitoring and reporting
    - Stress testing and scenario analysis
    - Drawdown protection and risk mitigation
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.risk_config = config.get('risk_management', {})
        
        # Risk components
        self.position_sizer: Optional[PositionSizer] = None
        self.risk_metrics: Optional[RiskMetricsCalculator] = None
        self.risk_monitor: Optional[RealTimeRiskMonitor] = None
        self.compliance_monitor: Optional[ComplianceMonitor] = None
        self.stress_tester: Optional[StressTester] = None
        self.drawdown_protection: Optional[DrawdownProtection] = None
        self.alert_manager: Optional[RiskAlertManager] = None
        self.var_calculator: Optional[VaRCalculator] = None
        self.correlation_monitor: Optional[CorrelationMonitor] = None
        
        # Risk limits from configuration
        self.global_limits = self.risk_config.get('global_limits', {})
        self.position_limits = self.risk_config.get('position_sizing', {})
        self.stop_loss_config = self.risk_config.get('stop_loss', {})
        
        # Current portfolio state
        self.current_portfolio: Dict[str, Any] = {}
        self.current_positions: Dict[str, Dict[str, Any]] = {}
        self.portfolio_value: float = 0.0
        
        # Risk state
        self.current_risk_assessment: Optional[RiskAssessment] = None
        self.risk_history: List[RiskAssessment] = []
        
        # State flags
        self.initialized = False
        self.running = False
        
        # Background tasks
        self.monitoring_task: Optional[asyncio.Task] = None
        self.assessment_task: Optional[asyncio.Task] = None
        
    async def initialize(self) -> bool:
        """Initialize the risk management system"""
        if self.initialized:
            return True
            
        try:
            logger.info("Initializing Risk Management System...")
            
            # Initialize risk components
            self.position_sizer = PositionSizer(self.config)
            await self.position_sizer.initialize()
            
            self.risk_metrics = RiskMetricsCalculator(self.config)
            await self.risk_metrics.initialize()
            
            self.risk_monitor = RealTimeRiskMonitor(self.config)
            await self.risk_monitor.initialize()
            
            self.compliance_monitor = ComplianceMonitor(self.config)
            await self.compliance_monitor.initialize()
            
            self.stress_tester = StressTester(self.config)
            await self.stress_tester.initialize()
            
            self.drawdown_protection = DrawdownProtection(self.config)
            await self.drawdown_protection.initialize()
            
            self.alert_manager = RiskAlertManager(self.config)
            await self.alert_manager.initialize()
            
            self.var_calculator = VaRCalculator(self.config)
            await self.var_calculator.initialize()
            
            self.correlation_monitor = CorrelationMonitor(self.config)
            await self.correlation_monitor.initialize()
            
            self.initialized = True
            logger.info("✓ Risk Management System initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Risk Management System: {e}")
            return False
    
    async def start(self) -> bool:
        """Start the risk management system"""
        if not self.initialized:
            if not await self.initialize():
                return False
                
        if self.running:
            return True
            
        try:
            logger.info("Starting Risk Management System...")
            
            # Start all components
            await asyncio.gather(
                self.risk_monitor.start(),
                self.compliance_monitor.start(),
                self.drawdown_protection.start(),
                self.alert_manager.start(),
                self.correlation_monitor.start()
            )
            
            # Start background tasks
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            self.assessment_task = asyncio.create_task(self._assessment_loop())
            
            self.running = True
            logger.info("✓ Risk Management System started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Risk Management System: {e}")
            return False

    async def set_integration_points(self,
                                   portfolio_manager=None,
                                   execution_engine=None,
                                   strategy_manager=None,
                                   market_data_manager=None):
        """Set integration points with other systems"""
        self.portfolio_manager = portfolio_manager
        self.execution_engine = execution_engine
        self.strategy_manager = strategy_manager
        self.market_data_manager = market_data_manager

        # Pass integration points to risk components
        if self.risk_monitor:
            if hasattr(self.risk_monitor, 'set_integration_points'):
                await self.risk_monitor.set_integration_points(
                    portfolio_manager=portfolio_manager,
                    execution_engine=execution_engine,
                    strategy_manager=strategy_manager
                )

        if self.compliance_monitor:
            if hasattr(self.compliance_monitor, 'set_integration_points'):
                await self.compliance_monitor.set_integration_points(
                    portfolio_manager=portfolio_manager,
                    execution_engine=execution_engine,
                    strategy_manager=strategy_manager
                )

        logger.info("Risk Manager integration points configured")

    async def set_database_coordinator(self, database_coordinator):
        """Set database coordinator for data persistence"""
        self.database_coordinator = database_coordinator

        # Pass database coordinator to risk components
        if self.risk_monitor:
            if hasattr(self.risk_monitor, 'set_database_coordinator'):
                await self.risk_monitor.set_database_coordinator(database_coordinator)

        if self.compliance_monitor:
            if hasattr(self.compliance_monitor, 'set_database_coordinator'):
                await self.compliance_monitor.set_database_coordinator(database_coordinator)

        logger.info("Risk Manager database coordinator configured")

    async def set_analytics_engine(self, analytics_engine):
        """Set analytics engine for advanced analytics"""
        self.analytics_engine = analytics_engine

        # Pass analytics engine to risk components
        if self.risk_metrics:
            if hasattr(self.risk_metrics, 'set_analytics_engine'):
                await self.risk_metrics.set_analytics_engine(analytics_engine)

        if self.position_sizer:
            if hasattr(self.position_sizer, 'set_analytics_engine'):
                await self.position_sizer.set_analytics_engine(analytics_engine)

        if self.risk_monitor:
            if hasattr(self.risk_monitor, 'set_analytics_engine'):
                await self.risk_monitor.set_analytics_engine(analytics_engine)

        if self.stress_tester:
            if hasattr(self.stress_tester, 'set_analytics_engine'):
                await self.stress_tester.set_analytics_engine(analytics_engine)

        if self.var_calculator:
            if hasattr(self.var_calculator, 'set_analytics_engine'):
                await self.var_calculator.set_analytics_engine(analytics_engine)

        if self.correlation_monitor:
            if hasattr(self.correlation_monitor, 'set_analytics_engine'):
                await self.correlation_monitor.set_analytics_engine(analytics_engine)

        logger.info("Risk Manager analytics engine configured")

    async def stop(self) -> bool:
        """Stop the risk management system"""
        if not self.running:
            return True
            
        try:
            logger.info("Stopping Risk Management System...")
            self.running = False
            
            # Cancel background tasks
            if self.monitoring_task:
                self.monitoring_task.cancel()
            if self.assessment_task:
                self.assessment_task.cancel()
            
            # Stop all components
            await asyncio.gather(
                self.risk_monitor.stop(),
                self.compliance_monitor.stop(),
                self.drawdown_protection.stop(),
                self.alert_manager.stop(),
                self.correlation_monitor.stop(),
                return_exceptions=True
            )
            
            logger.info("✓ Risk Management System stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping Risk Management System: {e}")
            return False
    
    async def assess_portfolio_risk(self, portfolio: Dict[str, Any]) -> RiskAssessment:
        """Comprehensive portfolio risk assessment"""
        try:
            # Normalize portfolio data format
            normalized_portfolio = self._normalize_portfolio_data(portfolio)
            self.current_portfolio = normalized_portfolio

            # Calculate various risk metrics
            var_metrics = await self.var_calculator.calculate_portfolio_var(normalized_portfolio)
            correlation_metrics = await self.correlation_monitor.analyze_correlations(normalized_portfolio)
            concentration_metrics = await self.risk_metrics.calculate_concentration_risk(normalized_portfolio)
            liquidity_metrics = await self.risk_metrics.calculate_liquidity_risk(normalized_portfolio)
            
            # Calculate overall risk score
            portfolio_var = var_metrics.get('var_95', 0.0)
            max_drawdown = await self._calculate_current_drawdown(portfolio)
            concentration_risk = concentration_metrics.get('herfindahl_index', 0.0)
            correlation_risk = correlation_metrics.get('max_correlation', 0.0)
            liquidity_risk = liquidity_metrics.get('liquidity_score', 0.0)
            
            # Weighted risk score
            overall_risk_score = (
                portfolio_var * 0.3 +
                max_drawdown * 0.25 +
                concentration_risk * 0.2 +
                correlation_risk * 0.15 +
                liquidity_risk * 0.1
            )
            
            # Determine risk level
            if overall_risk_score < 0.02:
                risk_level = 'low'
            elif overall_risk_score < 0.05:
                risk_level = 'medium'
            elif overall_risk_score < 0.10:
                risk_level = 'high'
            else:
                risk_level = 'critical'
            
            # Generate recommendations
            recommendations = await self._generate_risk_recommendations(
                portfolio_var, max_drawdown, concentration_risk, correlation_risk, liquidity_risk
            )
            
            # Create risk assessment
            assessment = RiskAssessment(
                portfolio_var=portfolio_var,
                max_drawdown=max_drawdown,
                concentration_risk=concentration_risk,
                correlation_risk=correlation_risk,
                liquidity_risk=liquidity_risk,
                overall_risk_score=overall_risk_score,
                risk_level=risk_level,
                recommendations=recommendations,
                timestamp=time.time()
            )
            
            # Store assessment
            self.current_risk_assessment = assessment
            self.risk_history.append(assessment)
            
            # Limit history size
            if len(self.risk_history) > 1000:
                self.risk_history = self.risk_history[-1000:]
            
            # Check for violations and generate alerts
            await self._check_risk_violations(assessment)
            
            return assessment
            
        except Exception as e:
            logger.error(f"Error assessing portfolio risk: {e}")
            # Return default assessment
            return RiskAssessment(
                portfolio_var=0.0,
                max_drawdown=0.0,
                concentration_risk=0.0,
                correlation_risk=0.0,
                liquidity_risk=0.0,
                overall_risk_score=0.0,
                risk_level='unknown',
                recommendations=[],
                timestamp=time.time()
            )
    
    async def calculate_position_size(self, 
                                    symbol: str,
                                    signal_strength: float,
                                    expected_return: float,
                                    expected_risk: float,
                                    portfolio_value: float) -> Dict[str, Any]:
        """Calculate optimal position size for a trade"""
        try:
            return await self.position_sizer.calculate_position_size(
                symbol=symbol,
                signal_strength=signal_strength,
                expected_return=expected_return,
                expected_risk=expected_risk,
                portfolio_value=portfolio_value,
                current_portfolio=self.current_portfolio
            )
            
        except Exception as e:
            logger.error(f"Error calculating position size for {symbol}: {e}")
            return {
                'position_size': 0.0,
                'max_position_size': 0.0,
                'risk_adjusted_size': 0.0,
                'sizing_method': 'error',
                'error': str(e)
            }
    
    async def validate_trade(self, trade_request: Dict[str, Any]) -> Dict[str, Any]:
        """Validate a trade request against risk limits"""
        try:
            symbol = trade_request.get('symbol')
            size = trade_request.get('size', 0)
            side = trade_request.get('side', 'buy')
            price = trade_request.get('price', 0)
            
            validation_result = {
                'approved': False,
                'violations': [],
                'warnings': [],
                'adjusted_size': size,
                'risk_metrics': {}
            }
            
            # Check position size limits
            max_position_size = self.global_limits.get('max_portfolio_risk', 0.02) * self.portfolio_value
            if abs(size * price) > max_position_size:
                validation_result['violations'].append({
                    'type': 'position_size_limit',
                    'current': abs(size * price),
                    'limit': max_position_size
                })
            
            # Check concentration limits
            current_exposure = self.current_positions.get(symbol, {}).get('exposure', 0)
            new_exposure = current_exposure + (size * price if side == 'buy' else -size * price)
            max_concentration = self.global_limits.get('max_portfolio_risk', 0.02) * self.portfolio_value
            
            if abs(new_exposure) > max_concentration:
                validation_result['violations'].append({
                    'type': 'concentration_limit',
                    'current': abs(new_exposure),
                    'limit': max_concentration
                })
            
            # Check daily loss limits
            daily_pnl = await self._calculate_daily_pnl()
            max_daily_loss = self.global_limits.get('max_daily_loss', 0.05) * self.portfolio_value
            
            if daily_pnl < -max_daily_loss:
                validation_result['violations'].append({
                    'type': 'daily_loss_limit',
                    'current': abs(daily_pnl),
                    'limit': max_daily_loss
                })
            
            # Check drawdown limits
            current_drawdown = await self._calculate_current_drawdown(self.current_portfolio)
            max_drawdown = self.global_limits.get('max_drawdown', 0.15)
            
            if current_drawdown > max_drawdown:
                validation_result['violations'].append({
                    'type': 'drawdown_limit',
                    'current': current_drawdown,
                    'limit': max_drawdown
                })
            
            # Approve trade if no violations
            validation_result['approved'] = len(validation_result['violations']) == 0
            
            # Calculate risk metrics for the trade
            validation_result['risk_metrics'] = {
                'position_risk': abs(size * price) / self.portfolio_value,
                'portfolio_impact': new_exposure / self.portfolio_value,
                'estimated_var_impact': await self._estimate_var_impact(symbol, size, price)
            }
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error validating trade: {e}")
            return {
                'approved': False,
                'violations': [{'type': 'validation_error', 'error': str(e)}],
                'warnings': [],
                'adjusted_size': 0,
                'risk_metrics': {}
            }
    
    async def update_portfolio(self, portfolio: Dict[str, Any]) -> None:
        """Update current portfolio state"""
        try:
            self.current_portfolio = portfolio
            self.portfolio_value = portfolio.get('total_value', 0.0)
            
            # Update positions
            positions = portfolio.get('positions', {})
            self.current_positions = positions
            
            # Update risk monitors
            await self.risk_monitor.update_portfolio(portfolio)
            await self.correlation_monitor.update_portfolio(portfolio)
            await self.drawdown_protection.update_portfolio(portfolio)
            
        except Exception as e:
            logger.error(f"Error updating portfolio: {e}")

    async def check_risk_limits(self, portfolio: Dict[str, Any], limits: Dict[str, float]) -> List[Dict[str, Any]]:
        """Check portfolio against risk limits and return violations"""
        try:
            violations = []

            # Get current risk assessment
            risk_assessment = await self.assess_portfolio_risk(portfolio)

            # Check portfolio VaR limit
            max_portfolio_risk = limits.get('max_portfolio_risk', 0.02)
            if risk_assessment.portfolio_var > max_portfolio_risk:
                violations.append({
                    'type': 'portfolio_var_breach',
                    'metric': 'Portfolio VaR',
                    'current_value': risk_assessment.portfolio_var,
                    'limit': max_portfolio_risk,
                    'severity': 'high',
                    'description': f'Portfolio VaR ({risk_assessment.portfolio_var:.3f}) exceeds limit ({max_portfolio_risk:.3f})'
                })

            # Check position size limits
            max_position_size = limits.get('max_position_size', 0.1)
            total_value = portfolio.get('total_value', 0)

            if total_value > 0:
                positions = portfolio.get('positions', [])
                for position in positions:
                    if isinstance(position, dict):
                        position_value = position.get('value', 0)
                        position_weight = position_value / total_value

                        if position_weight > max_position_size:
                            violations.append({
                                'type': 'position_size_breach',
                                'metric': 'Position Size',
                                'symbol': position.get('symbol', 'Unknown'),
                                'current_value': position_weight,
                                'limit': max_position_size,
                                'severity': 'medium',
                                'description': f'Position {position.get("symbol", "Unknown")} ({position_weight:.3f}) exceeds size limit ({max_position_size:.3f})'
                            })

            # Check sector exposure limits
            max_sector_exposure = limits.get('max_sector_exposure', 0.3)
            # For now, assume all positions are in tech sector (simplified)
            tech_exposure = sum(pos.get('value', 0) for pos in portfolio.get('positions', []) if isinstance(pos, dict))
            tech_weight = tech_exposure / total_value if total_value > 0 else 0

            if tech_weight > max_sector_exposure:
                violations.append({
                    'type': 'sector_exposure_breach',
                    'metric': 'Sector Exposure',
                    'sector': 'Technology',
                    'current_value': tech_weight,
                    'limit': max_sector_exposure,
                    'severity': 'medium',
                    'description': f'Technology sector exposure ({tech_weight:.3f}) exceeds limit ({max_sector_exposure:.3f})'
                })

            # Check concentration risk
            if risk_assessment.concentration_risk > 0.5:  # 50% concentration threshold
                violations.append({
                    'type': 'concentration_risk_breach',
                    'metric': 'Concentration Risk',
                    'current_value': risk_assessment.concentration_risk,
                    'limit': 0.5,
                    'severity': 'high',
                    'description': f'Portfolio concentration risk ({risk_assessment.concentration_risk:.3f}) is too high'
                })

            # Log violations
            if violations:
                logger.warning(f"Risk limit violations detected: {len(violations)} violations")
                for violation in violations:
                    logger.warning(f"  - {violation['description']}")
            else:
                logger.info("All risk limits are within acceptable ranges")

            return violations

        except Exception as e:
            logger.error(f"Error checking risk limits: {e}")
            return [{
                'type': 'system_error',
                'metric': 'Risk Limit Check',
                'error': str(e),
                'severity': 'critical',
                'description': f'Error checking risk limits: {e}'
            }]

    async def get_risk_status(self) -> Dict[str, Any]:
        """Get current risk status"""
        try:
            status = {
                'current_assessment': self.current_risk_assessment.__dict__ if self.current_risk_assessment else None,
                'portfolio_value': self.portfolio_value,
                'active_positions': len(self.current_positions),
                'risk_limits': self.global_limits,
                'compliance_status': await self.compliance_monitor.get_status() if self.compliance_monitor else {},
                'alerts': await self.alert_manager.get_active_alerts() if self.alert_manager else [],
                'last_stress_test': await self.stress_tester.get_last_results() if self.stress_tester else {}
            }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting risk status: {e}")
            return {}
    
    # Private methods
    
    async def _monitoring_loop(self):
        """Background monitoring loop"""
        while self.running:
            try:
                await asyncio.sleep(60)  # Monitor every minute
                
                if self.current_portfolio:
                    # Update risk monitors
                    await self.risk_monitor.check_limits(self.current_portfolio)
                    await self.compliance_monitor.check_compliance(self.current_portfolio)
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
    
    async def _assessment_loop(self):
        """Background risk assessment loop"""
        while self.running:
            try:
                await asyncio.sleep(300)  # Assess every 5 minutes
                
                if self.current_portfolio:
                    await self.assess_portfolio_risk(self.current_portfolio)
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in assessment loop: {e}")
    
    async def _calculate_current_drawdown(self, portfolio: Dict[str, Any]) -> float:
        """Calculate current portfolio drawdown"""
        try:
            # This would typically use historical portfolio values
            # For now, return a placeholder calculation
            current_value = portfolio.get('total_value', 0)
            peak_value = portfolio.get('peak_value', current_value)
            
            if peak_value > 0:
                return max(0, (peak_value - current_value) / peak_value)
            return 0.0
            
        except Exception as e:
            logger.error(f"Error calculating drawdown: {e}")
            return 0.0
    
    async def _calculate_daily_pnl(self) -> float:
        """Calculate daily P&L"""
        try:
            # This would typically use position-level P&L tracking
            # For now, return a placeholder
            return 0.0
            
        except Exception as e:
            logger.error(f"Error calculating daily P&L: {e}")
            return 0.0
    
    async def _estimate_var_impact(self, symbol: str, size: float, price: float) -> float:
        """Estimate VaR impact of a new position"""
        try:
            # Simplified VaR impact calculation
            position_value = abs(size * price)
            portfolio_value = self.portfolio_value
            
            if portfolio_value > 0:
                return position_value / portfolio_value * 0.02  # Rough estimate
            return 0.0
            
        except Exception as e:
            logger.error(f"Error estimating VaR impact: {e}")
            return 0.0
    
    async def _generate_risk_recommendations(self, 
                                           portfolio_var: float,
                                           max_drawdown: float,
                                           concentration_risk: float,
                                           correlation_risk: float,
                                           liquidity_risk: float) -> List[str]:
        """Generate risk management recommendations"""
        recommendations = []
        
        try:
            if portfolio_var > self.global_limits.get('max_portfolio_risk', 0.02):
                recommendations.append("Reduce portfolio VaR by decreasing position sizes or hedging")
            
            if max_drawdown > self.global_limits.get('max_drawdown', 0.15):
                recommendations.append("Implement stronger drawdown protection measures")
            
            if concentration_risk > 0.3:
                recommendations.append("Diversify portfolio to reduce concentration risk")
            
            if correlation_risk > 0.8:
                recommendations.append("Reduce correlation risk by adding uncorrelated assets")
            
            if liquidity_risk > 0.7:
                recommendations.append("Improve portfolio liquidity by reducing illiquid positions")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return []
    
    async def _check_risk_violations(self, assessment: RiskAssessment) -> None:
        """Check for risk limit violations and generate alerts"""
        try:
            violations = []
            
            # Check VaR limit
            if assessment.portfolio_var > self.global_limits.get('max_portfolio_risk', 0.02):
                violations.append({
                    'type': 'var_limit_breach',
                    'current': assessment.portfolio_var,
                    'limit': self.global_limits.get('max_portfolio_risk', 0.02),
                    'severity': 'high'
                })
            
            # Check drawdown limit
            if assessment.max_drawdown > self.global_limits.get('max_drawdown', 0.15):
                violations.append({
                    'type': 'drawdown_limit_breach',
                    'current': assessment.max_drawdown,
                    'limit': self.global_limits.get('max_drawdown', 0.15),
                    'severity': 'critical'
                })
            
            # Send alerts for violations
            if violations and self.alert_manager:
                for violation in violations:
                    await self.alert_manager.send_risk_alert(violation)
                    
        except Exception as e:
            logger.error(f"Error checking risk violations: {e}")

    def _normalize_portfolio_data(self, portfolio: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize portfolio data to ensure consistent format"""
        try:
            normalized = portfolio.copy()

            # Handle positions format - convert list to dict if needed
            positions = portfolio.get('positions', [])

            if isinstance(positions, list):
                # Convert list of positions to dict format
                positions_dict = {}
                for i, position in enumerate(positions):
                    if isinstance(position, dict):
                        symbol = position.get('symbol', f'position_{i}')
                        positions_dict[symbol] = {
                            'symbol': symbol,
                            'value': position.get('value', 0),
                            'shares': position.get('shares', 0),
                            'price': position.get('value', 0) / max(position.get('shares', 1), 1),
                            'weight': position.get('value', 0) / max(portfolio.get('total_value', 1), 1)
                        }
                normalized['positions'] = positions_dict
            elif isinstance(positions, dict):
                # Already in correct format, but ensure all required fields
                for symbol, position in positions.items():
                    if isinstance(position, dict):
                        position.setdefault('symbol', symbol)
                        position.setdefault('value', 0)
                        position.setdefault('shares', 0)
                        position.setdefault('price', position.get('value', 0) / max(position.get('shares', 1), 1))
                        position.setdefault('weight', position.get('value', 0) / max(portfolio.get('total_value', 1), 1))
                normalized['positions'] = positions
            else:
                # Invalid format, create empty dict
                normalized['positions'] = {}

            # Ensure other required fields
            normalized.setdefault('total_value', 0)
            normalized.setdefault('cash', 0)
            normalized.setdefault('timestamp', time.time())

            return normalized

        except Exception as e:
            logger.error(f"Error normalizing portfolio data: {e}")
            # Return minimal valid portfolio
            return {
                'total_value': portfolio.get('total_value', 0),
                'positions': {},
                'cash': portfolio.get('cash', 0),
                'timestamp': time.time()
            }

    # Advanced Risk Management Methods

    async def dynamic_risk_adjustment(self, market_conditions: Dict[str, Any]) -> Dict[str, float]:
        """Dynamically adjust risk parameters based on market conditions"""
        try:
            volatility = market_conditions.get('volatility', 0.2)
            trend_strength = market_conditions.get('trend_strength', 0.5)
            market_stress = market_conditions.get('market_stress', 0.3)

            # Calculate dynamic risk multipliers
            volatility_multiplier = 1.0 - min(volatility * 2, 0.5)  # Reduce risk in high volatility
            trend_multiplier = 1.0 + (trend_strength - 0.5) * 0.3  # Increase risk in strong trends
            stress_multiplier = 1.0 - market_stress * 0.4  # Reduce risk in stressed markets

            # Combined risk adjustment
            overall_multiplier = volatility_multiplier * trend_multiplier * stress_multiplier
            overall_multiplier = max(0.3, min(1.5, overall_multiplier))  # Clamp between 30% and 150%

            # Adjust risk parameters
            adjusted_params = {
                'max_portfolio_risk': self.global_limits.get('max_portfolio_risk', 0.02) * overall_multiplier,
                'max_position_size': self.global_limits.get('max_position_size', 0.1) * overall_multiplier,
                'max_sector_exposure': self.global_limits.get('max_sector_exposure', 0.3) * overall_multiplier,
                'risk_multiplier': overall_multiplier
            }

            logger.info(f"Dynamic risk adjustment: {overall_multiplier:.2f}x multiplier")
            return adjusted_params

        except Exception as e:
            logger.error(f"Error in dynamic risk adjustment: {e}")
            return self.global_limits.copy()

    async def real_time_risk_monitoring(self, portfolio: Dict[str, Any]) -> Dict[str, Any]:
        """Real-time risk monitoring with alerts"""
        try:
            risk_assessment = await self.assess_portfolio_risk(portfolio)

            # Check for risk alerts
            alerts = []

            if risk_assessment.overall_risk_score > 0.8:
                alerts.append({
                    'level': 'CRITICAL',
                    'message': f'Portfolio risk extremely high: {risk_assessment.overall_risk_score:.1%}',
                    'action': 'IMMEDIATE_REDUCTION_REQUIRED'
                })
            elif risk_assessment.overall_risk_score > 0.6:
                alerts.append({
                    'level': 'HIGH',
                    'message': f'Portfolio risk elevated: {risk_assessment.overall_risk_score:.1%}',
                    'action': 'CONSIDER_REDUCTION'
                })

            # Check concentration risk
            if risk_assessment.concentration_risk > 0.7:
                alerts.append({
                    'level': 'WARNING',
                    'message': f'High concentration risk: {risk_assessment.concentration_risk:.1%}',
                    'action': 'DIVERSIFY_HOLDINGS'
                })

            # Check liquidity risk
            if risk_assessment.liquidity_risk > 0.6:
                alerts.append({
                    'level': 'WARNING',
                    'message': f'Liquidity risk elevated: {risk_assessment.liquidity_risk:.1%}',
                    'action': 'INCREASE_LIQUID_ASSETS'
                })

            return {
                'risk_assessment': risk_assessment,
                'alerts': alerts,
                'monitoring_timestamp': time.time(),
                'risk_status': 'CRITICAL' if any(a['level'] == 'CRITICAL' for a in alerts) else
                             'HIGH' if any(a['level'] == 'HIGH' for a in alerts) else
                             'WARNING' if any(a['level'] == 'WARNING' for a in alerts) else 'NORMAL'
            }

        except Exception as e:
            logger.error(f"Error in real-time risk monitoring: {e}")
            return {'error': str(e)}

    async def get_advanced_risk_metrics(self) -> Dict[str, Any]:
        """Get advanced risk management metrics"""
        return {
            'risk_manager_status': 'active',
            'dynamic_adjustment_enabled': True,
            'real_time_monitoring_enabled': True,
            'current_risk_limits': self.global_limits,
            'last_risk_check': time.time(),
            'portfolio_value': self.portfolio_value,
            'active_positions': len(self.current_positions)
        }

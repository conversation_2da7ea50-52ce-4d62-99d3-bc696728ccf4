{"validation_id": "validation_**********_29488943", "validation_level": "comprehensive", "overall_status": "partial", "overall_score": 0.7895992935979301, "component_results": [{"component_name": "system_coordinator", "component_type": "core", "status": "completed", "integration_score": 0.894737831681114, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "team_manager", "component_type": "core", "status": "completed", "integration_score": 0.8386602180508405, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "data_manager", "component_type": "core", "status": "completed", "integration_score": 0.8199371246907081, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "analytics_engine", "component_type": "core", "status": "completed", "integration_score": 0.8229308654811939, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ollama_hub", "component_type": "core", "status": "completed", "integration_score": 0.8785495859993941, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "execution_engine", "component_type": "core", "status": "completed", "integration_score": 0.829660084698499, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "portfolio_manager", "component_type": "core", "status": "completed", "integration_score": 0.863219162931367, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "risk_manager", "component_type": "core", "status": "completed", "integration_score": 0.8664882003884548, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "strategy_manager", "component_type": "core", "status": "completed", "integration_score": 0.8139469028109325, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "competitive_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8798399244075427, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "tournament_framework", "component_type": "advanced", "status": "completed", "integration_score": 0.8630697907494329, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "self_improvement_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8282141938146668, "error_count": 0, "warnings": ["Integration issues in self_improvement_engine"], "dependencies_met": "True"}, {"component_name": "regime_adaptation_system", "component_type": "advanced", "status": "completed", "integration_score": 0.864878045639445, "error_count": 0, "warnings": [], "dependencies_met": "False"}, {"component_name": "performance_optimizer", "component_type": "advanced", "status": "completed", "integration_score": 0.8061847397772527, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "advanced_trading_engine", "component_type": "advanced", "status": "completed", "integration_score": 0.8545042498061715, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "ai_coordinator", "component_type": "advanced", "status": "completed", "integration_score": 0.9021692357612131, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "configuration_manager", "component_type": "integration", "status": "completed", "integration_score": 0.8943367937643085, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "mock_data_providers", "component_type": "integration", "status": "completed", "integration_score": 0.865455379617066, "error_count": 0, "warnings": ["Integration issues in mock_data_providers"], "dependencies_met": "True"}, {"component_name": "paper_trading_engine", "component_type": "integration", "status": "completed", "integration_score": 0.8451804342060913, "error_count": 0, "warnings": [], "dependencies_met": "True"}, {"component_name": "logging_audit_system", "component_type": "integration", "status": "completed", "integration_score": 0.7860321769520316, "error_count": 0, "warnings": [], "dependencies_met": "True"}], "integration_matrix": {"system_coordinator": {"system_coordinator": 1.0, "team_manager": 0.9974032584911754, "data_manager": 0.7881112790982552, "analytics_engine": 0.8913668846794591, "ollama_hub": 0.617334996634487, "execution_engine": 0.6558768355373656, "portfolio_manager": 0.8077857486677295, "risk_manager": 0.8236851477057174, "strategy_manager": 0.8052679203722365, "competitive_framework": 0.6369410006576868, "tournament_framework": 0.7755841585204365, "self_improvement_engine": 0.822387255139307, "regime_adaptation_system": 0.668812532666976, "performance_optimizer": 0.8547711158763867, "advanced_trading_engine": 0.8254215428806415, "ai_coordinator": 0.8434922043573854, "configuration_manager": 0.6769100718414185, "mock_data_providers": 0.8803830428991681, "paper_trading_engine": 0.6953359601629373, "logging_audit_system": 0.8525695276792469}, "team_manager": {"system_coordinator": 0.7840802881254695, "team_manager": 1.0, "data_manager": 0.8911995595235014, "analytics_engine": 0.7272184815865493, "ollama_hub": 0.6945058095686147, "execution_engine": 0.635396573984572, "portfolio_manager": 0.8127950483110418, "risk_manager": 0.8224917559845063, "strategy_manager": 0.7252177075232561, "competitive_framework": 0.6382887113622262, "tournament_framework": 0.6393679149057969, "self_improvement_engine": 0.8486182260593946, "regime_adaptation_system": 0.7691605240829851, "performance_optimizer": 0.7403002259104114, "advanced_trading_engine": 0.7531569483376175, "ai_coordinator": 0.611841066721019, "configuration_manager": 0.7591787053271694, "mock_data_providers": 0.6203292093082312, "paper_trading_engine": 0.7072920163207972, "logging_audit_system": 0.6485341229400355}, "data_manager": {"system_coordinator": 0.8492218371350981, "team_manager": 0.7618444310876541, "data_manager": 1.0, "analytics_engine": 0.8618510901461623, "ollama_hub": 0.6264995473741125, "execution_engine": 0.7249298912893787, "portfolio_manager": 0.6796727688360482, "risk_manager": 0.7307615782728326, "strategy_manager": 0.6657811168965583, "competitive_framework": 0.8040414054561514, "tournament_framework": 0.8622152569510838, "self_improvement_engine": 0.877262336799661, "regime_adaptation_system": 0.6211077163949672, "performance_optimizer": 0.7760418791923058, "advanced_trading_engine": 0.689342867197626, "ai_coordinator": 0.7265684573284295, "configuration_manager": 0.8403315304175114, "mock_data_providers": 0.7974098563304832, "paper_trading_engine": 0.7670884836984649, "logging_audit_system": 0.8508717037559665}, "analytics_engine": {"system_coordinator": 0.7497389383292165, "team_manager": 0.8741155953033407, "data_manager": 0.8585883712044613, "analytics_engine": 1.0, "ollama_hub": 0.7092453905774815, "execution_engine": 0.863826315943716, "portfolio_manager": 0.6070567707999769, "risk_manager": 0.7671809429167927, "strategy_manager": 0.9044566258271811, "competitive_framework": 0.789179021100106, "tournament_framework": 0.611664219565044, "self_improvement_engine": 0.7082907783781088, "regime_adaptation_system": 0.6983865854100447, "performance_optimizer": 0.7299522624463479, "advanced_trading_engine": 0.7128555959427546, "ai_coordinator": 0.6767714506993667, "configuration_manager": 0.6402915006043824, "mock_data_providers": 0.7006767647256377, "paper_trading_engine": 0.6469591094997328, "logging_audit_system": 0.8728634921169095}, "ollama_hub": {"system_coordinator": 0.7624424476487919, "team_manager": 0.8814822016411259, "data_manager": 0.6616650829746897, "analytics_engine": 0.7245420514170382, "ollama_hub": 1.0, "execution_engine": 0.8545018729787414, "portfolio_manager": 0.703124065757595, "risk_manager": 0.736859007623877, "strategy_manager": 0.7000117358296531, "competitive_framework": 0.8366902740590147, "tournament_framework": 0.6309395851309898, "self_improvement_engine": 0.8614068040359438, "regime_adaptation_system": 0.8294038650160779, "performance_optimizer": 0.8689721227911029, "advanced_trading_engine": 0.6276592874778273, "ai_coordinator": 0.7310299619483323, "configuration_manager": 0.8974214789169304, "mock_data_providers": 0.8404924687948057, "paper_trading_engine": 0.6278660137309504, "logging_audit_system": 0.7029742544013746}, "execution_engine": {"system_coordinator": 0.8323239481188802, "team_manager": 0.8112822185905414, "data_manager": 0.7352491080674987, "analytics_engine": 0.7127840684302447, "ollama_hub": 0.8673809455876029, "execution_engine": 1.0, "portfolio_manager": 0.909144307487807, "risk_manager": 0.8691498548354388, "strategy_manager": 0.8322766652974042, "competitive_framework": 0.8190645578642068, "tournament_framework": 0.8286262143061618, "self_improvement_engine": 0.8491544065978702, "regime_adaptation_system": 0.8640726129921623, "performance_optimizer": 0.7108096542829132, "advanced_trading_engine": 0.8668318139261788, "ai_coordinator": 0.8939529328755613, "configuration_manager": 0.6181636982955978, "mock_data_providers": 0.6822733396108484, "paper_trading_engine": 0.7960625212641989, "logging_audit_system": 0.7536676074938452}, "portfolio_manager": {"system_coordinator": 0.6456222311382378, "team_manager": 0.6562239384590852, "data_manager": 0.7713499090414854, "analytics_engine": 0.8766017793833103, "ollama_hub": 0.7895248053534477, "execution_engine": 0.7756495318353898, "portfolio_manager": 1.0, "risk_manager": 0.7864801879398704, "strategy_manager": 0.7057631064605341, "competitive_framework": 0.6077458486511476, "tournament_framework": 0.739203938166907, "self_improvement_engine": 0.6546534175244889, "regime_adaptation_system": 0.7408534406365905, "performance_optimizer": 0.6015804644200927, "advanced_trading_engine": 0.7039567732753103, "ai_coordinator": 0.7843247309197519, "configuration_manager": 0.8468146151293428, "mock_data_providers": 0.6299168532101164, "paper_trading_engine": 0.7784610156298707, "logging_audit_system": 0.854179946507416}, "risk_manager": {"system_coordinator": 0.8783912394597226, "team_manager": 0.8159081746895945, "data_manager": 0.8307897738366731, "analytics_engine": 0.612764243054293, "ollama_hub": 0.631944134342861, "execution_engine": 0.6204748987557496, "portfolio_manager": 0.7066723664600674, "risk_manager": 1.0, "strategy_manager": 0.7102170298675629, "competitive_framework": 0.6744011470642273, "tournament_framework": 0.8341532194799348, "self_improvement_engine": 0.7046483207032346, "regime_adaptation_system": 0.8933630811731462, "performance_optimizer": 0.8693647271565802, "advanced_trading_engine": 0.8850494406680764, "ai_coordinator": 0.7346822919974031, "configuration_manager": 0.7054083009526984, "mock_data_providers": 0.6196759124491364, "paper_trading_engine": 0.7643633097600722, "logging_audit_system": 0.8335548309890064}, "strategy_manager": {"system_coordinator": 0.8586292217939326, "team_manager": 0.7808969067879858, "data_manager": 0.8195061189709608, "analytics_engine": 0.7475493428547034, "ollama_hub": 0.6480167541576557, "execution_engine": 0.6713542749303743, "portfolio_manager": 0.7795787581125639, "risk_manager": 0.6248787313297354, "strategy_manager": 1.0, "competitive_framework": 0.6295790620005629, "tournament_framework": 0.6576213311829135, "self_improvement_engine": 0.7642679625077861, "regime_adaptation_system": 0.737280781704949, "performance_optimizer": 0.6540813232741591, "advanced_trading_engine": 0.8426484031910046, "ai_coordinator": 0.8000552446267014, "configuration_manager": 0.7972557225997315, "mock_data_providers": 0.8672523140482382, "paper_trading_engine": 0.8320663666456618, "logging_audit_system": 0.7617857746107302}, "competitive_framework": {"system_coordinator": 0.8736864972407856, "team_manager": 0.8181957088430487, "data_manager": 0.6866442480179863, "analytics_engine": 0.8939684307363485, "ollama_hub": 0.8147368857295176, "execution_engine": 0.6236025094785401, "portfolio_manager": 0.7269120862026255, "risk_manager": 0.7327125283866806, "strategy_manager": 0.7243344299608939, "competitive_framework": 1.0, "tournament_framework": 0.6733043438982502, "self_improvement_engine": 0.7695200341428381, "regime_adaptation_system": 0.6537900674431928, "performance_optimizer": 0.6773464803765034, "advanced_trading_engine": 0.7687751435459532, "ai_coordinator": 0.7920993107838691, "configuration_manager": 0.8631283385965861, "mock_data_providers": 0.6710551846663534, "paper_trading_engine": 0.6262838951976712, "logging_audit_system": 0.7876537505976823}, "tournament_framework": {"system_coordinator": 0.6745005495597016, "team_manager": 0.8967814186509203, "data_manager": 0.7610641849825037, "analytics_engine": 0.6478856563927938, "ollama_hub": 0.711356559199152, "execution_engine": 0.7670738605047014, "portfolio_manager": 0.6527690662399793, "risk_manager": 0.6361763899871534, "strategy_manager": 0.6500466968798284, "competitive_framework": 0.6737542106978927, "tournament_framework": 1.0, "self_improvement_engine": 0.7695212262601095, "regime_adaptation_system": 0.8529828211952657, "performance_optimizer": 0.6090280290038581, "advanced_trading_engine": 0.7381167552908272, "ai_coordinator": 0.7095603178350427, "configuration_manager": 0.6903525147243957, "mock_data_providers": 0.6420967541185267, "paper_trading_engine": 0.7095341042203898, "logging_audit_system": 0.7838240367877236}, "self_improvement_engine": {"system_coordinator": 0.725777960454512, "team_manager": 0.6768426291508836, "data_manager": 0.8109816353206192, "analytics_engine": 0.7965130180723491, "ollama_hub": 0.681102100507402, "execution_engine": 0.6607517735324535, "portfolio_manager": 0.7438034600074771, "risk_manager": 0.6844759949886691, "strategy_manager": 0.6061746699484456, "competitive_framework": 0.7482831245705244, "tournament_framework": 0.7616182759686124, "self_improvement_engine": 1.0, "regime_adaptation_system": 0.7287760913123775, "performance_optimizer": 0.7099551981883796, "advanced_trading_engine": 0.8960734571404356, "ai_coordinator": 0.660781681261812, "configuration_manager": 0.8402401162405028, "mock_data_providers": 0.7774329657933823, "paper_trading_engine": 0.7880815123343183, "logging_audit_system": 0.8688737976974312}, "regime_adaptation_system": {"system_coordinator": 0.8111050827157077, "team_manager": 0.641038562925022, "data_manager": 0.759047192114207, "analytics_engine": 0.7302177543746179, "ollama_hub": 0.7964914162104116, "execution_engine": 0.8836771481111255, "portfolio_manager": 0.8568626903717365, "risk_manager": 0.7632294332255956, "strategy_manager": 0.7147475740828384, "competitive_framework": 0.6728352027835596, "tournament_framework": 0.887180949703553, "self_improvement_engine": 0.8707333373915569, "regime_adaptation_system": 1.0, "performance_optimizer": 0.6473916103560884, "advanced_trading_engine": 0.8845825378061716, "ai_coordinator": 0.7111251013462138, "configuration_manager": 0.7013773607853336, "mock_data_providers": 0.8539646933055594, "paper_trading_engine": 0.8972632734486067, "logging_audit_system": 0.8064278849377987}, "performance_optimizer": {"system_coordinator": 0.6968758604846256, "team_manager": 0.653720442265897, "data_manager": 0.8470213579334203, "analytics_engine": 0.8819141804753388, "ollama_hub": 0.7012376870531136, "execution_engine": 0.8141929927247227, "portfolio_manager": 0.6593784990134361, "risk_manager": 0.8521771552246852, "strategy_manager": 0.6441454202750405, "competitive_framework": 0.8009224436886339, "tournament_framework": 0.8539062277799943, "self_improvement_engine": 0.8399191987770648, "regime_adaptation_system": 0.8485727793199445, "performance_optimizer": 1.0, "advanced_trading_engine": 0.7681980661160603, "ai_coordinator": 0.77943269881888, "configuration_manager": 0.6422397011702868, "mock_data_providers": 0.8007745541685417, "paper_trading_engine": 0.7534037037110637, "logging_audit_system": 0.604292719848425}, "advanced_trading_engine": {"system_coordinator": 0.7457944139952069, "team_manager": 0.645556387796642, "data_manager": 0.6767596988103128, "analytics_engine": 0.669661885831394, "ollama_hub": 0.7701205236258521, "execution_engine": 0.7057972618232443, "portfolio_manager": 0.8965689565090562, "risk_manager": 0.7309665806830838, "strategy_manager": 0.721044575828546, "competitive_framework": 0.7129804502481902, "tournament_framework": 0.7075376888409892, "self_improvement_engine": 0.8122443103263794, "regime_adaptation_system": 0.691888528324311, "performance_optimizer": 0.6954549448303763, "advanced_trading_engine": 1.0, "ai_coordinator": 0.8183295561028123, "configuration_manager": 0.6264891157796476, "mock_data_providers": 0.8156921792245624, "paper_trading_engine": 0.6840950485148162, "logging_audit_system": 0.6301141482966275}, "ai_coordinator": {"system_coordinator": 0.723521419156266, "team_manager": 0.8108217364907311, "data_manager": 0.8905218383456681, "analytics_engine": 0.6599830091070356, "ollama_hub": 0.8391091948791547, "execution_engine": 0.8620187557454704, "portfolio_manager": 0.7396589475864382, "risk_manager": 0.8801906131482948, "strategy_manager": 0.7641374402446373, "competitive_framework": 0.6602895545884674, "tournament_framework": 0.8101639161140421, "self_improvement_engine": 0.7247474630117384, "regime_adaptation_system": 0.6482670760925946, "performance_optimizer": 0.8297207053085813, "advanced_trading_engine": 0.6004230752315256, "ai_coordinator": 1.0, "configuration_manager": 0.600880501841498, "mock_data_providers": 0.8192464800131789, "paper_trading_engine": 0.7443528557852208, "logging_audit_system": 0.6851421004369109}, "configuration_manager": {"system_coordinator": 0.7357354060064698, "team_manager": 0.8393340401302346, "data_manager": 0.8111131734689145, "analytics_engine": 0.6647595981663678, "ollama_hub": 0.7173087364539978, "execution_engine": 0.7782886773515271, "portfolio_manager": 0.7122694496393108, "risk_manager": 0.7278653896146077, "strategy_manager": 0.709724626090108, "competitive_framework": 0.6951284051565668, "tournament_framework": 0.8243187128097571, "self_improvement_engine": 0.7623986556920757, "regime_adaptation_system": 0.8239149273014311, "performance_optimizer": 0.7697508976459635, "advanced_trading_engine": 0.6715254430636671, "ai_coordinator": 0.6942014196272777, "configuration_manager": 1.0, "mock_data_providers": 0.8323951056825758, "paper_trading_engine": 0.7215174088546709, "logging_audit_system": 0.8484496095759285}, "mock_data_providers": {"system_coordinator": 0.7206151230125958, "team_manager": 0.6778706632551941, "data_manager": 0.7028162502246279, "analytics_engine": 0.7630337630256169, "ollama_hub": 0.6200843640661563, "execution_engine": 0.6522109017394389, "portfolio_manager": 0.6171288838834373, "risk_manager": 0.8322387122977791, "strategy_manager": 0.6305262286147134, "competitive_framework": 0.7219618484173582, "tournament_framework": 0.8886310156611328, "self_improvement_engine": 0.6922297121720542, "regime_adaptation_system": 0.745941977819593, "performance_optimizer": 0.7971067243561988, "advanced_trading_engine": 0.721412262619884, "ai_coordinator": 0.8062224812907993, "configuration_manager": 0.6062426863445907, "mock_data_providers": 1.0, "paper_trading_engine": 0.7282729550003973, "logging_audit_system": 0.6348626412342008}, "paper_trading_engine": {"system_coordinator": 0.8172873254310296, "team_manager": 0.8029258925591205, "data_manager": 0.8197693745222145, "analytics_engine": 0.849351927746435, "ollama_hub": 0.7421767355713171, "execution_engine": 0.811457610027618, "portfolio_manager": 0.7175485242354691, "risk_manager": 0.6626279300848894, "strategy_manager": 0.8314663523734429, "competitive_framework": 0.6407112794074264, "tournament_framework": 0.7390315214064364, "self_improvement_engine": 0.7978642923430505, "regime_adaptation_system": 0.6858191392416599, "performance_optimizer": 0.876677148954102, "advanced_trading_engine": 0.6959911898315303, "ai_coordinator": 0.7174464549400992, "configuration_manager": 0.612341918554745, "mock_data_providers": 0.8482997696208033, "paper_trading_engine": 1.0, "logging_audit_system": 0.8022592268730866}, "logging_audit_system": {"system_coordinator": 0.7910536093994217, "team_manager": 0.7815150083181188, "data_manager": 0.7186981679839182, "analytics_engine": 0.7284482042550091, "ollama_hub": 0.7047113248730833, "execution_engine": 0.8806783405642801, "portfolio_manager": 0.6320544082609866, "risk_manager": 0.630288395199314, "strategy_manager": 0.7798044907534948, "competitive_framework": 0.6690040944172746, "tournament_framework": 0.8469605431377085, "self_improvement_engine": 0.7356857144129497, "regime_adaptation_system": 0.6125791414182687, "performance_optimizer": 0.8921037787501702, "advanced_trading_engine": 0.744400892164666, "ai_coordinator": 0.7277478530032752, "configuration_manager": 0.8883520671916423, "mock_data_providers": 0.7845369571882224, "paper_trading_engine": 0.6420890309050993, "logging_audit_system": 1.0}}, "performance_summary": {"initialization_time": 0.7781106143689142, "response_time": 0.919255712738642, "throughput": 0.6467681181453289, "memory_usage": 0.7287192159532925, "cpu_usage": 0.8767092376990849, "concurrent_operations": 0.7750073921385632}, "critical_issues": ["Components with dependency issues: regime_adaptation_system"], "recommendations": ["Improve overall system performance and stability", "Enhance component integration and communication"], "production_ready": "True", "timestamp": **********.839093}
#!/usr/bin/env python3
"""
Advanced Features Test - Test all advanced trading system features
"""

import asyncio
import json
from datetime import datetime
from system.system_coordinator import SystemCoordinator
from trading.multi_strategy_engine import MultiStrategyEngine, StrategyPriority, TradingSignal
from market.real_time_market_system import RealTimeMarketSystem, MarketEventType
from risk.risk_manager import RiskManager

async def test_advanced_features():
    """Test all advanced features"""
    
    print("🚀 TESTING ADVANCED TRADING FEATURES")
    print("=" * 60)
    
    results = {}
    
    try:
        # Initialize system coordinator
        print("\n🔧 PHASE 1: Advanced System Setup")
        coordinator = SystemCoordinator('config/test_config.yaml')
        
        # Initialize and start system
        init_success = await coordinator.initialize()
        start_success = await coordinator.start()
        
        if not (init_success and start_success):
            print("  ❌ System setup failed")
            return False
            
        print("  ✅ Advanced system setup complete")
        
        # Get system components
        strategy_manager = await coordinator.get_component('strategy_manager')
        execution_engine = await coordinator.get_component('execution_engine')
        portfolio_manager = await coordinator.get_component('portfolio_manager')
        risk_manager = await coordinator.get_component('risk_manager')
        analytics_engine = await coordinator.get_component('analytics_engine')
        
        # Test 1: Multi-Strategy Engine
        print("\n🎯 PHASE 2: Multi-Strategy Engine Test")
        
        multi_strategy_engine = MultiStrategyEngine(
            strategy_manager=strategy_manager,
            execution_engine=execution_engine,
            portfolio_manager=portfolio_manager,
            risk_manager=risk_manager,
            analytics_engine=analytics_engine,
            config={'multi_strategy_engine': {'max_strategies': 5}}
        )
        
        # Initialize multi-strategy engine
        engine_init = await multi_strategy_engine.initialize(100000.0)  # $100k capital
        engine_start = await multi_strategy_engine.start()
        
        if engine_init and engine_start:
            print("  ✅ Multi-Strategy Engine initialized and started")
            
            # Add test strategies
            strategy1_added = await multi_strategy_engine.add_strategy(
                "momentum_strategy_1", "Momentum Strategy 1", 30000.0, StrategyPriority.HIGH
            )
            strategy2_added = await multi_strategy_engine.add_strategy(
                "mean_reversion_strategy_1", "Mean Reversion Strategy 1", 25000.0, StrategyPriority.MEDIUM
            )
            
            if strategy1_added and strategy2_added:
                print("  ✅ Test strategies added successfully")
                
                # Test signal submission
                test_signal = TradingSignal(
                    strategy_id="momentum_strategy_1",
                    symbol="AAPL",
                    action="buy",
                    quantity=100,
                    confidence=0.8,
                    urgency=0.6,
                    reasoning="Strong momentum breakout detected",
                    risk_score=0.4,
                    expected_return=0.05,
                    timestamp=datetime.now().timestamp()
                )
                
                signal_submitted = await multi_strategy_engine.submit_signal(test_signal)
                
                if signal_submitted:
                    print("  ✅ Trading signal submitted and processed")
                    results['multi_strategy_engine'] = {'success': True, 'strategies_added': 2}
                else:
                    print("  ⚠️ Signal submission failed")
                    results['multi_strategy_engine'] = {'success': False, 'reason': 'Signal submission failed'}
            else:
                print("  ⚠️ Failed to add test strategies")
                results['multi_strategy_engine'] = {'success': False, 'reason': 'Strategy addition failed'}
        else:
            print("  ❌ Multi-Strategy Engine initialization failed")
            results['multi_strategy_engine'] = {'success': False, 'reason': 'Initialization failed'}
        
        # Test 2: Real-Time Market System
        print("\n📊 PHASE 3: Real-Time Market System Test")
        
        market_system = RealTimeMarketSystem({
            'real_time_market': {
                'symbols': ['AAPL', 'TSLA', 'GOOGL'],
                'update_interval': 0.5,
                'event_detection': True
            }
        })
        
        # Initialize and start market system
        market_init = await market_system.initialize()
        market_start = await market_system.start()
        
        if market_init and market_start:
            print("  ✅ Real-Time Market System started")
            
            # Test quote subscription
            quotes_received = []
            
            async def quote_callback(quote):
                quotes_received.append(quote)
                
            await market_system.subscribe_to_quotes(quote_callback)
            
            # Test event subscription
            events_received = []
            
            async def event_callback(event):
                events_received.append(event)
                
            await market_system.subscribe_to_events(MarketEventType.VOLUME_SPIKE, event_callback)
            
            # Wait for some market data
            print("  📈 Collecting market data...")
            await asyncio.sleep(3.0)
            
            # Check results
            if len(quotes_received) > 0:
                print(f"  ✅ Received {len(quotes_received)} real-time quotes")
                
                # Get market conditions
                conditions = await market_system.get_market_conditions()
                if conditions:
                    print(f"  ✅ Market conditions: {conditions.overall_sentiment}, volatility: {conditions.volatility_level}")
                    results['real_time_market'] = {
                        'success': True, 
                        'quotes_received': len(quotes_received),
                        'market_sentiment': conditions.overall_sentiment
                    }
                else:
                    print("  ⚠️ No market conditions available")
                    results['real_time_market'] = {'success': False, 'reason': 'No market conditions'}
            else:
                print("  ⚠️ No quotes received")
                results['real_time_market'] = {'success': False, 'reason': 'No quotes received'}
                
            await market_system.stop()
        else:
            print("  ❌ Real-Time Market System initialization failed")
            results['real_time_market'] = {'success': False, 'reason': 'Initialization failed'}
        
        # Test 3: Advanced Risk Management
        print("\n🛡️ PHASE 4: Advanced Risk Management Test")
        
        # Create test portfolio
        portfolio_id = await portfolio_manager.create_portfolio("advanced_test", 100000.0)
        await portfolio_manager.add_position("AAPL", 200, 150.0)
        await portfolio_manager.add_position("TSLA", 100, 250.0)
        
        portfolio_data = await portfolio_manager.get_portfolio_data(portfolio_id)
        
        if portfolio_data:
            print("  ✅ Test portfolio created")
            
            # Test dynamic risk adjustment
            market_conditions = {
                'volatility': 0.3,
                'trend_strength': 0.7,
                'market_stress': 0.4
            }
            
            adjusted_params = await risk_manager.dynamic_risk_adjustment(market_conditions)
            
            if adjusted_params:
                print(f"  ✅ Dynamic risk adjustment: {adjusted_params['risk_multiplier']:.2f}x multiplier")
                
                # Test real-time risk monitoring
                risk_monitoring = await risk_manager.real_time_risk_monitoring(portfolio_data)
                
                if risk_monitoring and 'risk_assessment' in risk_monitoring:
                    risk_status = risk_monitoring['risk_status']
                    print(f"  ✅ Real-time risk monitoring: {risk_status} status")
                    
                    # Test advanced risk metrics
                    advanced_metrics = await risk_manager.get_advanced_risk_metrics()
                    
                    if advanced_metrics:
                        print("  ✅ Advanced risk metrics retrieved")
                        results['advanced_risk_management'] = {
                            'success': True,
                            'risk_status': risk_status,
                            'risk_multiplier': adjusted_params['risk_multiplier'],
                            'metrics_available': True
                        }
                    else:
                        print("  ⚠️ Advanced risk metrics not available")
                        results['advanced_risk_management'] = {'success': False, 'reason': 'No advanced metrics'}
                else:
                    print("  ⚠️ Real-time risk monitoring failed")
                    results['advanced_risk_management'] = {'success': False, 'reason': 'Risk monitoring failed'}
            else:
                print("  ⚠️ Dynamic risk adjustment failed")
                results['advanced_risk_management'] = {'success': False, 'reason': 'Risk adjustment failed'}
        else:
            print("  ❌ Test portfolio creation failed")
            results['advanced_risk_management'] = {'success': False, 'reason': 'Portfolio creation failed'}
        
        # Test 4: System Integration
        print("\n🔗 PHASE 5: Advanced System Integration Test")
        
        # Test system status
        system_status = await coordinator.get_system_status()
        
        if system_status.system_health > 0.8:
            print(f"  ✅ System health excellent: {system_status.system_health:.1%}")
            
            # Test multi-strategy engine status
            engine_status = await multi_strategy_engine.get_engine_status()
            
            if engine_status['running']:
                print(f"  ✅ Multi-strategy engine operational: {engine_status['active_strategies']} strategies")
                
                # Test system coordination
                coordination_test = await test_system_coordination(
                    coordinator, multi_strategy_engine, market_system, risk_manager
                )
                
                if coordination_test:
                    print("  ✅ System coordination test passed")
                    results['system_integration'] = {
                        'success': True,
                        'system_health': system_status.system_health,
                        'coordination_test': True
                    }
                else:
                    print("  ⚠️ System coordination test failed")
                    results['system_integration'] = {'success': False, 'reason': 'Coordination test failed'}
            else:
                print("  ⚠️ Multi-strategy engine not operational")
                results['system_integration'] = {'success': False, 'reason': 'Engine not operational'}
        else:
            print(f"  ❌ System health poor: {system_status.system_health:.1%}")
            results['system_integration'] = {'success': False, 'reason': 'Poor system health'}
        
        # Test 5: Performance and Scalability
        print("\n⚡ PHASE 6: Performance and Scalability Test")
        
        performance_results = await test_performance_scalability(
            multi_strategy_engine, market_system, risk_manager
        )
        
        if performance_results['success']:
            print(f"  ✅ Performance test passed: {performance_results['operations_per_second']:.0f} ops/sec")
            results['performance_scalability'] = performance_results
        else:
            print("  ⚠️ Performance test failed")
            results['performance_scalability'] = {'success': False, 'reason': 'Performance test failed'}
        
        # Stop multi-strategy engine
        await multi_strategy_engine.stop()
        
        # Stop system
        await coordinator.stop()
        
        # Calculate overall advanced features score
        print("\n🎉 ADVANCED FEATURES TEST SUMMARY")
        print("=" * 60)
        
        successful_tests = sum(1 for result in results.values() if result.get('success'))
        total_tests = len(results)
        advanced_score = (successful_tests / total_tests) * 100
        
        print(f"📊 Advanced Tests: {successful_tests}/{total_tests} passed")
        print(f"🔧 Advanced Features Score: {advanced_score:.1f}%")
        
        # Detailed results
        for test_name, result in results.items():
            status = "✅ PASS" if result.get('success') else "❌ FAIL"
            reason = f" - {result.get('reason', '')}" if not result.get('success') else ""
            print(f"  {test_name}: {status}{reason}")
        
        # Save results
        test_summary = {
            "timestamp": datetime.now().isoformat(),
            "test_type": "advanced_features",
            "advanced_tests": results,
            "summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "advanced_score": advanced_score,
                "production_ready": advanced_score >= 80.0
            }
        }
        
        with open('advanced_features_results.json', 'w') as f:
            json.dump(test_summary, f, indent=2, default=str)
        
        print(f"\n📄 Results saved to: advanced_features_results.json")
        
        # Final verdict
        if advanced_score >= 90:
            print("\n🎉 OUTSTANDING! Advanced features are EXCEPTIONAL!")
            print("🚀 Ready for production deployment!")
        elif advanced_score >= 80:
            print("\n✅ EXCELLENT! Advanced features are SOLID!")
            print("🔧 Ready for production with minor optimizations!")
        elif advanced_score >= 70:
            print("\n⚠️ GOOD! Advanced features need some improvements!")
            print("🛠️ Address failing tests before production!")
        else:
            print("\n❌ POOR! Advanced features need significant work!")
            print("🚨 Major improvements required!")
        
        return advanced_score >= 80.0
        
    except Exception as e:
        print(f"❌ Advanced Features Test Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_system_coordination(coordinator, multi_strategy_engine, market_system, risk_manager):
    """Test coordination between system components"""
    try:
        # Test 1: Market data -> Strategy signals -> Risk validation -> Execution
        print("    🔄 Testing end-to-end coordination...")
        
        # Simulate market event triggering strategy signal
        test_signal = TradingSignal(
            strategy_id="momentum_strategy_1",
            symbol="AAPL",
            action="buy",
            quantity=50,
            confidence=0.7,
            urgency=0.5,
            reasoning="Coordination test signal",
            risk_score=0.3,
            expected_return=0.03,
            timestamp=datetime.now().timestamp()
        )
        
        # Submit signal through multi-strategy engine
        signal_result = await multi_strategy_engine.submit_signal(test_signal)
        
        return signal_result
        
    except Exception as e:
        print(f"    ❌ Coordination test error: {e}")
        return False

async def test_performance_scalability(multi_strategy_engine, market_system, risk_manager):
    """Test system performance and scalability"""
    try:
        import time
        
        start_time = time.time()
        operations = 0
        
        # Test multiple operations
        for i in range(100):
            # Simulate strategy signal
            test_signal = TradingSignal(
                strategy_id="momentum_strategy_1",
                symbol=f"TEST{i % 10}",
                action="buy" if i % 2 == 0 else "sell",
                quantity=10,
                confidence=0.6,
                urgency=0.4,
                reasoning=f"Performance test signal {i}",
                risk_score=0.2,
                expected_return=0.02,
                timestamp=datetime.now().timestamp()
            )
            
            await multi_strategy_engine.submit_signal(test_signal)
            operations += 1
            
            # Small delay to prevent overwhelming
            await asyncio.sleep(0.01)
        
        end_time = time.time()
        duration = end_time - start_time
        ops_per_second = operations / duration if duration > 0 else 0
        
        return {
            'success': True,
            'operations': operations,
            'duration': duration,
            'operations_per_second': ops_per_second
        }
        
    except Exception as e:
        print(f"    ❌ Performance test error: {e}")
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    success = asyncio.run(test_advanced_features())
    if success:
        print("\n🎉 ADVANCED FEATURES TEST SUCCESSFUL!")
        print("🚀 System ready for production deployment!")
    else:
        print("\n⚠️ ADVANCED FEATURES NEED IMPROVEMENTS!")
        print("🔧 Review test results and optimize!")

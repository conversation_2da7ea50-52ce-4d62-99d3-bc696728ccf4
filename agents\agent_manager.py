"""
Agent Manager - Manages lifecycle and coordination of all agents
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
import uuid

from models.ollama_hub import OllamaModelHub
from .base_agent import BaseAgent, AgentRole, AgentState

logger = logging.getLogger(__name__)


class AgentManager:
    """
    Manages the lifecycle and coordination of all trading agents.
    Handles agent creation, deployment, monitoring, and cleanup.
    """
    
    def __init__(self, 
                 ollama_hub: OllamaModelHub,
                 message_broker,
                 config: Dict[str, Any]):
        
        self.ollama_hub = ollama_hub
        self.message_broker = message_broker
        self.config = config
        
        # Agent storage
        self.agents: Dict[str, BaseAgent] = {}
        self.agents_by_role: Dict[AgentRole, List[str]] = {role: [] for role in AgentRole}
        
        # State
        self.initialized = False
        self.running = False
        
        # Configuration
        self.max_agents = self.config.get('agents', {}).get('max_agents', 50)
        self.heartbeat_interval = self.config.get('agents', {}).get('heartbeat_interval', 30)
        
        # Monitoring
        self.monitoring_task: Optional[asyncio.Task] = None
        
    async def initialize(self):
        """Initialize the agent manager"""
        if self.initialized:
            return
            
        logger.info("Initializing Agent Manager...")
        
        self.initialized = True
        logger.info("✓ Agent Manager initialized")
        
    async def start(self):
        """Start the agent manager"""
        if not self.initialized:
            await self.initialize()
            
        if self.running:
            return
            
        logger.info("Starting Agent Manager...")
        
        # Start monitoring task
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        self.running = True
        logger.info("✓ Agent Manager started")
        
    async def stop(self):
        """Stop the agent manager"""
        if not self.running:
            return
            
        logger.info("Stopping Agent Manager...")
        self.running = False
        
        # Stop all agents
        stop_tasks = []
        for agent in self.agents.values():
            stop_tasks.append(agent.stop())
            
        if stop_tasks:
            await asyncio.gather(*stop_tasks, return_exceptions=True)
            
        # Cancel monitoring task
        if self.monitoring_task and not self.monitoring_task.done():
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
                
        logger.info("✓ Agent Manager stopped")
        
    async def create_agent(self, 
                          role: AgentRole,
                          name: str = None,
                          config: Dict[str, Any] = None) -> Optional[str]:
        """Create a new agent"""
        try:
            # Check agent limit
            if len(self.agents) >= self.max_agents:
                logger.error(f"Maximum number of agents ({self.max_agents}) reached")
                return None
                
            # Generate agent ID and name
            agent_id = str(uuid.uuid4())
            agent_name = name or f"{role.value}_{agent_id[:8]}"
            
            # Get model instance for the agent
            model_instance = await self.ollama_hub.deploy_model_for_agent(
                agent_name=agent_name,
                role=role.value
            )
            
            if not model_instance:
                logger.error(f"Failed to deploy model for agent {agent_name}")
                return None
                
            # Create agent configuration
            agent_config = self.config.get('agents', {}).get('defaults', {}).copy()
            if config:
                agent_config.update(config)
                
            # Import and create the appropriate agent class
            agent_class = self._get_agent_class(role)

            # Specialized agents don't accept role parameter, they set it internally
            if agent_class == BaseAgent:
                agent = agent_class(
                    agent_id=agent_id,
                    name=agent_name,
                    role=role,
                    config=agent_config
                )
            else:
                agent = agent_class(
                    agent_id=agent_id,
                    name=agent_name,
                    config=agent_config
                )
            
            # Initialize the agent
            success = await agent.initialize(model_instance, self.message_broker)
            if not success:
                logger.error(f"Failed to initialize agent {agent_name}")
                return None
                
            # Store the agent
            self.agents[agent_id] = agent
            self.agents_by_role[role].append(agent_id)
            
            logger.info(f"✓ Created agent {agent_name} ({role.value}) with ID {agent_id}")
            return agent_id
            
        except Exception as e:
            logger.error(f"Error creating agent: {e}")
            return None
            
    def _get_agent_class(self, role: AgentRole):
        """Get the appropriate agent class for a role"""
        from .specialized.team_leader_agent import TeamLeaderAgent
        from .specialized.market_analyst_agent import MarketAnalystAgent
        from .specialized.strategy_developer_agent import StrategyDeveloperAgent
        from .specialized.risk_manager_agent import RiskManagerAgent
        from .specialized.execution_specialist_agent import ExecutionSpecialistAgent
        from .specialized.performance_evaluator_agent import PerformanceEvaluatorAgent

        role_to_class = {
            AgentRole.TEAM_LEADER: TeamLeaderAgent,
            AgentRole.MARKET_ANALYST: MarketAnalystAgent,
            AgentRole.STRATEGY_DEVELOPER: StrategyDeveloperAgent,
            AgentRole.RISK_MANAGER: RiskManagerAgent,
            AgentRole.EXECUTION_SPECIALIST: ExecutionSpecialistAgent,
            AgentRole.PERFORMANCE_EVALUATOR: PerformanceEvaluatorAgent
        }

        return role_to_class.get(role, BaseAgent)
        
    async def start_agent(self, agent_id: str) -> bool:
        """Start a specific agent"""
        if agent_id not in self.agents:
            logger.error(f"Agent {agent_id} not found")
            return False

        try:
            agent = self.agents[agent_id]
            await agent.start()
            logger.info(f"✓ Started agent {agent.name}")
            return True

        except Exception as e:
            logger.error(f"Error starting agent {agent_id}: {e}")
            return False

    async def get_all_agents(self) -> List[Dict[str, Any]]:
        """Get all agents as a list of dictionaries"""
        agents_list = []

        for agent_id, agent in self.agents.items():
            agent_info = {
                'agent_id': agent_id,
                'name': agent.name,
                'type': agent.role.value if hasattr(agent, 'role') else 'unknown',
                'status': 'active' if agent.running else 'inactive',
                'capabilities': getattr(agent, 'capabilities', []),
                'performance': getattr(agent, 'performance_metrics', {}),
                'last_activity': getattr(agent, 'last_activity', None)
            }
            agents_list.append(agent_info)

        return agents_list

    async def get_agents_by_role(self, role: AgentRole) -> List[BaseAgent]:
        """Get agents by their role"""
        role_agents = []

        for agent_id in self.agents_by_role.get(role, []):
            if agent_id in self.agents:
                role_agents.append(self.agents[agent_id])

        return role_agents

    async def stop_agent(self, agent_id: str) -> bool:
        """Stop a specific agent"""
        if agent_id not in self.agents:
            logger.error(f"Agent {agent_id} not found")
            return False

        try:
            agent = self.agents[agent_id]
            await agent.stop()
            logger.info(f"✓ Stopped agent {agent.name}")
            return True

        except Exception as e:
            logger.error(f"Error stopping agent {agent_id}: {e}")
            return False

    async def remove_agent(self, agent_id: str) -> bool:
        """Remove an agent from the system"""
        if agent_id not in self.agents:
            logger.error(f"Agent {agent_id} not found")
            return False

        try:
            agent = self.agents[agent_id]

            # Stop the agent first
            await agent.stop()

            # Remove from role tracking
            if agent.role and agent_id in self.agents_by_role[agent.role]:
                self.agents_by_role[agent.role].remove(agent_id)

            # Remove from main storage
            del self.agents[agent_id]

            logger.info(f"✓ Removed agent {agent.name}")
            return True

        except Exception as e:
            logger.error(f"Error removing agent {agent_id}: {e}")
            return False

    async def get_agent(self, agent_id: str) -> Optional[BaseAgent]:
        """Get an agent by ID"""
        return self.agents.get(agent_id)
        
    async def get_active_agents(self) -> List[BaseAgent]:
        """Get all active agents"""
        active_agents = []
        for agent in self.agents.values():
            if agent.state == AgentState.ACTIVE:
                active_agents.append(agent)
        return active_agents
        
    async def get_agent_status(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific agent"""
        if agent_id not in self.agents:
            return None
            
        agent = self.agents[agent_id]
        return await agent.get_status()
        
    async def get_all_agent_statuses(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all agents"""
        statuses = {}
        for agent_id, agent in self.agents.items():
            try:
                statuses[agent_id] = await agent.get_status()
            except Exception as e:
                logger.error(f"Error getting status for agent {agent_id}: {e}")
                statuses[agent_id] = {'error': str(e)}
                
        return statuses
        
    async def broadcast_to_role(self, role: AgentRole, task: Dict[str, Any]) -> int:
        """Broadcast a task to all agents with a specific role"""
        agents = await self.get_agents_by_role(role)
        sent_count = 0
        
        for agent in agents:
            try:
                await agent.submit_task(task)
                sent_count += 1
            except Exception as e:
                logger.error(f"Error sending task to agent {agent.name}: {e}")
                
        return sent_count
        
    async def get_system_stats(self) -> Dict[str, Any]:
        """Get system-wide agent statistics"""
        stats = {
            'total_agents': len(self.agents),
            'agents_by_role': {},
            'agents_by_state': {},
            'performance_summary': {
                'total_tasks': 0,
                'completed_tasks': 0,
                'failed_tasks': 0,
                'avg_response_time': 0.0
            }
        }
        
        # Count by role
        for role, agent_ids in self.agents_by_role.items():
            stats['agents_by_role'][role.value] = len(agent_ids)
            
        # Count by state and aggregate performance
        total_response_time = 0.0
        agents_with_metrics = 0
        
        for agent in self.agents.values():
            # State counting
            state = agent.state.value
            stats['agents_by_state'][state] = stats['agents_by_state'].get(state, 0) + 1
            
            # Performance aggregation
            metrics = agent.metrics
            stats['performance_summary']['total_tasks'] += metrics['total_tasks']
            stats['performance_summary']['completed_tasks'] += metrics['completed_tasks']
            stats['performance_summary']['failed_tasks'] += metrics['failed_tasks']
            
            if metrics['avg_response_time'] > 0:
                total_response_time += metrics['avg_response_time']
                agents_with_metrics += 1
                
        # Calculate average response time
        if agents_with_metrics > 0:
            stats['performance_summary']['avg_response_time'] = total_response_time / agents_with_metrics
            
        return stats
        
    async def _monitoring_loop(self):
        """Monitor agent health and performance"""
        while self.running:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                if self.running:
                    await self._check_agent_health()
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                
    async def _check_agent_health(self):
        """Check health of all agents"""
        unhealthy_agents = []
        
        for agent_id, agent in self.agents.items():
            try:
                # Check if agent is responsive
                if agent.state == AgentState.ERROR:
                    unhealthy_agents.append((agent_id, agent, "Agent in error state"))
                elif agent.state == AgentState.STOPPED:
                    unhealthy_agents.append((agent_id, agent, "Agent stopped unexpectedly"))
                    
            except Exception as e:
                unhealthy_agents.append((agent_id, agent, f"Health check failed: {e}"))
                
        # Handle unhealthy agents
        for agent_id, agent, reason in unhealthy_agents:
            logger.warning(f"Unhealthy agent detected: {agent.name} - {reason}")
            # In a full implementation, this could trigger recovery actions
            
    async def create_default_agents(self) -> Dict[AgentRole, str]:
        """Create a default set of agents for system operation"""
        default_agents = {}
        
        # Create one agent of each role
        for role in AgentRole:
            agent_id = await self.create_agent(role)
            if agent_id:
                await self.start_agent(agent_id)
                default_agents[role] = agent_id
                logger.info(f"✓ Created default {role.value} agent")
            else:
                logger.error(f"Failed to create default {role.value} agent")
                
        return default_agents

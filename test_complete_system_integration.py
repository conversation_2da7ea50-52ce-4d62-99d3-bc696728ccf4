#!/usr/bin/env python3
"""
Complete System Integration Test - Test all core system components together
"""

import asyncio
import json
import time
from datetime import datetime

# Core system components
from system.system_coordinator import SystemCoordinator

# Advanced features
from teams.competitive_cooperative_framework import CompetitiveCooperativeFramework, InteractionMode
from innovation.tournament_framework import InnovationTournamentFramework, TournamentType, InnovationCategory
from learning.self_improvement_engine import SelfImprovementEng<PERSON>, LearningType, KnowledgeType
from market.regime_adaptation_system import MarketRegimeAdaptationSystem, MarketRegime
from optimization.advanced_performance_optimizer import AdvancedPerformanceOptimizer, OptimizationType, PerformanceMetric

# Core components
from simulation.mock_data_providers import MockDataProviders, SimulationMode, MarketCondition
from trading.paper_trading_engine import PaperTradingEngine, OrderSide, OrderType
from config.configuration_manager import ConfigurationManager, ConfigType
from logging.audit_trail_system import LoggingAuditSystem, AuditEventType

async def test_complete_system_integration():
    """Complete system integration test"""
    
    print("🚀 COMPLETE SYSTEM INTEGRATION TEST")
    print("=" * 80)
    print("Testing all core and advanced system components together")
    print("=" * 80)
    
    results = {}
    
    try:
        # Phase 1: Core System Foundation
        print("\n🏗️ PHASE 1: CORE SYSTEM FOUNDATION")
        
        # Test Configuration Manager
        print("  📋 Testing Configuration Manager...")
        config_manager = ConfigurationManager()
        config_init = await config_manager.initialize()
        
        if config_init:
            # Test configuration operations
            test_config = {"test_setting": "test_value", "enabled": True}
            config_set = await config_manager.set_config("test_config", test_config)
            config_get = await config_manager.get_config("test_config")
            
            if config_set and config_get:
                print("    ✅ Configuration Manager: WORKING")
                results['config_manager'] = True
            else:
                print("    ⚠️ Configuration Manager: PARTIAL")
                results['config_manager'] = False
        else:
            print("    ❌ Configuration Manager: FAILED")
            results['config_manager'] = False
            
        # Test Logging & Audit System
        print("  📝 Testing Logging & Audit System...")
        logging_system = LoggingAuditSystem({
            'logging': {'level': 'INFO', 'file': 'logs/test.log'},
            'audit': {'database': 'data/test_audit.db'}
        })
        
        logging_init = await logging_system.initialize()
        logging_start = await logging_system.start()
        
        if logging_init and logging_start:
            # Test audit logging
            audit_id = await logging_system.log_audit_event(
                AuditEventType.SYSTEM_START, "test_system", "system", "test",
                {"test_data": "test_value"}
            )
            
            # Test performance logging
            perf_logged = await logging_system.log_performance_metric(
                "test_metric", 0.85, "test_entity", "test_type"
            )
            
            await logging_system.stop()
            
            if audit_id and perf_logged:
                print("    ✅ Logging & Audit System: WORKING")
                results['logging_audit'] = True
            else:
                print("    ⚠️ Logging & Audit System: PARTIAL")
                results['logging_audit'] = False
        else:
            print("    ❌ Logging & Audit System: FAILED")
            results['logging_audit'] = False
            
        # Test Mock Data Providers
        print("  📊 Testing Mock Data Providers...")
        mock_data = MockDataProviders({'simulation': {}})
        
        mock_init = await mock_data.initialize()
        mock_start = await mock_data.start()
        
        if mock_init and mock_start:
            # Test data generation
            real_time_data = await mock_data.get_real_time_data('AAPL')
            historical_data = await mock_data.get_mock_data('AAPL', time.time() - 3600, time.time())
            
            await mock_data.stop()
            
            if real_time_data or len(historical_data) > 0:
                print("    ✅ Mock Data Providers: WORKING")
                results['mock_data'] = True
            else:
                print("    ⚠️ Mock Data Providers: PARTIAL")
                results['mock_data'] = False
        else:
            print("    ❌ Mock Data Providers: FAILED")
            results['mock_data'] = False
            
        # Test Paper Trading Engine
        print("  💰 Testing Paper Trading Engine...")
        paper_trading = PaperTradingEngine({'paper_trading': {}})
        
        trading_init = await paper_trading.initialize()
        trading_start = await paper_trading.start()
        
        if trading_init and trading_start:
            # Test account creation and trading
            account_created = await paper_trading.create_account("test_account", 100000.0)
            
            if account_created:
                order_id = await paper_trading.place_order(
                    "test_account", "AAPL", OrderSide.BUY, OrderType.MARKET, 100
                )
                
                account_status = await paper_trading.get_account_status("test_account")
                
            await paper_trading.stop()
            
            if account_created and order_id and account_status:
                print("    ✅ Paper Trading Engine: WORKING")
                results['paper_trading'] = True
            else:
                print("    ⚠️ Paper Trading Engine: PARTIAL")
                results['paper_trading'] = False
        else:
            print("    ❌ Paper Trading Engine: FAILED")
            results['paper_trading'] = False
            
        # Phase 2: Advanced Features Integration
        print("\n🎯 PHASE 2: ADVANCED FEATURES INTEGRATION")
        
        # Mock components for advanced features
        class MockComponent:
            def __init__(self, name):
                self.name = name
                
        team_manager = MockComponent("team_manager")
        data_manager = MockComponent("data_manager")
        analytics_engine = MockComponent("analytics_engine")
        ollama_hub = MockComponent("ollama_hub")
        
        # Test all advanced features together
        print("  🤝 Testing Advanced Features Integration...")
        
        # Initialize all advanced features
        competitive_framework = CompetitiveCooperativeFramework(team_manager, {})
        tournament_framework = InnovationTournamentFramework(team_manager, competitive_framework, {})
        improvement_engine = SelfImprovementEngine(team_manager, ollama_hub, {})
        regime_system = MarketRegimeAdaptationSystem(team_manager, data_manager, {})
        performance_optimizer = AdvancedPerformanceOptimizer(team_manager, analytics_engine, {})
        
        # Initialize all systems
        advanced_systems = [
            ("Competitive Framework", competitive_framework),
            ("Tournament Framework", tournament_framework),
            ("Self-Improvement Engine", improvement_engine),
            ("Regime Adaptation", regime_system),
            ("Performance Optimizer", performance_optimizer)
        ]
        
        advanced_results = {}
        
        for system_name, system in advanced_systems:
            try:
                init_success = await system.initialize()
                start_success = await system.start()
                
                if init_success and start_success:
                    # Test basic functionality
                    if hasattr(system, 'get_framework_status'):
                        status = await system.get_framework_status()
                    elif hasattr(system, 'get_tournament_status'):
                        status = await system.get_tournament_status()
                    elif hasattr(system, 'get_improvement_status'):
                        status = await system.get_improvement_status()
                    elif hasattr(system, 'get_regime_performance'):
                        status = await system.get_regime_performance()
                    elif hasattr(system, 'get_optimizer_status'):
                        status = await system.get_optimizer_status()
                    else:
                        status = {'running': True}
                        
                    await system.stop()
                    
                    if status:
                        print(f"    ✅ {system_name}: WORKING")
                        advanced_results[system_name.lower().replace(' ', '_')] = True
                    else:
                        print(f"    ⚠️ {system_name}: PARTIAL")
                        advanced_results[system_name.lower().replace(' ', '_')] = False
                else:
                    print(f"    ❌ {system_name}: FAILED")
                    advanced_results[system_name.lower().replace(' ', '_')] = False
                    
            except Exception as e:
                print(f"    ❌ {system_name}: ERROR - {e}")
                advanced_results[system_name.lower().replace(' ', '_')] = False
                
        results['advanced_features'] = advanced_results
        
        # Phase 3: System Coordinator Integration
        print("\n🎛️ PHASE 3: SYSTEM COORDINATOR INTEGRATION")
        
        print("  🏗️ Testing System Coordinator...")
        try:
            coordinator = SystemCoordinator('config/test_config.yaml')
            
            coord_init = await coordinator.initialize()
            coord_start = await coordinator.start()
            
            if coord_init and coord_start:
                # Test component access
                components = {}
                component_names = ['team_manager', 'data_manager', 'analytics_engine', 'ollama_hub']
                
                for comp_name in component_names:
                    comp = await coordinator.get_component(comp_name)
                    components[comp_name] = comp is not None
                    
                coord_status = await coordinator.get_system_status()
                await coordinator.stop()
                
                if coord_status and any(components.values()):
                    print("    ✅ System Coordinator: WORKING")
                    results['system_coordinator'] = True
                else:
                    print("    ⚠️ System Coordinator: PARTIAL")
                    results['system_coordinator'] = False
            else:
                print("    ❌ System Coordinator: FAILED")
                results['system_coordinator'] = False
                
        except Exception as e:
            print(f"    ❌ System Coordinator: ERROR - {e}")
            results['system_coordinator'] = False
            
        # Phase 4: Integration Assessment
        print("\n🔗 PHASE 4: INTEGRATION ASSESSMENT")
        
        # Calculate overall scores
        core_systems = ['config_manager', 'logging_audit', 'mock_data', 'paper_trading']
        core_working = sum(1 for sys in core_systems if results.get(sys, False))
        core_score = (core_working / len(core_systems)) * 100
        
        advanced_working = sum(1 for result in advanced_results.values() if result)
        advanced_total = len(advanced_results)
        advanced_score = (advanced_working / advanced_total) * 100 if advanced_total > 0 else 0
        
        coordinator_score = 100 if results.get('system_coordinator', False) else 0
        
        overall_score = (core_score * 0.4 + advanced_score * 0.4 + coordinator_score * 0.2)
        
        print(f"  📊 Core Systems Score: {core_score:.1f}% ({core_working}/{len(core_systems)})")
        print(f"  📊 Advanced Features Score: {advanced_score:.1f}% ({advanced_working}/{advanced_total})")
        print(f"  📊 System Coordinator Score: {coordinator_score:.1f}%")
        print(f"  📊 Overall Integration Score: {overall_score:.1f}%")
        
        # Final Assessment
        print("\n🎉 FINAL INTEGRATION ASSESSMENT")
        print("=" * 80)
        
        total_components = len(core_systems) + advanced_total + 1  # +1 for coordinator
        working_components = core_working + advanced_working + (1 if results.get('system_coordinator', False) else 0)
        
        print(f"📊 Working Components: {working_components}/{total_components}")
        print(f"🔧 Integration Success Rate: {overall_score:.1f}%")
        
        # Detailed results
        print("\n📋 DETAILED INTEGRATION RESULTS:")
        
        print("  🏗️ Core Systems:")
        for system in core_systems:
            status = "✅ WORKING" if results.get(system, False) else "❌ FAILED"
            print(f"    {system.replace('_', ' ').title()}: {status}")
            
        print("  🎯 Advanced Features:")
        for feature, working in advanced_results.items():
            status = "✅ WORKING" if working else "❌ FAILED"
            print(f"    {feature.replace('_', ' ').title()}: {status}")
            
        print("  🎛️ System Integration:")
        coord_status = "✅ WORKING" if results.get('system_coordinator', False) else "❌ FAILED"
        print(f"    System Coordinator: {coord_status}")
        
        # Save comprehensive results
        integration_summary = {
            "timestamp": datetime.now().isoformat(),
            "test_type": "complete_system_integration",
            "core_systems": {sys: results.get(sys, False) for sys in core_systems},
            "advanced_features": advanced_results,
            "system_coordinator": results.get('system_coordinator', False),
            "scores": {
                "core_score": core_score,
                "advanced_score": advanced_score,
                "coordinator_score": coordinator_score,
                "overall_score": overall_score
            },
            "summary": {
                "total_components": total_components,
                "working_components": working_components,
                "integration_success_rate": overall_score,
                "system_ready": overall_score >= 80.0,
                "production_ready": overall_score >= 90.0
            }
        }
        
        with open('complete_system_integration_results.json', 'w') as f:
            json.dump(integration_summary, f, indent=2, default=str)
        
        print(f"\n📄 Complete results saved to: complete_system_integration_results.json")
        
        # Final verdict
        print("\n" + "=" * 80)
        if overall_score >= 95:
            print("🎉 OUTSTANDING! WORLD-CLASS SYSTEM INTEGRATION!")
            print("🚀 All systems operational with excellent integration!")
            print("🏆 Ready for sophisticated trading operations!")
        elif overall_score >= 85:
            print("🎉 EXCELLENT! COMPREHENSIVE SYSTEM INTEGRATION!")
            print("🚀 Most systems working with good integration!")
            print("✅ Ready for advanced trading operations!")
        elif overall_score >= 75:
            print("✅ VERY GOOD! SOLID SYSTEM INTEGRATION!")
            print("🔧 Core systems working with minor issues!")
            print("💪 Strong foundation for trading operations!")
        elif overall_score >= 65:
            print("✅ GOOD! BASIC SYSTEM INTEGRATION!")
            print("🛠️ Some systems working, needs improvement!")
            print("📈 Good progress on system assembly!")
        else:
            print("⚠️ NEEDS SIGNIFICANT IMPROVEMENT!")
            print("🔧 Major issues with system integration!")
            print("📋 Review failed components and address issues!")
        
        print("=" * 80)
        
        return overall_score >= 75.0
        
    except Exception as e:
        print(f"❌ Complete System Integration Test Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_complete_system_integration())
    if success:
        print("\n🎉 COMPLETE SYSTEM INTEGRATION TEST SUCCESSFUL!")
        print("🚀 Advanced Ollama Trading Agents System is FULLY ASSEMBLED!")
    else:
        print("\n⚠️ SYSTEM INTEGRATION NEEDS ATTENTION!")
        print("🔧 Review test results and address issues!")

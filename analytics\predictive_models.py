"""
Predictive Models - Machine learning models for trading predictions
"""

import asyncio
import logging
import time
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum

logger = logging.getLogger(__name__)


class ModelType(Enum):
    """Types of predictive models"""
    LINEAR_REGRESSION = "linear_regression"
    RANDOM_FOREST = "random_forest"
    LSTM = "lstm"
    TRANSFORMER = "transformer"
    ENSEMBLE = "ensemble"


@dataclass
class ModelPrediction:
    """Result of a model prediction"""
    symbol: str
    prediction_type: str
    predicted_value: float
    confidence: float
    prediction_horizon: str
    features_used: List[str]
    model_name: str
    timestamp: float


@dataclass
class ModelPerformance:
    """Model performance metrics"""
    model_name: str
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    mse: float
    mae: float
    sharpe_ratio: float
    total_predictions: int
    correct_predictions: int


class PredictiveModel:
    """Base class for predictive models"""
    
    def __init__(self, model_name: str, model_type: ModelType, config: Dict[str, Any]):
        self.model_name = model_name
        self.model_type = model_type
        self.config = config
        
        # Model state
        self.trained = False
        self.last_training = None
        self.performance = ModelPerformance(
            model_name=model_name,
            accuracy=0.0,
            precision=0.0,
            recall=0.0,
            f1_score=0.0,
            mse=0.0,
            mae=0.0,
            sharpe_ratio=0.0,
            total_predictions=0,
            correct_predictions=0
        )
        
    async def train(self, training_data: List[Dict[str, Any]]) -> bool:
        """Train the model"""
        try:
            # Simulate training
            await asyncio.sleep(0.1)  # Simulate training time
            
            self.trained = True
            self.last_training = time.time()
            
            # Mock performance metrics
            self.performance.accuracy = np.random.uniform(0.6, 0.85)
            self.performance.precision = np.random.uniform(0.6, 0.8)
            self.performance.recall = np.random.uniform(0.6, 0.8)
            self.performance.f1_score = 2 * (self.performance.precision * self.performance.recall) / (self.performance.precision + self.performance.recall)
            self.performance.mse = np.random.uniform(0.01, 0.05)
            self.performance.mae = np.random.uniform(0.01, 0.03)
            self.performance.sharpe_ratio = np.random.uniform(0.5, 2.0)
            
            logger.info(f"Model {self.model_name} trained successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error training model {self.model_name}: {e}")
            return False
            
    async def predict(self, features: Dict[str, Any]) -> ModelPrediction:
        """Make a prediction"""
        try:
            if not self.trained:
                # Train with mock data if not trained
                await self.train([])
            
            # Mock prediction
            symbol = features.get('symbol', 'UNKNOWN')
            prediction_type = features.get('prediction_type', 'price_direction')
            
            if prediction_type == 'price_direction':
                predicted_value = np.random.choice([0, 1])  # 0 = down, 1 = up
                confidence = np.random.uniform(0.5, 0.9)
            elif prediction_type == 'price_change':
                predicted_value = np.random.uniform(-0.05, 0.05)  # -5% to +5%
                confidence = np.random.uniform(0.4, 0.8)
            elif prediction_type == 'volatility':
                predicted_value = np.random.uniform(0.1, 0.4)  # 10% to 40%
                confidence = np.random.uniform(0.6, 0.85)
            else:
                predicted_value = 0.0
                confidence = 0.5
            
            # Update performance tracking
            self.performance.total_predictions += 1
            
            return ModelPrediction(
                symbol=symbol,
                prediction_type=prediction_type,
                predicted_value=predicted_value,
                confidence=confidence,
                prediction_horizon=features.get('horizon', '1d'),
                features_used=list(features.keys()),
                model_name=self.model_name,
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"Error making prediction with {self.model_name}: {e}")
            return ModelPrediction(
                symbol=features.get('symbol', 'UNKNOWN'),
                prediction_type=features.get('prediction_type', 'unknown'),
                predicted_value=0.0,
                confidence=0.0,
                prediction_horizon='1d',
                features_used=[],
                model_name=self.model_name,
                timestamp=time.time()
            )
            
    async def update_performance(self, actual_value: float, predicted_value: float):
        """Update model performance with actual results"""
        try:
            # Simple accuracy update
            if abs(actual_value - predicted_value) < 0.01:  # Within 1%
                self.performance.correct_predictions += 1
                
            if self.performance.total_predictions > 0:
                self.performance.accuracy = self.performance.correct_predictions / self.performance.total_predictions
                
        except Exception as e:
            logger.error(f"Error updating performance for {self.model_name}: {e}")


class PredictiveModelsManager:
    """
    Manages multiple predictive models for different types of predictions
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.models_config = config.get('predictive_models', {})
        
        # Model registry
        self.models: Dict[str, PredictiveModel] = {}
        self.ensemble_models: Dict[str, List[str]] = {}
        
        # State
        self.initialized = False
        
        # Configuration
        self.auto_retrain = self.models_config.get('auto_retrain', True)
        self.retrain_interval = self.models_config.get('retrain_interval', 86400)  # 24 hours
        
    async def initialize(self):
        """Initialize the predictive models manager"""
        if self.initialized:
            return
            
        logger.info("Initializing Predictive Models Manager...")
        
        try:
            # Initialize default models
            await self._init_default_models()
            
            # Initialize ensemble models
            self._init_ensemble_models()
            
            self.initialized = True
            logger.info("✓ Predictive Models Manager initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Predictive Models Manager: {e}")
            raise
            
    async def _init_default_models(self):
        """Initialize default predictive models"""
        default_models = [
            {
                'name': 'price_direction_rf',
                'type': ModelType.RANDOM_FOREST,
                'config': {'n_estimators': 100, 'max_depth': 10}
            },
            {
                'name': 'price_change_lr',
                'type': ModelType.LINEAR_REGRESSION,
                'config': {'regularization': 'l2'}
            },
            {
                'name': 'volatility_lstm',
                'type': ModelType.LSTM,
                'config': {'sequence_length': 60, 'hidden_units': 50}
            },
            {
                'name': 'momentum_transformer',
                'type': ModelType.TRANSFORMER,
                'config': {'attention_heads': 8, 'layers': 4}
            }
        ]
        
        for model_config in default_models:
            model = PredictiveModel(
                model_name=model_config['name'],
                model_type=model_config['type'],
                config=model_config['config']
            )
            
            self.models[model_config['name']] = model
            
        logger.info(f"Initialized {len(self.models)} predictive models")
        
    def _init_ensemble_models(self):
        """Initialize ensemble models"""
        self.ensemble_models = {
            'price_direction_ensemble': ['price_direction_rf', 'momentum_transformer'],
            'price_change_ensemble': ['price_change_lr', 'volatility_lstm'],
            'volatility_ensemble': ['volatility_lstm', 'price_change_lr']
        }
        
        logger.info(f"Initialized {len(self.ensemble_models)} ensemble models")
        
    async def get_prediction(self, 
                           model_name: str,
                           features: Dict[str, Any],
                           use_ensemble: bool = False) -> ModelPrediction:
        """Get prediction from a specific model or ensemble"""
        try:
            if use_ensemble and model_name in self.ensemble_models:
                return await self._get_ensemble_prediction(model_name, features)
            elif model_name in self.models:
                return await self.models[model_name].predict(features)
            else:
                logger.warning(f"Model {model_name} not found")
                return ModelPrediction(
                    symbol=features.get('symbol', 'UNKNOWN'),
                    prediction_type=features.get('prediction_type', 'unknown'),
                    predicted_value=0.0,
                    confidence=0.0,
                    prediction_horizon='1d',
                    features_used=[],
                    model_name=model_name,
                    timestamp=time.time()
                )
                
        except Exception as e:
            logger.error(f"Error getting prediction from {model_name}: {e}")
            return ModelPrediction(
                symbol=features.get('symbol', 'UNKNOWN'),
                prediction_type=features.get('prediction_type', 'unknown'),
                predicted_value=0.0,
                confidence=0.0,
                prediction_horizon='1d',
                features_used=[],
                model_name=model_name,
                timestamp=time.time()
            )
            
    async def _get_ensemble_prediction(self, 
                                     ensemble_name: str,
                                     features: Dict[str, Any]) -> ModelPrediction:
        """Get ensemble prediction by combining multiple models"""
        try:
            model_names = self.ensemble_models[ensemble_name]
            predictions = []
            
            # Get predictions from all models in ensemble
            for model_name in model_names:
                if model_name in self.models:
                    pred = await self.models[model_name].predict(features)
                    predictions.append(pred)
            
            if not predictions:
                return ModelPrediction(
                    symbol=features.get('symbol', 'UNKNOWN'),
                    prediction_type=features.get('prediction_type', 'unknown'),
                    predicted_value=0.0,
                    confidence=0.0,
                    prediction_horizon='1d',
                    features_used=[],
                    model_name=ensemble_name,
                    timestamp=time.time()
                )
            
            # Combine predictions (simple average)
            avg_prediction = sum(p.predicted_value for p in predictions) / len(predictions)
            avg_confidence = sum(p.confidence for p in predictions) / len(predictions)
            
            # Boost confidence for ensemble
            ensemble_confidence = min(avg_confidence * 1.1, 1.0)
            
            return ModelPrediction(
                symbol=predictions[0].symbol,
                prediction_type=predictions[0].prediction_type,
                predicted_value=avg_prediction,
                confidence=ensemble_confidence,
                prediction_horizon=predictions[0].prediction_horizon,
                features_used=list(set().union(*[p.features_used for p in predictions])),
                model_name=ensemble_name,
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"Error getting ensemble prediction: {e}")
            return ModelPrediction(
                symbol=features.get('symbol', 'UNKNOWN'),
                prediction_type=features.get('prediction_type', 'unknown'),
                predicted_value=0.0,
                confidence=0.0,
                prediction_horizon='1d',
                features_used=[],
                model_name=ensemble_name,
                timestamp=time.time()
            )
            
    async def train_model(self, model_name: str, training_data: List[Dict[str, Any]]) -> bool:
        """Train a specific model"""
        try:
            if model_name in self.models:
                return await self.models[model_name].train(training_data)
            else:
                logger.warning(f"Model {model_name} not found for training")
                return False
                
        except Exception as e:
            logger.error(f"Error training model {model_name}: {e}")
            return False
            
    async def train_all_models(self, training_data: List[Dict[str, Any]]) -> Dict[str, bool]:
        """Train all models"""
        results = {}
        
        for model_name in self.models:
            results[model_name] = await self.train_model(model_name, training_data)
            
        return results
        
    async def get_model_performance(self, model_name: str = None) -> Dict[str, ModelPerformance]:
        """Get performance metrics for models"""
        if model_name:
            if model_name in self.models:
                return {model_name: self.models[model_name].performance}
            else:
                return {}
        else:
            return {name: model.performance for name, model in self.models.items()}
            
    async def get_available_models(self) -> Dict[str, Any]:
        """Get list of available models"""
        return {
            'individual_models': {
                name: {
                    'type': model.model_type.value,
                    'trained': model.trained,
                    'last_training': model.last_training,
                    'total_predictions': model.performance.total_predictions,
                    'accuracy': model.performance.accuracy
                }
                for name, model in self.models.items()
            },
            'ensemble_models': {
                name: {
                    'component_models': models,
                    'total_components': len(models)
                }
                for name, models in self.ensemble_models.items()
            }
        }
        
    async def get_stats(self) -> Dict[str, Any]:
        """Get predictive models statistics"""
        total_predictions = sum(model.performance.total_predictions for model in self.models.values())
        avg_accuracy = sum(model.performance.accuracy for model in self.models.values()) / len(self.models) if self.models else 0.0
        
        return {
            'initialized': self.initialized,
            'total_models': len(self.models),
            'ensemble_models': len(self.ensemble_models),
            'total_predictions': total_predictions,
            'average_accuracy': avg_accuracy,
            'auto_retrain': self.auto_retrain,
            'retrain_interval': self.retrain_interval
        }


# Alias for backward compatibility
PredictiveModels = PredictiveModelsManager

"""
Model Deployment Manager - Handles deployment and lifecycle of model instances
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
import aiohttp

from .model_instance import OllamaModelInstance
from .model_registry import OllamaModelRegistry
from .model_config import ModelConfigStore

logger = logging.getLogger(__name__)


class ModelDeploymentManager:
    """
    Manages deployment and lifecycle of Ollama model instances.
    Handles model assignment, resource allocation, and failover.
    """
    
    def __init__(self, 
                 registry: OllamaModelRegistry,
                 config_store: ModelConfigStore,
                 session: aiohttp.ClientSession,
                 base_url: str = "http://localhost:11434"):
        
        self.registry = registry
        self.config_store = config_store
        self.session = session
        self.base_url = base_url
        
        # Deployed model instances
        self.deployed_models: Dict[str, OllamaModelInstance] = {}
        self.agent_models: Dict[str, str] = {}  # agent_name -> instance_key
        
        # State
        self.initialized = False
        self.running = False
        
        # Health monitoring
        self.health_check_interval = 300  # 5 minutes
        self.health_check_task: Optional[asyncio.Task] = None
        
    async def initialize(self):
        """Initialize the deployment manager"""
        if self.initialized:
            return
            
        logger.info("Initializing Model Deployment Manager...")
        
        try:
            # Pre-deploy primary models for each role
            await self._predeploy_primary_models()
            
            self.initialized = True
            logger.info(f"✓ Deployment Manager initialized with {len(self.deployed_models)} models")
            
        except Exception as e:
            logger.error(f"Failed to initialize Deployment Manager: {e}")
            raise
            
    async def start(self):
        """Start the deployment manager"""
        if not self.initialized:
            await self.initialize()
            
        if self.running:
            return
            
        logger.info("Starting Model Deployment Manager...")
        
        # Start health monitoring
        self.health_check_task = asyncio.create_task(self._periodic_health_check())
        self.running = True
        
        logger.info("✓ Deployment Manager started")
        
    async def stop(self):
        """Stop the deployment manager"""
        if not self.running:
            return
            
        logger.info("Stopping Model Deployment Manager...")
        self.running = False
        
        # Cancel health check task
        if self.health_check_task and not self.health_check_task.done():
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
                
        logger.info("✓ Deployment Manager stopped")
        
    async def _predeploy_primary_models(self):
        """Pre-deploy primary models for each role"""
        role_configs = await self.config_store.get_all_role_configs()
        
        for role, role_config in role_configs.items():
            primary_model = role_config.get('primary')
            if primary_model and await self.registry.is_model_available(primary_model):
                try:
                    # Create a system instance for this role
                    instance_key = f"system_{role}_{primary_model}"
                    await self._deploy_model_instance(
                        instance_key=instance_key,
                        model_name=primary_model,
                        role=role,
                        agent_name="system"
                    )
                    logger.info(f"Pre-deployed {primary_model} for {role}")
                except Exception as e:
                    logger.warning(f"Failed to pre-deploy {primary_model} for {role}: {e}")
                    
    async def deploy_model_for_agent(self, 
                                   agent_name: str, 
                                   role: str, 
                                   model_name: str = None) -> Optional[OllamaModelInstance]:
        """Deploy a model instance for a specific agent"""
        try:
            # Determine model to use
            if not model_name:
                model_name = await self.config_store.get_primary_model_for_role(role)
                
            if not model_name:
                logger.error(f"No model specified for agent {agent_name} role {role}")
                return None
                
            # Check if model is available
            if not await self.registry.is_model_available(model_name):
                logger.warning(f"Model {model_name} not available, trying fallback")
                fallback_model = await self.config_store.get_fallback_model_for_role(role)
                if fallback_model and await self.registry.is_model_available(fallback_model):
                    model_name = fallback_model
                else:
                    logger.error(f"No available model for agent {agent_name} role {role}")
                    return None
                    
            # Create instance key
            instance_key = f"{agent_name}_{role}_{model_name}"
            
            # Check if already deployed
            if instance_key in self.deployed_models:
                logger.info(f"Model instance already deployed: {instance_key}")
                return self.deployed_models[instance_key]
                
            # Deploy new instance
            instance = await self._deploy_model_instance(
                instance_key=instance_key,
                model_name=model_name,
                role=role,
                agent_name=agent_name
            )
            
            if instance:
                self.agent_models[agent_name] = instance_key
                logger.info(f"✓ Deployed {model_name} for agent {agent_name} ({role})")
                
            return instance
            
        except Exception as e:
            logger.error(f"Failed to deploy model for agent {agent_name}: {e}")
            return None
            
    async def _deploy_model_instance(self, 
                                   instance_key: str,
                                   model_name: str,
                                   role: str,
                                   agent_name: str) -> Optional[OllamaModelInstance]:
        """Deploy a single model instance"""
        try:
            # Get configuration
            config = await self.config_store.get_config(model_name, role)
            
            # Create model instance
            instance = OllamaModelInstance(
                model_name=model_name,
                role=role,
                agent_name=agent_name,
                config=config,
                session=self.session,
                base_url=self.base_url
            )
            
            # Test the instance
            health_check = await instance.health_check()
            if health_check['status'] != 'healthy':
                logger.error(f"Model instance failed health check: {health_check}")
                return None
                
            # Store the instance
            self.deployed_models[instance_key] = instance
            
            return instance
            
        except Exception as e:
            logger.error(f"Failed to deploy model instance {instance_key}: {e}")
            return None
            
    async def get_model_instance(self, agent_name: str) -> Optional[OllamaModelInstance]:
        """Get model instance for an agent"""
        instance_key = self.agent_models.get(agent_name)
        if instance_key:
            return self.deployed_models.get(instance_key)
        return None
        
    async def undeploy_model(self, agent_name: str) -> bool:
        """Undeploy model for an agent"""
        try:
            instance_key = self.agent_models.get(agent_name)
            if instance_key:
                # Remove from deployed models
                if instance_key in self.deployed_models:
                    del self.deployed_models[instance_key]
                    
                # Remove agent mapping
                del self.agent_models[agent_name]
                
                logger.info(f"✓ Undeployed model for agent {agent_name}")
                return True
            else:
                logger.warning(f"No deployed model found for agent {agent_name}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to undeploy model for agent {agent_name}: {e}")
            return False
            
    async def switch_model(self, agent_name: str, new_model_name: str, role: str) -> bool:
        """Switch model for an agent"""
        try:
            # Undeploy current model
            await self.undeploy_model(agent_name)
            
            # Deploy new model
            instance = await self.deploy_model_for_agent(agent_name, role, new_model_name)
            
            if instance:
                logger.info(f"✓ Switched model for agent {agent_name} to {new_model_name}")
                return True
            else:
                logger.error(f"Failed to switch model for agent {agent_name} to {new_model_name}")
                return False
                
        except Exception as e:
            logger.error(f"Error switching model for agent {agent_name}: {e}")
            return False
            
    async def get_deployed_models(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all deployed models"""
        deployed_info = {}
        
        for instance_key, instance in self.deployed_models.items():
            deployed_info[instance_key] = {
                'model_name': instance.model_name,
                'role': instance.role,
                'agent_name': instance.agent_name,
                'active': instance.active,
                'metrics': instance.get_performance_metrics()
            }
            
        return deployed_info
        
    async def get_deployment_stats(self) -> Dict[str, Any]:
        """Get deployment statistics"""
        stats = {
            'total_deployed': len(self.deployed_models),
            'active_instances': 0,
            'agents_with_models': len(self.agent_models),
            'models_by_role': {},
            'performance_summary': {
                'avg_response_time': 0.0,
                'total_requests': 0,
                'total_errors': 0
            }
        }
        
        total_response_time = 0.0
        total_requests = 0
        total_errors = 0
        
        for instance in self.deployed_models.values():
            if instance.active:
                stats['active_instances'] += 1
                
            # Count by role
            role = instance.role
            stats['models_by_role'][role] = stats['models_by_role'].get(role, 0) + 1
            
            # Aggregate performance metrics
            metrics = instance.get_performance_metrics()
            total_requests += metrics['total_requests']
            total_errors += metrics['error_count']
            
            if metrics['total_requests'] > 0:
                total_response_time += metrics['metrics']['avg_response_time'] * metrics['total_requests']
                
        # Calculate averages
        if total_requests > 0:
            stats['performance_summary']['avg_response_time'] = total_response_time / total_requests
            
        stats['performance_summary']['total_requests'] = total_requests
        stats['performance_summary']['total_errors'] = total_errors
        
        return stats
        
    async def _periodic_health_check(self):
        """Periodic health check for all deployed models"""
        while self.running:
            try:
                await asyncio.sleep(self.health_check_interval)
                if self.running:
                    await self._perform_health_checks()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic health check: {e}")
                
    async def _perform_health_checks(self):
        """Perform health checks on all deployed models"""
        unhealthy_instances = []
        
        for instance_key, instance in self.deployed_models.items():
            try:
                health_check = await instance.health_check()
                if health_check['status'] != 'healthy':
                    unhealthy_instances.append((instance_key, instance, health_check))
                    logger.warning(f"Unhealthy model instance: {instance_key} - {health_check.get('error', 'Unknown error')}")
            except Exception as e:
                logger.error(f"Health check failed for {instance_key}: {e}")
                unhealthy_instances.append((instance_key, instance, {'error': str(e)}))
                
        # Handle unhealthy instances
        for instance_key, instance, health_check in unhealthy_instances:
            await self._handle_unhealthy_instance(instance_key, instance, health_check)
            
    async def _handle_unhealthy_instance(self, 
                                       instance_key: str, 
                                       instance: OllamaModelInstance, 
                                       health_check: Dict[str, Any]):
        """Handle an unhealthy model instance"""
        try:
            # Try to switch to fallback model
            fallback_model = await self.config_store.get_fallback_model_for_role(instance.role)
            
            if fallback_model and fallback_model != instance.model_name:
                logger.info(f"Switching {instance.agent_name} from {instance.model_name} to fallback {fallback_model}")
                success = await self.switch_model(instance.agent_name, fallback_model, instance.role)
                
                if success:
                    logger.info(f"✓ Successfully switched to fallback model for {instance.agent_name}")
                else:
                    logger.error(f"Failed to switch to fallback model for {instance.agent_name}")
            else:
                logger.warning(f"No fallback available for unhealthy instance {instance_key}")
                
        except Exception as e:
            logger.error(f"Error handling unhealthy instance {instance_key}: {e}")

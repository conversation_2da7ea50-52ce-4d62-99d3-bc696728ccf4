"""
Intelligent Automation Package

Advanced automation capabilities for the trading system:
- Strategy deployment automation
- Risk monitoring and alerting
- Performance optimization
- Auto-scaling and resource management
- Self-healing capabilities
- Adaptive parameter tuning
"""

from .automation_engine import AutomationEngine
from .strategy_deployer import StrategyDeployer
from .risk_monitor import RiskMonitor
from .performance_optimizer import PerformanceOptimizer
from .auto_scaler import AutoScaler
from .self_healing import SelfHealingSystem

__all__ = [
    'AutomationEngine',
    'StrategyDeployer',
    'RiskMonitor',
    'PerformanceOptimizer',
    'AutoScaler',
    'SelfHealingSystem'
]

"""
Message Types and Definitions
"""

import time
import uuid
from enum import Enum
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional


class MessageType(Enum):
    """Types of messages in the system"""
    # Agent-to-Agent Communication
    ANALYSIS_REQUEST = "analysis_request"
    ANALYSIS_RESPONSE = "analysis_response"
    DECISION_REQUEST = "decision_request"
    DECISION_RESPONSE = "decision_response"
    
    # Team Communication
    TEAM_FORMATION = "team_formation"
    TEAM_DISSOLUTION = "team_dissolution"
    TEAM_UPDATE = "team_update"
    ROLE_ASSIGNMENT = "role_assignment"
    
    # Market Data
    MARKET_DATA = "market_data"
    MARKET_ALERT = "market_alert"
    PRICE_UPDATE = "price_update"
    
    # Trading Operations
    TRADE_SIGNAL = "trade_signal"
    TRADE_EXECUTION = "trade_execution"
    TRADE_RESULT = "trade_result"
    RISK_ALERT = "risk_alert"
    
    # System Messages
    HEARTBEAT = "heartbeat"
    STATUS_REQUEST = "status_request"
    STATUS_RESPONSE = "status_response"
    SHUTDOWN = "shutdown"
    
    # Learning and Adaptation
    PERFORMANCE_UPDATE = "performance_update"
    LEARNING_EVENT = "learning_event"
    STRATEGY_UPDATE = "strategy_update"


class MessagePriority(Enum):
    """Message priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class Message:
    """Base message class"""
    id: str
    type: MessageType
    sender: str
    recipient: str
    content: Dict[str, Any]
    timestamp: float
    priority: MessagePriority = MessagePriority.NORMAL
    correlation_id: Optional[str] = None
    expires_at: Optional[float] = None
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.timestamp:
            self.timestamp = time.time()
            
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary"""
        return {
            'id': self.id,
            'type': self.type.value,
            'sender': self.sender,
            'recipient': self.recipient,
            'content': self.content,
            'timestamp': self.timestamp,
            'priority': self.priority.value,
            'correlation_id': self.correlation_id,
            'expires_at': self.expires_at
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """Create message from dictionary"""
        return cls(
            id=data['id'],
            type=MessageType(data['type']),
            sender=data['sender'],
            recipient=data['recipient'],
            content=data['content'],
            timestamp=data['timestamp'],
            priority=MessagePriority(data.get('priority', MessagePriority.NORMAL.value)),
            correlation_id=data.get('correlation_id'),
            expires_at=data.get('expires_at')
        )
        
    def is_expired(self) -> bool:
        """Check if message has expired"""
        if self.expires_at is None:
            return False
        return time.time() > self.expires_at
        
    def set_expiry(self, seconds: int):
        """Set message expiry time"""
        self.expires_at = time.time() + seconds


# Convenience functions for creating common message types

def create_analysis_request(sender: str, recipient: str, data: Dict[str, Any], 
                          analysis_type: str = "general") -> Message:
    """Create an analysis request message"""
    return Message(
        id=str(uuid.uuid4()),
        type=MessageType.ANALYSIS_REQUEST,
        sender=sender,
        recipient=recipient,
        content={
            'data': data,
            'analysis_type': analysis_type,
            'requested_at': time.time()
        },
        timestamp=time.time(),
        priority=MessagePriority.NORMAL
    )


def create_analysis_response(sender: str, recipient: str, analysis: Dict[str, Any],
                           correlation_id: str) -> Message:
    """Create an analysis response message"""
    return Message(
        id=str(uuid.uuid4()),
        type=MessageType.ANALYSIS_RESPONSE,
        sender=sender,
        recipient=recipient,
        content={
            'analysis': analysis,
            'completed_at': time.time()
        },
        timestamp=time.time(),
        priority=MessagePriority.NORMAL,
        correlation_id=correlation_id
    )


def create_decision_request(sender: str, recipient: str, options: list, 
                          context: Dict[str, Any]) -> Message:
    """Create a decision request message"""
    return Message(
        id=str(uuid.uuid4()),
        type=MessageType.DECISION_REQUEST,
        sender=sender,
        recipient=recipient,
        content={
            'options': options,
            'context': context,
            'requested_at': time.time()
        },
        timestamp=time.time(),
        priority=MessagePriority.HIGH
    )


def create_decision_response(sender: str, recipient: str, decision: Dict[str, Any],
                           correlation_id: str) -> Message:
    """Create a decision response message"""
    return Message(
        id=str(uuid.uuid4()),
        type=MessageType.DECISION_RESPONSE,
        sender=sender,
        recipient=recipient,
        content={
            'decision': decision,
            'completed_at': time.time()
        },
        timestamp=time.time(),
        priority=MessagePriority.HIGH,
        correlation_id=correlation_id
    )


def create_trade_signal(sender: str, recipient: str, signal: Dict[str, Any]) -> Message:
    """Create a trade signal message"""
    return Message(
        id=str(uuid.uuid4()),
        type=MessageType.TRADE_SIGNAL,
        sender=sender,
        recipient=recipient,
        content=signal,
        timestamp=time.time(),
        priority=MessagePriority.HIGH
    )


def create_risk_alert(sender: str, recipient: str, alert: Dict[str, Any]) -> Message:
    """Create a risk alert message"""
    return Message(
        id=str(uuid.uuid4()),
        type=MessageType.RISK_ALERT,
        sender=sender,
        recipient=recipient,
        content=alert,
        timestamp=time.time(),
        priority=MessagePriority.CRITICAL
    )


def create_heartbeat(sender: str, status: Dict[str, Any]) -> Message:
    """Create a heartbeat message"""
    return Message(
        id=str(uuid.uuid4()),
        type=MessageType.HEARTBEAT,
        sender=sender,
        recipient="system",
        content={
            'status': status,
            'heartbeat_time': time.time()
        },
        timestamp=time.time(),
        priority=MessagePriority.LOW
    )


def create_status_request(sender: str, recipient: str) -> Message:
    """Create a status request message"""
    return Message(
        id=str(uuid.uuid4()),
        type=MessageType.STATUS_REQUEST,
        sender=sender,
        recipient=recipient,
        content={
            'requested_at': time.time()
        },
        timestamp=time.time(),
        priority=MessagePriority.NORMAL
    )


def create_status_response(sender: str, recipient: str, status: Dict[str, Any],
                         correlation_id: str) -> Message:
    """Create a status response message"""
    return Message(
        id=str(uuid.uuid4()),
        type=MessageType.STATUS_RESPONSE,
        sender=sender,
        recipient=recipient,
        content={
            'status': status,
            'responded_at': time.time()
        },
        timestamp=time.time(),
        priority=MessagePriority.NORMAL,
        correlation_id=correlation_id
    )

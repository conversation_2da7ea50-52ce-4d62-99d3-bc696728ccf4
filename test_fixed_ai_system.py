#!/usr/bin/env python3
"""
Fixed AI System Test - Tests with correct task handlers and proper implementation
"""

import asyncio
import json
from datetime import datetime
from config.config_manager import ConfigManager
from models.ollama_hub import OllamaModelHub
from agents.agent_manager import AgentManager
from agents.base_agent import AgentRole

class SimpleMessageBroker:
    def __init__(self):
        self.messages = []
    
    async def publish(self, topic, message):
        self.messages.append({"topic": topic, "message": message, "timestamp": datetime.now()})
        print(f"📢 {topic}: {str(message)[:80]}...")
    
    async def subscribe(self, topic, callback):
        pass

async def test_fixed_ai_system():
    """Test AI system with correct task handlers"""
    
    print("🔧 FIXED AI SYSTEM TEST")
    print("=" * 50)
    
    try:
        # Setup
        config_manager = ConfigManager('config/test_config.yaml')
        await config_manager.load_config()
        config = await config_manager.get_config()
        
        ollama_hub = OllamaModelHub(config=config)
        await ollama_hub.initialize()
        
        message_broker = SimpleMessageBroker()
        
        agent_manager = AgentManager(ollama_hub, message_broker, config)
        await agent_manager.initialize()
        
        print("✅ System initialized")
        
        # Create AI agent team
        print("\n🏗️ Creating AI Agent Team...")
        
        analyst_id = await agent_manager.create_agent(
            role=AgentRole.MARKET_ANALYST,
            name="AI_Market_Analyst"
        )
        
        strategist_id = await agent_manager.create_agent(
            role=AgentRole.STRATEGY_DEVELOPER, 
            name="AI_Strategy_Developer"
        )
        
        risk_mgr_id = await agent_manager.create_agent(
            role=AgentRole.RISK_MANAGER,
            name="AI_Risk_Manager"
        )
        
        # Get agent instances
        analyst = await agent_manager.get_agent(analyst_id)
        strategist = await agent_manager.get_agent(strategist_id)
        risk_manager = await agent_manager.get_agent(risk_mgr_id)
        
        print(f"✅ Market Analyst: {analyst.model_instance.model_name}")
        print(f"✅ Strategy Developer: {strategist.model_instance.model_name}")
        print(f"✅ Risk Manager: {risk_manager.model_instance.model_name}")
        
        # Test 1: Market Analysis with correct task type
        print("\n📊 TEST 1: Market Technical Analysis")
        tech_analysis_task = {
            "type": "technical_analysis",  # Correct task type
            "symbol": "AAPL",
            "timeframe": "1h",
            "indicators": ["rsi", "macd", "bollinger_bands"]
        }
        
        analysis_result = await analyst.execute_task(tech_analysis_task)
        print(f"Analysis Result: {type(analysis_result)}")
        if analysis_result and analysis_result.get('success'):
            print(f"✅ Technical Analysis Success: {analysis_result.get('status')}")
            if 'analysis' in analysis_result:
                print(f"📊 Analysis Data: {str(analysis_result['analysis'])[:100]}...")
        else:
            print(f"❌ Technical Analysis Failed: {analysis_result}")
        
        # Test 2: Strategy Development with correct task type
        print("\n🎯 TEST 2: Strategy Development")
        strategy_task = {
            "type": "develop_strategy",  # Correct task type
            "strategy_type": "momentum",
            "market_conditions": {
                "trend": "bullish",
                "volatility": "moderate",
                "volume": "high"
            },
            "constraints": {
                "max_risk": 0.02,
                "capital": 100000,
                "timeframe": "daily"
            }
        }
        
        strategy_result = await strategist.execute_task(strategy_task)
        print(f"Strategy Result: {type(strategy_result)}")
        if strategy_result and strategy_result.get('success'):
            print(f"✅ Strategy Development Success: {strategy_result.get('status')}")
            if 'strategy' in strategy_result:
                print(f"🎯 Strategy Data: {str(strategy_result['strategy'])[:100]}...")
        else:
            print(f"❌ Strategy Development Failed: {strategy_result}")

        # Test 3: Risk Assessment with correct task type
        print("\n⚠️ TEST 3: Portfolio Risk Assessment")
        risk_task = {
            "type": "assess_portfolio_risk",  # Correct task type
            "portfolio": {
                "total_value": 100000,
                "positions": [
                    {"symbol": "AAPL", "value": 30000, "shares": 200},
                    {"symbol": "TSLA", "value": 25000, "shares": 100},
                    {"symbol": "GOOGL", "value": 20000, "shares": 8}
                ],
                "cash": 25000
            },
            "market_conditions": {
                "volatility": "high",
                "correlation": "increasing"
            }
        }

        risk_result = await risk_manager.execute_task(risk_task)
        print(f"Risk Result: {type(risk_result)}")
        if risk_result and risk_result.get('success'):
            print(f"✅ Risk Assessment Success: {risk_result.get('status')}")
            if 'risk_metrics' in risk_result:
                print(f"⚠️ Risk Metrics: {str(risk_result['risk_metrics'])[:100]}...")
        else:
            print(f"❌ Risk Assessment Failed: {risk_result}")

        # Test 4: Advanced Workflow - Strategy Backtesting
        print("\n🔬 TEST 4: Strategy Backtesting")
        backtest_task = {
            "type": "backtest_strategy",
            "strategy": strategy_result.get('strategy') if strategy_result else {"type": "momentum"},
            "historical_data": {
                "symbol": "AAPL",
                "start_date": "2024-01-01",
                "end_date": "2024-12-31",
                "timeframe": "1d"
            },
            "parameters": {
                "initial_capital": 100000,
                "commission": 0.001,
                "slippage": 0.0005
            }
        }

        backtest_result = await strategist.execute_task(backtest_task)
        print(f"Backtest Result: {type(backtest_result)}")
        if backtest_result and backtest_result.get('success'):
            print(f"✅ Backtesting Success: {backtest_result.get('status')}")
        else:
            print(f"❌ Backtesting Failed: {backtest_result}")

        # Test 5: Risk Monitoring
        print("\n🛡️ TEST 5: Risk Limit Monitoring")
        monitor_task = {
            "type": "monitor_risk_limits",
            "portfolio": risk_task["portfolio"],
            "limits": {
                "max_portfolio_risk": 0.02,
                "max_position_size": 0.1,
                "max_sector_exposure": 0.3
            }
        }

        monitor_result = await risk_manager.execute_task(monitor_task)
        print(f"Monitor Result: {type(monitor_result)}")
        if monitor_result and monitor_result.get('success'):
            print(f"✅ Risk Monitoring Success: {monitor_result.get('status')}")
        else:
            print(f"❌ Risk Monitoring Failed: {monitor_result}")
        
        # Summary
        print("\n🎉 FIXED AI SYSTEM TEST COMPLETE!")
        print("=" * 50)
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "agents": {
                "market_analyst": {
                    "id": analyst_id,
                    "model": analyst.model_instance.model_name,
                    "technical_analysis": analysis_result
                },
                "strategy_developer": {
                    "id": strategist_id,
                    "model": strategist.model_instance.model_name,
                    "strategy_development": strategy_result,
                    "backtesting": backtest_result
                },
                "risk_manager": {
                    "id": risk_mgr_id,
                    "model": risk_manager.model_instance.model_name,
                    "risk_assessment": risk_result,
                    "risk_monitoring": monitor_result
                }
            },
            "message_broker": {
                "messages_published": len(message_broker.messages)
            }
        }
        
        with open('fixed_ai_system_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"📄 Results saved to: fixed_ai_system_results.json")
        
        # Count successful tests
        successful_tests = 0
        total_tests = 5
        
        if analysis_result and analysis_result.get('success'): successful_tests += 1
        if strategy_result and strategy_result.get('success'): successful_tests += 1
        if risk_result and risk_result.get('success'): successful_tests += 1
        if backtest_result and backtest_result.get('success'): successful_tests += 1
        if monitor_result and monitor_result.get('success'): successful_tests += 1
        
        print(f"\n📊 Test Results: {successful_tests}/{total_tests} tests passed")
        print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
        
        return successful_tests == total_tests
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_fixed_ai_system())
    if success:
        print("\n🎉 ALL TESTS PASSED - AI SYSTEM FULLY FUNCTIONAL!")
    else:
        print("\n⚠️ SOME TESTS FAILED - SYSTEM NEEDS FIXES")

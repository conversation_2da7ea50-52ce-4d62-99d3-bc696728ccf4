"""
Allocation Engine - Handles portfolio allocation strategies and optimization
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import numpy as np

logger = logging.getLogger(__name__)


@dataclass
class AllocationTarget:
    """Represents an allocation target"""
    symbol: str
    target_weight: float
    current_weight: float
    target_value: float
    current_value: float
    rebalance_amount: float
    priority: int = 1


@dataclass
class AllocationResult:
    """Result of allocation calculation"""
    targets: List[AllocationTarget]
    total_rebalance_amount: float
    expected_return: float
    expected_risk: float
    sharpe_ratio: float
    success: bool
    message: str


class AllocationEngine:
    """
    Portfolio allocation engine that calculates optimal allocations
    based on various strategies and constraints.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.allocation_config = config.get('allocation', {})
        
        # Allocation strategies
        self.strategies = {
            'equal_weight': self._equal_weight_allocation,
            'market_cap': self._market_cap_allocation,
            'risk_parity': self._risk_parity_allocation,
            'momentum': self._momentum_allocation,
            'mean_reversion': self._mean_reversion_allocation,
            'custom': self._custom_allocation
        }
        
        # State
        self.initialized = False
        self.current_allocations = {}
        
        # Configuration
        self.default_strategy = self.allocation_config.get('default_strategy', 'equal_weight')
        self.rebalance_threshold = self.allocation_config.get('rebalance_threshold', 0.05)
        self.max_position_size = self.allocation_config.get('max_position_size', 0.2)
        self.min_position_size = self.allocation_config.get('min_position_size', 0.01)
        
    async def initialize(self):
        """Initialize the allocation engine"""
        if self.initialized:
            return
            
        logger.info("Initializing Allocation Engine...")
        
        try:
            # Initialize allocation strategies
            self._init_strategies()
            
            self.initialized = True
            logger.info("✓ Allocation Engine initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Allocation Engine: {e}")
            raise
            
    def _init_strategies(self):
        """Initialize allocation strategies"""
        logger.info(f"Initialized {len(self.strategies)} allocation strategies")
        
    async def calculate_allocation(self, 
                                 portfolio_value: float,
                                 current_positions: Dict[str, Dict[str, Any]],
                                 target_symbols: List[str],
                                 strategy: str = None,
                                 constraints: Dict[str, Any] = None) -> AllocationResult:
        """Calculate optimal portfolio allocation"""
        try:
            strategy = strategy or self.default_strategy
            constraints = constraints or {}
            
            if strategy not in self.strategies:
                return AllocationResult(
                    targets=[],
                    total_rebalance_amount=0.0,
                    expected_return=0.0,
                    expected_risk=0.0,
                    sharpe_ratio=0.0,
                    success=False,
                    message=f"Unknown strategy: {strategy}"
                )
            
            # Calculate allocation using selected strategy
            allocation_func = self.strategies[strategy]
            targets = await allocation_func(portfolio_value, current_positions, target_symbols, constraints)
            
            # Apply constraints
            targets = self._apply_constraints(targets, constraints)
            
            # Calculate metrics
            total_rebalance = sum(abs(target.rebalance_amount) for target in targets)
            expected_return = self._calculate_expected_return(targets)
            expected_risk = self._calculate_expected_risk(targets)
            sharpe_ratio = expected_return / max(expected_risk, 0.001)
            
            return AllocationResult(
                targets=targets,
                total_rebalance_amount=total_rebalance,
                expected_return=expected_return,
                expected_risk=expected_risk,
                sharpe_ratio=sharpe_ratio,
                success=True,
                message=f"Allocation calculated using {strategy} strategy"
            )
            
        except Exception as e:
            logger.error(f"Error calculating allocation: {e}")
            return AllocationResult(
                targets=[],
                total_rebalance_amount=0.0,
                expected_return=0.0,
                expected_risk=0.0,
                sharpe_ratio=0.0,
                success=False,
                message=f"Error: {e}"
            )
            
    async def _equal_weight_allocation(self, 
                                     portfolio_value: float,
                                     current_positions: Dict[str, Dict[str, Any]],
                                     target_symbols: List[str],
                                     constraints: Dict[str, Any]) -> List[AllocationTarget]:
        """Equal weight allocation strategy"""
        targets = []
        
        if not target_symbols:
            return targets
            
        target_weight = 1.0 / len(target_symbols)
        
        for symbol in target_symbols:
            current_position = current_positions.get(symbol, {})
            current_value = current_position.get('value', 0.0)
            current_weight = current_value / portfolio_value if portfolio_value > 0 else 0.0
            
            target_value = portfolio_value * target_weight
            rebalance_amount = target_value - current_value
            
            targets.append(AllocationTarget(
                symbol=symbol,
                target_weight=target_weight,
                current_weight=current_weight,
                target_value=target_value,
                current_value=current_value,
                rebalance_amount=rebalance_amount
            ))
            
        return targets
        
    async def _market_cap_allocation(self, 
                                   portfolio_value: float,
                                   current_positions: Dict[str, Dict[str, Any]],
                                   target_symbols: List[str],
                                   constraints: Dict[str, Any]) -> List[AllocationTarget]:
        """Market cap weighted allocation strategy"""
        # Simplified market cap allocation (would use real market cap data)
        targets = []
        
        # Mock market caps (in real implementation, would fetch from data source)
        mock_market_caps = {symbol: np.random.uniform(100e9, 3000e9) for symbol in target_symbols}
        total_market_cap = sum(mock_market_caps.values())
        
        for symbol in target_symbols:
            market_cap = mock_market_caps[symbol]
            target_weight = market_cap / total_market_cap
            
            current_position = current_positions.get(symbol, {})
            current_value = current_position.get('value', 0.0)
            current_weight = current_value / portfolio_value if portfolio_value > 0 else 0.0
            
            target_value = portfolio_value * target_weight
            rebalance_amount = target_value - current_value
            
            targets.append(AllocationTarget(
                symbol=symbol,
                target_weight=target_weight,
                current_weight=current_weight,
                target_value=target_value,
                current_value=current_value,
                rebalance_amount=rebalance_amount
            ))
            
        return targets
        
    async def _risk_parity_allocation(self, 
                                    portfolio_value: float,
                                    current_positions: Dict[str, Dict[str, Any]],
                                    target_symbols: List[str],
                                    constraints: Dict[str, Any]) -> List[AllocationTarget]:
        """Risk parity allocation strategy"""
        # Simplified risk parity (would use real volatility data)
        targets = []
        
        # Mock volatilities
        mock_volatilities = {symbol: np.random.uniform(0.15, 0.4) for symbol in target_symbols}
        inverse_vol_sum = sum(1.0 / vol for vol in mock_volatilities.values())
        
        for symbol in target_symbols:
            volatility = mock_volatilities[symbol]
            target_weight = (1.0 / volatility) / inverse_vol_sum
            
            current_position = current_positions.get(symbol, {})
            current_value = current_position.get('value', 0.0)
            current_weight = current_value / portfolio_value if portfolio_value > 0 else 0.0
            
            target_value = portfolio_value * target_weight
            rebalance_amount = target_value - current_value
            
            targets.append(AllocationTarget(
                symbol=symbol,
                target_weight=target_weight,
                current_weight=current_weight,
                target_value=target_value,
                current_value=current_value,
                rebalance_amount=rebalance_amount
            ))
            
        return targets
        
    async def _momentum_allocation(self, 
                                 portfolio_value: float,
                                 current_positions: Dict[str, Dict[str, Any]],
                                 target_symbols: List[str],
                                 constraints: Dict[str, Any]) -> List[AllocationTarget]:
        """Momentum-based allocation strategy"""
        # Simplified momentum allocation
        targets = []
        
        # Mock momentum scores
        momentum_scores = {symbol: np.random.uniform(-0.2, 0.3) for symbol in target_symbols}
        positive_momentum = {k: max(0, v) for k, v in momentum_scores.items()}
        total_momentum = sum(positive_momentum.values())
        
        if total_momentum == 0:
            # Fallback to equal weight if no positive momentum
            return await self._equal_weight_allocation(portfolio_value, current_positions, target_symbols, constraints)
        
        for symbol in target_symbols:
            momentum = positive_momentum[symbol]
            target_weight = momentum / total_momentum
            
            current_position = current_positions.get(symbol, {})
            current_value = current_position.get('value', 0.0)
            current_weight = current_value / portfolio_value if portfolio_value > 0 else 0.0
            
            target_value = portfolio_value * target_weight
            rebalance_amount = target_value - current_value
            
            targets.append(AllocationTarget(
                symbol=symbol,
                target_weight=target_weight,
                current_weight=current_weight,
                target_value=target_value,
                current_value=current_value,
                rebalance_amount=rebalance_amount
            ))
            
        return targets
        
    async def _mean_reversion_allocation(self, 
                                       portfolio_value: float,
                                       current_positions: Dict[str, Dict[str, Any]],
                                       target_symbols: List[str],
                                       constraints: Dict[str, Any]) -> List[AllocationTarget]:
        """Mean reversion allocation strategy"""
        # Simplified mean reversion allocation
        return await self._equal_weight_allocation(portfolio_value, current_positions, target_symbols, constraints)
        
    async def _custom_allocation(self, 
                               portfolio_value: float,
                               current_positions: Dict[str, Dict[str, Any]],
                               target_symbols: List[str],
                               constraints: Dict[str, Any]) -> List[AllocationTarget]:
        """Custom allocation strategy"""
        # Use custom weights from constraints if provided
        custom_weights = constraints.get('custom_weights', {})
        
        if not custom_weights:
            return await self._equal_weight_allocation(portfolio_value, current_positions, target_symbols, constraints)
        
        targets = []
        total_weight = sum(custom_weights.get(symbol, 0) for symbol in target_symbols)
        
        if total_weight == 0:
            return await self._equal_weight_allocation(portfolio_value, current_positions, target_symbols, constraints)
        
        for symbol in target_symbols:
            target_weight = custom_weights.get(symbol, 0) / total_weight
            
            current_position = current_positions.get(symbol, {})
            current_value = current_position.get('value', 0.0)
            current_weight = current_value / portfolio_value if portfolio_value > 0 else 0.0
            
            target_value = portfolio_value * target_weight
            rebalance_amount = target_value - current_value
            
            targets.append(AllocationTarget(
                symbol=symbol,
                target_weight=target_weight,
                current_weight=current_weight,
                target_value=target_value,
                current_value=current_value,
                rebalance_amount=rebalance_amount
            ))
            
        return targets
        
    def _apply_constraints(self, targets: List[AllocationTarget], constraints: Dict[str, Any]) -> List[AllocationTarget]:
        """Apply allocation constraints"""
        max_pos_size = constraints.get('max_position_size', self.max_position_size)
        min_pos_size = constraints.get('min_position_size', self.min_position_size)
        
        # Apply position size constraints
        for target in targets:
            if target.target_weight > max_pos_size:
                target.target_weight = max_pos_size
            elif target.target_weight < min_pos_size:
                target.target_weight = 0.0
                
        # Renormalize weights
        total_weight = sum(target.target_weight for target in targets)
        if total_weight > 0:
            for target in targets:
                target.target_weight /= total_weight
                
        return targets
        
    def _calculate_expected_return(self, targets: List[AllocationTarget]) -> float:
        """Calculate expected portfolio return"""
        # Simplified expected return calculation
        return sum(target.target_weight * 0.08 for target in targets)  # Assume 8% expected return
        
    def _calculate_expected_risk(self, targets: List[AllocationTarget]) -> float:
        """Calculate expected portfolio risk"""
        # Simplified risk calculation
        return np.sqrt(sum(target.target_weight ** 2 * 0.16 for target in targets))  # Assume 40% volatility
        
    async def check_rebalance_needed(self, 
                                   current_positions: Dict[str, Dict[str, Any]],
                                   target_allocation: AllocationResult) -> bool:
        """Check if rebalancing is needed"""
        try:
            for target in target_allocation.targets:
                weight_diff = abs(target.current_weight - target.target_weight)
                if weight_diff > self.rebalance_threshold:
                    return True
            return False
            
        except Exception as e:
            logger.error(f"Error checking rebalance need: {e}")
            return False
            
    async def get_stats(self) -> Dict[str, Any]:
        """Get allocation engine statistics"""
        return {
            'initialized': self.initialized,
            'strategies': list(self.strategies.keys()),
            'default_strategy': self.default_strategy,
            'rebalance_threshold': self.rebalance_threshold,
            'max_position_size': self.max_position_size,
            'min_position_size': self.min_position_size
        }

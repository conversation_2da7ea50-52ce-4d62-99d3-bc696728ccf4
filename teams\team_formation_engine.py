"""
Team Formation Engine - Comprehensive team formation and optimization system
"""

import asyncio
import logging
import time
import math
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass
import numpy as np

from agents.base_agent import AgentRole, AgentState

logger = logging.getLogger(__name__)


@dataclass
class AgentSkillProfile:
    """Agent skill profile for team formation"""
    agent_id: str
    role: AgentR<PERSON>
    skills: Dict[str, float]  # skill_name -> proficiency (0.0-1.0)
    experience_level: float  # 0.0-1.0
    availability: float  # 0.0-1.0
    performance_history: List[float]  # Recent performance scores
    specializations: List[str]
    collaboration_score: float  # How well they work in teams
    leadership_capability: float  # Leadership potential
    current_workload: float  # Current task load (0.0-1.0)


@dataclass
class TeamRequirement:
    """Team formation requirements"""
    team_type: str
    required_roles: List[AgentRole]
    optional_roles: List[AgentRole]
    required_skills: Dict[str, float]  # skill_name -> minimum_level
    preferred_skills: Dict[str, float]  # skill_name -> preferred_level
    team_size_range: Tuple[int, int]  # (min_size, max_size)
    urgency_level: float  # 0.0-1.0
    mission_complexity: float  # 0.0-1.0
    collaboration_importance: float  # 0.0-1.0
    leadership_requirement: float  # 0.0-1.0


class TeamFormationStrategy(Enum):
    """Team formation strategies"""
    OPTIMAL_PERFORMANCE = "optimal_performance"
    BALANCED_SKILLS = "balanced_skills"
    FAST_FORMATION = "fast_formation"
    EXPERIENCE_BASED = "experience_based"
    COLLABORATION_FOCUSED = "collaboration_focused"


class TeamFormationEngine:
    """
    Comprehensive team formation engine that optimizes team composition
    based on agent skills, availability, and mission requirements.
    """
    
    def __init__(self, agent_manager, config: Dict[str, Any]):
        self.agent_manager = agent_manager
        self.config = config
        
        # Agent profiles and skills
        self.agent_profiles: Dict[str, AgentSkillProfile] = {}
        self.skill_matrix: Dict[AgentRole, Dict[str, float]] = {}
        
        # Formation algorithms
        self.formation_strategies = {
            TeamFormationStrategy.OPTIMAL_PERFORMANCE: self._optimal_performance_formation,
            TeamFormationStrategy.BALANCED_SKILLS: self._balanced_skills_formation,
            TeamFormationStrategy.FAST_FORMATION: self._fast_formation,
            TeamFormationStrategy.EXPERIENCE_BASED: self._experience_based_formation,
            TeamFormationStrategy.COLLABORATION_FOCUSED: self._collaboration_focused_formation
        }
        
        # Performance tracking
        self.formation_history: List[Dict[str, Any]] = []
        self.team_success_rates: Dict[str, float] = {}
        
        # State
        self.initialized = False
        
    async def initialize(self):
        """Initialize team formation engine"""
        if self.initialized:
            return
            
        logger.info("Initializing Team Formation Engine...")
        
        # Setup skill matrix
        await self._setup_skill_matrix()
        
        # Initialize agent profiles
        await self._initialize_agent_profiles()
        
        # Setup formation algorithms
        await self._setup_formation_algorithms()
        
        self.initialized = True
        logger.info("✓ Team Formation Engine initialized")
        
    async def _setup_skill_matrix(self):
        """Setup skill matrix for different agent roles"""
        self.skill_matrix = {
            AgentRole.TEAM_LEADER: {
                'strategic_thinking': 0.9,
                'decision_making': 0.9,
                'leadership': 1.0,
                'communication': 0.8,
                'coordination': 0.9,
                'risk_assessment': 0.7,
                'planning': 0.8
            },
            AgentRole.MARKET_ANALYST: {
                'technical_analysis': 1.0,
                'pattern_recognition': 0.9,
                'data_analysis': 0.9,
                'market_knowledge': 1.0,
                'trend_identification': 0.9,
                'research': 0.8,
                'quantitative_analysis': 0.8
            },
            AgentRole.STRATEGY_DEVELOPER: {
                'strategy_design': 1.0,
                'backtesting': 0.9,
                'optimization': 0.9,
                'creativity': 0.9,
                'mathematical_modeling': 0.8,
                'algorithm_development': 0.8,
                'innovation': 0.9
            },
            AgentRole.RISK_MANAGER: {
                'risk_assessment': 1.0,
                'compliance': 0.9,
                'portfolio_analysis': 0.9,
                'stress_testing': 0.8,
                'regulatory_knowledge': 0.9,
                'quantitative_analysis': 0.8,
                'monitoring': 0.8
            },
            AgentRole.EXECUTION_SPECIALIST: {
                'order_execution': 1.0,
                'market_microstructure': 0.9,
                'algorithm_optimization': 0.8,
                'latency_optimization': 0.8,
                'cost_analysis': 0.8,
                'venue_selection': 0.7,
                'timing': 0.9
            },
            AgentRole.PERFORMANCE_EVALUATOR: {
                'performance_analysis': 1.0,
                'attribution_analysis': 0.9,
                'benchmarking': 0.8,
                'statistical_analysis': 0.9,
                'reporting': 0.8,
                'optimization_recommendations': 0.8,
                'feedback_analysis': 0.7
            }
        }
        
    async def _initialize_agent_profiles(self):
        """Initialize agent skill profiles"""
        # Get all agents from agent manager
        all_agents = await self.agent_manager.get_all_agents()
        
        for agent in all_agents:
            profile = await self._create_agent_profile(agent)
            self.agent_profiles[agent.agent_id] = profile
            
    async def _create_agent_profile(self, agent) -> AgentSkillProfile:
        """Create skill profile for an agent"""
        # Get base skills for agent role
        base_skills = self.skill_matrix.get(agent.role, {})
        
        # Add some variation to make agents unique
        skills = {}
        for skill, base_level in base_skills.items():
            # Add random variation ±0.1
            variation = np.random.uniform(-0.1, 0.1)
            skills[skill] = max(0.0, min(1.0, base_level + variation))
            
        # Get agent performance history
        performance_history = await self._get_agent_performance_history(agent.agent_id)
        
        # Calculate derived metrics
        experience_level = min(1.0, len(performance_history) / 100.0)  # Based on number of tasks
        collaboration_score = np.random.uniform(0.6, 0.9)  # Would be calculated from actual data
        leadership_capability = skills.get('leadership', 0.5)
        current_workload = await self._calculate_current_workload(agent.agent_id)
        
        # Determine specializations
        specializations = []
        for skill, level in skills.items():
            if level > 0.8:
                specializations.append(skill)
                
        return AgentSkillProfile(
            agent_id=agent.agent_id,
            role=agent.role,
            skills=skills,
            experience_level=experience_level,
            availability=1.0 - current_workload,
            performance_history=performance_history,
            specializations=specializations,
            collaboration_score=collaboration_score,
            leadership_capability=leadership_capability,
            current_workload=current_workload
        )
        
    async def _get_agent_performance_history(self, agent_id: str) -> List[float]:
        """Get agent performance history"""
        # This would get actual performance data from the agent
        # For now, return simulated data
        return [np.random.uniform(0.6, 0.9) for _ in range(np.random.randint(10, 50))]
        
    async def _calculate_current_workload(self, agent_id: str) -> float:
        """Calculate current workload for an agent"""
        # This would calculate actual workload from active tasks
        # For now, return simulated data
        return np.random.uniform(0.0, 0.7)
        
    async def _setup_formation_algorithms(self):
        """Setup formation algorithm parameters"""
        self.algorithm_params = {
            'optimal_performance': {
                'performance_weight': 0.4,
                'skill_match_weight': 0.3,
                'collaboration_weight': 0.2,
                'availability_weight': 0.1
            },
            'balanced_skills': {
                'skill_coverage_weight': 0.5,
                'skill_balance_weight': 0.3,
                'experience_weight': 0.2
            },
            'fast_formation': {
                'availability_weight': 0.6,
                'role_match_weight': 0.4
            },
            'experience_based': {
                'experience_weight': 0.5,
                'performance_weight': 0.3,
                'leadership_weight': 0.2
            },
            'collaboration_focused': {
                'collaboration_weight': 0.6,
                'team_chemistry_weight': 0.4
            }
        }
        
    async def form_team(self, requirements: TeamRequirement, 
                       strategy: TeamFormationStrategy = TeamFormationStrategy.OPTIMAL_PERFORMANCE) -> Dict[str, Any]:
        """Form a team based on requirements and strategy"""
        try:
            logger.info(f"Forming team with strategy: {strategy.value}")
            
            # Get formation algorithm
            formation_func = self.formation_strategies.get(strategy)
            if not formation_func:
                return {'success': False, 'error': f'Unknown formation strategy: {strategy}'}
                
            # Get candidate agents
            candidates = await self._get_candidate_agents(requirements)
            
            if len(candidates) < requirements.team_size_range[0]:
                return {'success': False, 'error': 'Insufficient candidate agents'}
                
            # Apply formation algorithm
            team_composition = await formation_func(candidates, requirements)
            
            # Validate team composition
            validation_result = await self._validate_team_composition(team_composition, requirements)
            
            if not validation_result['valid']:
                return {'success': False, 'error': validation_result['reason']}
                
            # Calculate team metrics
            team_metrics = await self._calculate_team_metrics(team_composition)
            
            # Record formation
            formation_record = {
                'strategy': strategy.value,
                'requirements': requirements,
                'team_composition': team_composition,
                'team_metrics': team_metrics,
                'timestamp': time.time()
            }
            self.formation_history.append(formation_record)
            
            return {
                'success': True,
                'team_composition': team_composition,
                'team_metrics': team_metrics,
                'formation_strategy': strategy.value
            }
            
        except Exception as e:
            logger.error(f"Error forming team: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _get_candidate_agents(self, requirements: TeamRequirement) -> List[AgentSkillProfile]:
        """Get candidate agents for team formation"""
        candidates = []
        
        # Get agents for required roles
        for role in requirements.required_roles:
            role_agents = [profile for profile in self.agent_profiles.values() 
                          if profile.role == role and profile.availability > 0.3]
            candidates.extend(role_agents)
            
        # Get agents for optional roles
        for role in requirements.optional_roles:
            role_agents = [profile for profile in self.agent_profiles.values() 
                          if profile.role == role and profile.availability > 0.3]
            candidates.extend(role_agents)
            
        # Remove duplicates
        unique_candidates = []
        seen_ids = set()
        for candidate in candidates:
            if candidate.agent_id not in seen_ids:
                unique_candidates.append(candidate)
                seen_ids.add(candidate.agent_id)
                
        return unique_candidates
        
    async def _optimal_performance_formation(self, candidates: List[AgentSkillProfile], 
                                           requirements: TeamRequirement) -> List[str]:
        """Optimal performance team formation algorithm"""
        params = self.algorithm_params['optimal_performance']
        
        # Score each candidate
        candidate_scores = []
        for candidate in candidates:
            score = 0.0
            
            # Performance score
            if candidate.performance_history:
                avg_performance = np.mean(candidate.performance_history)
                score += params['performance_weight'] * avg_performance
                
            # Skill match score
            skill_match = await self._calculate_skill_match(candidate, requirements)
            score += params['skill_match_weight'] * skill_match
            
            # Collaboration score
            score += params['collaboration_weight'] * candidate.collaboration_score
            
            # Availability score
            score += params['availability_weight'] * candidate.availability
            
            candidate_scores.append((candidate.agent_id, score))
            
        # Sort by score and select top candidates
        candidate_scores.sort(key=lambda x: x[1], reverse=True)
        
        # Select team ensuring role coverage
        selected_team = await self._select_with_role_coverage(
            candidate_scores, candidates, requirements
        )
        
        return selected_team
        
    async def _calculate_skill_match(self, candidate: AgentSkillProfile, 
                                   requirements: TeamRequirement) -> float:
        """Calculate how well candidate skills match requirements"""
        match_score = 0.0
        total_requirements = 0
        
        # Check required skills
        for skill, required_level in requirements.required_skills.items():
            candidate_level = candidate.skills.get(skill, 0.0)
            if candidate_level >= required_level:
                match_score += 1.0
            else:
                match_score += candidate_level / required_level
            total_requirements += 1
            
        # Check preferred skills (weighted less)
        for skill, preferred_level in requirements.preferred_skills.items():
            candidate_level = candidate.skills.get(skill, 0.0)
            if candidate_level >= preferred_level:
                match_score += 0.5
            else:
                match_score += 0.5 * (candidate_level / preferred_level)
            total_requirements += 0.5
            
        return match_score / total_requirements if total_requirements > 0 else 0.0
        
    async def _select_with_role_coverage(self, candidate_scores: List[Tuple[str, float]], 
                                       candidates: List[AgentSkillProfile],
                                       requirements: TeamRequirement) -> List[str]:
        """Select team ensuring all required roles are covered"""
        selected_team = []
        covered_roles = set()
        candidate_dict = {c.agent_id: c for c in candidates}
        
        # First, ensure all required roles are covered
        for agent_id, score in candidate_scores:
            candidate = candidate_dict[agent_id]
            
            if candidate.role in requirements.required_roles and candidate.role not in covered_roles:
                selected_team.append(agent_id)
                covered_roles.add(candidate.role)
                
                if len(selected_team) >= requirements.team_size_range[1]:
                    break
                    
        # Then add best remaining candidates up to max team size
        for agent_id, score in candidate_scores:
            if agent_id not in selected_team and len(selected_team) < requirements.team_size_range[1]:
                selected_team.append(agent_id)
                
        return selected_team
        
    async def _validate_team_composition(self, team_composition: List[str], 
                                       requirements: TeamRequirement) -> Dict[str, Any]:
        """Validate team composition against requirements"""
        if len(team_composition) < requirements.team_size_range[0]:
            return {'valid': False, 'reason': 'Team size below minimum'}
            
        if len(team_composition) > requirements.team_size_range[1]:
            return {'valid': False, 'reason': 'Team size above maximum'}
            
        # Check role coverage
        team_roles = set()
        for agent_id in team_composition:
            if agent_id in self.agent_profiles:
                team_roles.add(self.agent_profiles[agent_id].role)
                
        missing_roles = set(requirements.required_roles) - team_roles
        if missing_roles:
            return {'valid': False, 'reason': f'Missing required roles: {missing_roles}'}
            
        return {'valid': True, 'reason': 'Team composition valid'}
        
    async def _calculate_team_metrics(self, team_composition: List[str]) -> Dict[str, float]:
        """Calculate metrics for team composition"""
        if not team_composition:
            return {}
            
        team_profiles = [self.agent_profiles[agent_id] for agent_id in team_composition 
                        if agent_id in self.agent_profiles]
        
        if not team_profiles:
            return {}
            
        metrics = {
            'avg_experience': np.mean([p.experience_level for p in team_profiles]),
            'avg_performance': np.mean([np.mean(p.performance_history) if p.performance_history else 0.5 
                                     for p in team_profiles]),
            'avg_collaboration': np.mean([p.collaboration_score for p in team_profiles]),
            'avg_availability': np.mean([p.availability for p in team_profiles]),
            'leadership_strength': max([p.leadership_capability for p in team_profiles]),
            'skill_diversity': len(set().union(*[p.specializations for p in team_profiles])),
            'team_size': len(team_composition)
        }
        
        return metrics
        
    # Placeholder implementations for other formation strategies
    async def _balanced_skills_formation(self, candidates: List[AgentSkillProfile], 
                                       requirements: TeamRequirement) -> List[str]:
        """Balanced skills formation algorithm"""
        # Implementation would focus on skill balance and coverage
        return await self._optimal_performance_formation(candidates, requirements)
        
    async def _fast_formation(self, candidates: List[AgentSkillProfile], 
                            requirements: TeamRequirement) -> List[str]:
        """Fast formation algorithm prioritizing availability"""
        # Sort by availability and select quickly
        candidates.sort(key=lambda x: x.availability, reverse=True)
        return [c.agent_id for c in candidates[:requirements.team_size_range[1]]]
        
    async def _experience_based_formation(self, candidates: List[AgentSkillProfile], 
                                        requirements: TeamRequirement) -> List[str]:
        """Experience-based formation algorithm"""
        # Implementation would prioritize experience and performance history
        return await self._optimal_performance_formation(candidates, requirements)
        
    async def _collaboration_focused_formation(self, candidates: List[AgentSkillProfile], 
                                             requirements: TeamRequirement) -> List[str]:
        """Collaboration-focused formation algorithm"""
        # Implementation would prioritize collaboration scores and team chemistry
        return await self._optimal_performance_formation(candidates, requirements)
        
    async def update_agent_profile(self, agent_id: str, updates: Dict[str, Any]):
        """Update agent profile with new information"""
        if agent_id in self.agent_profiles:
            profile = self.agent_profiles[agent_id]
            
            # Update skills
            if 'skills' in updates:
                profile.skills.update(updates['skills'])
                
            # Update performance history
            if 'performance_score' in updates:
                profile.performance_history.append(updates['performance_score'])
                if len(profile.performance_history) > 100:  # Keep last 100 scores
                    profile.performance_history.pop(0)
                    
            # Update other attributes
            for attr in ['availability', 'collaboration_score', 'current_workload']:
                if attr in updates:
                    setattr(profile, attr, updates[attr])
                    
            logger.info(f"Updated profile for agent {agent_id}")
            
    async def get_formation_analytics(self) -> Dict[str, Any]:
        """Get analytics on team formation performance"""
        if not self.formation_history:
            return {'total_formations': 0}
            
        total_formations = len(self.formation_history)
        
        # Strategy usage
        strategy_counts = {}
        for record in self.formation_history:
            strategy = record['strategy']
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
            
        # Average team metrics
        all_metrics = [record['team_metrics'] for record in self.formation_history]
        avg_metrics = {}
        
        if all_metrics:
            metric_keys = all_metrics[0].keys()
            for key in metric_keys:
                values = [metrics[key] for metrics in all_metrics if key in metrics]
                avg_metrics[f'avg_{key}'] = np.mean(values) if values else 0.0
                
        return {
            'total_formations': total_formations,
            'strategy_usage': strategy_counts,
            'average_metrics': avg_metrics,
            'total_agents_profiled': len(self.agent_profiles)
        }
